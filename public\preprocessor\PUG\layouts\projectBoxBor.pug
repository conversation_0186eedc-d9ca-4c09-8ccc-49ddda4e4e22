mixin projectBoxBor(color, icon, head, text, progressDoneSteps=false, progressTotalSteps=5)
    -let circleProgress = Math.ceil((progressDoneSteps / progressTotalSteps) * 100);
    .meta-project.bor.d-flex(class=color)
        .icon-wrap.d-flex.align-items-center.justify-content-center.me-3
            if progressDoneSteps > 0
                .progress-bar(data-steps=circleProgress, style=`--value:${circleProgress}`)
            else
                .border-bar
            .icon.d-flex.align-items-center.justify-content-center
                img(src=`images/${icon}-project-icon.svg`, alt="")
            if progressDoneSteps > 0
                .over
                    h2.mb-0 #{circleProgress}%
        .copy
            h2.mb-1 #[a(href="track-project.html")=head]
            p.text-white=text