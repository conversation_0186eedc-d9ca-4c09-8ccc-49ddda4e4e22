mixin headClient(h1, backLink=false, newCta=false, star=false)
    section.client-header
        .container-xxl
            hr.mt-0.mb-4.border-white
            if backLink
                .back-page
                    a.d-inline-flex.align-items-center.text-decoration-none(href='team-portal.html') #[img.me-2(src='images/back-arrow-icon.svg', alt='') ] #{backLink}
            .meta(class=(backLink ? 'd-flex' : ''))
                if star
                    .heading-title.d-flex.align-items-center
                        .icon-star.me-2
                            img(src="images/star-icon.svg", alt="", height="30", width="30")
                        h1.heavy.text-white.mb-0 !{h1}
                else
                    h1.heavy.text-white.mb-0 !{h1}
                if newCta && backLink
                    .add-task.ms-auto.d-flex
                        a.cta.d-inline-flex.align-items-center.text-uppercase.text-decoration-none.border-1.py-2.px-4.mt-0(href=`${newCta.replaceAll(' ', '-').toLowerCase()}.html`)=newCta
                            span.img.ms-2
                                +plusIcon('#ff5811')