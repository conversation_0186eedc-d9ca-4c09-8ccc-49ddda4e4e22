.projects-hub {
	background: #fff;
	height: calc(100vh - 180px);
	overflow: hidden;

	.controls-hub {
		flex: 0 0 76px;
		height: 100%;
		overflow-x: hidden;
		overflow-y: auto;
		padding-inline: 1.25rem;
		width: 76px;

		hr {
			border-color: #111;
			opacity: 1;
		}

		.action {
			+ .action {
				margin-top: 10px;
			}
		}
	}

	.projects-columns {
		border-left: 0.5px solid #111;
		gap: 2rem;
		overflow-x: auto;
		overflow-y: hidden;
		padding: 2rem 3rem;

		.project-column {
			flex: 0 0 320px;
			height: 100%;
			width: 320px;

			.head {
				border-bottom: 0.5px solid #111;

				h2 {
					font-size: 16px;
				}

				.sort {
					color: rgba(#111, 0.5);
					cursor: pointer;
					font: 500 14px $inter;
				}

				.icon-more {
					color: #bcbcbc;
					cursor: pointer;
				}
			}

			.project-list {
				height: calc(100vh - 336px);
				overflow-y: auto;

				.project-box {
					&__flag-list {
						width: 1rem;
					}

					&__flag {
						border-radius: 10px 0 0 10px;
						height: 0.5rem;
						width: 1rem;

						&.gold {
							background: #e9a302;
						}

						&.green {
							background: #21b583;
						}

						&.blue {
							background: #405fc1;
						}

						&.orange {
							background: #ff5811;
						}

						&.dull {
							background: #bcbcbc;
						}

						+ .project-box__flag {
							margin-top: 5px;
						}
					}

					&__meta {
						background: #f8f7f4;
						border-radius: 0.5rem;
						padding: 1rem 1rem 0.625rem;
					}

					&__phase {
						margin-bottom: 10px;

						h2 {
							color: #bcbcbc;
							font-size: 12px;
						}
					}

					&__name {
						h2 {
							color: #111;
							font: 16px $inter;
						}
					}

					&__info {
						color: rgba(#111, 0.5);
						font: 500 14px $inter;
					}
				}
			}

			.foot {
				border-top: 0.5px solid #111;
				padding: 1.25rem 1rem;

				.add-project-cta {
					background: #f8f7f4;
					border-radius: 0.5rem;
					color: #000;
					font: 500 16px/0.8 $inter;
					padding: 0.8rem 1rem;
				}

				.icon {
					height: 18px;
					width: 18px;
				}
			}

			&.stale,
			&.dull {
				.head {
					.meta {
						h2 {
							color: #c5c5c5;
						}
					}
				}

				.foot {
					.icon {
						svg {
							path {
								stroke: #c5c5c5;
							}
						}
					}
				}
			}

			@each $name, $color in $colors {
				&.#{ $name } {
					@include project-column($color);
				}
			}
		}
	}
}
