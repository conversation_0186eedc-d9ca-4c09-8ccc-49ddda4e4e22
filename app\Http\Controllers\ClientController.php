<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use App\Models\SocialDetail;
use App\Models\Client;
use App\Models\Role;
use App\Models\User;
use App\Models\Phase;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class ClientController extends Controller
{
    //



    public function index()
    {


        $clients = Client::with('brands')->get();
        return view('client.index', compact('clients'));
    }


    public function edit_client(Request $request, $id)
    {

        $client = Client::with('brands')->findOrFail($id);
        $brands = Brand::all();
        $brands = $brands->unique('name');
        $social_details = $client->socialDetails;
        $users = User::whereHas('role', function ($query) {
            $query->whereNotIn('name', ['Client Member', 'Brand Member']);
        })->get();
        return view('client.edit_client', compact('client', 'brands', 'users', 'social_details'));
    }


    public function show($id)
    {
        $client = Client::with('brands')->findOrFail($id);
        return view('client.show', compact('client'));
    }

    public function add_client()
    {
        $socialDetails = SocialDetail::all();
        $brands = Brand::all();
        $brands = $brands->unique('name');
        $users = User::whereHas('role', function ($query) {
            $query->whereNotIn('name', ['Client Member', 'Brand Member']);
        })->get();
        if ($brands->isEmpty()) {
            return view('client.add_client');
        } else {
            return view('client.add_client', compact('users', 'brands', 'socialDetails'));
        }
    }

    public function save_client(Request $request)
    {
        Log::info('Starting client creation process', ['request_data' => $request->except(['logo'])]);

        $request->validate([
            'name' => 'required|string|max:255|unique:clients,name',
            'job_code' => 'required|string',
            'social_detail' => 'required|array',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'brands' => 'nullable'
        ]);

        try {
            DB::beginTransaction();


            $client = new Client();
            $client->name = $request->name;
            $client->job_code = $request->job_code;
            $client->user_id = auth()->user()->id;

            if ($request->file('logo')) {
                Log::info('Processing logo upload');
                $logo = $request->file('logo');
                $filename = time() . '.' . $logo->getClientOriginalExtension();
                $logo->storeAs('images', $filename, 'public');
                $client->logo = $filename;
            }

            $client->save();
            Log::info('Client base record created', ['client_id' => $client->id]);

            if ($request->filled('social_detail') && is_array($request->social_detail)) {

                $result = $this->sendCredentialsToClient($request, $client);
                if (!$result['success']) {
                    Log::error('Failed to send credentials to client', ['errors' => $result['errors']]);
                    DB::rollBack();
                    return back()->withErrors($result['errors'])->withInput();
                }
            }

            if ($request->brands) {
                Log::info('Processing brand attachments', ['brands' => $request->brands]);
                if (gettype($request->brands) == 'array') {
                    foreach ($request->brands as $brand_id) {
                        $client->brands()->attach($brand_id);
                    }
                } else {
                    if (!$request->brands === 'null') {
                        $client->brands()->attach($request->brands);
                    }
                }
            }

            if ($request->users) {
                $users = json_decode($request->users, true);
                if (!$client->users()->exists()) {
                    foreach ($users as $user_id) {
                        $client->users()->attach($user_id);
                    }
                } else {
                    if (!$request->users == null) {
                        foreach ($users as $user_id) {
                            if (!$client->users()->where('user_id', '=', $user_id)->exists()) {
                                $client->users()->attach($user_id);
                            }
                        }
                    }
                }
            }

            DB::commit();
            Log::info('Client creation completed successfully', ['client_id' => $client->id]);

            if (auth()->user()->role->name == 'SuperAdmin') {
                return redirect()->route('admin-clients')->with('success', 'Client added successfully');
            } else {
                return redirect()->route('add-client')->with('success', 'Client added successfully');
            }
        } catch (\Exception $e) {
            Log::error('Error occurred while saving client', [
                'error_message' => $e->getMessage(),
                'error_line' => $e->getLine(),
                'error_file' => $e->getFile(),
                'stack_trace' => $e->getTraceAsString()
            ]);
            DB::rollBack();
            return back()->withErrors(['error' => 'An error occurred while saving the client. Please try again.'])->withInput();
        }
    }


    protected function sendCredentialsToClient(Request $request, Client $client)
    {
        Log::info('Starting to send credentials to client', ['client_id' => $client->id]);

        // Validate the social_detail input
        $validator = Validator::make($request->all(), [
            'social_detail' => 'required|array',
            'social_detail.*' => 'email|distinct',
        ]);

        if ($validator->fails()) {


            Log::error('Validation failed for social details', ['errors' => $validator->errors()->toArray()]);
            return ['success' => false, 'errors' => $validator->errors()];
        }

        foreach ($request->social_detail as $email) {


            try {
                $social_detail =  SocialDetail::create([
                    'value' => $email,
                    'assigend_to_model' => 'Client',
                    'type' => 'email',
                    'assigned_to_id' => $client->id,
                ]);

                DB::table('social_details_pivot')->insert([
                    'social_detail_id' => $social_detail->id,
                    'client_id' => $client->id,
                ]);

                $randomPassword = Str::random(12);
                $clientRole = Role::where('name', 'Client Member')->first();


                if (User::where('email', $email)->exists()) {

                    return [
                        'success' => false,
                        'errors' => ['social_detail' => ["The email '$email' is already in use."]]
                    ];
                }


                $clientUser = User::create([
                    'name' => $request->name,
                    'email' => $email,
                    'password' => Hash::make($randomPassword),
                    'role_id' => $clientRole->id,
                ]);

                db::table('client_user_links')->insert([
                    'client_id' => $client->id,
                    'user_id' => $clientUser->id,
                ]);

                Log::info('Successfully created user and sent notification', [
                    'user_id' => $clientUser->id,
                    'email' => $email
                ]);

                // $clientUser->notify(new \App\Notifications\ClientCreatedNotification($clientUser, $randomPassword));
            } catch (\Exception $e) {
                Log::error('Error in credential creation process', [
                    'email' => $email,
                    'error_message' => $e->getMessage(),
                    'error_line' => $e->getLine(),
                    'error_file' => $e->getFile()
                ]);
                throw $e;
            }
        }

        Log::info('Successfully completed sending credentials to all emails', ['client_id' => $client->id]);
        return ['success' => true];
    }












    public function update_client(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:clients,name,' . $request->route("client_id"),
            'job_code' => 'required|string',
            'social_detail' => 'required|array',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'brands' => 'nullable',
        ]);

        try {
            DB::beginTransaction();
            $clientId = $request->route('client_id');
            $client = Client::findOrFail($clientId);

            $client->name = $request->name;
            $client->job_code = $request->job_code;
            if ($request->file('logo')) {
                $logo = $request->file('logo');
                $filename = time() . '.' . $logo->getClientOriginalExtension();
                $logo->storeAs('images', $filename, 'public');
                $client->logo = $filename;
            }
            $client->save();

            // Get existing social details and their associated users
            $existingSocialDetails = SocialDetail::where('assigned_to_id', $client->id)
                ->where('assigend_to_model', 'Client')
                ->where('type', 'email')
                ->get();

            $existingEmails = $existingSocialDetails->pluck('value')->toArray();
            $newEmails = $request->social_detail;

            // Find emails to remove
            $emailsToRemove = array_diff($existingEmails, $newEmails);

            // Remove users and social details for removed emails
            foreach ($emailsToRemove as $email) {
                $user = User::where('email', $email)->first();
                if ($user) {
                    // Remove user-client link
                    DB::table('client_user_links')->where('client_id', $client->id)
                        ->where('user_id', $user->id)
                        ->delete();
                }
            }

            // Delete old social details
            DB::table('social_details_pivot')->where('client_id', $client->id)->delete();
            SocialDetail::where('assigned_to_id', $client->id)
                ->where('assigend_to_model', 'Client')
                ->where('type', 'email')
                ->delete();

            // Process new social details
            if ($request->filled('social_detail') && is_array($request->social_detail)) {
                foreach ($request->social_detail as $email) {
                    if ($email) {
                        // Create social detail
                        $social_detail = SocialDetail::create([
                            'value' => $email,
                            'assigend_to_model' => 'Client',
                            'type' => 'email',
                            'assigned_to_id' => $client->id,
                        ]);

                        DB::table('social_details_pivot')->insert([
                            'social_detail_id' => $social_detail->id,
                            'client_id' => $client->id,
                        ]);

                        // Check if user exists
                        $existingUser = User::where('email', $email)->first();

                        if (!$existingUser) {
                            // Create new user if doesn't exist
                            $randomPassword = Str::random(12);
                            $clientRole = Role::where('name', 'Client Member')->first();

                            $newUser = User::create([
                                'name' => $request->name,
                                'email' => $email,
                                'password' => Hash::make($randomPassword),
                                'role_id' => $clientRole->id,
                            ]);

                            // Link user to client
                            DB::table('client_user_links')->insert([
                                'client_id' => $client->id,
                                'user_id' => $newUser->id,
                            ]);

                            // Send notification
                            // $newUser->notify(new \App\Notifications\ClientCreatedNotification($newUser, $randomPassword));
                        } else {
                            // Link existing user to client if not already linked
                            if (!$client->users()->where('user_id', $existingUser->id)->exists()) {
                                DB::table('client_user_links')->insert([
                                    'client_id' => $client->id,
                                    'user_id' => $existingUser->id,
                                ]);
                            }
                        }
                    }
                }
            }

            // Handle brands
            if ($request->brands) {
                if (gettype($request->brands) == 'array') {
                    foreach ($request->brands as $brand_id) {
                        $client->brands()->attach($brand_id);
                    }
                } else {
                    if ($request->brands !== 'null') {
                        $client->brands()->attach($request->brands);
                    }
                }
            }

            // Handle additional users
            if ($request->users) {
                $users = json_decode($request->users, true);
                // Get current users
                $currentUsers = $client->users()->pluck('user_id')->toArray();

                // Users to add (in new selection but not in current)
                $usersToAdd = array_diff($users, $currentUsers);

                // Users to remove (in current but not in new selection)
                $usersToRemove = array_diff($currentUsers, $users);

                // Add new users
                if (!empty($usersToAdd)) {
                    foreach ($usersToAdd as $user_id) {
                        $client->users()->attach($user_id);
                    }
                }

                // Remove unselected users
                if (!empty($usersToRemove)) {
                    foreach ($usersToRemove as $user_id) {
                        $client->users()->detach($user_id);
                    }
                }
            } else {
                // If no users are selected, remove all existing users
                $client->users()->detach();
            }

            DB::commit();

            if (auth()->user()->hasRole(['SuperAdmin', 'Admin'])) {
                return redirect()->route('admin-clients')->with('success', 'Client updated successfully');
            } else {
                return redirect()->route('admin-clients')->with('success', 'Client updated successfully');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating client', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return back()->withErrors(['error' => 'An error occurred while updating the client.'])->withInput();
        }
    }


    public function client_users(Request $request)
    {
        $clientId = $request->input('client_id');
        if (!$clientId) {
            return response()->json(['error' => 'Client ID is required'], 400);
        }
        $client = Client::findOrFail($clientId);

        $users = $client->users()->get();

        return response()->json([
            'success' => true,
            'client' => $client,
            'users' => $users,
        ]);
    }

    public function client_social_details(Request $request)
    {
        $clientId = $request->input('client_id');
        if (!$clientId) {
            return response()->json(['error' => 'Client ID is required'], 400);
        }
        
        $client = Client::findOrFail($clientId);
        $socialDetails = $client->socialDetails()->where('type', 'email')->get();

        return response()->json([
            'success' => true,
            'client' => $client,
            'social_details' => $socialDetails,
        ]);
    }

    public function dashboard()
    {
        $user = auth()->user();
        $client = Client::whereHas('linkedUsers', function ($query) use ($user) {
            $query->where('users.id', $user->id);
        })->first();

        $clientProjects = $client ? $client->projects()->with(['phases', 'timeline'])->get() : collect();

        $activeProjects = $clientProjects->filter(function ($project) {
            return optional($project->timeline)->type !== 'voyager' && $project->archived == false;
        })->map(function ($project) {
            $currentPhase = Phase::find($project->phase_id);
            $phaseColor = $currentPhase ? $currentPhase->color_code ?? '#6c757d' : '#6c757d';
            $colorClass = $currentPhase ? $currentPhase->getColorClass() : 'gray';


            $phaseIcon = $currentPhase ? $currentPhase->icon : 'images/content-project-icon.svg';
            if ($phaseIcon) {
                $phaseIcon = str_replace('localhost', '127.0.0.1:8000', $phaseIcon);
            }
            $currentPhaseOrder = $currentPhase ? $currentPhase->order : 1;
            $totalPhases = $project->phases->count();

            return [
                'project' => $project,
                'currentPhase' => $currentPhase,
                'phaseColor' => $phaseColor,
                'colorClass' => $colorClass,
                'phaseIcon' => $phaseIcon,
                'currentPhaseOrder' => $currentPhaseOrder,
                'totalPhases' => $totalPhases,
                'category_id' => $project->category_id ?? null, // Use null as a default if category_id is missing
            ];
        });

        $archivedProjects = $clientProjects->filter(function ($project) {
            return $project->archived == true;
        });

        $voyagerProjects = $clientProjects->filter(function ($project) {
            return optional($project->timeline)->type === 'voyager';
        });

        $lastPhase = Phase::orderBy('id', 'desc')->first();
        $lastPhaseId = $lastPhase ? $lastPhase->id : null;

        return view('client.dashboard', compact('user', 'client', 'activeProjects', 'archivedProjects', 'voyagerProjects', 'lastPhaseId'));
    }


    public function projects()
    {
        return view('client.Projects');
    }
}
