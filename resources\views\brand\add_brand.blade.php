@extends('layout.app')
@section('title', 'Add Enterprise')
@section('content')


@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
@endif
@if (session('error'))
    <script>
        @foreach ((array) session('error') as $message)
            errorToast({{ json_encode($message) }});
        @endforeach
    </script>
@endif


@if ($errors->any())
    @foreach ($errors->all() as $error)
        <script>
            errorToast(@json($error));
        </script>
    @endforeach
@endif



<!-- New Code  -->

<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            {{-- @if(admin_superadmin_permissions()) --}}
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-brands') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            {{-- @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('brands') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @endif --}}
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">New <i>Enterprise</i></h1>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl projects-row">
        <div class="project-column task-overview">
            <div class="col-head align-items-center d-flex justify-content-between">
                <h2 class="text-uppercase">SET UP NEW Enterprise</h2>
                <p class="fst-italic">Note: Only clients who have sub-companies should be set up as a Enterprise</p>
            </div>
            <hr class="mt-0 mb-4 border-white" />
            <form class="row form-wrap" action="{{ route('save-brand') }}" method="post" enctype="multipart/form-data" id="add_brand_form">
                @csrf
                <input type="hidden" name="admin_id" value="{{ Auth::user()->id }}">
                <div class="col-md-4 col-xl-3 mb-4">
                    <input name="job_code" class="form-control border-0" type="text" id="job_code" placeholder="JOB CODE PREFIX" />
                    <div class="form-text fst-italic opacity-50">Suggested Length: 2-4 Characters</div>
                </div>
                <div class="col-md-8 col-xl-9 mb-4">
                    <input name="name" class="form-control border-0" type="text" id="name" placeholder="Add a Enterprise name..." />
                </div>
                <div class="col-head col-12 mt-5">
                     <h2 class="text-uppercase">
                        CONTACTS 
                        <span class="d-inline-block ms-3 text-capitalize">
                            <span class="alert  d-inline-block py-2 px-3 mb-0 fs-6 fw-normal border  shadow-sm" style="background-color:#111">
                            <i class="fas fa-sticky-note me-1"
                            style="background-color: orangered; color: white; padding: 4px; border-radius: 4px; display: inline-block;"></i>

                            <strong>Note:</strong> User can login through these contact details, given email and a random strong password will be sent to the user for login
                            </span>
                        </span>
                        </h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4 d-flex align-items-center justify-content-between">
                    <h2 class="text-white mb-0">Add Contacts</h2>
                    <button id="addMoreContactsButton" type="button" class="cta ms-3 mt-0">Add More Contact</button>
                </div>
                <div id="contactsWrapper">
					<div class="col-12 mt-4 contacts-input">
						<div class="input-wrap d-flex">
							<div class="icon d-flex align-items-center justify-content-center"><img src="{{ asset('images/email-icon.svg') }}" alt="" /></div>
							<div class="input flex-grow-1">
								<input class="form-control border-0" type="email" id="email" name="social_detail[]" value="{{ old('social_detail') }}" />
							</div>
						</div>
					</div>
				</div>
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">ENTERPRISE</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4">
                    <div class="row">
                        <div class="col-md-4">
                            <h2 class="text-white">Add clients to Enterprise</h2>
                        </div>
                        <div class="col-md-8">
                            <input type="hidden" name="clients" class="d-none" value="[]" id="clients">
                            <div class="brand-wrap">
                                    @php if( isset( $clients ) && count($clients) > 0 )
                                        foreach( $clients as $client ) {
                                            echo '<div class="brand-select d-flex align-items-center justify-content-between">
                                                <div class="name text-uppercase">'.$client->name.'</div>
                                                <button class="cta text-uppercase mt-0" data-client-id="'.$client->id.'" type="button">Add</button>
                                            </div>';
                                        }
                                    @endphp
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h2 class="text-white">Upload Logo</h2>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="upload-btn-wrapper">
                                <input type="file" name="logo" id="logo" class="d-none" />
                                <button type="button" id="uploadTrigger" class="btn-upload text-uppercase text-white d-flex align-items-center">
                                    <i class="bi bi-upload me-2"></i> Upload a file
                                </button>
                            </div>
                            <div class="upload-files d-none d-flex align-items-center justify-content-between mt-2">
                                <div class="form-text fst-italic opacity-50" id="selectedFileName"></div>
                                <span class="remove-selected" style="cursor: pointer;"> <i class="fa-solid fa-xmark fa-lg" style="color: #ffffff;"></i> </span>
                            </div>
                        </div>
                    </div>
                    <div class="form-text fst-italic opacity-50">Suggested Format: One-color white SVG or PNG</div>
                </div>
                <div class="col-12 mt-5 text-center">
                    <button class="cta text-uppercase mt-0" type="submit">CREATE Brand</button>
                </div>
            </form>
        </div>
    </div>
</section>


<script>

document.addEventListener('DOMContentLoaded', function () {

initializeContactManagement();
});




function initializeContactManagement() {
    const addMoreContactsButton = document.getElementById('addMoreContactsButton');
    const contactsWrapper = document.getElementById('contactsWrapper');
    
    if (addMoreContactsButton && contactsWrapper) {
        let contactCounter = 1;
        
        addMoreContactsButton.addEventListener('click', function() {
            contactCounter++;
            addNewContactField(contactsWrapper, contactCounter);
        });
    }
}

function addNewContactField(contactsWrapper, contactCounter) {
    const newContactId = `email_${contactCounter}`;
    
    const newContactDiv = document.createElement('div');
    newContactDiv.className = 'col-12 mt-3 contacts-input';
    
    const iconSrc = document.querySelector('.contacts-input .icon img')?.src || '';
    
    newContactDiv.innerHTML = `
        <div class="input-wrap d-flex">
            <div class="icon d-flex align-items-center justify-content-center"><img src="${iconSrc}" alt="" /></div>
            <div class="input flex-grow-1">
                <input class="form-control border-0" type="email" id="${newContactId}" name="social_detail[]" />
            </div>
            <div class="remove-contact ms-2">
                <button type="button" class="btn btn-sm btn-danger">Remove</button>
            </div>
        </div>
    `;
    contactsWrapper.appendChild(newContactDiv);

   
    if (typeof validation !== 'undefined') {
        validation.addField(`#${newContactId}`, [
            {
                rule: 'email',
                errorMessage: 'Please provide a valid email address',
            }
        ]);
    }
    
   
    const removeButton = newContactDiv.querySelector('.remove-contact button');
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            if (typeof validation !== 'undefined') {
                validation.removeField(`#${newContactId}`);
            }
            contactsWrapper.removeChild(newContactDiv);
        });
    }
}








    // handle file upload 
    document.getElementById("uploadTrigger").addEventListener("click", function () {
        document.getElementById("logo").click();
    });

    document.getElementById("logo").addEventListener("change", function () {
        const file = this.files[0];
        if (file) {
            console.log("selected file");
            document.getElementById("selectedFileName").append(file.name);
            document.querySelector(".upload-files").classList.remove("d-none");
        }
    });
    document.querySelector(".remove-selected").addEventListener("click", function () {
        document.getElementById("logo").value = "";
        document.getElementById("selectedFileName").innerText = "";
        document.querySelector(".upload-files").classList.add("d-none");
    });


    // handle brand selection 
    document.querySelectorAll('.brand-select').forEach(function (item) {
        const clientsInput = document.querySelector('[name="clients"]');

        item.querySelector('button').addEventListener('click', function () {
            const clientId = this.getAttribute('data-client-id');
            let selectedClients = JSON.parse(clientsInput.value || '[]');

            item.classList.toggle('added');
            this.classList.toggle('added');

            if (item.classList.contains('added')) {
                if (!selectedClients.includes(clientId)) {
                    selectedClients.push(clientId);
                    this.innerText = 'Added';
                }
            } else {
                selectedClients = selectedClients.filter(id => id !== clientId);
                this.innerText = 'Add';
            }
            clientsInput.value = JSON.stringify(selectedClients);
        });
        console.log(clientsInput.value, "value");
    });



    /**
     * validate form
     */
    const form_id = 'add_brand_form';
    let args = {
            rules : {
                '#name': GlobalFormValidator.validators.required,
                '#job_code': GlobalFormValidator.validators.required,
                '#email': GlobalFormValidator.validators.required,
            },
            messages: {
                '#name': "Please select a brand",
                '#job_code': "Please add a job code",
                '#email': "Please add a contact information",
            },
            onSuccess: (form) => {
                form.submit();
            }
        }
    let Validator = new GlobalFormValidator(form_id, args);
    Validator.initialize();

   
</script>

@endsection