- let pageName = 'Client Portal';
- let mainPage = pageName;
- let clientPage = 'Archived Projects';
- let pageTitle = `${pageName}: ${clientPage}`;

include ../layouts/svgIcon.pug
include ../layouts/projectBoxMetaRow.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('All, <i>Projects</i>', false, false, false, 'Back to Client Portal', false, true)

        section.client-project.pt-0
            .container-xxl
                hr.mt-0.mb-4.border-white
                .projects-list-table
                    .head.d-flex.align-items-center.justify-content-between
                        .years
                            each year, index in ['All', 2025, 2024, 2023]
                                a(href="#", class=(index === 0 ? 'active' : ''))=year
                        .pages.d-flex
                            span Page
                            each i, index in [1,2]
                                a(href="#", class=(index === 0 ? 'active' : ''))=i
                    +projectBoxMetaRow('orange', 'checked', '[SP-006-25]', 'Sample Project Website 2025 Voyager Lorem Ipsum', ['Site Analytics', 'Billing History'])
                    - for(let i = 0; i < 11; i++)
                        +projectBoxMetaRow('orange', 'checked', '[SP-006-24]', 'Sample Project Lorem Ipsum Set Dolor', ['Site Analytics', 'Billing History'])
                    .d-flex.justify-content-end.my-5
                        .pages.d-flex
                            span Page
                            each i, index in [1,2]
                                a(href="#", class=(index === 0 ? 'active' : ''))=i
                hr.mt-0.mb-4.border-white

        section.ask-question
            .container-xxl
                .d-flex.align-items-center.justify-content-between.pb-4
                    .head
                        h2.mb-0 Have a question?#[br] #[i We’re here to help.]
                    .cta-row
                        each link in ['GET EMAIL UPDATES', 'NEED HELP? CONTACT']
                            a.cta.link.text-decoration-none.text-white(href="#")=link
                hr.mt-0.mt-4.border-white

        include ../partials/footer.pug
        +footer()