@extends('layout.app')
@section('title', 'Edit Company')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">
                    <h4>
                        @if(request()->route()->named('edit-company'))
                            Edit Company
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('edit-company'))
                        @if(isset($no_companies_found_error) && $no_companies_found_error!='')
                            <div class="no_companies_found_error">{{ $no_companies_found_error }}</div>
                        @else

                            <div class="see-all-tasks-btn-div">
                            @if(Auth::user()->role_id=='1')
                                <form method="post" action="{{ route('delete-company', ['company_id'=>request()->route("company_id")]) }}">
                                    @csrf
                                    @method("delete")
                                    <a class="me-2" href="" onclick="event.preventDefault(); this.closest('form').submit()"><button>Delete Company <i class="fa fa-trash"></i></button></a>
                                </form>
                            @endif
                            </div>


                            @if(Session::has('company_updated_success_message'))
                                <div class="bg-success text-light text-center mx-auto my-1 p-2 w-50 rounded">
                                    {{ Session::get('company_updated_success_message') }}
                                </div>
                            @endif
                            <div class="edit-company-div mx-auto rounded">
                                <form action="{{ route('update-company', ['company_id'=>request()->route("company_id")]) }}" method="post" class="border border-secondary w-50 mt-1 mx-auto p-3"  name="edit-company-form">
                                    @csrf
                                  
                                    <div class="d-flex">
                                        <div class="edit-company-div-label">
                                            Company Name:
                                        </div>
                                        <div class="edit-company-div-input d-flex flex-column">
                                            <input class='edit-company-input' type="text" name="company_name" value="{{ $company->name }}">
                                            @error("company_name")
                                                <div class="text-danger">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <br>
                                    <div class="d-flex">
                                        <div class="edit-company-div-label">
                                            Assign Projects:
                                        </div>
                                        <div class="edit-company-div-input d-flex flex-column">
                                            <select class="edit-company-select companies-list-dropdown" id="0" name="project_id[]" multiple>
                                                <option value=''>Select Company</option>
                                            </select>
                                            @error("company_id")
                                                <div class="text-danger">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <br>
                                    <div>
                                        <button type="submit">Update</button>
                                    </div>
                                </form>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection


@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }
        .main-div{
            display:flex;
            height:100%;
            background-color:white;
        }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;

            left: 0;
            bottom:0;
            z-index: 0;
            height: calc(100vh - 50px);
            overflow:scroll;
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
            left: 0;
            bottom:0;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            /* padding:0px 50px;
            display:flex;
            justify-content:center; */
        }
        .see-all-tasks-btn-div{
            display: flex;
            justify-content: end;
        }
        form[name="edit-company-form"]{
            width:100%;
            text-align:center;
        }
        /*.company-details-div{
            margin-top:10px;
            padding:10px;
            width:50%;
            border:1px solid grey;
            display: flex;
            justify-content: center;
        }
        .company-details-div-label{
            width:25%;
            text-align:left;
        }
        .company-details-div-input{
            width:75%;
            text-align:left;
        }*/
        .edit-company-div{
            width:100%;
            display: block;
        }
        .edit-company-div-label{
            text-align:left;
            width:60%;
        }
        .edit-company-div-input{
            width:70%;
        }
        .edit-company-input{
            width:70%;
        }
        .edit-company-select{
            width: 70%;
        }
        .no_companies_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
        select option:selected{
            background-color:red;
        }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){
            load_projects_in_dropdown(0);

            function load_projects_in_dropdown($user_id=0){
                $.ajax({
                    url: "{{ route('projects-ajax') }}",
                    method: "get",
                    success: function(response){
                        
                        $('.companies-list-dropdown#0').html("<option value=''>Select Company</option>");//to destroy cached response
                        if(response!=""){
                            $.each(response, function(index, project) {
                                let checkedAttribute = ''; // Initialize checked attribute
                                
                                // Check if the current project's ID is present in the selectedCompanyIds array
                                if ((<?php echo json_encode($project_ids); ?>) .includes(project.id)) {
                                    checkedAttribute = 'selected';
                                }

                                $('.companies-list-dropdown').append(
                                    `<option value="${project.id}" data-project_id="${project.id}" ${checkedAttribute}>${project.name} (${project.id})</option>`
                                );
                            });

                                
                        }
                        else
                        {
                        }
                    }
                })
            }
        });
    </script>
@endpush