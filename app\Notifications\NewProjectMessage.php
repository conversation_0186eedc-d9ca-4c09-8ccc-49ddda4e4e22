<?php

namespace App\Notifications;

use App\Models\ProjectMessageThread;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewProjectMessage extends Notification
{
    use Queueable;

    protected $message;

    public function __construct(ProjectMessageThread $message)
    {
        $this->message = $message;
    }

    public function via($notifiable): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('New Message: ' . $this->message->subject)
            ->greeting('Hello ' . $notifiable->name)
            ->line('A new message has been posted in project: ' . $this->message->project->name)
            ->line('From: ' . $this->message->postedBy->name)
            ->line('Subject: ' . $this->message->subject)
            ->line('Message:')
            ->line($this->message->message)
            ->action('View Message', url('/projects/' . $this->message->project_id . '/messages'))
            ->line('Thank you for using our application!');
    }
}
