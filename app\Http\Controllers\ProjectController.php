<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use App\Models\Client;
use App\Models\Phase;
use App\Models\ProjectMessage;
use App\Models\ProjectMessageFile;
use App\Models\Status;
use App\Models\Timeline;
use App\Models\Category;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\User;
use App\Models\SocialDetail;
use App\Models\Task;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use App\Notifications\ProjectAssigned;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ProjectMessageThread;

class ProjectController extends Controller
{
    //
    public function index()
    {
        $user = Auth::user();
        if ($user->role === 1) {
            $projects = Project::all();
            return view('project.index', compact('projects'));
        } else {
            $projects = $user->projects()->get();
            return view('project.index', compact('projects'));
        }
    }



    public function show_projects(Request $request)
    {
        //If user role is Admin, show all accounts i.e. Admin can access/view all user projects
        if (Auth::user()->role->name == 'SuperAdmin' || Auth::user()->role->name == 'Admin') {
            $year = $request->input('year');
            $query = Project::query();
            if ($year && $year !== 'all') {
                $query->whereYear('created_at', $year);
            }
            $years = Project::selectRaw('YEAR(created_at) as year') //IMP
                ->distinct()
                ->orderByDesc('year')
                ->pluck('year')
                ->toArray();

            $projects = $query->paginate(1)->withQueryString(); //IMP

            if ($projects->isNotEmpty()) {
                return view('admin.projects.index', compact('projects', 'year', 'years'));
            } else {
                return view('admin.projects.index', ['no_projects_found_error' => 'No projects found!!']);
            }
        } else {
            $projects = Auth::user()->projects()->get();
            // return $projects;
            if ($projects) {
                return view('project.index', ['projects' => $projects]);
            } else {
                return view('project.index', ['no_projects_found_error' => 'You are not assigned any projects. Please connect to Admin.']);
            }
        }
    }

    public function show_projects_ajax(Request $request)
    {
        $projects = Project::all();
        return $projects;
    }

    public function add_project()
    {
        // $users = User::all();
        $phases = Phase::all();
        $timelines = Timeline::all();
        $clients = Client::orderBy('name', 'asc')->get();

        return view('project.add_project', compact('phases', 'timelines', 'clients'));
    }


    // public function save_project(Request $request){

    //     dd( $request->all() );

    //     $validate = Validator::make($request->all(),[
    //         'project_title' => 'required|unique:projects,name,' . $request->route('project_id'),
    //     ]);

    //     if($validate->fails()){
    //         return back()->withErrors($validate)->withInput();
    //     };

    //     if( $request->input('social_detail') ){
    //         $social_detail = new SocialDetail();
    //         $social_detail->value = $request->input('social_detail');
    //         $social_detail->assigend_to_model = 'Project';
    //         $social_detail->save();
    //     }
    //     else{
    //         $social_detail = null;
    //     }


    //     $project = new Project;
    //     $project->name = $request->input('project_title');
    //     $project->status_id = "1";
    //     $project->job_code = $request->input('job_code');
    //     $project->client_id = $request->input('client_id');
    //     $project->assigned_by = Auth::user()->id;
    //     $project->social_detail_id = $social_detail ? $social_detail->id : null;
    //     $project->timeline_id = $request->input("timeline_type");
    //     $project->kickoff_type = $request->input("kickoff_type");
    //     $project->kickoff_title = $request->input("kickoff_title");
    //     $project->kickoff_description = $request->input("kickoff_description");

    //     if( $request->file('kickoff_file') ){
    //         $file = $request->file('kickoff_file');
    //         $filename = time() . '.' . $file->getClientOriginalExtension();
    //         $file->move(public_path('images'), $filename);
    //         $project->kickoff_file = $filename;
    //     }

    //     // 2. Save the new project to the database
    //     $project->save(); // This will generate and assign the $project->id

    //     if($project){

    //         //IMP
    //         $projectDurations = $request->only([
    //             'project_duration1',
    //             'project_duration2',
    //             'project_duration3',
    //             'project_duration4',
    //             'project_duration5',
    //         ]);

    //         $phaseDurationsToAttach = [];

    //         foreach ($projectDurations as $key => $durationValue) {
    //             if ($durationValue !== null) {
    //                 $phaseNumber = (int) str_replace('project_duration', '', $key);
    //                 $phaseId = $phaseNumber + 1; // Adjust if your phase numbering starts differently

    //                 // Assuming you have Phase records with these IDs
    //                 $phase = Phase::find($phaseId);

    //                 if ($phase) {
    //                     $phaseDurationsToAttach[$phaseId] = ['project_duration' => $durationValue . ' weeks'];
    //                 }
    //             }
    //         }

    //         // Attach the phases to the newly created project with their durations
    //         $project->phases()->attach($phaseDurationsToAttach);

    //         // Assign Users
    //         $users = $request->assign_to_user;
    //         if (!is_array($users)) {
    //             $users = (array) $users;
    //         }

    //         $usersToAttach = [];

    //         foreach ($users as $userId) {
    //             $userIdInt = (int) $userId;
    //             $usersToAttach[] = $userIdInt;
    //         }

    //         // Attach users without removing existing ones
    //         $project->users()->syncWithoutDetaching($usersToAttach);

    //         return back()->with('project_added_success_message', 'Project Added successfully.');
    //     }
    //     else{
    //         return back()->with('project_not_added_error_message', 'Project not Added successfully.');
    //     }
    // }

    public function save_project(Request $request)
    {


        // return $request->all();
        $validate = Validator::make($request->all(), [
            'project_title' => ['required', Rule::unique('projects', 'name')->whereNull('deleted_at')->ignore($request->route('project_id'))], //IMP
            'client_id' => 'required|exists:clients,id',
            'job_code' => 'required',
            'assigned_users' => 'required',
            'invoice_schedule' => 'required',
            'harvest_link' => 'nullable',
            'social_detail' => 'required|array',

            'kickoff_type' => 'nullable|in:yes,no',
            'kickoff_title' => 'nullable|required_if:kickoff_type,yes|string|max:255',
            'kickoff_description' => 'nullable|required_if:kickoff_type,yes|string',
            'kickoff_file' => 'nullable|file|mimes:jpg,jpeg,png,pdf,docx|max:2048',
        ]);



        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }





        $status = Status::where('name', 'New')->first();

        // Save Project
        // $project = new Project;
        // $project->name = $request->input('project_title');
        // $project->status_id = $status->id;
        // $project->job_code = $request->input('job_code');
        // $project->client_id = $request->input('client_id');
        // $project->assigned_by = Auth::id();
        // $project->timeline_id = $request->input("timeline_type");
        // $project->kickoff_type = $request->input("kickoff_type");
        // $project->kickoff_title = $request->input("kickoff_title");
        // $project->kickoff_description = $request->input("kickoff_description");
        // $project->invoice_schedule = $request->input('invoice_schedule');
        // $project->pm_hours = $request->input('pm_hours');
        // $project->designer_hours = $request->input('designer_hours');
        // $project->developer_hours = $request->input('developer_hours');
        // $project->harvest_link = $request->input('harvest_link');

        $project = new Project;
        $project->name = $request->input('project_title');
        $project->status_id = $status->id;
        $project->job_code = $request->input('job_code');
        $project->client_id = $request->input('client_id');
        $project->assigned_by = Auth::id();
        $project->timeline_id = $request->input("timeline_type");
        $project->invoice_schedule = $request->input('invoice_schedule');
        $project->pm_hours = $request->input('pm_hours');
        $project->designer_hours = $request->input('designer_hours');
        $project->developer_hours = $request->input('developer_hours');
        $project->cs_hours = $request->input('cs_hours');
        $project->harvest_link = $request->input('harvest_link');
        $project->save();

        if ($request->input('kickoff_type') === 'yes') {
            if ($project->kickoff_message_id) {
                // Update existing kickoff message
                $kickoffMessage = ProjectMessageThread::find($project->kickoff_message_id);
                if ($kickoffMessage) {
                    $kickoffMessage->subject = $request->input("kickoff_title");
                    $kickoffMessage->message = $request->input("kickoff_description");
                    $kickoffMessage->save();
                }
            } 
            else {
                // Create new kickoff message
                $kickoffMessage = new ProjectMessageThread;
                $kickoffMessage->project_id = $project->id;
                $kickoffMessage->subject = $request->input("kickoff_title");
                $kickoffMessage->message = $request->input("kickoff_description");
                $kickoffMessage->posted_by = Auth::id();
                $kickoffMessage->is_kickoff = 1;
                $kickoffMessage->save();
        
                $project->kickoff_message_id = $kickoffMessage->id;
                $project->save();
            }
        }

        // Handle file upload
        if ($request->hasFile('kickoff_file') && isset($kickoffMessage)) {
            // Delete previous kickoff file if it exists
            $existingFile = ProjectMessageFile::where('message_id', $kickoffMessage->id)->first();
        
            if ($existingFile) {
                // Optionally delete from storage (uncomment if needed)
                // Storage::delete($existingFile->file_path);
                $existingFile->delete();
            }
        
            // Store the new file
            $file = $request->file('kickoff_file');
            $path = $file->store('project-messages/' . $project->id);
        
            ProjectMessageFile::create([
                'message_id' => $kickoffMessage->id,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $path,
                'file_type' => $file->getClientMimeType(),
                'file_size' => $file->getSize()
            ]);
        }
        


        $voyagerProject = false;

        $projecttimeLineType = Timeline::find($request->input('timeline_type'));
        if ($projecttimeLineType->name == 'Voyager Project') {
            $voyagerProject = true;
        }


        if ($voyagerProject) {
            $phase = Phase::where('name', 'Voyager')->first();
            $project->phase_id = $phase->id;


            $flag = false;

            foreach ($phase->categories as $cat) {

                if ($cat->order === 1 && $flag === false) {

                    $flag = true;
                    $category_id = $cat->id;

                    $project->category_id = $category_id;
                }
            }
        } else  if ($request->input('phase_id')) {
            $phase = Phase::find($request->input('phase_id'));
            if ($phase) {
                $project->phase_id = $phase->id;
            }
        } else {

            $inputKeys = $request->keys();


            $durationKeys = collect($inputKeys)
                ->filter(fn($key) => str_starts_with($key, 'project_duration'))
                ->map(fn($key) => (int) str_replace('project_duration', '', $key));


            $targetKeys = collect($inputKeys)
                ->filter(fn($key) => str_starts_with($key, 'project_target'))
                ->map(fn($key) => (int) str_replace('project_target', '', $key));


            $commonIndexes = $durationKeys->intersect($targetKeys);


            $smallestIndex = $commonIndexes->sort()->first();

            if ($smallestIndex) {

                $phase = Phase::find($smallestIndex);
                if ($phase) {
                    $project->phase_id = $phase->id;
                }

                $lastCategory = $phase->categories->sortByDesc('order')->first();

                if ($lastCategory) {
                    $project->category_id = $lastCategory->id;
                }
            }
        }

        if ($request->input('category_id')) {

            $category = Category::find($request->input('category_id'));
            if ($category) {
                $project->category_id = $category->id;
            }
        }


        $project->save();

        // Save Social Detail
        if ($request->filled('social_detail') && is_array($request->social_detail)) {
            foreach ($request->social_detail as $email) {
                $social_detail =  SocialDetail::create([
                    'value' => $email,
                    'assigend_to_model' => 'Project',
                    'type' => 'email',
                    'assigned_to_id' => $project->id,
                ]);

                DB::table('social_details_pivot')->insert([
                    'social_detail_id' => $social_detail->id,
                    'project_id' => $project->id,
                ]);
            }
        }

        // Add Category

        $phaseDurationsToAttach = [];

        foreach ($request->all() as $key => $value) {
            // Process duration fields
            if (Str::startsWith($key, 'project_duration') && !empty($value)) {
                $phaseIndex = (int) str_replace('project_duration', '', $key);
                $phaseId = $phaseIndex;
                $phase = Phase::find($phaseId);

                if ($phase) {
                    // Initialize the array for this phase if not already exists
                    if (!isset($phaseDurationsToAttach[$phaseId])) {
                        $phaseDurationsToAttach[$phaseId] = [];
                    }

                    // Add duration
                    $phaseDurationsToAttach[$phaseId]['project_duration'] = $value;
                }
            }

            // Process target date fields
            if (Str::startsWith($key, 'project_target') && !empty($value)) {
                $phaseIndex = (int) str_replace('project_target', '', $key);
                $phaseId = $phaseIndex;
                $phase = Phase::find($phaseId);

                if ($phase) {
                    // Initialize the array for this phase if not already exists
                    if (!isset($phaseDurationsToAttach[$phaseId])) {
                        $phaseDurationsToAttach[$phaseId] = [];
                    }

                    // Add target date
                    $phaseDurationsToAttach[$phaseId]['project_target'] = $value;
                }
            }
        }

        $maintenance_timeline_type = Timeline::where('name', '=', 'Maintenance Project')->first();
        $maintenance_project_id = null;
        if ($maintenance_timeline_type) {
            $maintenance_project_id = $maintenance_timeline_type->id;
        }
        if ($request->input('timeline_type') != $maintenance_project_id) { //If the selected timeline_type's value is NOT the ID of the Maintenance Project, don't insert project duration and target into database
            // Now attach phases that have at least one value (either duration or target date)
            if (!empty($phaseDurationsToAttach)) {
                $currentPhaseIds = $project->phases()->pluck('phases.id')->toArray();

                foreach ($phaseDurationsToAttach as $phaseId => $data) {
                    if (in_array($phaseId, $currentPhaseIds)) {
                        $project->phases()->updateExistingPivot($phaseId, $data);
                    } else {
                        $project->phases()->attach([$phaseId => $data]);
                    }
                }
            }
        }

        // Attach Users
        $users = $request->input('assigned_users', []);
        $usersToAttach = is_array($users) ? $users : [(int) $users];

        $project->users()->syncWithoutDetaching($usersToAttach);
        foreach ($usersToAttach as $user) {
            try {
                $user = User::find($user);
                $user->notify(new ProjectAssigned($project));
            } catch (\Exception $e) {
                Log::error("Notification failed for user ID $user->name: " . $e->getMessage());
                // Optional: show debug error temporarily
                dd("Notification failed: " . $e->getMessage());
            }
        }

        return back()->with('success', 'Project added successfully.');
    }



    public function edit_project(Request $request, $id)
    {

        $project = Project::findOrFail($id);
        $phases = Phase::all();
        $timelines = Timeline::all();
        $clients = Client::all();
        $project_client = Client::find($project->client_id);

        $users = $project_client->users()->get();

        $assigned_user_ids = $project->users->pluck('id')->toArray();



        $social_details = $project->socialDetails;

        // 👉 Append kickoff message details directly into the $project object
        if ($project->kickoff_message_id) {
            $kickoffMessage = ProjectMessageThread::find($project->kickoff_message_id);

            if ($kickoffMessage) {
                $project->kickoff_title = $kickoffMessage->subject;
                $project->kickoff_description = $kickoffMessage->message;
                $project->kickoff_type = $kickoffMessage->is_kickoff ? 'yes' : 'no';
                
                // ✅ Get the file path only
                $kickoffFile = ProjectMessageFile::where('message_id', $kickoffMessage->id)->first();

                $project->kickoff_file = $kickoffFile ? $kickoffFile->file_path : null;
            }
        }
        else {
            $project->kickoff_type = 'no';
            $project->kickoff_title = null;
            $project->kickoff_description = null;
            $project->kickoff_file = null;
        }

        return view('project.show_project', compact('project', 'users',  'phases', 'timelines', 'clients', 'social_details', 'assigned_user_ids'));
    }


    // TODO: if project timeline type is changed to voyager then the phase_id should be set to voyager phase and category_id should be set to the first category of the voyager phase
    public function update_project(Request $request)
    {

        $project = Project::findOrFail($request->route('project_id'));

        $validate = Validator::make($request->all(), [
            'project_title' => 'required|unique:projects,name,' . $project->id,
            'client_id' => 'required|exists:clients,id',
            'job_code' => 'required',
            'assigned_users' => 'required',
            'invoice_schedule' => 'required',
            'harvest_link' => 'nullable',
            'social_detail' => 'required|array',

            'kickoff_type' => 'nullable|in:yes,no',
            'kickoff_title' => 'nullable|required_if:kickoff_type,yes|string|max:255',
            'kickoff_description' => 'nullable|required_if:kickoff_type,yes|string',
            'kickoff_file' => 'nullable|file|mimes:jpg,jpeg,png,pdf,docx|max:2048',
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }



        // $project->name = $request->input('project_title');
        // $project->client_id = $request->input('client_id');
        // $project->job_code = $request->input('job_code');
        // $project->timeline_id = $request->input('timeline_type');
        // $project->kickoff_type = $request->input('kickoff_type');
        // $project->kickoff_title = $request->input('kickoff_title');
        // $project->kickoff_description = $request->input('kickoff_description');
        // $project->invoice_schedule = $request->input('invoice_schedule');
        // $project->pm_hours = $request->input('pm_hours');
        // $project->designer_hours = $request->input('designer_hours');
        // $project->developer_hours = $request->input('developer_hours');
        // $project->harvest_link = $request->input('harvest_link');

        $project->name = $request->input('project_title');
        $project->job_code = $request->input('job_code');
        $project->client_id = $request->input('client_id');
        $project->assigned_by = Auth::id();
        $project->timeline_id = $request->input("timeline_type");
        $project->invoice_schedule = $request->input('invoice_schedule');
        $project->pm_hours = $request->input('pm_hours');
        $project->designer_hours = $request->input('designer_hours');
        $project->developer_hours = $request->input('developer_hours');
        $project->cs_hours = $request->input('cs_hours');
        $project->harvest_link = $request->input('harvest_link');

       

        // if ($request->hasFile('kickoff_file')) {
        //     $file = $request->file('kickoff_file');
        //     $filename = time() . '.' . $file->getClientOriginalExtension();
        //     $file->storeAs('images', $filename, 'public');
        //     $project->kickoff_file = $filename;
        // }


        $category = Category::find($request->input('category_id'));
        if ($category) {
            $project->category_id = $category->id;
        }

        $phase = Phase::find($request->input('phase_id'));
        if ($phase) {
            $project->phase_id = $phase->id;
        }
        $project->save();

        if ($request->input('kickoff_type') === 'yes') {
            if ($project->kickoff_message_id) {
                // Update existing kickoff message
                $kickoffMessage = ProjectMessageThread::find($project->kickoff_message_id);
                if ($kickoffMessage) {
                    $kickoffMessage->subject = $request->input("kickoff_title");
                    $kickoffMessage->message = $request->input("kickoff_description");
                    $kickoffMessage->save();
                }
            } 
            else {
                // Create new kickoff message
                $kickoffMessage = new ProjectMessageThread;
                $kickoffMessage->project_id = $project->id;
                $kickoffMessage->subject = $request->input("kickoff_title");
                $kickoffMessage->message = $request->input("kickoff_description");
                $kickoffMessage->posted_by = Auth::id();
                $kickoffMessage->is_kickoff = 1;
                $kickoffMessage->save();
        
                $project->kickoff_message_id = $kickoffMessage->id;
                $project->save();
            }
        }

        if ($request->hasFile('kickoff_file') && isset($kickoffMessage)) {
            // Delete previous kickoff file if it exists
            $existingFile = ProjectMessageFile::where('message_id', $kickoffMessage->id)->first();
        
            if ($existingFile) {
                // Optionally delete from storage (uncomment if needed)
                // Storage::delete($existingFile->file_path);
                $existingFile->delete();
            }
        
            // Store the new file
            $file = $request->file('kickoff_file');
            $path = $file->store('project-messages/' . $project->id);
        
            ProjectMessageFile::create([
                'message_id' => $kickoffMessage->id,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $path,
                'file_type' => $file->getClientMimeType(),
                'file_size' => $file->getSize()
            ]);
        }
        



        DB::table('social_details_pivot')->where('project_id', $project->id)->delete();
        SocialDetail::where('assigned_to_id', $project->id)
            ->where('assigend_to_model', 'Project')
            ->where('type', 'email')
            ->delete();


        if ($request->filled('social_detail') && is_array($request->social_detail)) {
            foreach ($request->social_detail as $email) {
                if ($email) {
                    $social_detail = SocialDetail::create([
                        'value' => $email,
                        'assigend_to_model' => 'Project',
                        'type' => 'email',
                        'assigned_to_id' => $project->id,
                    ]);

                    DB::table('social_details_pivot')->insert([
                        'social_detail_id' => $social_detail->id,
                        'project_id' => $project->id,
                    ]);
                }
            }
        }




        DB::table('social_details_pivot')->where('project_id', $project->id)->delete();
        SocialDetail::where('assigned_to_id', $project->id)
            ->where('assigend_to_model', 'Project')
            ->where('type', 'email')
            ->delete();


        if ($request->filled('social_detail') && is_array($request->social_detail)) {
            foreach ($request->social_detail as $email) {
                if ($email) {
                    $social_detail = SocialDetail::create([
                        'value' => $email,
                        'assigend_to_model' => 'Project',
                        'type' => 'email',
                        'assigned_to_id' => $project->id,
                    ]);

                    DB::table('social_details_pivot')->insert([
                        'social_detail_id' => $social_detail->id,
                        'project_id' => $project->id,
                    ]);
                }
            }
        }




        $phaseDurationsToAttach = [];

        foreach ($request->all() as $key => $value) {
            // Process duration fields
            if (Str::startsWith($key, 'project_duration') && !empty($value)) {
                $phaseIndex = (int) str_replace('project_duration', '', $key);
                $phaseId = $phaseIndex;
                $phase = Phase::find($phaseId);

                if ($phase) {
                    // Initialize the array for this phase if not already exists
                    if (!isset($phaseDurationsToAttach[$phaseId])) {
                        $phaseDurationsToAttach[$phaseId] = [];
                    }

                    // Add duration
                    $phaseDurationsToAttach[$phaseId]['project_duration'] = $value;
                }
            }

            // Process target date fields
            if (Str::startsWith($key, 'project_target') && !empty($value)) {
                $phaseIndex = (int) str_replace('project_target', '', $key);
                $phaseId = $phaseIndex;
                $phase = Phase::find($phaseId);

                if ($phase) {
                    // Initialize the array for this phase if not already exists
                    if (!isset($phaseDurationsToAttach[$phaseId])) {
                        $phaseDurationsToAttach[$phaseId] = [];
                    }

                    // Add target date
                    $phaseDurationsToAttach[$phaseId]['project_target'] = $value;
                }
            }
        }

        // Now process the phases that have data
        if (!empty($phaseDurationsToAttach)) {
            // Get current phase IDs associated with the project
            $currentPhaseIds = $project->phases()->pluck('phases.id')->toArray();

            foreach ($phaseDurationsToAttach as $phaseId => $data) {
                if (in_array($phaseId, $currentPhaseIds)) {
                    // Update existing pivot if the phase is already associated
                    $project->phases()->updateExistingPivot($phaseId, $data);
                } else {
                    // Attach new phase if it's not already associated
                    $project->phases()->attach([$phaseId => $data]);
                }
            }
        }

        // Sync Users
        $users = $request->input('assigned_users', []);
        $usersToAttach = is_array($users) ? $users : [(int) $users];

        $project->users()->sync($usersToAttach);
        foreach ($usersToAttach as $userId) {
            try {
                $user = User::find($userId);
                $user->notify(new ProjectAssigned($project));
            } catch (\Exception $e) {
                Log::error("Notification failed for user ID $user->name: " . $e->getMessage());
            }
        }

        return redirect()->route('admin-projects')->with('project_updated_success_message', 'Project updated successfully.');
    }

    public function delete_project(Request $request)
    {
        $project = Project::where('id', '=', $request->route("project_id"));
        if ($project) {
            $project->delete();
            if ($project) {
                return back()->with("project_deleted_success_message", "Project deleted successfully");
            } else {
            }
        }
    }


    public function show_project(Request $request)
    {
        $project_id = $request->route('project_id');
        $project = Project::where('id', $project_id)->first();
        return view('projects', compact('project'));
    }

    public function show_single_project_tasks(Request $request)
    {
        $project_id = $request->route('project_id');
        $project = Project::findOrFail($project_id);
        return view('project.tasks', compact('project'));
    }


    public function update_information(Request $request)
    {
        DB::beginTransaction();
        try {
            $project_id = $request->route('project_id');
            $project = Project::find($project_id);

            if (!$project) {
                return response()->json(['success' => false, 'message' => 'Project not found'], 400);
            }

            $request->validate([
                'category_id' => 'required|exists:categories,id',
                'phase_id' => 'required|exists:phases,id',
            ]);

            $category = Category::find($request->category_id);
            $phase = Phase::with('categories')->find($request->phase_id);
            $currentPhase = Phase::find($project->phase_id);

            if (!$category || !$phase) {
                return response()->json(['success' => false, 'message' => 'Category or Phase not found'], 400);
            }

            // If phase is changing
            if ($project->phase_id != $phase->id) {
                // Check if we're moving backwards in phases
                if ($phase->order < $currentPhase->order) {

                    $phasesToDelete = Phase::where('order', '>=', $phase->order)->pluck('id');
                    // Delete completion records for those phases
                    $project->phaseCompletions()
                        ->whereIn('phase_id', $phasesToDelete)
                        ->delete();
                } else {
                    // Moving forward, mark current phase as complete
                    $project->phaseCompletions()->create([
                        'phase_id' => $project->phase_id,
                        'is_completed' => true,
                        'completed_at' => now()
                    ]);
                }
            }

            // Update project
            $project->category_id = $category->id;
            $project->phase_id = $phase->id;
            $project->save();

            // Calculate percentage
            $percentage = 0;
            $order = $category->order ?? 0;
            $categories = $phase->categories;
            $count = $categories->count();

            if ($count > 0) {
                $percentage = ($order / $count) * 100;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Project associations updated successfully',
                'percentage' => round($percentage, 2) // rounding for better presentation
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update project information: ' . $e->getMessage()
            ], 500);
        }
    }

    public function ajax_fetch_project_message_reasons(Request $request)
    {
        $project_message_reasons = ProjectMessage::whereNotNull('reason')
            ->distinct('reason')
            ->select('reason')
            ->get();
        return $project_message_reasons;
    }



    public function trackProject(Request $request, $id)
    {
        $project = Project::findOrFail($id);
        $phases = Phase::with(['completion'])->get();
        $phase = $project->phases->firstWhere('id', $project->phase_id);

        $client = $project->client;

        if (!$phase) {

            $phase = $phases->last();
        }

        $currentPhaseCompletionDate = $phase?->pivot->project_target ?? '';
        $timeline = $project->timeline; // Get the timeline information

        $lastPhase = $project->phases->sortByDesc('order')->first();
        $completionDate = $lastPhase->pivot->project_target ?? '';

        $currentStep = $project->currentStep()->latest()->first();

        $currentActionMessage = $project->messages()->latest()->first();
        $allMessages = $project->messages()->orderBy('created_at', 'desc')->get();
        // 1. Group Messages by Specific Reasons
        // These messages will be passed as a collection grouped by their 'reason' attribute.
        $groupedMessagesByReason = $allMessages->filter(function ($message) {
            return !empty($message->reason); // Only include messages that have a specific reason
        })->groupBy('reason');

        // Fetch latest messages for the project
        $messages = ProjectMessageThread::with(['postedBy', 'files'])
            ->where('project_id', $id)
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get();

        // If kickoff exists, prepend it as the first message
        if ($project->kickoff_title && $project->kickoff_description) {
            $kickoffMessage = new \stdClass();
            $kickoffMessage->id = null;
            $kickoffMessage->subject = $project->kickoff_title;
            $kickoffMessage->message = $project->kickoff_description;
            $kickoffMessage->created_at = $project->created_at;
            $kickoffMessage->postedBy = $project->client;
            $kickoffMessage->files = collect();
            $kickoffMessage->is_kickoff = true;
        }
        
        return view('project.track_project', compact(
            'project',
            'phase',
            'phases',
            'timeline',
            'completionDate',
            'messages',
            'groupedMessagesByReason',
            'currentPhaseCompletionDate',
            'currentStep',
            'client',
            'currentActionMessage'
        ));
    }

    /**
     * Archive a project
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function archiveProject(Request $request, $id)
    {
        try {
            $project = Project::findOrFail($id);
            $project->archived = true;
            $project->save();

            return response()->json([
                'success' => true,
                'message' => 'Project archived successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to archive project: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unarchive a project
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function unarchiveProject(Request $request, $id)
    {
        try {
            $project = Project::findOrFail($id);
            $project->archived = false;
            $project->save();

            return response()->json([
                'success' => true,
                'message' => 'Project unarchived successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unarchive project: ' . $e->getMessage()
            ], 500);
        }
    }

    public function markPhaseComplete(Request $request, Project $project)
    {
        try {
            DB::beginTransaction();

            // Validate request
            $validated = $request->validate([
                'phase_id' => 'required|exists:phases,id',
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'completion_date' => 'required'
            ]);

            // Create or update current step
            $project->currentStep()->updateOrCreate(
                ['project_id' => $project->id],
                [
                    'title' => $validated['title'],
                    'description' => $validated['description']
                ]
            );

            // Mark phase as complete
            $project->phaseCompletions()->create([
                'phase_id' => $validated['phase_id'],
                'is_completed' => true,
                'completed_at' => $validated['completion_date']
            ]);

            // Get the next phase
            $currentPhase = Phase::find($validated['phase_id']);
            $nextPhase = Phase::where('order', '>', $currentPhase->order)
                ->orderBy('order')
                ->first();



            // If there's a next phase, update the project's phase
            if ($nextPhase) {


                $project->phase_id = $nextPhase->id;
                $project->category_id = $nextPhase->categories->first()->id ?? null;
                $project->save();
            }

            DB::commit();



            return response()->json([
                'success' => true,
                'message' => 'Phase marked as complete successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark phase as complete: ' . $e->getMessage()
            ], 500);
        }
    }

    public function checkPhaseCompletion(Project $project, Phase $phase)
    {
        $isCompleted = $project->phaseCompletions()
            ->where('phase_id', $phase->id)
            ->exists();

        return response()->json([
            'isCompleted' => $isCompleted
        ]);
    }

    public function messageCenter(Request $request, $id)
    {
        $project = Project::findOrFail($id);
        $client = $project->client;

        // Get the latest message separately
        $latestMessage = ProjectMessageThread::with(['postedBy', 'files'])
            ->where('project_id', $id)
            ->orderBy('created_at', 'desc')
            ->first();

        // Get paginated messages excluding the latest one
        $messages = ProjectMessageThread::with(['postedBy', 'files'])
            ->where('project_id', $id)
            ->when($latestMessage, function ($query) use ($latestMessage) {
                return $query->where('id', '!=', $latestMessage->id);
            })
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $phase = Phase::find($project->phase_id);

        return view('message-center', compact('project', 'phase', 'messages', 'client', 'latestMessage'));
    }

    public function viewMessage(Request $request)
    {
        $project = Project::findOrFail($request->route('project_id'));
        $projectMessage = ProjectMessage::findOrFail($request->route('message_id'));
        $projectMessageAttachments = $projectMessage->attachments;
        return view('view-message', compact('project', 'projectMessage', 'projectMessageAttachments'));
    }

    public function newThread(Request $request)
    {
        $project = Project::findOrFail($request->route('project_id'));
        $projectMessage = ProjectMessage::findOrFail($request->route('message_id'));
        $projectMessageAttachments = $projectMessage->attachments;
        return view('new-thread', compact('project', 'projectMessage', 'projectMessageAttachments'));
    }





    public function newMessageThread(Request $request, $id)
    {
        $project = Project::findOrFail($id);
        $client = $project->client;



        $clientUser = Client::whereHas('linkedUsers', function ($query) use ($client) {
            $query->where('clients.id', $client->id);
        })->first();



        $projectUsers = $project->users()->get();

        $projectUsers = $projectUsers->filter(fn($user) => $user->resource && $user->resource->team === 'US');

        $usersCount = $projectUsers->count();

        // Get the original message if this is a reply
        $originalMessage = null;
        if ($request->has('message_id')) {
            $originalMessage = ProjectMessageThread::with(['postedBy', 'files'])
                ->where('id', $request->message_id)
                ->where('project_id', $id)
                ->firstOrFail();
        }

        $defaultRecipients = User::where('name', 'like', '%drew%')
            ->orWhere('name', 'like', '%sally%')
            ->orWhere('name', 'like', '%robert%')
            ->get();




        //      $defaultRecipients = $project->users->where('id', '!=', auth()->id())->values();

        // $client = $project->client;
        // $clientUser = null;

        // if ($client) {
        //     $clientLinks = DB::table('client_user_links')->where('client_id', $client->id)->first();

        //     if ($clientLinks) {
        //         $clientUser = User::find($clientLinks->user_id);
        //     }

        //     if ($clientUser && $clientUser->id !== auth()->id() && !$defaultRecipients->contains('id', $clientUser->id)) {
        //         $defaultRecipients->push($clientUser);
        //     }
        // }









        return view('new-message-thread', compact('project', 'client', 'projectUsers', 'usersCount', 'defaultRecipients', 'originalMessage', 'clientUser'));
    }

    public function fileUpload(Request $request, $id)
    {
        $project = Project::findOrFail($id);
        $projectMessages = $project->messages()->orderBy('id', 'desc')->get();

        // **1. Find all attachments related to this particular project**
        // We can eager load messages with their attachments
        $projectWithAllAttachments = Project::with('messages.attachments')
            ->find($id);

        // Flatten all attachments from all messages into a single collection
        $allProjectAttachments = collect();
        foreach ($projectWithAllAttachments->messages as $message) {
            $allProjectAttachments = $allProjectAttachments->merge($message->attachments);
        }
        // If you need them sorted (e.g., by creation date of attachment)
        $allProjectAttachments = $allProjectAttachments->sortByDesc('created_at');

        // **2. Find its latest message (with attachments)**
        // Your existing logic for this is efficient and correct for in-memory filtering
        $latestProjectMessage = $projectMessages->filter(function ($message) {
            return $message->attachments->isNotEmpty();
        })->sortByDesc('created_at  ')->first();

        // Get the phase for the project
        $phase = Phase::find($project->phase_id);

        return view('file-upload', compact(
            'project',
            'phase',
            'projectMessages',
            'latestProjectMessage',
            'allProjectAttachments' // Pass the new collection of all attachments
        ));
    }

    public function billingHistory(Request $request, $id)
    {
        $project = Project::findOrFail($id);
        $phase = Phase::find($project->phase_id);

        return view('billing-history', compact('project', 'phase'));
    }
}
