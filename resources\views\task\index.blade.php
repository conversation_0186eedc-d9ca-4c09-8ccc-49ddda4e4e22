@extends('layout.app')
@section('title', 'loginorg')
@section('content')




<!-- hshd -->

<section class="client-header">

    
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @endif
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Tasks <i>Overview</i></h1>
            <div class="add-task ms-auto d-flex">
                @if(admin_superadmin_permissions())
                <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0" href="{{ route('add-new-task') }}"
                    >NEW TASK<span class="img ms-2">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                            <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5"></path></svg></span
                ></a>
                @endif
            </div>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl">
        <div class="row projects-row">
            <div class="col-md-3 col-xl-2 project-column quick-links">
                <h2 class="text-uppercase">STATUS</h2>
                <hr class="mt-0 mb-4 border-white" />
                <div class="statuses-list d-flex flex-column">
                <a class="{{ request()->has('status') ? '' : 'active' }}" 
                    href="{{ route('admin-tasks') }}">
                    All ({{ $alltaskcount }})
                </a>
                @foreach($statuses as $status)
                @php 
                    $tasks = $status->tasks()->where('status_id', $status->id)->get();
                @endphp
                        <a class="{{ request('status') == $status->id ? 'active' : '' }}" 
                            href="{{ route('admin-tasks', ['status' => $status->id]) }}">
                            {{ $status->name }} ({{ count($tasks) }})
                        </a>
                @endforeach

                </div>
            </div>
            <div class="col-md-9 col-xl-10 project-column task-overview">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">TASKS OVERVIEW</h2>
                    <div class="sort d-flex align-items-center">
                        <div class="sort-by d-flex align-items-center">
                            <h3>Sort By: Date</h3>
                            <span class="down ms-2"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" /></span>
                        </div>
                        <div class="more-icon ms-3"><img src="{{ asset('images/three-dots-more.svg') }}" alt="" /></div>
                    </div>
                </div>
                <hr class="mt-0 mb-4 border-white" />
                <div class="task-overview-list">
                    <div class="head-sort mb-4">
                                    
                    @php
                        $status_id = request()->route('status'); 
                        $status = \App\Models\Status::find($status_id);
                    @endphp

                 
                    @if($status_id == 1)
                        <h2 class="text-white text-uppercase ur">Urgent</h2>
                    @elseif($status)
                        <h2 class="text-white text-uppercase s->n">{{ $status->name }}</h2>
                    @else
                        <h2 class="text-white text-uppercase sn">{{ $status_name }}</h2>
                    @endif
                    </div>
                    
                    @foreach( $projects as $project )
                        @if( count($project->tasks) > 0 )
                        @foreach(  $project->tasks as $task  )
                            <div class="task-project d-flex purple">
                                <div class="star-check d-flex">
                                    <div class="check-icon me-3"><i class="bi bi-square text-white"></i></div>
                                </div>
                                <div class="copy d-flex flex-column flex-grow-1">
                                    <h2 class="text-uppercase">{{ $project->job_code }} {{ $project->name }} - {{ $project->status->name }}</h2>
                                        @foreach( $project->tasks as $task )
                                        @if( $task->status->name !== 'Urgent' )
                                            <div class="detail d-flex align-items-center w-100">
                                                <div class="date me-3">{{ $task->created_at->format('M j') }}</div>
                                                <div class="task me-3"><a href="{{ route('view-task', ['task_id' => $task->id] ) }}">{{ $task->name }}</a></div>
                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                    <div class="text over-text d-grid">
                                                        <p class="text-truncate">{{ $task->description }}.</p>
                                                    </div>
                                                    <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', ['task_id' => $task->id] ) }}">Read More</a>
                                                </div>
                                            </div>
                                            @else
                                            <div class="detail d-flex align-items-center w-100">
                                                <div class="date-cta py-1 px-2 rounded-1 d-flex align-items-center me-3"><i class="bi bi-clock me-1"></i> {{ $task->created_at->format('M j') }}</div>
                                                <div class="task me-3"><a href="{{ route('view-task', ['task_id' => $task->id] ) }}">{{ $task->name }}</a></div>
                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                    <div class="text over-text d-grid">
                                                        <p class="text-truncate">{{ $task->description }}.</p>
                                                    </div>
                                                    <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', ['task_id' => $task->id] ) }}">Read More</a>
                                                </div>
                                            </div>
                                            @endif
                                        @endforeach
                                </div>
                            </div>
                        @endforeach
                        @endif
                       
                    @endforeach
              
                    @if( 2 === 1 )
                    <div class="head-sort mb-4">
                        <h2 class="text-white text-uppercase">All</h2>
                        
                    </div>
                    
                    @foreach($projects as $project )
                    @foreach(  $project->tasks as $task  ) 
                    @if( $task->status->name !== 'Urgent' )
                    <div class="task-project d-flex purple">
                        <div class="star-check d-flex">
                            <div class="check-icon me-3"><i class="bi bi-square text-white"></i></div>
                        </div>
                        <div class="copy d-flex flex-column flex-grow-1">
                            <h2 class="text-uppercase">{{ $project->job_code }} {{ $project->name }} - {{ $task->status->name }}</h2>
                                @foreach( $project->tasks as $task )
                                    <div class="detail d-flex align-items-center w-100">
                                        <div class="date me-3">{{ $task->created_at }}</div>
                                        <div class="task me-3"><a href="{{ route('view-task', ['task_id' => $task->id] ) }}">{{ $task->name }}</a></div>
                                        <div class="text d-flex flex-grow-1 align-items-center">
                                            <div class="text over-text d-grid">
                                                <p class="text-truncate">{{ $task->description }}.</p>
                                            </div>
                                            <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', ['task_id' => $task->id] ) }}">Read More</a>
                                        </div>
                                    </div>
                                @endforeach
                        </div>
                    </div>
                    @endif
                    @endforeach
                    @endforeach
                    @endif
                 
                </div>
            </div>
        </div>
    </div>
</section>




@endsection