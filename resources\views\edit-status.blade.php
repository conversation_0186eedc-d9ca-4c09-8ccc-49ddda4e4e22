@extends('layout.app')
@section('title', 'loginorg')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">

                    <h4>
                        @if(request()->route()->named('edit-status'))
                            Edit Status
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('edit-status'))
                        @if(isset($no_projects_found_error) && $no_projects_found_error!='')
                            <div class="no_projects_found_error">{{ $no_projects_found_error }}</div>
                        @else
                            <div class="" id="add-status">
                                <form action="{{ route('update-status', ['status_id' => $status->id]) }}" method="post" class="add-status-form mx-auto col-6 border border-secondary p-2">
                                    @csrf
                                    <div class="d-flex">
                                        <div class="edit-status-div-label">
                                            Status Name:
                                        </div>
                                        <div class="edit-status-status-div-input d-flex flex-column">
                                            <input type="text" name="status_name" value="{{ $status->name }}">
                                            @error("status_name")
                                                <div class="text-danger">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <br>
                                    <div class="d-flex">
                                        <input type="submit" value="Submit" class="mx-auto">
                                    </div>
                                </form>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection


@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }
        .main-div{
            display:flex;
            height:100%;
            background-color:white;
        }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;

            left: 0;
            bottom:0;
            z-index: 0;
            height: calc(100vh - 50px);
            overflow:scroll;
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
            left: 0;
            bottom:0;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            /* padding:0px 50px;
            display:flex;
            justify-content:center; */
        }
        form[name="edit-status-form"]{
            width:100%;
            text-align:center;
        }
        /*.status-details-div{
            margin-top:10px;
            padding:10px;
            width:50%;
            border:1px solid grey;
            display: flex;
            justify-content: center;
        }
        .status-details-div-label{
            width:25%;
            text-align:left;
        }
        .status-details-div-input{
            width:75%;
            text-align:left;
        }*/
        .edit-status-div{
            width:100%;
            display: block;
        }
        .edit-status-div-label{
            text-align:left;
            width:50%;
        }
        .edit-status-div-input{
            width:50%;
        }
        .no_projects_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){

        });
    </script>
@endpush