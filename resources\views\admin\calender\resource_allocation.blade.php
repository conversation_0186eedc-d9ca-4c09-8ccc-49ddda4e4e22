@extends('admin.layouts.app')
@section('title', 'Resource Allocation')
@section('content')

<div>
    <!-- Notification container for AJAX responses -->
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; width: 350px;"></div>

    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                @if($isAdmin)
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal
                </a>
                @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('dashboard') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Dashboard
                </a>
                @endif
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project Regroup- <i>Resource Allocation</i></h1>
                </div>
            </div>
        </div>
    </section>

    <section class="client-project pt-0">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            @if($isAdmin)
            <div class="view-options d-flex mb-4">
                <a href="{{ route('admin-projects-list-view') }}" class="me-3">List View</a>
                <a href="{{ route('admin-calender') }}" class="me-3">Calendar View</a>
                <a href="{{ route('resource-allocation') }}" class="active me-3">Resource Allocation</a>
            </div>
            @endif

            <div id="calendar" class="mb-4"></div>


        </div>
    </section>

    <section class="ask-question">
        <div class="container-xxl">
            <div class="d-flex align-items-center justify-content-between pb-4">
                <div class="head">
                    <h2 class="mb-0">
                        Have a question?<br />
                        <i>We're here to help.</i>
                    </h2>
                </div>
                <div class="cta-row">
                    <a class="cta link text-decoration-none text-white" href="#">GET EMAIL UPDATES</a>
                    <a class="cta link text-decoration-none text-white" href="#">NEED HELP? CONTACT</a>
                </div>
            </div>
            <hr class="mt-0 mt-4 border-white" />
        </div>
    </section>
</div>

@endsection

@push('styles')
<style>


.view-options a {
        color: #fff;
        text-decoration: none;
        padding: 8px 15px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .view-options a:hover, .view-options a.active {
        background-color: #ff4c00;
        border-color: #ff4c00;
    }
    .btn-orange {
        background-color: white;
        color: black;
        border: 2px solid #ff4c00;
        width: 200px;
        border-radius: 30px;
        padding: 10px 20px;
        text-align: center;
        font-weight: 600;
    }

    .btn-orange:hover {
        background-color: #ff4c00;
        border-color: #ff4c00;
    }

    .calendar-legend {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 5px;
    }

    .calendar-legend h5 {
        color: #fff;
        margin-bottom: 10px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        color: #fff;
    }

    .color-box {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 5px;
        border-radius: 3px;
    }

    /* Custom FullCalendar Styling */
    #calendar {
        background-color: #111;
        border-radius: 5px;
        padding: 15px;
        font-family: "Inter", sans-serif;
    }

    /* Calendar Header Styling */
    .fc-toolbar {
        margin-bottom: 20px !important;
    }

    .fc-toolbar h2 {
        color: #fff !important;
        font-family: "Futura Std", serif !important;
        font-size: 24px !important;
    }

    /* Calendar Button Styling */
    .fc-button {
        background-color: #282828 !important;
        color: black !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        box-shadow: none !important;
        text-shadow: none !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
    }

    .fc-button:hover {
        background-color: #ff4c00 !important;
        border-color: #ff4c00 !important;
    }

    .fc-state-active,
    .fc-state-down {
        background-color: #ff4c00 !important;
        border-color: #ff4c00 !important;
    }

    /* Calendar Table Styling */
    .fc-day-header {
        background-color: #282828 !important;
        color: #fff !important;
        font-family: "Futura Std", serif !important;
        font-weight: 700 !important;
        padding: 10px 0 !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .fc-day {
        background-color: #1a1a1a !important;
        border-color: rgba(255, 255, 255, 0.05) !important;
    }

    .fc-day-number {
        color: #fff !important;
        padding: 8px !important;
    }

    .fc-day-top {
        background-color: #1a1a1a !important;
    }

    .fc-today {
        background-color: rgba(241, 165, 51, 0.1) !important;
    }

    .fc-today-button{
        background-color: #ff4c00 !important;
        border-color: #ff4c00 !important;
    }

    /* Event Styling */
    .fc-event {
        border-radius: 3px !important;
        border: none !important;
        padding: 2px 5px !important;
        font-size: 12px !important;
        white-space: normal !important; /* Allow text to wrap */
        height: auto !important; /* Allow height to adjust to content */
        min-height: 22px !important; /* Slightly reduced minimum height for events */
        overflow: visible !important; /* Ensure content is visible */
        line-height: 1.3 !important; /* Slightly tighter line spacing */
    }

    /* Resource allocation event styling */
    .resource-event {
        padding: 3px 6px !important;
        margin-bottom: 2px !important;
        font-size: 12px !important;
        border-radius: 4px !important;
        font-weight: 600 !important;
    }

    .fc-more {
        color: #ff4c00 !important;
    }

    .fc-title {
        font-weight: 600 !important;
        white-space: normal !important; /* Allow title to wrap */
        overflow: visible !important; /* Show overflow content */
        text-overflow: clip !important; /* Don't truncate text */
        display: block !important; /* Ensure title takes full width */
        line-height: 1.3 !important; /* Slightly tighter line spacing */
        margin: 0 !important;
        padding: 0 !important;
    }

    .fc-title small {
        font-weight: 400 !important;
        opacity: 0.8 !important;
    }

    /* Resource allocation specific styles */
    .resource-event {
        padding: 3px 6px !important;
        margin-bottom: 2px !important;
        font-size: 12px !important;
        border-radius: 4px !important;
        font-weight: 600 !important;
    }

    /* Week-spanning resource allocation bars */
    .fc-event[data-resource-type] {
        border-left: 4px solid rgba(255, 255, 255, 0.3) !important;
        margin-bottom: 3px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important;
    }

    .fc-event[data-resource-type]:hover {
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
        transform: translateY(-1px) !important;
    }

    /* Holiday styling */
    .holiday-event {
        margin-top: 2px !important;
        border-radius: 3px !important;
        padding: 2px 4px !important;
        min-height: 20px !important;
    }

    .memorial-day-event {
        background-color: #808080 !important;
        color: white !important;
    }
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.4.0/fullcalendar.css" />
@endpush

@push('script')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.4.0/fullcalendar.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
    $(document).ready(function() {

const devTeamColor = '#a15cd4';
const designTeamColor = '#f45689';
const pmTeamColor = '#ff4c00';
const holidayColors = {
    global: '#ffc107',
    personal: '#28a745'
    
};

let events = [];


 
@foreach($weekDates as $dateKey => $dateData)
    @if($dateData['date']->dayOfWeek == 1) {{-- Only create events for Mondays (1) --}}
        @php
            // Calculate exact number of days based on percentage (more precise)
            // 100% = 5 days, so each 20% = 1 day, but allow decimals for precision
            $designerDays = ($dateData['designer_allocation']['percentage'] / 20);
            $developerDays = ($dateData['developer_allocation']['percentage'] / 20);
            $pmDays = ($dateData['pm_allocation']['percentage'] / 20);
            
            // Ensure minimum of 0.1 days and maximum of 5 days
            $designerDays = max(0.1, min(5, $designerDays));
            $developerDays = max(0.1, min(5, $developerDays));
            $pmDays = max(0.1, min(5, $pmDays));
            
            // Calculate end time based on decimal days (convert to hours)
            $designerEndTime = $dateData['date']->copy()->addDays(floor($designerDays))->addHours(($designerDays - floor($designerDays)) * 24);
            $developerEndTime = $dateData['date']->copy()->addDays(floor($developerDays))->addHours(($developerDays - floor($developerDays)) * 24);
            $pmEndTime = $dateData['date']->copy()->addDays(floor($pmDays))->addHours(($pmDays - floor($pmDays)) * 24);
        @endphp
        
        // Designer allocation - priority 1
        events.push({
            title: 'Designer: {{ $dateData['designer_allocation']['percentage'] }}% ({{ round($dateData['designer_allocation']['allocated_hours']) }}/{{ round($dateData['designer_allocation']['available_hours']) }}h)',
            start: '{{ $dateData['date']->format('Y-m-d H:i:s') }}',
            end: '{{ $designerEndTime->format('Y-m-d H:i:s') }}',
            allDay: false,
            color: '#f45689',
            resourceType: 'designer',
            weekStart: '{{ $dateData['date']->format('Y-m-d') }}',
            weekEnd: '{{ $dateData['week_end']->format('Y-m-d') }}',
            order: 1 
        });

        // Developer allocation - priority 2
        events.push({
            title: 'Developer: {{ $dateData['developer_allocation']['percentage'] }}% ({{ round($dateData['developer_allocation']['allocated_hours']) }}/{{ round($dateData['developer_allocation']['available_hours']) }}h)',
            start: '{{ $dateData['date']->format('Y-m-d H:i:s') }}',
            end: '{{ $developerEndTime->format('Y-m-d H:i:s') }}',
            allDay: false,
            color: '#a15cd4',
            className: 'dev-team-event resource-event',
            resourceType: 'developer',
            weekStart: '{{ $dateData['date']->format('Y-m-d') }}',
            weekEnd: '{{ $dateData['week_end']->format('Y-m-d') }}',
            order: 2 
        });

        // Project manager allocation - priority 3
        events.push({
            title: 'PM: {{ $dateData['pm_allocation']['percentage'] }}% ({{ round($dateData['pm_allocation']['allocated_hours']) }}/{{ round($dateData['pm_allocation']['available_hours']) }}h)',
            start: '{{ $dateData['date']->format('Y-m-d H:i:s') }}',
            end: '{{ $pmEndTime->format('Y-m-d H:i:s') }}',
            allDay: false,
            color: '#ff4c00',
            className: 'pm-team-event resource-event',
            resourceType: 'pm',
            weekStart: '{{ $dateData['date']->format('Y-m-d') }}',
            weekEnd: '{{ $dateData['week_end']->format('Y-m-d') }}',
            order: 3 
        });
    @endif
@endforeach

@if(isset($holidays))
@foreach($holidays as $holiday)
    events.push({
        title: '{{ !$holiday->is_global && $holiday->user ? $holiday->user->name . " is out" : $holiday->title }}',
        start: '{{ $holiday->start_date }}',
        @if($holiday->end_date)
                end: '{{ \Carbon\Carbon::parse($holiday->end_date)->addDay()->toDateString() }}'/* calendar styling shows start-date to end-date - 1 so increase one day to display start-date to end-date in calendar */,
                @endif
        allDay: true,
        color: {{ $holiday->is_global ? 'holidayColors.global' : 'holidayColors.personal' }},
        description: '{{ !$holiday->is_global && $holiday->user ? str_replace("'", "\\'", $holiday->user->name) . " is on personal leave" . ($holiday->title ? " - " . str_replace("'", "\\'", $holiday->title) : "") : str_replace("'", "\\'", $holiday->description ?: "") }}',
        holiday: true,
        holidayId: {{ $holiday->id }},
        isGlobal: {{ $holiday->is_global ? 'true' : 'false' }},
        userId: {{ $holiday->user_id ?: 'null' }},
        order: 10 
    });
@endforeach
@endif

// Initialize calendar
$('#calendar').fullCalendar({
    header: {
        left: 'prev,next today',
        center: 'title',
        right: 'month,agendaWeek'
    },
    defaultDate: '{{ date("Y-m-d") }}',
    navLinks: true,
    editable: false,
    eventLimit: 6,
    events: events,
    themeSystem: 'standard',
    timeFormat: 'h:mm A',
    displayEventTime: false,
    displayEventEnd: false,
   
    eventOrder: 'order',
    eventRender: function(event, element) {
        if (event.description) {
            element.attr('title', event.description);
        }

       
        if (event.holiday) {
            element.addClass('holiday-event');
            if (event.userName) {
                element.find('.fc-title').append('<br/><small>' + event.userName + '</small>');
            }
        }

        if (event.resourceType) {
            // Show week-spanning events (Monday to Friday)
            const eventDate = moment(event.start);
            const eventEndDate = moment(event.end);
            
            // Only show the event if it's a weekday (Monday to Friday)
            if (eventDate.day() === 0 || eventDate.day() === 6) { // Hide on weekends
                return false;
            }

            // Add data attribute for styling
            element.attr('data-resource-type', event.resourceType);

            const titleParts = event.title.split('(');
            if (titleParts.length > 1) {
                const percentPart = titleParts[0];
                const hoursPart = '(' + titleParts[1];
                
                let titleHtml = percentPart + ' ' + hoursPart;
                
                element.find('.fc-title').html(titleHtml);
                
                if (event.title.includes('Memorial Day')) {
                    element.addClass('holiday-event');
                    element.css('background-color', '#808080');
                }
                
                // Handle infinity cases
                if (hoursPart.includes('Infinity') || hoursPart.includes('NaN')) {
                    element.find('.fc-title').html('Infinity% (0/0)');
                }
            }
        }

        element.css('transition', 'all 0.2s ease');

        element.hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('z-index', '10');
                $(this).css('box-shadow', '0 4px 8px rgba(0, 0, 0, 0.3)');
            },
            function() {
                $(this).css('transform', 'scale(1)');
                $(this).css('z-index', '1');
                $(this).css('box-shadow', 'none');
            }
        );
    }
});

function adjustCalendarForScreenSize() {
    const windowWidth = $(window).width();

    if (windowWidth < 768) {
        $('#calendar').fullCalendar('changeView', 'listMonth');
        $('#calendar').fullCalendar('option', 'header', {
            left: 'prev,next',
            center: 'title',
            right: 'today'
        });
    } else if (windowWidth < 992) {
        $('#calendar').fullCalendar('option', 'header', {
            left: 'prev,next today',
            center: 'title',
            right: 'month'
        });
    } else {
        $('#calendar').fullCalendar('option', 'header', {
            left: 'prev,next today',
            center: 'title',
            right: 'month,agendaWeek'
        });
    }
}

adjustCalendarForScreenSize();

$(window).resize(function() {
    adjustCalendarForScreenSize();
});
});
    </script>
@endpush