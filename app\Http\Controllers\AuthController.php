<?php

namespace App\Http\Controllers;

use App\Models\AccessLevel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Company;
use App\Models\Project;
use App\Models\Task;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\PasswordReset;

class AuthController extends Controller
{
    //opens view file for register page
    public function register()
    {
        return view('register');
    }

    //performs logic for register page
    public function register_check(Request $request)
    {

        $validated = $request->validate([
            'name' => 'required',
            'email' => 'required|email|unique:users',
            'password' => 'required|confirmed',
        ]);
        $user = User::create($validated);
        if ($user) {
            return redirect('accounts')->with('Success', 'User Registered Successfully..');
        } else {
            return back()->with('Error', 'There was some error Registering Your Account...');
        }
    }

    //opens view file for login page
    public function login()
    {
        return view('login');
    }

    //performs logic for login page
    public function login_check(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $user = User::where('email', $credentials['email'])->first();

        if ($user) {
            if (Hash::check($credentials['password'], $user->password)) {
                Auth::login($user);

                $redirectUrl = '/dashboard';



                if ($user->role && $user->role->name === 'SuperAdmin' || $user->access_level_id === (AccessLevel::where('name', '=', 'Admin')->first())->id) {
                    $redirectUrl = route('admin-dashboard');
                }

                if ($user->role && $user->role->name === "Client Member") {
                    $redirectUrl = route('client.dashboard');
                }

                if ($user->role && $user->role->name === "Brand Member") {

                    $redirectUrl = route('brand.dashboard');
                }



                return redirect()->intended($redirectUrl)
                    ->with('logged-in-message', 'You are logged-in successfully!!');
            } else {
                return back()->with('password_incorrect_error', 'The Password is incorrect !!')->onlyInput('email');
            }
        } else {
            return back()->with('email_not_registered_error', 'This Email is not registered !!')->onlyInput('');
        }
    }



    public function logout(Request $request)
    {
        if ($request->isMethod('get')) {
            return back();
        }
        Auth::logout();
        $request->session()->flush();
        return redirect('login');
    }

    public function company_selection(Request $request)
    {
        if ($request->input("company_id")) {
            $company_user = Company::with(['user'])->find($request->input("company_id"));

            $projects = $company_user->projects()->get();
            Session::put('company', $company_user);
            return view('company_selection', ['company' => $company_user, 'project_tasks' => $projects]);
        }
        return view('company_selection');
    }

    public function companies_list(Request $request)
    {

        if (Auth::user()->role_id == '1') //Admin can view all companies
        {
            $company = Company::with('user')->latest()->get();
        } else //MCC's and SCC's (Non-Admins) can view only their companies
        {
            $company = Company::with('user')->where('user_id', '=', Auth::user()->id)->latest()->get();
        }

        return $company;

        // $users = User::with('companies')->get();
        // return $users;
    }

    public function companies_list_of_user_ajax(Request $request)
    {

        // $user_id = $request->input('user_id');
        // $companies = Company::where('user_id', '=', $user_id)->get();
        $companies = Company::get();

        return $companies;

        // $users = User::with('companies')->get();
        // return $users;
    }


    //     $validation = $request->validate([
    //         'name' => 'required'
    //     ]);

    //     $company = new Company();
    //     $company->user_id = Auth::user()->id;
    //     $company->name = $request->name;
    //     $company->save();
    //     if($company){
    //         return "company inserted in database!";
    //     }
    //     // $user = User::findOrFail($request->session()->get('user-id'));
    //     // return view('company_creation', ['user' => $user]);
    // }
    public function add_company(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'users' => 'nullable|array', // Must be an array
            'users.*' => 'exists:users,id' // Ensure each ID exists
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $company = Company::create([
            'name' => $request->input('name'),
            'description' => $request->input('description'),
            'owner_id' => Auth::user()->id,
        ]);

        // Attach users to the company
        $company->users()->attach($request->input('users'));

        return redirect()->route('companies.index')->with('success', 'Company created successfully.');
    }

    public function edit_company(Request $request)
    {
        $company = Company::where('id', '=', $request->route('company_id'))->first();
        $selected_projects = $company->projects()->get();

        if ($company) {
            $selected_project_array = [];
            if ($selected_projects) {
                foreach ($selected_projects as $selected_project) { //IMP
                    $selected_project_array[] = $selected_project->pivot->project_id;
                }
            }
            // if($company->projects()){
            // $company_id = $project->companies()->first()->pivot->company_id;//company_id is not stored in projects table so it will be accessed from pivot table of companies and projects table

            // $company = Company::where('id', '=', $company_id)->first();

            return view('edit-company', ['company' => $company, 'project_ids' => $selected_project_array]);
            // }
        } else {
            abort(404);
            // return view('edit-project',['no_projects_found_error' => 'Project Not Found']);
        }
    }

    public function update_company(Request $request)
    {
        if ($request->isMethod('get')) {
            return redirect()->route('edit-company', ['company_id' => $request->route('company_id')]);
        }

        $validate = Validator::make($request->all(), [
            'company_name' => 'required|unique:companies,name,' . $request->route('company_id'),
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        };

        $company = Company::where('id', '=', $request->route("company_id"))->first();
        // return $company->projects()->get();
        // Or, more efficiently if you have an array of company IDs:
        $projectIds = $request->input('project_id');
        if (!is_array($projectIds)) {
            $projectIds = (array) $projectIds; // Cast to an array
        }
        $projectsToAttach = [];
        foreach ($projectIds as $projectId) {
            $projectIdInt = (int) $projectId; //convert to integer such that array should have ony integer values whether array element = 1 or more
            $projectsToAttach[$projectIdInt] = ['assigned_by_user' => Auth::user()->id];
        }
        $company->projects()->syncWithoutDetaching($projectsToAttach);
        $company->save();
        if ($company) {
            return back()->with('company_updated_success_message', 'Company updated successfully.');
        } else {
            return back()->with('company_not_updated_error_message', 'Company not updated successfully.');
        }
    }

    public function delete_company(Request $request)
    {
        $company = Company::where('id', $request->route("company_id"))->first();

        if ($company) {
            if (Session::has('company') && Session::get('company')->id === $company->id) {
                Session::forget('company');
            }
            $company->delete();
            return redirect('company-selection')->with("company_deleted_success_message", "Company deleted");
        }

        return back()->withErrors(['company_not_found' => 'Company not found']);
    }

    public function get_user_companies_and_projects(Request $request)
    {

        $id = $request->route("id");
        // $users = User::with('companies.projects')->get();
        // $users = User::with(['companies' => function ($query) use ($id) {
        //     $query->where('id', $id) // Filter companies with company_id = $id
        //           ->with('projects'); // Eager load projects for these companies
        // }])->whereHas('companies', function ($query) use ($id) {
        //     $query->where('id', $id); // Ensure that the company has company_id = $id
        // })->get();

        $company = Company::with(['user', 'projects'])->find($id);
        Session::put('company', $company);
        return redirect('company-selection');
    }



    public function forgotPasswordPage(Request $request)
    {
        return view('auth.forgotPasswordPage');
    }



    public function forgotPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);
        $status = Password::sendResetLink((
            $request->only('email')
        ));

        if ($status === Password::RESET_LINK_SENT) {
            return redirect()->route('forgotPasswordPage')->with([
                'success' => 'Reset link has been sent. Please check your mail.',
                'email' => $request->email
            ]);
        }
        return back()->withErrors(['email' => trans($status)]);
    }






    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with('success', 'Password updated Successfully')
            : back()->withErrors(['email' => [__($status)]]);
    }
}
