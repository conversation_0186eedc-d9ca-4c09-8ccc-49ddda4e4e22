- let pageName = 'Client Portal';
- let mainPage = 'Track Project';
- let pageTitle = `${pageName}: Track Project`;

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

mixin timelineBar()
    each color, index in ['orange', 'green', 'pink', 'purple', 'orange']
        if index === 0
            .bar.first.orange(data-color='orange')
                .icon-bar
        else if index === 1
            .bar.second.green(data-color='green')
                .icon-bar
        else
            .bar(class=color, class=(index === 4 ? 'last' : ''), data-color=color)
                .icon-bar

mixin timelineBarStatus()
    each color, index in ['orange', 'green', 'pink', 'purple', 'orange']
        if index === 0
            .bar.first.orange(data-color='orange')
                .icon-bar(style='width:100%')
        else if index === 1
            .bar.second.green.current(data-color='green')
                .icon-bar(data-bar-width='80')
                    .icon-circle
                    .icon.d-flex.align-items-center.justify-content-center.bg-white.rounded-circle
                        img(src='images/content-project-icon.svg', alt='')
        else
            .bar(class=color, class=(index === 4 ? 'last' : ''), data-color=color)

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>[SP-003-25] Sample Project 3</i>', false, true, true, 'Back to Client Portal')

        section.client-project.bg-white
            .container-xxl
                .heading
                    h2.mb-0 Track #[i Project]

                .status.d-flex.justify-content-between
                    +statusSection('CURRENT STATUS', 'Waiting for Content and Approvals', '!')
                    +statusSection('CURRENT LAUNCH ESTIMATE', 'June 10th, 2025', '?', true)

        section.project-timeline.bg-white.d-none
            .container-xxl
                .head.d-flex.align-items-center.mb-5
                    h2.mb-0 SSGF Project #[i Timeline]
                    img.ms-4(src="images/head-arrow-icon.svg", alt="", width='100', height='46')

                .card-row
                    mixin cardTimeline(color, num, head, subhead, text, meta, icon, extraClass=false)
                        .card-timeline.d-flex.flex-column.align-self-end(class=color, class=extraClass)
                            .flag
                                .head
                                    h2.text-white.lh-1.mb-0=num
                                    h3.text-white.text-uppercase.mb-0=head
                                .text
                                    p.text-black #[strong=subhead]
                                    p=text
                                    p.text-uppercase.h3.mt-3=meta
                            .icon-wrap.d-flex.align-items-center
                                span.line
                                .icon.d-flex.align-items-center.justify-content-center
                                    img(src=`images/${icon}-project-icon.svg`, alt="")

                    +cardTimeline('orange', '01', 'Define the Project', 'End-in-mind planning.', 'Starting off the project right by deciding deliverables, parameters and metrics for success', 'DEFINE: 1 week', 'define')
                    +cardTimeline('green', '02', 'Content, sitemap wireframes ', 'Content organization and creation.', 'Unifying your message across copy and visuals.', 'content: 3-4 weeks', 'content')
                    +cardTimeline('pink', '03', 'Design ', 'The message, visualized.', 'Making sure your content shines through supporting imagery, visuals and branding.', 'design: 4-8 weeks', 'design', 'align-items-center')
                    +cardTimeline('purple', '04', 'Code ', 'HTML, CMS, etc.', 'Taking everything we’ve developed together and implementing it in a live environment.', 'code: 4-8 weeks', 'code', 'align-items-center')
                    +cardTimeline('orange', '05', 'deploy and manage', 'Launch, SEO, ongoing maintenance.', 'Finishing off the project as strong as we started it.', 'DEploy: 1 week', 'deploy', 'align-items-end')

                .timeline-bar.d-flex
                    +timelineBar

        section.timeline-status.bg-white
            .container-xxl
                #timelineBar.timeline-bar.d-flex
                    +timelineBarStatus

                .row.status-meta.justify-content-center.green
                    .col-lg-10
                        .head.d-flex.justify-content-between
                            +statusSection('Current Step', 'Content, Sitemap, Wireframes')
                            .text.text-md-end.mt-4.mt-md-0
                                h2.text-uppercase.mb-2 Current STATUS:
                                h3.mb-0 #[span.counter(data-countTo='80', data-duration="1000") 0]%
                        .content-meta
                            p #[strong.d-block Content organization and creation.] Unifying your message across copy and visuals.
                            .actions.d-flex.align-items-center.justify-content-between.mt-4
                                .text.d-flex.align-items-center
                                    a.circle-icon.me-3(href='#') !
                                    .copy
                                        p #[strong.d-block Action needed] Waiting for content and approvals, see below for more.
                                .meta
                                    h2.text-uppercase.mb-0 PHASE ENDS 3/1

        section.actions-need.bg-white
            .container-xxl
                .heading
                    h2.text-uppercase.text-orange.mb-1 ACTION NEEDED
                    h3.d-flex.align-items-center.mb-2 Missing Content #[a.circle-icon.ms-2(href='#') !]
                    h4.p.fst-italic.fw-medium.mb-0 Please Note: For each day past the deadline, project phases and final launch date moves back accordingly.

                mixin checkAction(title, status, description, actions)
                    .check-actions.d-flex.align-items-center
                        .check-icon
                            i.bi.bi-square
                        .text
                            p #[strong.d-block= title] #[strong.d-block.text-orange= status] #{description}
                        .cta-row.ms-auto.d-flex
                            each action in actions
                                a.cta.cta-action(href=action.href)
                                    if action.icon
                                        i.me-2.bi(class=`bi-${action.icon}`)
                                    | #{action.text}

                +checkAction('Logo', 'Due 1/31/25', 'SSGF needs this asset before we can begin.', [{text: 'UPLOAD', href: '#', icon: 'upload'}])

                +checkAction('Home Page Copy', 'Sent to you 2/5/25, due back 2/15/2025', 'SSGF needs edits or approval before we can move forward.', [
                    {text: 'VIEW DESIGNS', href: '#'},
                    {text: 'APPROVE', href: '#', icon: 'check2-circle'},
                    {text: 'SUBMIT EDIT', href: '#', icon: 'envelope'}
                ])

        section.actions-need.bg-white
            .container-xxl
                .heading
                    h2.text-uppercase.text-orange.mb-1 ACTION NEEDED
                    h3.d-flex.align-items-center.mb-2 Message from Developer
                .message-box.d-flex.mt-3
                    .pic
                        img(src='images/drew-pic.jpg', alt='')
                    .message-wrap.d-flex.align-items-start
                        .message
                            h2 #[span.quote "]You’re six days behind schedule, buddy.#[span.quote "]
                            .copy
                                p Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam viverra dolor eget velit suscipit dapibus. Suspendisse posuere elit ut arcu facilisis semper. Nunc accumsan ut justo sed sollicitudin. Fusce vulputate, nibh eget aliquam interdum, ipsum metus tempor nisl, sit amet cursus neque...#[a(href='message.html') See More ]
                                .meta.d-flex
                                    a.user.me-4(href="#")  Drew M.
                                    span.date 12/12/25
                        a.cta.d-inline-flex.align-items-center.text-decoration-none.py-2.ms-5.mt-0(href='#') #[i.bi.bi-envelope.me-2] REPLY
                .messages-wrap
                    .head
                        span#moreMessages More Messages #[img(src="images/down-arrow-orange.svg", alt="", width="18", height="10") ]
                    .messages-list
                        .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.orange
                            .col-md-auto.icons.d-flex
                                .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                                .icon.pic #[img(src='images/drew-pic.jpg', alt='')]
                            .col-md-auto.name
                                p Drew M.
                            .col-md-auto.date
                                p 12/12/25
                            .col-md-auto.notification
                                p #[a(href="#") You’re six days behind schedule, buddy]
                            .col-md.message.d-flex.align-items-center
                                .over-text.d-grid
                                    p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                a.ms-2(href="#") Read More
                            .col-md-auto.reply
                                a.cta.d-inline-flex.align-items-center.text-decoration-none.py-2.mt-0(href='#') #[i.bi.bi-envelope.me-2] REPLY

                        .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.orange
                            .col-md-auto.icons.d-flex
                                .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                                .icon.pic #[img(src='images/sally-pic.png', alt='')]
                            .col-md-auto.name
                                p Sally  M.
                            .col-md-auto.date
                                p 12/11/25
                            .col-md-auto.notification
                                p #[a(href="#") Just checking in]
                            .col-md.message.d-flex.align-items-center
                                .over-text.d-grid
                                    p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                a.ms-2(href="#") Read More
                            .col-md-auto.reply
                                a.cta.d-inline-flex.align-items-center.text-decoration-none.py-2.mt-0(href='#') #[i.bi.bi-envelope.me-2] REPLY

                        .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.orange
                            .col-md-auto.icons.d-flex
                                .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                                .icon.pic #[img(src='images/drew-pic.jpg', alt='')]
                            .col-md-auto.name
                                p Drew M.
                            .col-md-auto.date
                                p 12/11/25
                            .col-md-auto.notification
                                p #[a(href="#") You’re six days behind schedule, buddy]
                            .col-md.message.d-flex.align-items-center
                                .over-text.d-grid
                                    p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                a.ms-2(href="#") Read More
                            .col-md-auto.reply
                                a.cta.d-inline-flex.align-items-center.text-decoration-none.py-2.mt-0(href='#') #[i.bi.bi-envelope.me-2] REPLY

                .text-center.project-contact.pt-5.pb-0
                    a.cta.d-inline-block.text-decoration-none.text-uppercase.mt-4.mt-md-0(href='#') GO TO MESSAGE CENTER

        section.current-timeline.bg-white
            .container-xxl
                .heading.mb-4.d-md-flex.align-items-md-baseline.justify-content-between
                    h2.mb-0 Current Timeline
                    #tabTimeline.tab-timeline
                        each tab, index in ['Project Phases', 'Billing History']
                            span.tab(data-timeline-tab=tab.replace(' ', '-').toLowerCase(), class=(index === 0 ? 'active' : ''))=tab
                mixin metaRow(color, title, duration, target, showTargetIcon, status, showStatusIcon, blame=false)
                    .meta-row.d-flex.mt-3(class=color)
                        .logo.d-flex.align-items-center.justify-content-center.rounded-circle #[img(src=`images/${title}-project-icon.svg`, alt='')]
                        .wrap-meta.ms-3.d-flex.flex-grow-1.rounded-2
                            .title.d-flex.align-items-center.justify-content-center=title.toUpperCase()
                            .duration.col-meta.align-self-center #[strong Duration:] #{duration}
                            .target.col-meta.align-self-center
                                .text.d-inline-block
                                    if showTargetIcon
                                        span.check #[i.bi.bi-check2-circle]
                                    | #[strong Target:] #{target}
                                    if blame
                                        i.blame.ms-3.text-orange=blame
                            .status.col-meta.align-self-center.text-center
                                .text.d-inline-block
                                    if showStatusIcon
                                        span.check #[i.bi.bi-check2-circle]
                                    if status != '-'
                                        | #[strong Completed:] #{status}
                                    else
                                        | #[strong -]
                .tab-timeline-content(data-timeline-tab='project-phases')
                    +metaRow('orange', 'define', '1 Week', '1/31/25', true, '1/30/25', true)
                    +metaRow('green', 'content', '3-4 Weeks', '2/12/25', false, '-', false, '6 Days Past Due')
                    +metaRow('pink', 'design', '4-8 Weeks', '4/01/25', false, '-', false)
                    +metaRow('purple', 'code', '4-8 Weeks', '5/05/25', false, '-', false)
                    +metaRow('orange', 'deploy', '4-8 Weeks', '6/10/25', false, '-', false)

                .tab-timeline-content.d-none(data-timeline-tab='billing-history')
                    .meta-row.d-flex.mt-3(class='orange')
                        .logo.d-flex.align-items-center.justify-content-center.rounded-circle #[img(src='images/billing-project-icon.svg', alt='')]
                        .wrap-meta.payment.ms-3.d-flex.flex-grow-1.rounded-2
                            .title.d-flex.align-items-center.justify-content-center BILLING
                            .payment-type.col-meta.d-none.d-lg-block.align-self-center #[strong Payment Type]
                            .due-date.col-meta.d-none.d-lg-block.align-self-center.text-center #[strong Due Date]
                            .amount.col-meta.d-none.d-lg-block.align-self-center.text-center #[strong Amount]
                            .received.col-meta.d-none.d-lg-block.align-self-center.text-center #[strong Received]
                    .payment-list
                        mixin paymentRow(type, dueDate, amount='$2500', received='-', blame=false, done=false)
                            .payment-row.d-flex.flex-column.flex-sm-row
                                .payment-type.col-meta.align-self-center.text-center.text-md-start
                                    .col-meta.px-0.d-md-none #[strong Payment Type]

                                    if blame && done
                                        .d-flex
                                            span.check.text-orange.me-1 #[i.bi.bi-check2-circle]
                                            .text #[span.text #[strong #{type}]] #[br] #[i.blame.text-orange #{blame}]
                                    else if done
                                        span.check.text-orange.me-1 #[i.bi.bi-check2-circle]
                                        span.text #[strong #{type}]
                                    else
                                        strong #{type}

                                .due-date.col-meta.align-self-center.text-center.d-flex.flex-column
                                    .col-meta.px-0.d-md-none #[strong Due Date]
                                    | #{dueDate}
                                .amount.col-meta.align-self-center.text-center.d-flex.flex-column
                                    .col-meta.px-0.d-md-none #[strong Amount]
                                    | #{amount}
                                .received.col-meta.align-self-center.text-center.d-flex.flex-column
                                    .col-meta.px-0.d-md-none #[strong Received]
                                    | #{received}

                        +paymentRow('Initial Deposit', '12/31/25', '$2500', '12/30/25', false, true)
                        +paymentRow('Monthly Payment: January', '1/1/25', '$2500', '1/10/25', false, true)
                        +paymentRow('Monthly Payment: February', '2/1/25', '$2500', '-', '18 Days Past Due', true)
                        +paymentRow('Monthly Payment: March', '3/1/25', '$2500', '-')
                        +paymentRow('Monthly Payment: April', '4/1/25', '$2500', '-')
                        +paymentRow('Monthly Payment: May', '5/1/25', '$2500', '-')
                        +paymentRow('Monthly Payment: June', '6/1/25', '$2500', '-')
                        +paymentRow('Project Launch Payment', '6/10/25', '$2500', '-')
                        +paymentRow('Post-Launch: Monthly Maintenance Retainer', 'Ongoing', '$100', '-')

        section.project-contact.bg-white
            .container.text-center
                a.cta.d-inline-block.text-decoration-none.text-uppercase.mt-4.mt-md-0(href='#') GET EMAIL UPDATES
                a.cta.d-inline-block.text-decoration-none.text-uppercase.mt-4.mt-md-0(href='#') NEED HELP? CONTACT

        include ../partials/footer.pug
        +footer()