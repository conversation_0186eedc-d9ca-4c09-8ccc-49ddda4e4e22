<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comment_attachments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('comment_id')->nullable()->references('id')->on('comments');
            $table->text('comment_attachment')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comment_attachments', function (Blueprint $table) {
            $table->dropForeign(['comment_id']);  // Dropping the foreign key constraint
        });
        Schema::dropIfExists('comment_attachments');
    }
};
