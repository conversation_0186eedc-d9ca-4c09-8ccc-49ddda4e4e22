const colorPickerFields = document.querySelectorAll('.input_color');

colorPickerFields.forEach((field) => {
	const text = field.querySelector('.value');
	const picker = field.querySelector('.picker');

	if (!text || !picker) return;

	const syncColor = (source, target) => {
		target.value = source.value;
	};

	if (text.value) {
		syncColor(text, picker);
	}

	text.addEventListener('change', () => {
		syncColor(text, picker);
	});

	picker.addEventListener('input', () => {
		syncColor(picker, text);
	});
});
