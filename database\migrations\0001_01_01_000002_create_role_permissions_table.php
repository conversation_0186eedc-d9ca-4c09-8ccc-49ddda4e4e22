<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('role_permissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId("permission_id")->references("id")->on("permissions");
            $table->foreignId("role_id")->references("id")->on("roles");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('role_permissions', function (Blueprint $table) {
            $table->dropForeign(['permission_id']);  // Dropping the foreign key constraint
            $table->dropForeign(['role_id']);  // Dropping the foreign key constraint
        });
        Schema::dropIfExists('role_permissions');
    }
};
