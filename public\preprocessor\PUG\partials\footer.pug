mixin footer(logged=false, hr=true)
    footer.footer#footer
        if !logged
            .container-xxl.pt-4
                .row
                    .col-md-auto.col-lg-2
                        .logo.position-relative
                            #confettiBox.confetti_box
                            img.white(src='images/logo.svg', alt='', height='50', width='125')
                    .col-md
                        .row
                            mixin addressBox(head, text, email='To come', phone='To come')
                                .col-md.col-md-6.col-lg.mt-5.mb-0.mb-md-5.mb-lg-0.mt-md-0
                                    .location
                                        h2.text-uppercase=head
                                        p #{text[0]}#[br] #{text[1]}#[br] #{text[2]} #[span.d-block E: #[a.hover-underline(href='#') #{email}]]  #[span.d-block T: #[a.hover-underline(href='#') #{phone}]]
                            +addressBox(
                                'bos',
                                [
                                    '26 Howland Street',
                                    'Plymouth MA 02360',
                                    null
                                ],
                                '<EMAIL>',
                                '****** 732 9903'
                            )
                            +addressBox(
                                'pvd',
                                [
                                    '36 Bellair Drive',
                                    'Warwick RI 02886',
                                    null
                                ]
                            )
                            +addressBox(
                                'Alb',
                                [
                                    '684 NY 28',
                                    'Warrensburg NY 12885',
                                    null
                                ]
                            )
                            +addressBox(
                                'eu',
                                [
                                    '75 AV Parmentier',
                                    '75011, Paris II',
                                    'Iîl de France, France'
                                ]
                            )
        if hr
            .container-xxl
                hr(class=(logged ? 'mt-0 mb-4' : 'my-4'))

        .container-xxl
            .row.justify-content-lg-between
                .col-lg-auto
                    p &copy; 2025 Shields SGF LLC
                .col-lg-auto
                    p #[a.hover-underline(href='#') Privacy Policy] | #[a.hover-underline(href='#') Diversity Policy]
    if pageName !== 'Status Hub'
        .cursor-holder.d-none.d-xl-block
            .cursor
            .cursor

    script(src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.5/dist/js/bootstrap.bundle.min.js', integrity='sha384-k6d4wzSIapyDyv1kpU366/PK5hCdSbCRGRCMv+eplOQJWyd1fbcAu9OCUj5zNLiq', crossorigin='anonymous')
    script(src='https://code.jquery.com/jquery-3.6.4.min.js', integrity='sha256-oP6HI9z1XaZNBrJURtCoUT5SUnxFr8s3BzRl+cbzUq8=', crossorigin='anonymous')
    //- script(src='https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js', integrity='sha512-bPs7Ae6pVvhOSiIcyUClR7/q2OAsRiovw4vAkX+zJbw3ShAeeqezq50RIIcIURq7Oa20rW2n2q+fyXBNcU9lrw==', crossorigin='anonymous', referrerpolicy='no-referrer')
    //- script(src='js/projectHover.js')
    //- script(src='js/mosaicGallery.js')
    if pageName === 'Edit Team Member' || pageName === 'New Team Member'
        script(src='js/colorPicker.js')

    script(src='js/app.js')
    script(src='https://cdn.jsdelivr.net/npm/tsparticles-confetti@2.10.1/tsparticles.confetti.bundle.min.js')
    script(src='js/confettiLogo.js')