
<div>
    <style>
        #icon{
          height: 20px !important;
        }
        </style>
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                @php                   
                    $projectId = $project->id;

                    if ((string)$redirect === '1') {
                        $backUrl = route('edit-project', ['project_id' => $projectId]);
                        $backText = 'Back to Edit Project';
                    } else {
                        $backUrl = route('statushub');
                        $backText = 'Back to StatusHub';
                    }
                @endphp
                @if (admin_superadmin_permissions())                    
                    <a class="d-inline-flex align-items-center text-decoration-none"
                    href="{{ $backUrl }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />
                        {{ $backText }}
                    </a>
                @else
                   <a class="d-inline-flex align-items-center text-decoration-none"
                    href="{{ $backUrl }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />
                        {{ $backText }}
                    </a>
                @endif

            </div>

            @if (admin_superadmin_permissions())
                <div class="meta d-flex">
                    <h1 class="heavy text-white mb-0"> {{$project->name}} <i>Tasks</i></h1>
                    <div class="add-task ms-auto d-flex">
                        <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0"
                            href="{{ route('add-new-task') }}">
                            NEW TASK
                            <span class="img ms-2">
                                <svg width="21" height="21" viewBox="0 0 21 21" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                                    <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5">
                                    </path>
                                </svg>
                            </span>
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>


    <section class="client-project project-dashboard">

        <div class="container-xxl">
            <div class="row projects-row">

                <div class="col-md-3 col-xl-2 project-column quick-links">
                    <h2 class="text-uppercase">STATUSES</h2>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="statuses-list d-flex flex-column">
                        <a class="{{ $selectedStatus === 'all' ? 'active' : '' }}"
                            wire:click.prevent="filterByStatus('all')" href="#">
                            All ({{ $allTaskCount }})
                        </a>
                        @foreach ($statuses as $status)
                            <a class="{{ $selectedStatus == $status->id ? 'active' : '' }}"
                                wire:click.prevent="filterByStatus({{ $status->id }})" href="#">
                                {{-- {{ $status->name }} ({{ $status->tasks_count }}) --}}
                                {{ $status->name }} ({{ $statusCounts[$status->id] ?? 0 }})
                            </a>
                        @endforeach
                    </div>
                </div>


                <div wire:loading.class="blur-sm" wire:target="filterByStatus"
                class="col-md-9 col-xl-10 project-column task-overview">

                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">{{$project->name}} Tasks</h2>
                    <div class="sort d-flex align-items-center">


                    </div>
                </div>
                <hr class="mt-0 mb-4 border-white" />


                @if ($hasUrgentTasks)
                    <div class="task-overview-list">
                        @foreach ($groupedTasks['urgent'] as $task)
                        @php
                            $taskProject = $task->project;
                            $statusName = strtolower($task->status->name ?? '');
                        @endphp
                        @if($statusName !== 'recently finished' || $selectedStatus == $task->status_id)
                            <div class="task-project d-flex purple">
                                <div class="icon d-flex align-items-center justify-content-center">
                                    <img src="{{ asset('images/star-icon.svg') }}" alt="" width="20" height="20" />
                                </div>
                                <div class="copy d-flex flex-column flex-grow-1">
                                    <h2 class="text-uppercase">URGENT</h2>
                                    <div class="detail d-flex align-items-center w-100">
                                        <div class="date me-3">{{ \Carbon\Carbon::parse($task->due_date)->format('d/m/y') }}</div>
                                        <div class="task me-3"><a href="{{ route('view-task', $task->id) }}">{{ $task->name }}</a></div>
                                        <div class="text d-flex flex-grow-1 align-items-center">
                                            <div class="text over-text d-grid">
                                                <p class="text-truncate"> {{ strip_tags(html_entity_decode($task->description)) }}</p>
                                            </div>
                                            <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', $task->id) }}">Read More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @endforeach
                    </div>

                    @if ($hasMoreUrgent)
                        <div class="load-more text-center mt-4 mb-5">
                            <button wire:click="loadMoreUrgent" class="btn btn-outline-light">Load More</button>
                        </div>
                    @endif
                @endif


                <div class="task-overview-list">
                    @if ($sortField !== 'job' && $selectedStatus !== 'all')
                        <div class="head-sort mb-4">

                        </div>
                    @endif


                    @foreach ($groupedTasks['regular'] as $projectName => $tasks)
                        <div class="head-sort mb-4">

                        </div>

                        @foreach ($tasks as $task)
                        @php
                            $taskProject = $task->project;
                            $statusName = strtolower($task->status->name ?? '');
                        @endphp
                        @if($statusName !== 'recently finished' || $selectedStatus == $task->status_id)
                            <div class="task-project d-flex purple">
                                <div class="icon d-flex align-items-center justify-content-center">
                                    @if(strtolower($task->status->name ?? '') == 'new')
                                        <img id="icon" src="{{ asset('images/alert-icon.svg') }}" alt="" width="20" height="20" />
                                    @else
                                        <!-- No icon for regular tasks -->
                                    @endif
                                </div>
                                <div class="copy d-flex flex-column flex-grow-1">
                                    <h2 class="text-uppercase">{{ $task->status->name ?? 'Task' }}</h2>
                                    <div class="detail d-flex align-items-center w-100">
                                        <div class="date me-3">{{ \Carbon\Carbon::parse($task->created_at)->format('d/m/y') }}</div>
                                        <div class="task me-3"><a href="{{ route('view-task', $task->id) }}">{{ $task->name }}</a></div>
                                        <div class="text d-flex flex-grow-1 align-items-center">
                                            <div class="text over-text d-grid">
                                                <p class="text-truncate"> {{ strip_tags(html_entity_decode($task->description)) }}</p>
                                            </div>
                                            <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', $task->id) }}">Read More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                        @endforeach
                    @endforeach
                </div>

                @if ($hasMoreRegular)
                    <div class="load-more text-center mt-4 mb-5">
                        <button wire:click="loadMoreRegular" class="btn btn-outline-light">Load More</button>
                    </div>
                @endif
            </div>

            </div>
        </div>
    </section>




</div>