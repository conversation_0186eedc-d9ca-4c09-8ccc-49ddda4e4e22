<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_messages_attachments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('message_id')->nullable(); // <--- CRUCIAL CHANGE: Links to messages
            $table->string('file_name');     // Original file name (e.g., 'report.pdf')
            $table->timestamps();

            // Define the foreign key relationship to the project_messages table
            $table->foreign('message_id')
                  ->references('id')
                  ->on('project_messages') // <--- Links to project_messages table
                  ->onDelete('cascade'); // If a message is deleted, its attachments are also deleted
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('project_messages_attachments', function (Blueprint $table) {
            $table->dropForeign(['message_id']);
        });
        Schema::dropIfExists('project_messages_attachments');
    }
};