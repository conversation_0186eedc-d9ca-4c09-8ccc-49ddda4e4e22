- let pageName = 'New Team Member';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/fieldForms.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('New <i>Team Member</i>', 'Back to Team Portal')

        section.team-dashboard.pt-0.pb-5
            .container-xxl
                hr.mt-0.mb-4.border-white
                form.edit-member.row(action="")
                    .col-md-auto
                        .upload-icon.d-flex.align-items-center.justify-content-center
                            img(src="images/upload-member-icon.svg", alt="", height="60", width="40")

                    .col-md
                        .row
                            .col-md-6.mb-4
                                +inputFieldLabel('Name')
                            .col-md-6.mb-4
                                +inputColor('User Color', '#FE5811')
                            .col-md-6.mb-4
                                +inputFieldLabel('Email', 'email')
                            .col-md-6.mb-4
                                +inputFieldLabel('Temporary Password', 'password')
                            .col-md-6.mb-4
                                +selectFieldLabel('Set Team', ['Dev Team', 'Design Team',  'Admin'])
                            .col-md-6.mb-4
                                +selectFieldLabel('Set Access Level', ['Team Member', 'Lorem',  'Ipsum'])
                            .col-12.cta-row.text-center
                                button.cta.orange.ms-auto CREATE TEAM MEMBER

        include ../partials/footer.pug
        +footer(true)