@extends('layout.app')
@section('title', 'loginorg')
@section('content')



<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
        </div>
        <div class="meta d-flex justify-content-between align-items-center">
            <div class="copy">
                <h1 class="text-white mb-0">All, <i>Roles</i></h1>
            </div>
        </div>
    </div>
</section>
@if(isset($roles) && !empty($roles))
<section class="client-project pt-0">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="projects-list-table">
            @foreach($roles as $role)
            <div class="meta-project d-flex orange">
                <div class="copy flex-grow-1 d-flex align-items-end justify-content-between">
                    <div class="text">
                        <h2 class="mb-1"><a href="#">{{ $role->name }}</a></h2>
                        <p class="text-white">{{ $role->created_at->format('Y-m-d') }}</p>
                    </div>
                    <div class="cta-row"><a href="{{ route('edit-role', ['role_id' => $role->id]) }}">Assign Permissions</a></div>
                </div>
            </div>
            @endforeach
            
        </div>
        <br>
        <hr class="mt-0 mb-4 border-white" />
    </div>
</section>
@endif
<section class="ask-question">
    <div class="container-xxl">
        <div class="d-flex align-items-center justify-content-between pb-4">
            <div class="head">
                <h2 class="mb-0">
                    Need to Set Extra Role?<br />
                    <i>We’re here to help.</i>
                </h2>
            </div>
            <div class="cta-row"><a class="cta link text-decoration-none text-white" href="{{ route('add-role') }}">Add Role</a></div>
        </div>
        <hr class="mt-0 mt-4 border-white" />
    </div>
</section>


@endsection




