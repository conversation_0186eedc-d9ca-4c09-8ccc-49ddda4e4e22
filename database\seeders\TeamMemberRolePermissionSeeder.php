<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TeamMemberRolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the Admin role ID
        $adminRoleId = DB::table('roles')->where('name', 'Team Member')->value('id');

        if (!$adminRoleId) {
            $this->command->error("Admin role not found.");
            return;
        }

        // Get all permission IDs
        $allPermissions = DB::table('permissions')->pluck('id')->toArray();

        // Get currently assigned permissions for Admin
        $existingPermissions = DB::table('role_permissions')
            ->where('role_id', $adminRoleId)
            ->pluck('permission_id')
            ->toArray();

        // Find missing permissions
        $missingPermissions = array_diff($allPermissions, $existingPermissions);

        // Prepare insert data
        $insertData = [];
        foreach ($missingPermissions as $permissionId) {
            $insertData[] = [
                'role_id' => $adminRoleId,
                'permission_id' => $permissionId,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        }

        // Insert missing permissions
        if (!empty($insertData)) {
            DB::table('role_permissions')->insert($insertData);
            $this->command->info("Assigned " . count($insertData) . " missing permissions to Admin role.");
        } else {
            $this->command->info("All permissions are already assigned to Admin role.");
        }
    }
}
