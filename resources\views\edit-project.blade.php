@extends('layout.app')
@section('title', 'Edit Project')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">

                    <h4>
                        @if(request()->route()->named('edit-project'))
                            Edit Project
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('edit-project'))
                        @if(isset($no_projects_found_error) && $no_projects_found_error!='')
                            <div class="no_projects_found_error">{{ $no_projects_found_error }}</div>
                        @else

                            <div class="see-all-tasks-btn-div">
                            @if(Auth::user()->role_id=='1')
                                <form method="post" action="{{ route('delete-project', ['project_id'=>request()->route("project_id")]) }}">
                                    @csrf
                                    @method("delete")
                                    <a class="me-2" href="" onclick="event.preventDefault(); this.closest('form').submit()"><button>Delete Project <i class="fa fa-trash"></i></button></a>
                                </form>
                                <a class="me-2" href={{ route('projects') }}><button>See All Projects <i class="fa fa-list"></i></button></a>
                                <a href={{ route('add-status') }}><button class="me-2">Add Status</button></a>
                            @endif
                            <a href={{ route('show-tasks',['project_id' => $project->id]) }}><button>See All Tasks of this project <i class="fa fa-list"></i></button></a>
                            </div>


                            @if(Session::has('project_updated_success_message'))
                                <div class="bg-success text-light text-center mx-auto my-1 p-2 w-50 rounded">
                                    {{ Session::get('project_updated_success_message') }}
                                </div>
                            @endif
                            <div class="edit-project-div mx-auto rounded">
                                <form action="{{ route('update-project', ['project_id'=>request()->route("project_id")]) }}" method="post" class="border border-secondary w-50 mt-1 mx-auto p-3"  name="edit-project-form">
                                    @csrf
                                    @method("put")

                                   
                                    <div class="d-flex">
                                        <div class="edit-project-div-label">
                                            Project Name:
                                        </div>
                                        <div class="edit-project-div-input d-flex flex-column">
                                            <input class='edit-project-input' type="text" name="project_name" value="{{ $project->name }}">
                                            @error("project_name")
                                                <div class="text-danger">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <br>
                                    <div class="d-flex">
                                        <div class="edit-project-div-label">
                                            Assign to Company:
                                        </div>
                                        <div class="edit-project-div-input d-flex flex-column">
                                            <select class="edit-project-select companies-list-dropdown" id="0" name="company_id[]" multiple>
                                                <option value=''>Select Company</option>
                                            </select>
                                            @error("company_id")
                                                <div class="text-danger">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <br>
                                    <div class="d-flex mb-1">
                                        <div class="edit-project-div-label">
                                            Project Status:  
                                        </div>
                                        <div class="edit-project-div-input input-group d-flex align-items-center gap-2">
                                            <select class="edit-project-select" name="status_id">
                                                <option value="">Select Status</option>
                                                @foreach($status as $status)
                                                    <option value="{{ $status->id }}" @selected($status->id == $project->status_id)>{{ $status->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <br>
                                    <div>
                                        <button type="submit">Update</button>
                                    </div>
                                </form>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection


@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }
        .main-div{
            display:flex;
            height:100%;
            background-color:white;
        }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;

            left: 0;
            bottom:0;
            z-index: 0;
            height: calc(100vh - 50px);
            overflow:scroll;
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
            left: 0;
            bottom:0;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            /* padding:0px 50px;
            display:flex;
            justify-content:center; */
        }
        .see-all-tasks-btn-div{
            display: flex;
            justify-content: end;
        }
        form[name="edit-project-form"]{
            width:100%;
            text-align:center;
        }
        /*.project-details-div{
            margin-top:10px;
            padding:10px;
            width:50%;
            border:1px solid grey;
            display: flex;
            justify-content: center;
        }
        .project-details-div-label{
            width:25%;
            text-align:left;
        }
        .project-details-div-input{
            width:75%;
            text-align:left;
        }*/
        .edit-project-div{
            width:100%;
            display: block;
        }
        .edit-project-div-label{
            text-align:left;
            width:60%;
        }
        .edit-project-div-input{
            width:70%;
        }
        .edit-project-input{
            width:70%;
        }
        .edit-project-select{
            width: 70%;
        }
        .no_projects_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
        .status-modal-wrap{
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 500px;
            width: 100%;
            background-color: #fff;
        }
        .status-modal-wrap .status-modal{
            padding: 1rem;
        }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){
            load_companies_in_dropdown(0);

            function load_companies_in_dropdown($user_id=0){
                $.ajax({
                    url: "{{ route('companies-list-of-user-ajax') }}",
                    method: "get",
                    success: function(response){
                        
                        $('.companies-list-dropdown#0').html("<option value=''>Select Company</option>");//to destroy cached response
                        if(response!=""){
                            $.each(response, function(index, company) {
                                let checkedAttribute = ''; // Initialize checked attribute
                                
                                // Check if the current company's ID is present in the selectedCompanyIds array
                                if ((<?php echo isset($company_ids) ? json_encode($company_ids) : ''; ?>) .includes(company.id)) {
                                    checkedAttribute = 'selected';
                                }

                                $('.companies-list-dropdown').append(
                                    `<option value="${company.id}" data-company_id="${company.id}" ${checkedAttribute}>${company.name} (${company.id})</option>`
                                );
                            });

                                
                        }
                        else
                        {
                        }
                    }
                })
            }
        });
    </script>
@endpush