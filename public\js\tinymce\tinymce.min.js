/**
 * TinyMCE version 7.8.0 (TBD)
 */
!function(){"use strict";var e=function(e){if(null===e)return"null";if(void 0===e)return"undefined";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t},t=function(e){return{eq:e}},n=t((function(e,t){return e===t})),o=function(e){return t((function(t,n){if(t.length!==n.length)return!1;for(var o=t.length,r=0;r<o;r++)if(!e.eq(t[r],n[r]))return!1;return!0}))},r=function(e){return t((function(r,s){var a=Object.keys(r),i=Object.keys(s);if(!function(e,n){return function(e,n){return t((function(t,o){return e.eq(n(t),n(o))}))}(o(e),(function(e){return function(e,t){return Array.prototype.slice.call(e).sort(t)}(e,n)}))}(n).eq(a,i))return!1;for(var l=a.length,d=0;d<l;d++){var c=a[d];if(!e.eq(r[c],s[c]))return!1}return!0}))},s=t((function(t,n){if(t===n)return!0;var a=e(t);return a===e(n)&&(function(e){return-1!==["undefined","boolean","number","string","function","xml","null"].indexOf(e)}(a)?t===n:"array"===a?o(s).eq(t,n):"object"===a&&r(s).eq(t,n))}));const a=Object.getPrototypeOf,i=(e,t,n)=>{var o;return!!n(e,t.prototype)||(null===(o=e.constructor)||void 0===o?void 0:o.name)===t.name},l=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&i(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":t})(t)===e,d=e=>t=>typeof t===e,c=e=>t=>e===t,u=(e,t)=>f(e)&&i(e,t,((e,t)=>a(e)===t)),m=l("string"),f=l("object"),g=e=>u(e,Object),p=l("array"),h=c(null),b=d("boolean"),v=c(void 0),y=e=>null==e,C=e=>!y(e),w=d("function"),E=d("number"),x=(e,t)=>{if(p(e)){for(let n=0,o=e.length;n<o;++n)if(!t(e[n]))return!1;return!0}return!1},_=()=>{},S=(e,t)=>(...n)=>e(t.apply(null,n)),k=(e,t)=>n=>e(t(n)),N=e=>()=>e,R=e=>e,A=(e,t)=>e===t;function T(e,...t){return(...n)=>{const o=t.concat(n);return e.apply(null,o)}}const O=e=>t=>!e(t),B=e=>()=>{throw new Error(e)},P=e=>e(),D=e=>{e()},L=N(!1),M=N(!0);class I{constructor(e,t){this.tag=e,this.value=t}static some(e){return new I(!0,e)}static none(){return I.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?I.some(e(this.value)):I.none()}bind(e){return this.tag?e(this.value):I.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:I.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return C(e)?I.some(e):I.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}I.singletonNone=new I(!1);const F=Array.prototype.slice,U=Array.prototype.indexOf,z=Array.prototype.push,j=(e,t)=>U.call(e,t),H=(e,t)=>j(e,t)>-1,$=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return!0;return!1},V=(e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o},q=(e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)},W=(e,t)=>{for(let n=e.length-1;n>=0;n--)t(e[n],n)},K=(e,t)=>{const n=[],o=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?n:o).push(s)}return{pass:n,fail:o}},Y=(e,t)=>{const n=[];for(let o=0,r=e.length;o<r;o++){const r=e[o];t(r,o)&&n.push(r)}return n},G=(e,t,n)=>(W(e,((e,o)=>{n=t(n,e,o)})),n),X=(e,t,n)=>(q(e,((e,o)=>{n=t(n,e,o)})),n),Z=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++){const r=e[o];if(t(r,o))return I.some(r);if(n(r,o))break}return I.none()},Q=(e,t)=>Z(e,t,L),J=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return I.some(n);return I.none()},ee=e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!p(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);z.apply(t,e[n])}return t},te=(e,t)=>ee(V(e,t)),ne=(e,t)=>{for(let n=0,o=e.length;n<o;++n)if(!0!==t(e[n],n))return!1;return!0},oe=e=>{const t=F.call(e,0);return t.reverse(),t},re=(e,t)=>Y(e,(e=>!H(t,e))),se=(e,t)=>{const n={};for(let o=0,r=e.length;o<r;o++){const r=e[o];n[String(r)]=t(r,o)}return n},ae=(e,t)=>{const n=F.call(e,0);return n.sort(t),n},ie=(e,t)=>t>=0&&t<e.length?I.some(e[t]):I.none(),le=e=>ie(e,0),de=e=>ie(e,e.length-1),ce=w(Array.from)?Array.from:e=>F.call(e),ue=(e,t)=>{for(let n=0;n<e.length;n++){const o=t(e[n],n);if(o.isSome())return o}return I.none()},me=(e,t)=>{const n=[],o=w(t)?e=>$(n,(n=>t(n,e))):e=>H(n,e);for(let t=0,r=e.length;t<r;t++){const r=e[t];o(r)||n.push(r)}return n},fe=Object.keys,ge=Object.hasOwnProperty,pe=(e,t)=>{const n=fe(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}},he=(e,t)=>be(e,((e,n)=>({k:n,v:t(e,n)}))),be=(e,t)=>{const n={};return pe(e,((e,o)=>{const r=t(e,o);n[r.k]=r.v})),n},ve=e=>(t,n)=>{e[n]=t},ye=(e,t,n,o)=>{pe(e,((e,r)=>{(t(e,r)?n:o)(e,r)}))},Ce=(e,t)=>{const n={};return ye(e,t,ve(n),_),n},we=(e,t)=>{const n=[];return pe(e,((e,o)=>{n.push(t(e,o))})),n},Ee=e=>we(e,R),xe=(e,t)=>_e(e,t)?I.from(e[t]):I.none(),_e=(e,t)=>ge.call(e,t),Se=(e,t)=>_e(e,t)&&void 0!==e[t]&&null!==e[t],ke=e=>{const t={};return q(e,(e=>{t[e]={}})),fe(t)},Ne=e=>void 0!==e.length,Re=Array.isArray,Ae=(e,t,n)=>{if(!e)return!1;if(n=n||e,Ne(e)){for(let o=0,r=e.length;o<r;o++)if(!1===t.call(n,e[o],o,e))return!1}else for(const o in e)if(_e(e,o)&&!1===t.call(n,e[o],o,e))return!1;return!0},Te=(e,t)=>{const n=[];return Ae(e,((o,r)=>{n.push(t(o,r,e))})),n},Oe=(e,t)=>{const n=[];return Ae(e,((o,r)=>{t&&!t(o,r,e)||n.push(o)})),n},Be=(e,t,n,o)=>{let r=v(n)?e[0]:n;for(let n=0;n<e.length;n++)r=t.call(o,r,e[n],n);return r},Pe=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++)if(t.call(n,e[o],o,e))return o;return-1},De=e=>e[e.length-1],Le=e=>{let t,n=!1;return(...o)=>(n||(n=!0,t=e.apply(null,o)),t)},Me=()=>Ie(0,0),Ie=(e,t)=>({major:e,minor:t}),Fe={nu:Ie,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?Me():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const o=e[n];if(o.test(t))return o}})(e,t);if(!n)return{major:0,minor:0};const o=e=>Number(t.replace(n,"$"+e));return Ie(o(1),o(2))})(e,n)},unknown:Me},Ue=(e,t)=>{const n=String(t).toLowerCase();return Q(e,(e=>e.search(n)))},ze=(e,t,n)=>""===t||e.length>=t.length&&e.substr(n,n+t.length)===t,je=(e,t)=>$e(e,t)?((e,t)=>e.substring(t))(e,t.length):e,He=(e,t,n=0,o)=>{const r=e.indexOf(t,n);return-1!==r&&(!!v(o)||r+t.length<=o)},$e=(e,t)=>ze(e,t,0),Ve=(e,t)=>ze(e,t,e.length-t.length),qe=e=>t=>t.replace(e,""),We=qe(/^\s+|\s+$/g),Ke=qe(/^\s+/g),Ye=qe(/\s+$/g),Ge=e=>e.length>0,Xe=e=>!Ge(e),Ze=(e,t=10)=>{const n=parseInt(e,t);return isNaN(n)?I.none():I.some(n)},Qe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Je=e=>t=>He(t,e),et=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>He(e,"edge/")&&He(e,"chrome")&&He(e,"safari")&&He(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Qe],search:e=>He(e,"chrome")&&!He(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>He(e,"msie")||He(e,"trident")},{name:"Opera",versionRegexes:[Qe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Je("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Je("firefox")},{name:"Safari",versionRegexes:[Qe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(He(e,"safari")||He(e,"mobile/"))&&He(e,"applewebkit")}],tt=[{name:"Windows",search:Je("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>He(e,"iphone")||He(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Je("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Je("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Je("linux"),versionRegexes:[]},{name:"Solaris",search:Je("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Je("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Je("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],nt={browsers:N(et),oses:N(tt)},ot="Edge",rt="Chromium",st="Opera",at="Firefox",it="Safari",lt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isEdge:o(ot),isChromium:o(rt),isIE:o("IE"),isOpera:o(st),isFirefox:o(at),isSafari:o(it)}},dt=()=>lt({current:void 0,version:Fe.unknown()}),ct=lt,ut=(N(ot),N(rt),N("IE"),N(st),N(at),N(it),"Windows"),mt="Android",ft="Linux",gt="macOS",pt="Solaris",ht="FreeBSD",bt="ChromeOS",vt=e=>{const t=e.current,n=e.version,o=e=>()=>t===e;return{current:t,version:n,isWindows:o(ut),isiOS:o("iOS"),isAndroid:o(mt),isMacOS:o(gt),isLinux:o(ft),isSolaris:o(pt),isFreeBSD:o(ht),isChromeOS:o(bt)}},yt=()=>vt({current:void 0,version:Fe.unknown()}),Ct=vt,wt=(N(ut),N("iOS"),N(mt),N(ft),N(gt),N(pt),N(ht),N(bt),e=>window.matchMedia(e).matches);let Et=Le((()=>((e,t,n)=>{const o=nt.browsers(),r=nt.oses(),s=t.bind((e=>((e,t)=>ue(t.brands,(t=>{const n=t.brand.toLowerCase();return Q(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Fe.nu(parseInt(t.version,10),0)})))})))(o,e))).orThunk((()=>((e,t)=>Ue(e,t).map((e=>{const n=Fe.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e))).fold(dt,ct),a=((e,t)=>Ue(e,t).map((e=>{const n=Fe.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e).fold(yt,Ct),i=((e,t,n,o)=>{const r=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!r,a=e.isiOS()||e.isAndroid(),i=a||o("(pointer:coarse)"),l=r||!s&&a&&o("(min-device-width:768px)"),d=s||a&&!l,c=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),u=!d&&!l&&!c;return{isiPad:N(r),isiPhone:N(s),isTablet:N(l),isPhone:N(d),isTouch:N(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:N(c),isDesktop:N(u)}})(a,s,e,n);return{browser:s,os:a,deviceType:i}})(window.navigator.userAgent,I.from(window.navigator.userAgentData),wt)));const xt=()=>Et(),_t=window.navigator.userAgent,St=xt(),kt=St.browser,Nt=St.os,Rt=St.deviceType,At=-1!==_t.indexOf("Windows Phone"),Tt={transparentSrc:"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",documentMode:kt.isIE()?document.documentMode||7:10,cacheSuffix:null,container:null,canHaveCSP:!kt.isIE(),windowsPhone:At,browser:{current:kt.current,version:kt.version,isChromium:kt.isChromium,isEdge:kt.isEdge,isFirefox:kt.isFirefox,isIE:kt.isIE,isOpera:kt.isOpera,isSafari:kt.isSafari},os:{current:Nt.current,version:Nt.version,isAndroid:Nt.isAndroid,isChromeOS:Nt.isChromeOS,isFreeBSD:Nt.isFreeBSD,isiOS:Nt.isiOS,isLinux:Nt.isLinux,isMacOS:Nt.isMacOS,isSolaris:Nt.isSolaris,isWindows:Nt.isWindows},deviceType:{isDesktop:Rt.isDesktop,isiPad:Rt.isiPad,isiPhone:Rt.isiPhone,isPhone:Rt.isPhone,isTablet:Rt.isTablet,isTouch:Rt.isTouch,isWebView:Rt.isWebView}},Ot=/^\s*|\s*$/g,Bt=e=>y(e)?"":(""+e).replace(Ot,""),Pt=function(e,t,n,o){o=o||this,e&&(n&&(e=e[n]),Ae(e,((e,r)=>!1!==t.call(o,e,r,n)&&(Pt(e,t,n,o),!0))))},Dt={trim:Bt,isArray:Re,is:(e,t)=>t?!("array"!==t||!Re(e))||typeof e===t:void 0!==e,toArray:e=>{if(Re(e))return e;{const t=[];for(let n=0,o=e.length;n<o;n++)t[n]=e[n];return t}},makeMap:(e,t,n={})=>{const o=m(e)?e.split(t||","):e||[];let r=o.length;for(;r--;)n[o[r]]={};return n},each:Ae,map:Te,grep:Oe,inArray:(e,t)=>{if(e)for(let n=0,o=e.length;n<o;n++)if(e[n]===t)return n;return-1},hasOwn:_e,extend:(e,...t)=>{for(let n=0;n<t.length;n++){const o=t[n];for(const t in o)if(_e(o,t)){const n=o[t];void 0!==n&&(e[t]=n)}}return e},walk:Pt,resolve:(e,t=window)=>{const n=e.split(".");for(let e=0,o=n.length;e<o&&(t=t[n[e]]);e++);return t},explode:(e,t)=>p(e)?e:""===e?[]:Te(e.split(t||","),Bt),_addCacheSuffix:e=>{const t=Tt.cacheSuffix;return t&&(e+=(-1===e.indexOf("?")?"?":"&")+t),e}},Lt=(e,t,n=A)=>e.exists((e=>n(e,t))),Mt=(e,t,n=A)=>It(e,t,n).getOr(e.isNone()&&t.isNone()),It=(e,t,n)=>e.isSome()&&t.isSome()?I.some(n(e.getOrDie(),t.getOrDie())):I.none(),Ft=(e,t)=>e?I.some(t):I.none(),Ut="undefined"!=typeof window?window:Function("return this;")(),zt=(e,t)=>((e,t)=>{let n=null!=t?t:Ut;for(let t=0;t<e.length&&null!=n;++t)n=n[e[t]];return n})(e.split("."),t),jt=Object.getPrototypeOf,Ht=e=>{const t=zt("ownerDocument.defaultView",e);return f(e)&&((e=>((e,t)=>{const n=((e,t)=>zt(e,t))(e,t);if(null==n)throw new Error(e+" not available on this browser");return n})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(jt(e).constructor.name))},$t=e=>e.dom.nodeName.toLowerCase(),Vt=e=>e.dom.nodeType,qt=e=>t=>Vt(t)===e,Wt=e=>Kt(e)&&Ht(e.dom),Kt=qt(1),Yt=qt(3),Gt=qt(9),Xt=qt(11),Zt=e=>t=>Kt(t)&&$t(t)===e,Qt=(e,t,n)=>{if(!(m(n)||b(n)||E(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},Jt=(e,t,n)=>{Qt(e.dom,t,n)},en=(e,t)=>{const n=e.dom;pe(t,((e,t)=>{Qt(n,t,e)}))},tn=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},nn=(e,t)=>I.from(tn(e,t)),on=(e,t)=>{const n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},rn=(e,t)=>{e.dom.removeAttribute(t)},sn=e=>X(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),an=(e,t)=>{const n=tn(e,t);return void 0===n||""===n?[]:n.split(" ")},ln=e=>void 0!==e.dom.classList,dn=e=>an(e,"class"),cn=(e,t)=>((e,t,n)=>{const o=an(e,t).concat([n]);return Jt(e,t,o.join(" ")),!0})(e,"class",t),un=(e,t)=>((e,t,n)=>{const o=Y(an(e,t),(e=>e!==n));return o.length>0?Jt(e,t,o.join(" ")):rn(e,t),!1})(e,"class",t),mn=(e,t)=>{ln(e)?e.dom.classList.add(t):cn(e,t)},fn=e=>{0===(ln(e)?e.dom.classList:dn(e)).length&&rn(e,"class")},gn=(e,t)=>{ln(e)?e.dom.classList.remove(t):un(e,t),fn(e)},pn=(e,t)=>ln(e)&&e.dom.classList.contains(t),hn=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},bn=(e,t)=>{const n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return hn(n.childNodes[0])},vn=(e,t)=>{const n=(t||document).createElement(e);return hn(n)},yn=(e,t)=>{const n=(t||document).createTextNode(e);return hn(n)},Cn=hn,wn=(e,t,n)=>I.from(e.dom.elementFromPoint(t,n)).map(hn),En=(e,t)=>{const n=[],o=e=>(n.push(e),t(e));let r=t(e);do{r=r.bind(o)}while(r.isSome());return n},xn=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},_n=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Sn=(e,t)=>e.dom===t.dom,kn=(e,t)=>{const n=e.dom,o=t.dom;return n!==o&&n.contains(o)},Nn=e=>Cn(e.dom.ownerDocument),Rn=e=>Gt(e)?e:Nn(e),An=e=>Cn(Rn(e).dom.defaultView),Tn=e=>I.from(e.dom.parentNode).map(Cn),On=e=>I.from(e.dom.parentElement).map(Cn),Bn=(e,t)=>{const n=w(t)?t:L;let o=e.dom;const r=[];for(;null!==o.parentNode&&void 0!==o.parentNode;){const e=o.parentNode,t=Cn(e);if(r.push(t),!0===n(t))break;o=e}return r},Pn=e=>I.from(e.dom.previousSibling).map(Cn),Dn=e=>I.from(e.dom.nextSibling).map(Cn),Ln=e=>oe(En(e,Pn)),Mn=e=>En(e,Dn),In=e=>V(e.dom.childNodes,Cn),Fn=(e,t)=>{const n=e.dom.childNodes;return I.from(n[t]).map(Cn)},Un=e=>Fn(e,0),zn=e=>Fn(e,e.dom.childNodes.length-1),jn=e=>e.dom.childNodes.length,Hn=e=>Xt(e)&&C(e.dom.host),$n=e=>Cn(e.dom.getRootNode()),Vn=e=>Hn(e)?e:(e=>{const t=e.dom.head;if(null==t)throw new Error("Head is not available yet");return Cn(t)})(Rn(e)),qn=e=>Cn(e.dom.host),Wn=e=>{if(C(e.target)){const t=Cn(e.target);if(Kt(t)&&Kn(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return le(t)}}return I.from(e.target)},Kn=e=>C(e.dom.shadowRoot),Yn=e=>{const t=Yt(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return(e=>{const t=$n(e);return Hn(t)?I.some(t):I.none()})(Cn(t)).fold((()=>n.body.contains(t)),k(Yn,qn))};var Gn=(e,t,n,o,r)=>e(n,o)?I.some(n):w(r)&&r(n)?I.none():t(n,o,r);const Xn=(e,t,n)=>{let o=e.dom;const r=w(n)?n:L;for(;o.parentNode;){o=o.parentNode;const e=Cn(o);if(t(e))return I.some(e);if(r(e))break}return I.none()},Zn=(e,t,n)=>Gn(((e,t)=>t(e)),Xn,e,t,n),Qn=(e,t)=>{const n=e=>{for(let o=0;o<e.childNodes.length;o++){const r=Cn(e.childNodes[o]);if(t(r))return I.some(r);const s=n(e.childNodes[o]);if(s.isSome())return s}return I.none()};return n(e.dom)},Jn=(e,t,n)=>Xn(e,(e=>xn(e,t)),n),eo=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return _n(n)?I.none():I.from(n.querySelector(e)).map(Cn)})(t,e),to=(e,t,n)=>Gn(((e,t)=>xn(e,t)),Jn,e,t,n),no=(e,t=!1)=>{return Yn(e)?e.dom.isContentEditable:(n=e,to(n,"[contenteditable]")).fold(N(t),(e=>"true"===oo(e)));var n},oo=e=>e.dom.contentEditable,ro=(e,t)=>{e.dom.contentEditable=t?"true":"false"},so=e=>void 0!==e.style&&w(e.style.getPropertyValue),ao=(e,t,n)=>{if(!m(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);so(e)&&e.style.setProperty(t,n)},io=(e,t,n)=>{const o=e.dom;ao(o,t,n)},lo=(e,t)=>{const n=e.dom;pe(t,((e,t)=>{ao(n,t,e)}))},co=(e,t)=>{const n=e.dom,o=window.getComputedStyle(n).getPropertyValue(t);return""!==o||Yn(e)?o:uo(n,t)},uo=(e,t)=>so(e)?e.style.getPropertyValue(t):"",mo=(e,t)=>{const n=e.dom,o=uo(n,t);return I.from(o).filter((e=>e.length>0))},fo=e=>{const t={},n=e.dom;if(so(n))for(let e=0;e<n.style.length;e++){const o=n.style.item(e);t[o]=n.style[o]}return t},go=(e,t)=>{((e,t)=>{so(e)&&e.style.removeProperty(t)})(e.dom,t),Lt(nn(e,"style").map(We),"")&&rn(e,"style")},po=(e,t)=>{Tn(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},ho=(e,t)=>{Dn(e).fold((()=>{Tn(e).each((e=>{vo(e,t)}))}),(e=>{po(e,t)}))},bo=(e,t)=>{Un(e).fold((()=>{vo(e,t)}),(n=>{e.dom.insertBefore(t.dom,n.dom)}))},vo=(e,t)=>{e.dom.appendChild(t.dom)},yo=(e,t)=>{po(e,t),vo(t,e)},Co=(e,t)=>{q(t,(t=>{vo(e,t)}))},wo=e=>{e.dom.textContent="",q(In(e),(e=>{Eo(e)}))},Eo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},xo=e=>{const t=In(e);var n,o;t.length>0&&(n=e,q(o=t,((e,t)=>{const r=0===t?n:o[t-1];ho(r,e)}))),Eo(e)},_o=e=>V(e,Cn),So=e=>e.dom.innerHTML,ko=(e,t)=>{const n=Nn(e).dom,o=Cn(n.createDocumentFragment()),r=((e,t)=>{const n=(t||document).createElement("div");return n.innerHTML=e,In(Cn(n))})(t,n);Co(o,r),wo(e),vo(e,o)},No=(e,t,n,o)=>((e,t,n,o,r)=>{const s=((e,t)=>n=>{e(n)&&t((e=>{const t=Cn(Wn(e).getOr(e.target)),n=()=>e.stopPropagation(),o=()=>e.preventDefault(),r=S(o,n);return((e,t,n,o,r,s,a)=>({target:e,x:t,y:n,stop:o,prevent:r,kill:s,raw:a}))(t,e.clientX,e.clientY,n,o,r,e)})(n))})(n,o);return e.dom.addEventListener(t,s,r),{unbind:T(Ro,e,t,s,r)}})(e,t,n,o,!1),Ro=(e,t,n,o)=>{e.dom.removeEventListener(t,n,o)},Ao=(e,t)=>({left:e,top:t,translate:(n,o)=>Ao(e+n,t+o)}),To=Ao,Oo=(e,t)=>void 0!==e?e:void 0!==t?t:0,Bo=e=>{const t=e.dom,n=t.ownerDocument.body;return n===t?To(n.offsetLeft,n.offsetTop):Yn(e)?(e=>{const t=e.getBoundingClientRect();return To(t.left,t.top)})(t):To(0,0)},Po=e=>{const t=void 0!==e?e.dom:document,n=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return To(n,o)},Do=(e,t,n)=>{const o=(void 0!==n?n.dom:document).defaultView;o&&o.scrollTo(e,t)},Lo=(e,t)=>{xt().browser.isSafari()&&w(e.dom.scrollIntoViewIfNeeded)?e.dom.scrollIntoViewIfNeeded(!1):e.dom.scrollIntoView(t)},Mo=(e,t,n,o)=>({x:e,y:t,width:n,height:o,right:e+n,bottom:t+o}),Io=e=>{const t=void 0===e?window:e,n=t.document,o=Po(Cn(n));return(e=>{const t=void 0===e?window:e;return xt().browser.isFirefox()?I.none():I.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,n=e.clientWidth,r=e.clientHeight;return Mo(o.left,o.top,n,r)}),(e=>Mo(Math.max(e.pageLeft,o.left),Math.max(e.pageTop,o.top),e.width,e.height)))},Fo=(e,t)=>{let n=[];return q(In(e),(e=>{t(e)&&(n=n.concat([e])),n=n.concat(Fo(e,t))})),n},Uo=(e,t)=>((e,t)=>{const n=void 0===t?document:t.dom;return _n(n)?[]:V(n.querySelectorAll(e),Cn)})(t,e),zo=(e,t,n)=>Xn(e,t,n).isSome(),jo=(e,t)=>((e,t)=>{const n=e.dom;return n.parentNode?((e,t)=>Q(e.dom.childNodes,(e=>t(Cn(e)))).map(Cn))(Cn(n.parentNode),(n=>!Sn(e,n)&&t(n))):I.none()})(e,t).isSome(),Ho=(e,t)=>Qn(e,t).isSome();class $o{constructor(e,t){this.node=e,this.rootNode=t,this.current=this.current.bind(this),this.next=this.next.bind(this),this.prev=this.prev.bind(this),this.prev2=this.prev2.bind(this)}current(){return this.node}next(e){return this.node=this.findSibling(this.node,"firstChild","nextSibling",e),this.node}prev(e){return this.node=this.findSibling(this.node,"lastChild","previousSibling",e),this.node}prev2(e){return this.node=this.findPreviousNode(this.node,e),this.node}findSibling(e,t,n,o){if(e){if(!o&&e[t])return e[t];if(e!==this.rootNode){let t=e[n];if(t)return t;for(let o=e.parentNode;o&&o!==this.rootNode;o=o.parentNode)if(t=o[n],t)return t}}}findPreviousNode(e,t){if(e){const n=e.previousSibling;if(this.rootNode&&n===this.rootNode)return;if(n){if(!t)for(let e=n.lastChild;e;e=e.lastChild)if(!e.lastChild)return e;return n}const o=e.parentNode;if(o&&o!==this.rootNode)return o}}}const Vo="\ufeff",qo="\xa0",Wo=e=>e===Vo,Ko=/^[ \t\r\n]*$/,Yo=e=>Ko.test(e),Go=e=>"\n"===e||"\r"===e,Xo=(e,t=4,n=!0,o=!0)=>{const r=((e,t)=>t<=0?"":new Array(t+1).join(" "))(0,t),s=e.replace(/\t/g,r),a=X(s,((e,t)=>(e=>-1!==" \f\t\v".indexOf(e))(t)||t===qo?e.pcIsSpace||""===e.str&&n||e.str.length===s.length-1&&o||((e,t)=>t<e.length&&t>=0&&Go(e[t]))(s,e.str.length+1)?{pcIsSpace:!1,str:e.str+qo}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:Go(t),str:e.str+t}),{pcIsSpace:!1,str:""});return a.str},Zo=e=>t=>!!t&&t.nodeType===e,Qo=e=>!!e&&!Object.getPrototypeOf(e),Jo=Zo(1),er=e=>Jo(e)&&Wt(Cn(e)),tr=e=>{const t=e.toLowerCase();return e=>C(e)&&e.nodeName.toLowerCase()===t},nr=e=>{const t=e.map((e=>e.toLowerCase()));return e=>{if(e&&e.nodeName){const n=e.nodeName.toLowerCase();return H(t,n)}return!1}},or=(e,t)=>{const n=t.toLowerCase().split(" ");return t=>{if(Jo(t)){const o=t.ownerDocument.defaultView;if(o)for(let r=0;r<n.length;r++){const s=o.getComputedStyle(t,null);if((s?s.getPropertyValue(e):null)===n[r])return!0}}return!1}},rr=e=>Jo(e)&&e.hasAttribute("data-mce-bogus"),sr=e=>Jo(e)&&"TABLE"===e.tagName,ar=e=>t=>{if(er(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},ir=nr(["textarea","input"]),lr=Zo(3),dr=Zo(4),cr=Zo(7),ur=Zo(8),mr=Zo(9),fr=Zo(11),gr=tr("br"),pr=tr("img"),hr=ar("true"),br=ar("false"),vr=e=>er(e)&&e.isContentEditable&&C(e.parentElement)&&!e.parentElement.isContentEditable,yr=nr(["td","th"]),Cr=nr(["td","th","caption"]),wr=nr(["video","audio","object","embed"]),Er=tr("li"),xr=tr("details"),_r=tr("summary"),Sr={skipBogus:!0,includeZwsp:!1,checkRootAsContent:!1},kr=e=>Jo(e)&&e.hasAttribute("data-mce-bookmark");const Nr=(e,t,n,o)=>lr(e)&&!((e,t,n)=>Yo(e.data)&&!((e,t,n)=>{const o=Cn(t),r=Cn(e),s=n.getWhitespaceElements();return zo(r,(e=>_e(s,$t(e))),T(Sn,o))})(e,t,n))(e,t,n)&&(!o.includeZwsp||!(e=>{for(const t of e)if(!Wo(t))return!1;return!0})(e.data)),Rr=(e,t,n,o)=>w(o.isContent)&&o.isContent(t)||((e,t)=>Jo(e)&&_e(t.getNonEmptyElements(),e.nodeName))(t,e)||kr(t)||(e=>Jo(e)&&"A"===e.nodeName&&!e.hasAttribute("href")&&(e.hasAttribute("name")||e.hasAttribute("id")))(t)||Nr(t,n,e,o)||br(t)||hr(t)&&(e=>On(Cn(e)).exists((e=>!no(e))))(t),Ar=(e,t,n)=>{const o={...Sr,...n};if(o.checkRootAsContent&&Rr(e,t,t,o))return!1;let r=t.firstChild,s=0;if(!r)return!0;const a=new $o(r,t);do{if(o.skipBogus&&Jo(r)){const e=r.getAttribute("data-mce-bogus");if(e){r=a.next("all"===e);continue}}if(ur(r))r=a.next(!0);else if(gr(r))s++,r=a.next();else{if(Rr(e,r,t,o))return!1;r=a.next()}}while(r);return s<=1},Tr=(e,t,n)=>Ar(e,t.dom,{checkRootAsContent:!0,...n}),Or=(e,t,n)=>Rr(e,t,t,{includeZwsp:Sr.includeZwsp,...n}),Br=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},Pr=e=>{const t=Br(I.none()),n=()=>t.get().each((e=>clearInterval(e)));return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:o=>{n(),t.set(I.some(setInterval(o,e)))}}},Dr=()=>{const e=(e=>{const t=Br(I.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(I.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(I.some(e))}}})(_);return{...e,on:t=>e.get().each(t)}},Lr=e=>{const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":"html"},Mr=e=>"html"!==Lr(e),Ir=e=>Mr(e.nodeName),Fr=e=>Lr(e.nodeName),Ur=["svg","math"],zr="data-mce-block",jr=e=>V((e=>Y(fe(e),(e=>!/[A-Z]/.test(e))))(e),(e=>{const t=CSS.escape(e);return`${t}:`+V(Ur,(e=>`not(${e} ${t})`)).join(":")})).join(","),Hr=(e,t)=>C(t.querySelector(e))?(t.setAttribute(zr,"true"),"inline-boundary"===t.getAttribute("data-mce-selected")&&t.removeAttribute("data-mce-selected"),!0):(t.removeAttribute(zr),!1),$r=(e,t)=>{const n=jr(e.getTransparentElements()),o=jr(e.getBlockElements());return Y(t.querySelectorAll(n),(e=>Hr(o,e)))},Vr=(e,t,n)=>{var o;const r=n?"lastChild":"firstChild";for(let n=t[r];n;n=n[r])if(Ar(e,n,{checkRootAsContent:!0}))return void(null===(o=n.parentNode)||void 0===o||o.removeChild(n))},qr=(e,t,n)=>{const o=e.getBlockElements(),r=Cn(t),s=e=>$t(e)in o,a=e=>Sn(e,r);q(_o(n),(t=>{Xn(t,s,a).each((n=>{const o=(t=>Y(In(t),(t=>s(t)&&!e.isValidChild($t(n),$t(t)))))(t);if(o.length>0){const t=On(n);q(o,(t=>{Xn(t,s,a).each((n=>{((e,t,n)=>{const o=document.createRange(),r=t.parentNode;if(r){o.setStartBefore(t),o.setEndBefore(n);const s=o.extractContents();Vr(e,s,!0),o.setStartAfter(n),o.setEndAfter(t);const a=o.extractContents();Vr(e,a,!1),Ar(e,s,{checkRootAsContent:!0})||r.insertBefore(s,t),Ar(e,n,{checkRootAsContent:!0})||r.insertBefore(n,t),Ar(e,a,{checkRootAsContent:!0})||r.insertBefore(a,t),r.removeChild(t)}})(e,n.dom,t.dom)}))})),t.each((t=>$r(e,t.dom)))}}))}))},Wr=(e,t)=>{const n=$r(e,t);qr(e,t,n),((e,t,n)=>{q([...n,...Zr(e,t)?[t]:[]],(t=>q(Uo(Cn(t),t.nodeName.toLowerCase()),(t=>{Qr(e,t.dom)&&xo(t)}))))})(e,t,n)},Kr=(e,t)=>{if(Xr(e,t)){const n=jr(e.getBlockElements());Hr(n,t)}},Yr=e=>e.hasAttribute(zr),Gr=(e,t)=>_e(e.getTransparentElements(),t),Xr=(e,t)=>Jo(t)&&Gr(e,t.nodeName),Zr=(e,t)=>Xr(e,t)&&Yr(t),Qr=(e,t)=>Xr(e,t)&&!Yr(t),Jr=(e,t)=>1===t.type&&Gr(e,t.name)&&m(t.attr(zr)),es=xt().browser,ts=e=>Q(e,Kt),ns=(e,t)=>e.children&&H(e.children,t),os=(e,t={})=>{let n=0;const o={},r=Cn(e),s=Rn(r),a=e=>{vo(Vn(r),e)},i=e=>{const t=Vn(r);eo(t,"#"+e).each(Eo)},l=e=>xe(o,e).getOrThunk((()=>({id:"mce-u"+n++,passed:[],failed:[],count:0}))),d=e=>new Promise(((n,r)=>{let i;const d=Dt._addCacheSuffix(e),c=l(d);o[d]=c,c.count++;const u=(e,t)=>{q(e,D),c.status=t,c.passed=[],c.failed=[],i&&(i.onload=null,i.onerror=null,i=null)},m=()=>u(c.passed,2),f=()=>u(c.failed,3);if(n&&c.passed.push(n),r&&c.failed.push(r),1===c.status)return;if(2===c.status)return void m();if(3===c.status)return void f();c.status=1;const g=vn("link",s.dom);en(g,{rel:"stylesheet",type:"text/css",id:c.id}),t.contentCssCors&&Jt(g,"crossOrigin","anonymous"),t.referrerPolicy&&Jt(g,"referrerpolicy",t.referrerPolicy),i=g.dom,i.onload=m,i.onerror=f,a(g),Jt(g,"href",d)})),c=e=>{const t=Dt._addCacheSuffix(e);xe(o,t).each((e=>{0==--e.count&&(delete o[t],i(e.id))}))};return{load:d,loadRawCss:(e,t)=>{const n=l(e);o[e]=n,n.count++;const r=vn("style",s.dom);en(r,{rel:"stylesheet",type:"text/css",id:n.id,"data-mce-key":e}),r.dom.innerHTML=t,a(r)},loadAll:e=>Promise.allSettled(V(e,(e=>d(e).then(N(e))))).then((e=>{const t=K(e,(e=>"fulfilled"===e.status));return t.fail.length>0?Promise.reject(V(t.fail,(e=>e.reason))):V(t.pass,(e=>e.value))})),unload:c,unloadRawCss:e=>{xe(o,e).each((t=>{0==--t.count&&(delete o[e],i(t.id))}))},unloadAll:e=>{q(e,(e=>{c(e)}))},_setReferrerPolicy:e=>{t.referrerPolicy=e},_setContentCssCors:e=>{t.contentCssCors=e}}},rs=(()=>{const e=new WeakMap;return{forElement:(t,n)=>{const o=$n(t).dom;return I.from(e.get(o)).getOrThunk((()=>{const t=os(o,n);return e.set(o,t),t}))}}})(),ss=(e,t)=>C(e)&&(Or(t,e)||t.isInline(e.nodeName.toLowerCase())),as=e=>(e=>"span"===e.nodeName.toLowerCase())(e)&&"bookmark"===e.getAttribute("data-mce-type"),is=(e,t,n,o)=>{var r;const s=o||t;if(Jo(t)&&as(t))return t;const a=t.childNodes;for(let t=a.length-1;t>=0;t--)is(e,a[t],n,s);if(Jo(t)){const e=t.childNodes;1===e.length&&as(e[0])&&(null===(r=t.parentNode)||void 0===r||r.insertBefore(e[0],t))}return(e=>fr(e)||mr(e))(t)||Or(n,t)||(e=>!!Jo(e)&&e.childNodes.length>0)(t)||((e,t,n)=>lr(e)&&e.data.length>0&&((e,t,n)=>{const o=new $o(e,t).prev(!1),r=new $o(e,t).next(!1),s=v(o)||ss(o,n),a=v(r)||ss(r,n);return s&&a})(e,t,n))(t,s,n)||e.remove(t),t},ls=Dt.makeMap,ds=/[&<>\"\u0060\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,cs=/[<>&\u007E-\uD7FF\uE000-\uFFEF]|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,us=/[<>&\"\']/g,ms=/&#([a-z0-9]+);?|&([a-z0-9]+);/gi,fs={128:"\u20ac",130:"\u201a",131:"\u0192",132:"\u201e",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02c6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017d",145:"\u2018",146:"\u2019",147:"\u201c",148:"\u201d",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02dc",153:"\u2122",154:"\u0161",155:"\u203a",156:"\u0153",158:"\u017e",159:"\u0178"},gs={'"':"&quot;","'":"&#39;","<":"&lt;",">":"&gt;","&":"&amp;","`":"&#96;"},ps={"&lt;":"<","&gt;":">","&amp;":"&","&quot;":'"',"&apos;":"'"},hs=(e,t)=>{const n={};if(e){const o=e.split(",");t=t||10;for(let e=0;e<o.length;e+=2){const r=String.fromCharCode(parseInt(o[e],t));if(!gs[r]){const t="&"+o[e+1]+";";n[r]=t,n[t]=r}}return n}},bs=hs("50,nbsp,51,iexcl,52,cent,53,pound,54,curren,55,yen,56,brvbar,57,sect,58,uml,59,copy,5a,ordf,5b,laquo,5c,not,5d,shy,5e,reg,5f,macr,5g,deg,5h,plusmn,5i,sup2,5j,sup3,5k,acute,5l,micro,5m,para,5n,middot,5o,cedil,5p,sup1,5q,ordm,5r,raquo,5s,frac14,5t,frac12,5u,frac34,5v,iquest,60,Agrave,61,Aacute,62,Acirc,63,Atilde,64,Auml,65,Aring,66,AElig,67,Ccedil,68,Egrave,69,Eacute,6a,Ecirc,6b,Euml,6c,Igrave,6d,Iacute,6e,Icirc,6f,Iuml,6g,ETH,6h,Ntilde,6i,Ograve,6j,Oacute,6k,Ocirc,6l,Otilde,6m,Ouml,6n,times,6o,Oslash,6p,Ugrave,6q,Uacute,6r,Ucirc,6s,Uuml,6t,Yacute,6u,THORN,6v,szlig,70,agrave,71,aacute,72,acirc,73,atilde,74,auml,75,aring,76,aelig,77,ccedil,78,egrave,79,eacute,7a,ecirc,7b,euml,7c,igrave,7d,iacute,7e,icirc,7f,iuml,7g,eth,7h,ntilde,7i,ograve,7j,oacute,7k,ocirc,7l,otilde,7m,ouml,7n,divide,7o,oslash,7p,ugrave,7q,uacute,7r,ucirc,7s,uuml,7t,yacute,7u,thorn,7v,yuml,ci,fnof,sh,Alpha,si,Beta,sj,Gamma,sk,Delta,sl,Epsilon,sm,Zeta,sn,Eta,so,Theta,sp,Iota,sq,Kappa,sr,Lambda,ss,Mu,st,Nu,su,Xi,sv,Omicron,t0,Pi,t1,Rho,t3,Sigma,t4,Tau,t5,Upsilon,t6,Phi,t7,Chi,t8,Psi,t9,Omega,th,alpha,ti,beta,tj,gamma,tk,delta,tl,epsilon,tm,zeta,tn,eta,to,theta,tp,iota,tq,kappa,tr,lambda,ts,mu,tt,nu,tu,xi,tv,omicron,u0,pi,u1,rho,u2,sigmaf,u3,sigma,u4,tau,u5,upsilon,u6,phi,u7,chi,u8,psi,u9,omega,uh,thetasym,ui,upsih,um,piv,812,bull,816,hellip,81i,prime,81j,Prime,81u,oline,824,frasl,88o,weierp,88h,image,88s,real,892,trade,89l,alefsym,8cg,larr,8ch,uarr,8ci,rarr,8cj,darr,8ck,harr,8dl,crarr,8eg,lArr,8eh,uArr,8ei,rArr,8ej,dArr,8ek,hArr,8g0,forall,8g2,part,8g3,exist,8g5,empty,8g7,nabla,8g8,isin,8g9,notin,8gb,ni,8gf,prod,8gh,sum,8gi,minus,8gn,lowast,8gq,radic,8gt,prop,8gu,infin,8h0,ang,8h7,and,8h8,or,8h9,cap,8ha,cup,8hb,int,8hk,there4,8hs,sim,8i5,cong,8i8,asymp,8j0,ne,8j1,equiv,8j4,le,8j5,ge,8k2,sub,8k3,sup,8k4,nsub,8k6,sube,8k7,supe,8kl,oplus,8kn,otimes,8l5,perp,8m5,sdot,8o8,lceil,8o9,rceil,8oa,lfloor,8ob,rfloor,8p9,lang,8pa,rang,9ea,loz,9j0,spades,9j3,clubs,9j5,hearts,9j6,diams,ai,OElig,aj,oelig,b0,Scaron,b1,scaron,bo,Yuml,m6,circ,ms,tilde,802,ensp,803,emsp,809,thinsp,80c,zwnj,80d,zwj,80e,lrm,80f,rlm,80j,ndash,80k,mdash,80o,lsquo,80p,rsquo,80q,sbquo,80s,ldquo,80t,rdquo,80u,bdquo,810,dagger,811,Dagger,81g,permil,81p,lsaquo,81q,rsaquo,85c,euro",32),vs=(e,t)=>e.replace(t?ds:cs,(e=>gs[e]||e)),ys=(e,t)=>e.replace(t?ds:cs,(e=>e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":gs[e]||"&#"+e.charCodeAt(0)+";")),Cs=(e,t,n)=>{const o=n||bs;return e.replace(t?ds:cs,(e=>gs[e]||o[e]||e))},ws={encodeRaw:vs,encodeAllRaw:e=>(""+e).replace(us,(e=>gs[e]||e)),encodeNumeric:ys,encodeNamed:Cs,getEncodeFunc:(e,t)=>{const n=hs(t)||bs,o=ls(e.replace(/\+/g,","));return o.named&&o.numeric?(e,t)=>e.replace(t?ds:cs,(e=>void 0!==gs[e]?gs[e]:void 0!==n[e]?n[e]:e.length>1?"&#"+(1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536)+";":"&#"+e.charCodeAt(0)+";")):o.named?t?(e,t)=>Cs(e,t,n):Cs:o.numeric?ys:vs},decode:e=>e.replace(ms,((e,t)=>t?(t="x"===t.charAt(0).toLowerCase()?parseInt(t.substr(1),16):parseInt(t,10))>65535?(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t))):fs[t]||String.fromCharCode(t):ps[e]||bs[e]||(e=>{const t=vn("div").dom;return t.innerHTML=e,t.textContent||t.innerText||e})(e)))},Es=(e,t)=>(e=Dt.trim(e))?e.split(t||" "):[],xs=e=>new RegExp("^"+e.replace(/([?+*])/g,".$1")+"$"),_s=e=>Object.freeze(["id","accesskey","class","dir","lang","style","tabindex","title","role",..."html4"!==e?["contenteditable","contextmenu","draggable","dropzone","hidden","spellcheck","translate","itemprop","itemscope","itemtype"]:[],..."html5-strict"!==e?["xml:lang"]:[]]),Ss=e=>{let t,n;t="address blockquote div dl fieldset form h1 h2 h3 h4 h5 h6 hr menu ol p pre table ul",n="a abbr b bdo br button cite code del dfn em embed i iframe img input ins kbd label map noscript object q s samp script select small span strong sub sup textarea u var #text #comment","html4"!==e&&(t+=" article aside details dialog figure main header footer hgroup section nav a ins del canvas map",n+=" audio canvas command data datalist mark meter output picture progress time wbr video ruby bdi keygen svg"),"html5-strict"!==e&&(n=[n,"acronym applet basefont big font strike tt"].join(" "),t=[t,"center dir isindex noframes"].join(" "));const o=[t,n].join(" ");return{blockContent:t,phrasingContent:n,flowContent:o}},ks=e=>{const{blockContent:t,phrasingContent:n,flowContent:o}=Ss(e),r=e=>Object.freeze(e.split(" "));return Object.freeze({blockContent:r(t),phrasingContent:r(n),flowContent:r(o)})},Ns={html4:Le((()=>ks("html4"))),html5:Le((()=>ks("html5"))),"html5-strict":Le((()=>ks("html5-strict")))},Rs=(e,t)=>{const{blockContent:n,phrasingContent:o,flowContent:r}=Ns[e]();return"blocks"===t?I.some(n):"phrasing"===t?I.some(o):"flow"===t?I.some(r):I.none()},As=e=>I.from(/^(@?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)$/.exec(e)).map((e=>({preset:"@"===e[1],name:e[2]}))),Ts={},Os=Dt.makeMap,Bs=Dt.each,Ps=Dt.extend,Ds=Dt.explode,Ls=(e,t={})=>{const n=Os(e," ",Os(e.toUpperCase()," "));return Ps(n,t)},Ms=e=>Ls("td th li dt dd figcaption caption details summary",e.getTextBlockElements()),Is=(e,t)=>{if(e){const n={};return m(e)&&(e={"*":e}),Bs(e,((e,o)=>{n[o]=n[o.toUpperCase()]="map"===t?Os(e,/[, ]/):Ds(e,/[, ]/)})),n}},Fs=(e={})=>{var t;const n={},o={};let r=[];const s={},a={},i=(t,n,o)=>{const r=e[t];if(r)return Os(r,/[, ]/,Os(r.toUpperCase(),/[, ]/));{let e=Ts[t];return e||(e=Ls(n,o),Ts[t]=e),e}},l=null!==(t=e.schema)&&void 0!==t?t:"html5",d=(e=>{const t=_s(e),{phrasingContent:n,flowContent:o}=Ss(e),r={},s=(e,t,n)=>{r[e]={attributes:se(t,N({})),attributesOrder:t,children:se(n,N({}))}},a=(e,n="",o="")=>{const r=Es(o),a=Es(e);let i=a.length;const l=[...t,...Es(n)];for(;i--;)s(a[i],l.slice(),r)},i=(e,t)=>{const n=Es(e),o=Es(t);let s=n.length;for(;s--;){const e=r[n[s]];for(let t=0,n=o.length;t<n;t++)e.attributes[o[t]]={},e.attributesOrder.push(o[t])}};return"html5-strict"!==e&&(q(Es("acronym applet basefont big font strike tt"),(e=>{a(e,"",n)})),q(Es("center dir isindex noframes"),(e=>{a(e,"",o)}))),a("html","manifest","head body"),a("head","","base command link meta noscript script style title"),a("title hr noscript br"),a("base","href target"),a("link","href rel media hreflang type sizes hreflang"),a("meta","name http-equiv content charset"),a("style","media type scoped"),a("script","src async defer type charset"),a("body","onafterprint onbeforeprint onbeforeunload onblur onerror onfocus onhashchange onload onmessage onoffline ononline onpagehide onpageshow onpopstate onresize onscroll onstorage onunload",o),a("dd div","",o),a("address dt caption","","html4"===e?n:o),a("h1 h2 h3 h4 h5 h6 pre p abbr code var samp kbd sub sup i b u bdo span legend em strong small s cite dfn","",n),a("blockquote","cite",o),a("ol","reversed start type","li"),a("ul","","li"),a("li","value",o),a("dl","","dt dd"),a("a","href target rel media hreflang type","html4"===e?n:o),a("q","cite",n),a("ins del","cite datetime",o),a("img","src sizes srcset alt usemap ismap width height"),a("iframe","src name width height",o),a("embed","src type width height"),a("object","data type typemustmatch name usemap form width height",[o,"param"].join(" ")),a("param","name value"),a("map","name",[o,"area"].join(" ")),a("area","alt coords shape href target rel media hreflang type"),a("table","border","caption colgroup thead tfoot tbody tr"+("html4"===e?" col":"")),a("colgroup","span","col"),a("col","span"),a("tbody thead tfoot","","tr"),a("tr","","td th"),a("td","colspan rowspan headers",o),a("th","colspan rowspan headers scope abbr",o),a("form","accept-charset action autocomplete enctype method name novalidate target",o),a("fieldset","disabled form name",[o,"legend"].join(" ")),a("label","form for",n),a("input","accept alt autocomplete checked dirname disabled form formaction formenctype formmethod formnovalidate formtarget height list max maxlength min multiple name pattern readonly required size src step type value width"),a("button","disabled form formaction formenctype formmethod formnovalidate formtarget name type value","html4"===e?o:n),a("select","disabled form multiple name required size","option optgroup"),a("optgroup","disabled label","option"),a("option","disabled label selected value"),a("textarea","cols dirname disabled form maxlength name readonly required rows wrap"),a("menu","type label",[o,"li"].join(" ")),a("noscript","",o),"html4"!==e&&(a("wbr"),a("ruby","",[n,"rt rp"].join(" ")),a("figcaption","",o),a("mark rt rp bdi","",n),a("summary","",[n,"h1 h2 h3 h4 h5 h6"].join(" ")),a("canvas","width height",o),a("data","value",n),a("video","src crossorigin poster preload autoplay mediagroup loop muted controls width height buffered",[o,"track source"].join(" ")),a("audio","src crossorigin preload autoplay mediagroup loop muted controls buffered volume",[o,"track source"].join(" ")),a("picture","","img source"),a("source","src srcset type media sizes"),a("track","kind src srclang label default"),a("datalist","",[n,"option"].join(" ")),a("article section nav aside main header footer","",o),a("hgroup","","h1 h2 h3 h4 h5 h6"),a("figure","",[o,"figcaption"].join(" ")),a("time","datetime",n),a("dialog","open",o),a("command","type label icon disabled checked radiogroup command"),a("output","for form name",n),a("progress","value max",n),a("meter","value min max low high optimum",n),a("details","open",[o,"summary"].join(" ")),a("keygen","autofocus challenge disabled form keytype name"),s("svg","id tabindex lang xml:space class style x y width height viewBox preserveAspectRatio zoomAndPan transform".split(" "),[])),"html5-strict"!==e&&(i("script","language xml:space"),i("style","xml:space"),i("object","declare classid code codebase codetype archive standby align border hspace vspace"),i("embed","align name hspace vspace"),i("param","valuetype type"),i("a","charset name rev shape coords"),i("br","clear"),i("applet","codebase archive code object alt name width height align hspace vspace"),i("img","name longdesc align border hspace vspace"),i("iframe","longdesc frameborder marginwidth marginheight scrolling align"),i("font basefont","size color face"),i("input","usemap align"),i("select"),i("textarea"),i("h1 h2 h3 h4 h5 h6 div p legend caption","align"),i("ul","type compact"),i("li","type"),i("ol dl menu dir","compact"),i("pre","width xml:space"),i("hr","align noshade size width"),i("isindex","prompt"),i("table","summary width frame rules cellspacing cellpadding align bgcolor"),i("col","width align char charoff valign"),i("colgroup","width align char charoff valign"),i("thead","align char charoff valign"),i("tr","align char charoff valign bgcolor"),i("th","axis align char charoff valign nowrap bgcolor width height"),i("form","accept"),i("td","abbr axis scope align char charoff valign nowrap bgcolor width height"),i("tfoot","align char charoff valign"),i("tbody","align char charoff valign"),i("area","nohref"),i("body","background bgcolor text link vlink alink")),"html4"!==e&&(i("input button select textarea","autofocus"),i("input textarea","placeholder"),i("a","download"),i("link script img","crossorigin"),i("img","loading"),i("iframe","sandbox seamless allow allowfullscreen loading referrerpolicy")),"html4"!==e&&q([r.video,r.audio],(e=>{delete e.children.audio,delete e.children.video})),q(Es("a form meter progress dfn"),(e=>{r[e]&&delete r[e].children[e]})),delete r.caption.children.table,delete r.script,r})(l);!1===e.verify_html&&(e.valid_elements="*[*]");const c=Is(e.valid_styles),u=Is(e.invalid_styles,"map"),g=Is(e.valid_classes,"map"),h=i("whitespace_elements","pre script noscript style textarea video audio iframe object code"),v=i("self_closing_elements","colgroup dd dt li option p td tfoot th thead tr"),y=i("void_elements","area base basefont br col frame hr img input isindex link meta param embed source wbr track"),C=i("boolean_attributes","checked compact declare defer disabled ismap multiple nohref noresize noshade nowrap readonly selected autoplay loop controls allowfullscreen"),w="td th iframe video audio object script code",E=i("non_empty_elements",w+" pre svg textarea summary",y),x=i("move_caret_before_on_enter_elements",w+" table",y),_="h1 h2 h3 h4 h5 h6",S=i("text_block_elements",_+" p div address pre form blockquote center dir fieldset header footer article section hgroup aside main nav figure"),k=i("block_elements","hr table tbody thead tfoot th tr td li ol ul caption dl dt dd noscript menu isindex option datalist select optgroup figcaption details summary html body multicol listing",S),R=i("text_inline_elements","span strong b em i font s strike u var cite dfn code mark q sup sub samp"),A=i("transparent_elements","a ins del canvas map"),T=i("wrap_block_elements","pre "+_);Bs("script noscript iframe noframes noembed title style textarea xmp plaintext".split(" "),(e=>{a[e]=new RegExp("</"+e+"[^>]*>","gi")}));const O=e=>{const t=I.from(n["@"]),o=/[*?+]/;q(((e,t)=>{const n=/^([#+\-])?([^\[!\/]+)(?:\/([^\[!]+))?(?:(!?)\[([^\]]+)])?$/;return te(Es(t,","),(t=>{const o=n.exec(t);if(o){const t=o[1],n=o[2],r=o[3],s=o[4],a=o[5],i={attributes:{},attributesOrder:[]};if(e.each((e=>((e,t)=>{pe(e.attributes,((e,n)=>{t.attributes[n]=e})),t.attributesOrder.push(...e.attributesOrder)})(e,i))),"#"===t?i.paddEmpty=!0:"-"===t&&(i.removeEmpty=!0),"!"===s&&(i.removeEmptyAttrs=!0),a&&((e,t)=>{const n=/^([!\-])?(\w+[\\:]:\w+|[^=~<]+)?(?:([=~<])(.*))?$/,o=/[*?+]/,{attributes:r,attributesOrder:s}=t;q(Es(e,"|"),(e=>{const a=n.exec(e);if(a){const e={},n=a[1],i=a[2].replace(/[\\:]:/g,":"),l=a[3],d=a[4];if("!"===n&&(t.attributesRequired=t.attributesRequired||[],t.attributesRequired.push(i),e.required=!0),"-"===n)return delete r[i],void s.splice(Dt.inArray(s,i),1);if(l&&("="===l?(t.attributesDefault=t.attributesDefault||[],t.attributesDefault.push({name:i,value:d}),e.defaultValue=d):"~"===l?(t.attributesForced=t.attributesForced||[],t.attributesForced.push({name:i,value:d}),e.forcedValue=d):"<"===l&&(e.validValues=Dt.makeMap(d,"?"))),o.test(i)){const n=e;t.attributePatterns=t.attributePatterns||[],n.pattern=xs(i),t.attributePatterns.push(n)}else r[i]||s.push(i),r[i]=e}}))})(a,i),r&&(i.outputName=n),"@"===n){if(!e.isNone())return[];e=I.some(i)}return[r?{name:n,element:i,aliasName:r}:{name:n,element:i}]}return[]}))})(t,null!=e?e:""),(({name:e,element:t,aliasName:s})=>{if(s&&(n[s]=t),o.test(e)){const n=t;n.pattern=xs(e),r.push(n)}else n[e]=t}))},B=e=>{r=[],q(fe(n),(e=>{delete n[e]})),O(e)},P=(e,t)=>{var r,a;delete Ts.text_block_elements,delete Ts.block_elements;const i=!!t.extends&&!oe(t.extends),d=t.extends;if(o[e]=d?o[d]:{},s[e]=null!=d?d:e,E[e.toUpperCase()]={},E[e]={},i||(k[e.toUpperCase()]={},k[e]={}),d&&!n[e]&&n[d]){const t=(e=>{const t=e=>p(e)?V(e,t):(e=>f(e)&&e.source&&"[object RegExp]"===Object.prototype.toString.call(e))(e)?new RegExp(e.source,e.flags):f(e)?he(e,t):e;return t(e)})(n[d]);delete t.removeEmptyAttrs,delete t.removeEmpty,n[e]=t}else n[e]={attributesOrder:[],attributes:{}};if(p(t.attributes)){const o=e=>{s.attributesOrder.push(e),s.attributes[e]={}},s=null!==(r=n[e])&&void 0!==r?r:{};delete s.attributesDefault,delete s.attributesForced,delete s.attributePatterns,delete s.attributesRequired,s.attributesOrder=[],s.attributes={},q(t.attributes,(e=>{const t=_s(l);As(e).each((({preset:e,name:n})=>{e?"global"===n&&q(t,o):o(n)}))})),n[e]=s}if(b(t.padEmpty)){const o=null!==(a=n[e])&&void 0!==a?a:{};o.paddEmpty=t.padEmpty,n[e]=o}if(p(t.children)){const n={},r=e=>{n[e]={}},s=e=>{Rs(l,e).each((e=>{q(e,r)}))};q(t.children,(e=>{As(e).each((({preset:e,name:t})=>{e?s(t):r(t)}))})),o[e]=n}d&&pe(o,((t,n)=>{t[d]&&(o[n]=t=Ps({},o[n]),t[e]=t[d])}))},D=e=>{f(e)?pe(e,((e,t)=>P(t,e))):m(e)&&(e=>{q((e=>{const t=/^(~)?(.+)$/;return te(Es(e,","),(e=>{const n=t.exec(e);return n?[{cloneName:"~"===n[1]?"span":"div",name:n[2]}]:[]}))})(null!=e?e:""),(({name:e,cloneName:t})=>{P(e,{extends:t})}))})(e)},L=e=>{q((e=>{const t=/^([+\-]?)([A-Za-z0-9_\-.\u00b7\u00c0-\u00d6\u00d8-\u00f6\u00f8-\u037d\u037f-\u1fff\u200c-\u200d\u203f-\u2040\u2070-\u218f\u2c00-\u2fef\u3001-\ud7ff\uf900-\ufdcf\ufdf0-\ufffd]+)\[([^\]]+)]$/;return te(Es(e,","),(e=>{const n=t.exec(e);if(n){const e=n[1],t=e?(e=>"-"===e?"remove":"add")(e):"replace";return[{operation:t,name:n[2],validChildren:te(Es(n[3],"|"),(e=>As(e).toArray()))}]}return[]}))})(null!=e?e:""),(({operation:e,name:t,validChildren:n})=>{const r="replace"===e?{"#comment":{}}:o[t],s=t=>{"remove"===e?delete r[t]:r[t]={}};q(n,(({preset:e,name:t})=>{e?(e=>{Rs(l,e).each((e=>{q(e,s)}))})(t):s(t)})),o[t]=r}))},M=e=>{const t=n[e];if(t)return t;let o=r.length;for(;o--;){const t=r[o];if(t.pattern.test(e))return t}},F=N(c),U=N(u),z=N(g),j=N(C),H=N(k),$=N(S),W=N(R),K=N(Object.seal(y)),Y=N(v),G=N(E),X=N(x),Z=N(h),Q=N(A),J=N(T),ee=N(Object.seal(a)),ne=(e,t)=>{const n=M(e);if(n){if(!t)return!0;{if(n.attributes[t])return!0;const e=n.attributePatterns;if(e){let n=e.length;for(;n--;)if(e[n].pattern.test(t))return!0}}}return!1},oe=e=>_e(H(),e),re=e=>!$e(e,"#")&&ne(e)&&!oe(e),ae=N(s);return e.valid_elements?(B(e.valid_elements),Bs(d,((e,t)=>{o[t]=e.children}))):(Bs(d,((e,t)=>{n[t]={attributes:e.attributes,attributesOrder:e.attributesOrder},o[t]=e.children})),Bs(Es("strong/b em/i"),(e=>{const t=Es(e,"/");n[t[1]].outputName=t[0]})),Bs(R,((t,o)=>{n[o]&&(e.padd_empty_block_inline_children&&(n[o].paddInEmptyBlock=!0),n[o].removeEmpty=!0)})),Bs(Es("ol ul blockquote a table tbody"),(e=>{n[e]&&(n[e].removeEmpty=!0)})),Bs(Es("p h1 h2 h3 h4 h5 h6 th td pre div address caption li summary"),(e=>{n[e]&&(n[e].paddEmpty=!0)})),Bs(Es("span"),(e=>{n[e].removeEmptyAttrs=!0}))),delete n.svg,D(e.custom_elements),L(e.valid_children),O(e.extended_valid_elements),L("+ol[ul|ol],+ul[ul|ol]"),Bs({dd:"dl",dt:"dl",li:"ul ol",td:"tr",th:"tr",tr:"tbody thead tfoot",tbody:"table",thead:"table",tfoot:"table",legend:"fieldset",area:"map",param:"video audio object"},((e,t)=>{n[t]&&(n[t].parentsRequired=Es(e))})),e.invalid_elements&&Bs(Ds(e.invalid_elements),(e=>{n[e]&&delete n[e]})),M("span")||O("span[!data-mce-type|*]"),{type:l,children:o,elements:n,getValidStyles:F,getValidClasses:z,getBlockElements:H,getInvalidStyles:U,getVoidElements:K,getTextBlockElements:$,getTextInlineElements:W,getBoolAttrs:j,getElementRule:M,getSelfClosingElements:Y,getNonEmptyElements:G,getMoveCaretBeforeOnEnterElements:X,getWhitespaceElements:Z,getTransparentElements:Q,getSpecialElements:ee,isValidChild:(e,t)=>{const n=o[e.toLowerCase()];return!(!n||!n[t.toLowerCase()])},isValid:ne,isBlock:oe,isInline:re,isWrapper:e=>_e(J(),e)||re(e),getCustomElements:ae,addValidElements:O,setValidElements:B,addCustomElements:D,addValidChildren:L}},Us=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},zs=e=>(e=>{return{value:(t=e,je(t,"#").toUpperCase())};var t})(Us(e.red)+Us(e.green)+Us(e.blue)),js=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,Hs=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,$s=(e,t,n,o)=>((e,t,n,o)=>({red:e,green:t,blue:n,alpha:o}))(parseInt(e,10),parseInt(t,10),parseInt(n,10),parseFloat(o)),Vs=e=>{const t=js.exec(e);if(null!==t)return I.some($s(t[1],t[2],t[3],"1"));const n=Hs.exec(e);return null!==n?I.some($s(n[1],n[2],n[3],n[4])):I.none()},qs=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,Ws=e=>Vs(e).map(zs).map((e=>"#"+e.value)).getOr(e),Ks=(e={},t)=>{const n=/(?:url(?:(?:\(\s*\"([^\"]+)\"\s*\))|(?:\(\s*\'([^\']+)\'\s*\))|(?:\(\s*([^)\s]+)\s*\))))|(?:\'([^\']+)\')|(?:\"([^\"]+)\")/gi,o=/\s*([^:]+):\s*([^;]+);?/g,r=/\s+$/,s={};let a,i;const l=Vo;t&&(a=t.getValidStyles(),i=t.getInvalidStyles());const d="\\\" \\' \\; \\: ; : \ufeff".split(" ");for(let e=0;e<d.length;e++)s[d[e]]=l+e,s[l+e]=d[e];const c={parse:t=>{const a={};let i=!1;const d=e.url_converter,u=e.url_converter_scope||c,m=(e,t,n)=>{const o=a[e+"-top"+t];if(!o)return;const r=a[e+"-right"+t];if(!r)return;const s=a[e+"-bottom"+t];if(!s)return;const i=a[e+"-left"+t];if(!i)return;const l=[o,r,s,i];let d=l.length-1;for(;d--&&l[d]===l[d+1];);d>-1&&n||(a[e+t]=-1===d?l[0]:l.join(" "),delete a[e+"-top"+t],delete a[e+"-right"+t],delete a[e+"-bottom"+t],delete a[e+"-left"+t])},f=e=>{const t=a[e];if(!t)return;const n=t.indexOf(",")>-1?[t]:t.split(" ");let o=n.length;for(;o--;)if(n[o]!==n[0])return!1;return a[e]=n[0],!0},g=e=>(i=!0,s[e]),p=(e,t)=>(i&&(e=e.replace(/\uFEFF[0-9]/g,(e=>s[e]))),t||(e=e.replace(/\\([\'\";:])/g,"$1")),e),h=e=>String.fromCharCode(parseInt(e.slice(1),16)),b=e=>e.replace(/\\[0-9a-f]+/gi,h),v=(t,n,o,r,s,a)=>{if(s=s||a)return"'"+(s=p(s)).replace(/\'/g,"\\'")+"'";if(n=p(n||o||r||""),!e.allow_script_urls){const t=n.replace(/[\s\r\n]+/g,"");if(/(java|vb)script:/i.test(t))return"";if(!e.allow_svg_data_urls&&/^data:image\/svg/i.test(t))return""}return d&&(n=d.call(u,n,"style")),"url('"+n.replace(/\'/g,"\\'")+"')"};if(t){let s;for(t=(t=t.replace(/[\u0000-\u001F]/g,"")).replace(/\\[\"\';:\uFEFF]/g,g).replace(/\"[^\"]+\"|\'[^\']+\'/g,(e=>e.replace(/[;:]/g,g)));s=o.exec(t);){o.lastIndex=s.index+s[0].length;let t=s[1].replace(r,"").toLowerCase(),d=s[2].replace(r,"");if(t&&d){if(t=b(t),d=b(d),-1!==t.indexOf(l)||-1!==t.indexOf('"'))continue;if(!e.allow_script_urls&&("behavior"===t||/expression\s*\(|\/\*|\*\//.test(d)))continue;"font-weight"===t&&"700"===d?d="bold":"color"!==t&&"background-color"!==t||(d=d.toLowerCase()),"rgb"==(E=d,js.test(E)?"rgb":Hs.test(E)?"rgba":"other")&&Vs(d).each((e=>{d=Ws(qs(e)).toLowerCase()})),d=d.replace(n,v),a[t]=i?p(d,!0):d}}m("border","",!0),m("border","-width"),m("border","-color"),m("border","-style"),m("padding",""),m("margin",""),C="border-style",w="border-color",f(y="border-width")&&f(C)&&f(w)&&(a.border=a[y]+" "+a[C]+" "+a[w],delete a[y],delete a[C],delete a[w]),"medium none"===a.border&&delete a.border,"none"===a["border-image"]&&delete a["border-image"]}var y,C,w,E;return a},serialize:(e,t)=>{let n="";const o=(t,o)=>{const r=o[t];if(r)for(let t=0,o=r.length;t<o;t++){const o=r[t],s=e[o];s&&(n+=(n.length>0?" ":"")+o+": "+s+";")}};return t&&a?(o("*",a),o(t,a)):pe(e,((e,o)=>{e&&((e,t)=>{if(!i||!t)return!0;let n=i["*"];return!(n&&n[e]||(n=i[t],n&&n[e]))})(o,t)&&(n+=(n.length>0?" ":"")+o+": "+e+";")})),n}};return c},Ys={keyLocation:!0,layerX:!0,layerY:!0,returnValue:!0,webkitMovementX:!0,webkitMovementY:!0,keyIdentifier:!0,mozPressure:!0},Gs=(e,t)=>{const n=null!=t?t:{};for(const t in e)_e(Ys,t)||(n[t]=e[t]);return C(e.composedPath)&&(n.composedPath=()=>e.composedPath()),C(e.getModifierState)&&(n.getModifierState=t=>e.getModifierState(t)),C(e.getTargetRanges)&&(n.getTargetRanges=()=>e.getTargetRanges()),n},Xs=(e,t,n,o)=>{var r;const s=Gs(t,o);return s.type=e,y(s.target)&&(s.target=null!==(r=s.srcElement)&&void 0!==r?r:n),(e=>y(e.preventDefault)||(e=>e instanceof Event||w(e.initEvent))(e))(t)&&(s.preventDefault=()=>{s.defaultPrevented=!0,s.isDefaultPrevented=M,w(t.preventDefault)&&t.preventDefault()},s.stopPropagation=()=>{s.cancelBubble=!0,s.isPropagationStopped=M,w(t.stopPropagation)&&t.stopPropagation()},s.stopImmediatePropagation=()=>{s.isImmediatePropagationStopped=M,s.stopPropagation()},(e=>e.isDefaultPrevented===M||e.isDefaultPrevented===L)(s)||(s.isDefaultPrevented=!0===s.defaultPrevented?M:L,s.isPropagationStopped=!0===s.cancelBubble?M:L,s.isImmediatePropagationStopped=L)),s},Zs=/^(?:mouse|contextmenu)|click/,Qs=(e,t,n,o)=>{e.addEventListener(t,n,o||!1)},Js=(e,t,n,o)=>{e.removeEventListener(t,n,o||!1)},ea=(e,t)=>{const n=Xs(e.type,e,document,t);if((e=>C(e)&&Zs.test(e.type))(e)&&v(e.pageX)&&!v(e.clientX)){const t=n.target.ownerDocument||document,o=t.documentElement,r=t.body,s=n;s.pageX=e.clientX+(o&&o.scrollLeft||r&&r.scrollLeft||0)-(o&&o.clientLeft||r&&r.clientLeft||0),s.pageY=e.clientY+(o&&o.scrollTop||r&&r.scrollTop||0)-(o&&o.clientTop||r&&r.clientTop||0)}return n},ta=(e,t,n)=>{const o=e.document,r={type:"ready"};if(n.domLoaded)return void t(r);const s=()=>{Js(e,"DOMContentLoaded",s),Js(e,"load",s),n.domLoaded||(n.domLoaded=!0,t(r)),e=null};"complete"===o.readyState||"interactive"===o.readyState&&o.body?s():Qs(e,"DOMContentLoaded",s),n.domLoaded||Qs(e,"load",s)};class na{constructor(){this.domLoaded=!1,this.events={},this.count=1,this.expando="mce-data-"+(+new Date).toString(32),this.hasFocusIn="onfocusin"in document.documentElement,this.count=1}bind(e,t,n,o){const r=this;let s;const a=window,i=e=>{r.executeHandlers(ea(e||a.event),l)};if(!e||lr(e)||ur(e))return n;let l;e[r.expando]?l=e[r.expando]:(l=r.count++,e[r.expando]=l,r.events[l]={}),o=o||e;const d=t.split(" ");let c=d.length;for(;c--;){let t=d[c],u=i,m=!1,f=!1;"DOMContentLoaded"===t&&(t="ready"),r.domLoaded&&"ready"===t&&"complete"===e.readyState?n.call(o,ea({type:t})):(r.hasFocusIn||"focusin"!==t&&"focusout"!==t||(m=!0,f="focusin"===t?"focus":"blur",u=e=>{const t=ea(e||a.event);t.type="focus"===t.type?"focusin":"focusout",r.executeHandlers(t,l)}),s=r.events[l][t],s?"ready"===t&&r.domLoaded?n(ea({type:t})):s.push({func:n,scope:o}):(r.events[l][t]=s=[{func:n,scope:o}],s.fakeName=f,s.capture=m,s.nativeHandler=u,"ready"===t?ta(e,u,r):Qs(e,f||t,u,m)))}return e=s=null,n}unbind(e,t,n){if(!e||lr(e)||ur(e))return this;const o=e[this.expando];if(o){let r=this.events[o];if(t){const o=t.split(" ");let s=o.length;for(;s--;){const t=o[s],a=r[t];if(a){if(n){let e=a.length;for(;e--;)if(a[e].func===n){const n=a.nativeHandler,o=a.fakeName,s=a.capture,i=a.slice(0,e).concat(a.slice(e+1));i.nativeHandler=n,i.fakeName=o,i.capture=s,r[t]=i}}n&&0!==a.length||(delete r[t],Js(e,a.fakeName||t,a.nativeHandler,a.capture))}}}else pe(r,((t,n)=>{Js(e,t.fakeName||n,t.nativeHandler,t.capture)})),r={};for(const e in r)if(_e(r,e))return this;delete this.events[o];try{delete e[this.expando]}catch(t){e[this.expando]=null}}return this}fire(e,t,n){return this.dispatch(e,t,n)}dispatch(e,t,n){if(!e||lr(e)||ur(e))return this;const o=ea({type:t,target:e},n);do{const t=e[this.expando];t&&this.executeHandlers(o,t),e=e.parentNode||e.ownerDocument||e.defaultView||e.parentWindow}while(e&&!o.isPropagationStopped());return this}clean(e){if(!e||lr(e)||ur(e))return this;if(e[this.expando]&&this.unbind(e),e.getElementsByTagName||(e=e.document),e&&e.getElementsByTagName){this.unbind(e);const t=e.getElementsByTagName("*");let n=t.length;for(;n--;)(e=t[n])[this.expando]&&this.unbind(e)}return this}destroy(){this.events={}}cancel(e){return e&&(e.preventDefault(),e.stopImmediatePropagation()),!1}executeHandlers(e,t){const n=this.events[t],o=n&&n[e.type];if(o)for(let t=0,n=o.length;t<n;t++){const n=o[t];if(n&&!1===n.func.call(n.scope,e)&&e.preventDefault(),e.isImmediatePropagationStopped())return}}}na.Event=new na;const oa=Dt.each,ra=Dt.grep,sa="data-mce-style",aa=Dt.makeMap("fill-opacity font-weight line-height opacity orphans widows z-index zoom"," "),ia=(e,t,n)=>{y(n)||""===n?rn(e,t):Jt(e,t,n)},la=e=>e.replace(/[A-Z]/g,(e=>"-"+e.toLowerCase())),da=(e,t)=>{let n=0;if(e)for(let o=e.nodeType,r=e.previousSibling;r;r=r.previousSibling){const e=r.nodeType;(!t||!lr(r)||e!==o&&r.data.length)&&(n++,o=e)}return n},ca=(e,t)=>{const n=tn(t,"style"),o=e.serialize(e.parse(n),$t(t));ia(t,sa,o)},ua=(e,t,n)=>{const o=la(t);y(n)||""===n?go(e,o):io(e,o,((e,t)=>E(e)?_e(aa,t)?e+"":e+"px":e)(n,o))},ma=(e,t={})=>{const n={},o=window,r={};let s=0;const a=rs.forElement(Cn(e),{contentCssCors:t.contentCssCors,referrerPolicy:t.referrerPolicy}),i=[],l=t.schema?t.schema:Fs({}),d=Ks({url_converter:t.url_converter,url_converter_scope:t.url_converter_scope},t.schema),c=t.ownEvents?new na:na.Event,u=l.getBlockElements(),f=t=>t&&e&&m(t)?e.getElementById(t):t,h=e=>{const t=f(e);return C(t)?Cn(t):null},b=(e,t,n="")=>{let o;const r=h(e);if(C(r)&&Kt(r)){const e=G[t];o=e&&e.get?e.get(r.dom,t):tn(r,t)}return C(o)?o:n},v=e=>{const t=f(e);return y(t)?[]:t.attributes},E=(e,n,o)=>{B(e,(e=>{if(Jo(e)){const r=Cn(e),s=""===o?null:o,a=tn(r,n),i=G[n];i&&i.set?i.set(r.dom,s,n):ia(r,n,s),a!==s&&t.onSetAttrib&&t.onSetAttrib({attrElm:r.dom,attrName:n,attrValue:s})}}))},x=()=>t.root_element||e.body,S=(t,n)=>((e,t,n)=>{let o=0,r=0;const s=e.ownerDocument;if(n=n||e,t){if(n===e&&t.getBoundingClientRect&&"static"===co(Cn(e),"position")){const n=t.getBoundingClientRect();return o=n.left+(s.documentElement.scrollLeft||e.scrollLeft)-s.documentElement.clientLeft,r=n.top+(s.documentElement.scrollTop||e.scrollTop)-s.documentElement.clientTop,{x:o,y:r}}let a=t;for(;a&&a!==n&&a.nodeType&&!ns(a,n);){const e=a;o+=e.offsetLeft||0,r+=e.offsetTop||0,a=e.offsetParent}for(a=t.parentNode;a&&a!==n&&a.nodeType&&!ns(a,n);)o-=a.scrollLeft||0,r-=a.scrollTop||0,a=a.parentNode;r+=(e=>es.isFirefox()&&"table"===$t(e)?ts(In(e)).filter((e=>"caption"===$t(e))).bind((e=>ts(Mn(e)).map((t=>{const n=t.dom.offsetTop,o=e.dom.offsetTop,r=e.dom.offsetHeight;return n<=o?-r:0})))).getOr(0):0)(Cn(t))}return{x:o,y:r}})(e.body,f(t),n),k=(e,t,n)=>{const o=f(e);var r;if(!y(o)&&(er(o)||Jo(r=o)&&"http://www.w3.org/2000/svg"===r.namespaceURI))return n?co(Cn(o),la(t)):("float"===(t=t.replace(/-(\D)/g,((e,t)=>t.toUpperCase())))&&(t="cssFloat"),o.style?o.style[t]:void 0)},R=e=>{const t=f(e);if(!t)return{w:0,h:0};let n=k(t,"width"),o=k(t,"height");return n&&-1!==n.indexOf("px")||(n="0"),o&&-1!==o.indexOf("px")||(o="0"),{w:parseInt(n,10)||t.offsetWidth||t.clientWidth,h:parseInt(o,10)||t.offsetHeight||t.clientHeight}},A=(e,t)=>{if(!e)return!1;const n=p(e)?e:[e];return $(n,(e=>xn(Cn(e),t)))},T=(e,t,n,o)=>{const r=[];let s=f(e);o=void 0===o;const a=n||("BODY"!==x().nodeName?x().parentNode:null);if(m(t))if("*"===t)t=Jo;else{const e=t;t=t=>A(t,e)}for(;s&&!(s===a||y(s.nodeType)||mr(s)||fr(s));){if(!t||t(s)){if(!o)return[s];r.push(s)}s=s.parentNode}return o?r:null},O=(e,t,n)=>{let o=t;if(e){m(t)&&(o=e=>A(e,t));for(let t=e[n];t;t=t[n])if(w(o)&&o(t))return t}return null},B=function(e,t,n){const o=null!=n?n:this;if(p(e)){const n=[];return oa(e,((e,r)=>{const s=f(e);s&&n.push(t.call(o,s,r))})),n}{const n=f(e);return!!n&&t.call(o,n)}},P=(e,t)=>{B(e,(e=>{pe(t,((t,n)=>{E(e,n,t)}))}))},D=(e,t)=>{B(e,(e=>{const n=Cn(e);ko(n,t)}))},L=(t,n,o,r,s)=>B(t,(t=>{const a=m(n)?e.createElement(n):n;return C(o)&&P(a,o),r&&(!m(r)&&r.nodeType?a.appendChild(r):m(r)&&D(a,r)),s?a:t.appendChild(a)})),M=(t,n,o)=>L(e.createElement(t),t,n,o,!0),I=ws.encodeAllRaw,F=(e,t)=>B(e,(e=>{const n=Cn(e);return t&&q(In(n),(e=>{Yt(e)&&0===e.dom.length?Eo(e):po(n,e)})),Eo(n),n.dom})),U=(e,t,n)=>{B(e,(e=>{if(Jo(e)){const o=Cn(e),r=t.split(" ");q(r,(e=>{C(n)?(n?mn:gn)(o,e):((e,t)=>{const n=ln(e)?e.dom.classList.toggle(t):((e,t)=>H(dn(e),t)?un(e,t):cn(e,t))(e,t);fn(e)})(o,e)}))}}))},z=(e,t,n)=>B(t,(o=>{var r;const s=p(t)?e.cloneNode(!0):e;return n&&oa(ra(o.childNodes),(e=>{s.appendChild(e)})),null===(r=o.parentNode)||void 0===r||r.replaceChild(s,o),o})),j=()=>e.createRange(),V=(n,r,s,a)=>{if(p(n)){let e=n.length;const t=[];for(;e--;)t[e]=V(n[e],r,s,a);return t}return!t.collect||n!==e&&n!==o||i.push([n,r,s,a]),c.bind(n,r,s,a||Y)},W=(t,n,r)=>{if(p(t)){let e=t.length;const o=[];for(;e--;)o[e]=W(t[e],n,r);return o}if(i.length>0&&(t===e||t===o)){let e=i.length;for(;e--;){const[o,s,a]=i[e];t!==o||n&&n!==s||r&&r!==a||c.unbind(o,s,a)}}return c.unbind(t,n,r)},K=e=>{if(e&&er(e)){const t=e.getAttribute("data-mce-contenteditable");return t&&"inherit"!==t?t:"inherit"!==e.contentEditable?e.contentEditable:null}return null},Y={doc:e,settings:t,win:o,files:r,stdMode:!0,boxModel:!0,styleSheetLoader:a,boundEvents:i,styles:d,schema:l,events:c,isBlock:e=>m(e)?_e(u,e):Jo(e)&&(_e(u,e.nodeName)||Zr(l,e)),root:null,clone:(e,t)=>e.cloneNode(t),getRoot:x,getViewPort:e=>{const t=Io(e);return{x:t.x,y:t.y,w:t.width,h:t.height}},getRect:e=>{const t=f(e),n=S(t),o=R(t);return{x:n.x,y:n.y,w:o.w,h:o.h}},getSize:R,getParent:(e,t,n)=>{const o=T(e,t,n,!1);return o&&o.length>0?o[0]:null},getParents:T,get:f,getNext:(e,t)=>O(e,t,"nextSibling"),getPrev:(e,t)=>O(e,t,"previousSibling"),select:(n,o)=>{var r,s;const a=null!==(s=null!==(r=f(o))&&void 0!==r?r:t.root_element)&&void 0!==s?s:e;return w(a.querySelectorAll)?ce(a.querySelectorAll(n)):[]},is:A,add:L,create:M,createHTML:(e,t,n="")=>{let o="<"+e;for(const e in t)Se(t,e)&&(o+=" "+e+'="'+I(t[e])+'"');return Xe(n)&&_e(l.getVoidElements(),e)?o+" />":o+">"+n+"</"+e+">"},createFragment:t=>{const n=e.createElement("div"),o=e.createDocumentFragment();let r;for(o.appendChild(n),t&&(n.innerHTML=t);r=n.firstChild;)o.appendChild(r);return o.removeChild(n),o},remove:F,setStyle:(e,n,o)=>{B(e,(e=>{const r=Cn(e);ua(r,n,o),t.update_styles&&ca(d,r)}))},getStyle:k,setStyles:(e,n)=>{B(e,(e=>{const o=Cn(e);pe(n,((e,t)=>{ua(o,t,e)})),t.update_styles&&ca(d,o)}))},removeAllAttribs:e=>B(e,(e=>{const t=e.attributes;for(let n=t.length-1;n>=0;n--)e.removeAttributeNode(t.item(n))})),setAttrib:E,setAttribs:P,getAttrib:b,getPos:S,parseStyle:e=>d.parse(e),serializeStyle:(e,t)=>d.serialize(e,t),addStyle:t=>{if(Y!==ma.DOM&&e===document){if(n[t])return;n[t]=!0}let o=e.getElementById("mceDefaultStyles");if(!o){o=e.createElement("style"),o.id="mceDefaultStyles",o.type="text/css";const t=e.head;t.firstChild?t.insertBefore(o,t.firstChild):t.appendChild(o)}o.styleSheet?o.styleSheet.cssText+=t:o.appendChild(e.createTextNode(t))},loadCSS:e=>{e||(e=""),q(e.split(","),(e=>{r[e]=!0,a.load(e).catch(_)}))},addClass:(e,t)=>{U(e,t,!0)},removeClass:(e,t)=>{U(e,t,!1)},hasClass:(e,t)=>{const n=h(e),o=t.split(" ");return C(n)&&ne(o,(e=>pn(n,e)))},toggleClass:U,show:e=>{B(e,(e=>go(Cn(e),"display")))},hide:e=>{B(e,(e=>io(Cn(e),"display","none")))},isHidden:e=>{const t=h(e);return C(t)&&Lt(mo(t,"display"),"none")},uniqueId:e=>(e||"mce_")+s++,setHTML:D,getOuterHTML:e=>{const t=h(e);return C(t)?Jo(t.dom)?t.dom.outerHTML:(e=>{const t=vn("div"),n=Cn(e.dom.cloneNode(!0));return vo(t,n),So(t)})(t):""},setOuterHTML:(e,t)=>{B(e,(e=>{Jo(e)&&(e.outerHTML=t)}))},decode:ws.decode,encode:I,insertAfter:(e,t)=>{const n=f(t);return B(e,(e=>{const t=null==n?void 0:n.parentNode,o=null==n?void 0:n.nextSibling;return t&&(o?t.insertBefore(e,o):t.appendChild(e)),e}))},replace:z,rename:(e,t)=>{if(e.nodeName!==t.toUpperCase()){const n=M(t);return oa(v(e),(t=>{E(n,t.nodeName,b(e,t.nodeName))})),z(n,e,!0),n}return e},findCommonAncestor:(e,t)=>{let n=e;for(;n;){let e=t;for(;e&&n!==e;)e=e.parentNode;if(n===e)break;n=n.parentNode}return!n&&e.ownerDocument?e.ownerDocument.documentElement:n},run:B,getAttribs:v,isEmpty:(e,t,n)=>{if(g(t)){const o=e=>{const n=e.nodeName.toLowerCase();return Boolean(t[n])};return Ar(l,e,{...n,isContent:o})}return Ar(l,e,n)},createRng:j,nodeIndex:da,split:(e,t,n)=>{let o,r,s=j();if(e&&t&&e.parentNode&&t.parentNode){const a=e.parentNode;return s.setStart(a,da(e)),s.setEnd(t.parentNode,da(t)),o=s.extractContents(),s=j(),s.setStart(t.parentNode,da(t)+1),s.setEnd(a,da(e)+1),r=s.extractContents(),a.insertBefore(is(Y,o,l),e),n?a.insertBefore(n,e):a.insertBefore(t,e),a.insertBefore(is(Y,r,l),e),F(e),n||t}},bind:V,unbind:W,fire:(e,t,n)=>c.dispatch(e,t,n),dispatch:(e,t,n)=>c.dispatch(e,t,n),getContentEditable:K,getContentEditableParent:e=>{const t=x();let n=null;for(let o=e;o&&o!==t&&(n=K(o),null===n);o=o.parentNode);return n},isEditable:e=>{if(C(e)){const t=Jo(e)?e:e.parentElement;return C(t)&&er(t)&&no(Cn(t))}return!1},destroy:()=>{if(i.length>0){let e=i.length;for(;e--;){const[t,n,o]=i[e];c.unbind(t,n,o)}}pe(r,((e,t)=>{a.unload(t),delete r[t]}))},isChildOf:(e,t)=>e===t||t.contains(e),dumpRng:e=>"startContainer: "+e.startContainer.nodeName+", startOffset: "+e.startOffset+", endContainer: "+e.endContainer.nodeName+", endOffset: "+e.endOffset},G=((e,t,n)=>{const o=t.keep_values,r={set:(e,o,r)=>{const s=Cn(e);w(t.url_converter)&&C(o)&&(o=t.url_converter.call(t.url_converter_scope||n(),String(o),r,e)),ia(s,"data-mce-"+r,o),ia(s,r,o)},get:(e,t)=>{const n=Cn(e);return tn(n,"data-mce-"+t)||tn(n,t)}},s={style:{set:(t,n)=>{const r=Cn(t);o&&ia(r,sa,n),rn(r,"style"),m(n)&&lo(r,e.parse(n))},get:t=>{const n=Cn(t),o=tn(n,sa)||tn(n,"style");return e.serialize(e.parse(o),$t(n))}}};return o&&(s.href=s.src=r),s})(d,t,N(Y));return Y};ma.DOM=ma(document),ma.nodeIndex=da;const fa=ma.DOM;class ga{constructor(e={}){this.states={},this.queue=[],this.scriptLoadedCallbacks={},this.queueLoadedCallbacks=[],this.loading=!1,this.settings=e}_setReferrerPolicy(e){this.settings.referrerPolicy=e}loadScript(e){return new Promise(((t,n)=>{const o=fa;let r;const s=()=>{o.remove(a),r&&(r.onerror=r.onload=r=null)},a=o.uniqueId();r=document.createElement("script"),r.id=a,r.type="text/javascript",r.src=Dt._addCacheSuffix(e),this.settings.referrerPolicy&&o.setAttrib(r,"referrerpolicy",this.settings.referrerPolicy),r.onload=()=>{s(),t()},r.onerror=()=>{s(),n("Failed to load script: "+e)},(document.getElementsByTagName("head")[0]||document.body).appendChild(r)}))}isDone(e){return 2===this.states[e]}markDone(e){this.states[e]=2}add(e){const t=this;return t.queue.push(e),void 0===t.states[e]&&(t.states[e]=0),new Promise(((n,o)=>{t.scriptLoadedCallbacks[e]||(t.scriptLoadedCallbacks[e]=[]),t.scriptLoadedCallbacks[e].push({resolve:n,reject:o})}))}load(e){return this.add(e)}remove(e){delete this.states[e],delete this.scriptLoadedCallbacks[e]}loadQueue(){const e=this.queue;return this.queue=[],this.loadScripts(e)}loadScripts(e){const t=this,n=(e,n)=>{xe(t.scriptLoadedCallbacks,n).each((t=>{q(t,(t=>t[e](n)))})),delete t.scriptLoadedCallbacks[n]},o=e=>{const t=Y(e,(e=>"rejected"===e.status));return t.length>0?Promise.reject(te(t,(({reason:e})=>p(e)?e:[e]))):Promise.resolve()},r=e=>Promise.allSettled(V(e,(e=>2===t.states[e]?(n("resolve",e),Promise.resolve()):3===t.states[e]?(n("reject",e),Promise.reject(e)):(t.states[e]=1,t.loadScript(e).then((()=>{t.states[e]=2,n("resolve",e);const s=t.queue;return s.length>0?(t.queue=[],r(s).then(o)):Promise.resolve()}),(()=>(t.states[e]=3,n("reject",e),Promise.reject(e)))))))),s=e=>(t.loading=!0,r(e).then((e=>{t.loading=!1;const n=t.queueLoadedCallbacks.shift();return I.from(n).each(D),o(e)}))),a=ke(e);return t.loading?new Promise(((e,n)=>{t.queueLoadedCallbacks.push((()=>{s(a).then(e,n)}))})):s(a)}}ga.ScriptLoader=new ga;const pa={},ha=Br("en"),ba=()=>xe(pa,ha.get()),va={getData:()=>he(pa,(e=>({...e}))),setCode:e=>{e&&ha.set(e)},getCode:()=>ha.get(),add:(e,t)=>{let n=pa[e];n||(pa[e]=n={});const o=V(fe(t),(e=>e.toLowerCase()));pe(t,((e,r)=>{const s=r.toLowerCase();s!==r&&((e,t)=>{const n=e.indexOf(t);return-1!==n&&e.indexOf(t,n+1)>n})(o,s)?(_e(t,s)||(n[s]=e),n[r]=e):n[s]=e}))},translate:e=>{const t=ba().getOr({}),n=e=>w(e)?Object.prototype.toString.call(e):o(e)?"":""+e,o=e=>""===e||null==e,r=e=>{const o=n(e);return _e(t,o)?n(t[o]):xe(t,o.toLowerCase()).map(n).getOr(o)},s=e=>e.replace(/{context:\w+}$/,"");if(o(e))return"";if(f(a=e)&&_e(a,"raw"))return n(e.raw);var a;if((e=>p(e)&&e.length>1)(e)){const t=e.slice(1);return s(r(e[0]).replace(/\{([0-9]+)\}/g,((e,o)=>_e(t,o)?n(t[o]):e)))}return s(r(e))},isRtl:()=>ba().bind((e=>xe(e,"_dir"))).exists((e=>"rtl"===e)),hasCode:e=>_e(pa,e)},ya=()=>{const e=[],t={},n={},o=[],r=(e,t)=>{const n=Y(o,(n=>n.name===e&&n.state===t));q(n,(e=>e.resolve()))},s=e=>_e(t,e),a=(e,n)=>{const o=va.getCode();!o||n&&-1===(","+(n||"")+",").indexOf(","+o+",")||ga.ScriptLoader.add(t[e]+"/langs/"+o+".js")},i=(e,t="added")=>"added"===t&&(e=>_e(n,e))(e)||"loaded"===t&&s(e)?Promise.resolve():new Promise((n=>{o.push({name:e,state:t,resolve:n})}));return{items:e,urls:t,lookup:n,get:e=>{if(n[e])return n[e].instance},requireLangPack:(e,t)=>{!1!==ya.languageLoad&&(s(e)?a(e,t):i(e,"loaded").then((()=>a(e,t))))},add:(t,o)=>(e.push(o),n[t]={instance:o},r(t,"added"),o),remove:e=>{delete t[e],delete n[e]},createUrl:(e,t)=>m(t)?m(e)?{prefix:"",resource:t,suffix:""}:{prefix:e.prefix,resource:t,suffix:e.suffix}:t,load:(e,o)=>{if(t[e])return Promise.resolve();let s=m(o)?o:o.prefix+o.resource+o.suffix;0!==s.indexOf("/")&&-1===s.indexOf("://")&&(s=ya.baseURL+"/"+s),t[e]=s.substring(0,s.lastIndexOf("/"));const a=()=>(r(e,"loaded"),Promise.resolve());return n[e]?a():ga.ScriptLoader.add(s).then(a)},waitFor:i}};ya.languageLoad=!0,ya.baseURL="",ya.PluginManager=ya(),ya.ThemeManager=ya(),ya.ModelManager=ya();const Ca=(e,t)=>{let n=null;return{cancel:()=>{h(n)||(clearTimeout(n),n=null)},throttle:(...o)=>{h(n)&&(n=setTimeout((()=>{n=null,e.apply(null,o)}),t))}}},wa=(e,t)=>{let n=null;const o=()=>{h(n)||(clearTimeout(n),n=null)};return{cancel:o,throttle:(...r)=>{o(),n=setTimeout((()=>{n=null,e.apply(null,r)}),t)}}},Ea=N("mce-annotation"),xa=N("data-mce-annotation"),_a=N("data-mce-annotation-uid"),Sa=N("data-mce-annotation-active"),ka=N("data-mce-annotation-classes"),Na=N("data-mce-annotation-attrs"),Ra=e=>t=>Sn(t,e),Aa=(e,t)=>{const n=e.selection.getRng(),o=Cn(n.startContainer),r=Cn(e.getBody()),s=t.fold((()=>"."+Ea()),(e=>`[${xa()}="${e}"]`)),a=Fn(o,n.startOffset).getOr(o);return to(a,s,Ra(r)).bind((t=>nn(t,`${_a()}`).bind((n=>nn(t,`${xa()}`).map((t=>{const o=Oa(e,n);return{uid:n,name:t,elements:o}}))))))},Ta=(e,t)=>on(e,"data-mce-bogus")||((e,t,n)=>Jn(e,'[data-mce-bogus="all"]',n).isSome())(e,0,Ra(t)),Oa=(e,t)=>{const n=Cn(e.getBody()),o=Uo(n,`[${_a()}="${t}"]`);return Y(o,(e=>!Ta(e,n)))},Ba=(e,t)=>{const n=Cn(e.getBody()),o=Uo(n,`[${xa()}="${t}"]`),r={};return q(o,(e=>{if(!Ta(e,n)){const t=tn(e,_a()),n=xe(r,t).getOr([]);r[t]=n.concat([e])}})),r},Pa=()=>window.crypto.getRandomValues(new Uint32Array(1))[0]/4294967295;let Da=0;const La=e=>{const t=(new Date).getTime(),n=Math.floor(1e9*Pa());return Da++,e+"_"+n+Da+String(t)},Ma=(e,t)=>Cn(e.dom.cloneNode(t)),Ia=e=>Ma(e,!1),Fa=e=>Ma(e,!0),Ua=(e,t,n=L)=>{const o=new $o(e,t),r=e=>{let t;do{t=o[e]()}while(t&&!lr(t)&&!n(t));return I.from(t).filter(lr)};return{current:()=>I.from(o.current()).filter(lr),next:()=>r("next"),prev:()=>r("prev"),prev2:()=>r("prev2")}},za=(e,t)=>{const n=t||(t=>e.isBlock(t)||gr(t)||br(t)),o=(e,t,n,r)=>{if(lr(e)){const n=r(e,t,e.data);if(-1!==n)return I.some({container:e,offset:n})}return n().bind((e=>o(e.container,e.offset,n,r)))};return{backwards:(t,r,s,a)=>{const i=Ua(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.prev().map((e=>({container:e,offset:e.length})))),s).getOrNull()},forwards:(t,r,s,a)=>{const i=Ua(t,null!=a?a:e.getRoot(),n);return o(t,r,(()=>i.next().map((e=>({container:e,offset:0})))),s).getOrNull()}}},ja=(e=>{const t=t=>e(t)?I.from(t.dom.nodeValue):I.none();return{get:n=>{if(!e(n))throw new Error("Can only get text value of a text node");return t(n).getOr("")},getOption:t,set:(t,n)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=n}}})(Yt),Ha=e=>ja.get(e),$a=(e,t)=>ja.set(e,t),Va=e=>{let t;return n=>(t=t||se(e,M),_e(t,$t(n)))},qa=e=>Kt(e)&&"br"===$t(e),Wa=Va(["h1","h2","h3","h4","h5","h6","p","div","address","pre","form","blockquote","center","dir","fieldset","header","footer","article","section","hgroup","aside","nav","figure"]),Ka=Va(["ul","ol","dl"]),Ya=Va(["li","dd","dt"]),Ga=Va(["thead","tbody","tfoot"]),Xa=Va(["td","th"]),Za=Va(["pre","script","textarea","style"]),Qa=()=>{const e=vn("br");return Jt(e,"data-mce-bogus","1"),e},Ja=e=>{wo(e),vo(e,Qa())},ei=Vo,ti=Wo,ni=e=>e.replace(/\uFEFF/g,""),oi=Jo,ri=lr,si=e=>(ri(e)&&(e=e.parentNode),oi(e)&&e.hasAttribute("data-mce-caret")),ai=e=>ri(e)&&ti(e.data),ii=e=>si(e)||ai(e),li=e=>e.firstChild!==e.lastChild||!gr(e.firstChild),di=e=>{const t=e.container();return!!lr(t)&&(t.data.charAt(e.offset())===ei||e.isAtStart()&&ai(t.previousSibling))},ci=e=>{const t=e.container();return!!lr(t)&&(t.data.charAt(e.offset()-1)===ei||e.isAtEnd()&&ai(t.nextSibling))},ui=e=>ri(e)&&e.data[0]===ei,mi=e=>ri(e)&&e.data[e.data.length-1]===ei,fi=e=>e&&e.hasAttribute("data-mce-caret")?((e=>{var t;const n=e.getElementsByTagName("br"),o=n[n.length-1];rr(o)&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o))})(e),e.removeAttribute("data-mce-caret"),e.removeAttribute("data-mce-bogus"),e.removeAttribute("style"),e.removeAttribute("data-mce-style"),e.removeAttribute("_moz_abspos"),e):null,gi=e=>si(e.startContainer),pi=Math.round,hi=e=>e?{left:pi(e.left),top:pi(e.top),bottom:pi(e.bottom),right:pi(e.right),width:pi(e.width),height:pi(e.height)}:{left:0,top:0,bottom:0,right:0,width:0,height:0},bi=(e,t)=>(e=hi(e),t||(e.left=e.left+e.width),e.right=e.left,e.width=0,e),vi=(e,t,n)=>e>=0&&e<=Math.min(t.height,n.height)/2,yi=(e,t)=>{const n=Math.min(t.height/2,e.height/2);return e.bottom-n<t.top||!(e.top>t.bottom)&&vi(t.top-e.bottom,e,t)},Ci=(e,t)=>e.top>t.bottom||!(e.bottom<t.top)&&vi(t.bottom-e.top,e,t),wi=(e,t,n)=>{const o=Math.max(Math.min(t,e.left+e.width),e.left),r=Math.max(Math.min(n,e.top+e.height),e.top);return Math.sqrt((t-o)*(t-o)+(n-r)*(n-r))},Ei=e=>{const t=e.startContainer,n=e.startOffset;return t===e.endContainer&&t.hasChildNodes()&&e.endOffset===n+1?t.childNodes[n]:null},xi=(e,t)=>{if(Jo(e)&&e.hasChildNodes()){const n=e.childNodes,o=((e,t,n)=>Math.min(Math.max(e,0),n))(t,0,n.length-1);return n[o]}return e},_i=new RegExp("[\u0300-\u036f\u0483-\u0487\u0488-\u0489\u0591-\u05bd\u05bf\u05c1-\u05c2\u05c4-\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7-\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u08e3-\u0902\u093a\u093c\u0941-\u0948\u094d\u0951-\u0957\u0962-\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2-\u09e3\u0a01-\u0a02\u0a3c\u0a41-\u0a42\u0a47-\u0a48\u0a4b-\u0a4d\u0a51\u0a70-\u0a71\u0a75\u0a81-\u0a82\u0abc\u0ac1-\u0ac5\u0ac7-\u0ac8\u0acd\u0ae2-\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62-\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c00\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55-\u0c56\u0c62-\u0c63\u0c81\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc-\u0ccd\u0cd5-\u0cd6\u0ce2-\u0ce3\u0d01\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62-\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb-\u0ebc\u0ec8-\u0ecd\u0f18-\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86-\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039-\u103a\u103d-\u103e\u1058-\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085-\u1086\u108d\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752-\u1753\u1772-\u1773\u17b4-\u17b5\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927-\u1928\u1932\u1939-\u193b\u1a17-\u1a18\u1a1b\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1ab0-\u1abd\u1abe\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80-\u1b81\u1ba2-\u1ba5\u1ba8-\u1ba9\u1bab-\u1bad\u1be6\u1be8-\u1be9\u1bed\u1bef-\u1bf1\u1c2c-\u1c33\u1c36-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1cf4\u1cf8-\u1cf9\u1dc0-\u1df5\u1dfc-\u1dff\u200c-\u200d\u20d0-\u20dc\u20dd-\u20e0\u20e1\u20e2-\u20e4\u20e5-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302d\u302e-\u302f\u3099-\u309a\ua66f\ua670-\ua672\ua674-\ua67d\ua69e-\ua69f\ua6f0-\ua6f1\ua802\ua806\ua80b\ua825-\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\ua9e5\uaa29-\uaa2e\uaa31-\uaa32\uaa35-\uaa36\uaa43\uaa4c\uaa7c\uaab0\uaab2-\uaab4\uaab7-\uaab8\uaabe-\uaabf\uaac1\uaaec-\uaaed\uaaf6\uabe5\uabe8\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe2f\uff9e-\uff9f]"),Si=e=>m(e)&&e.charCodeAt(0)>=768&&_i.test(e),ki=hr,Ni=br,Ri=gr,Ai=lr,Ti=nr(["script","style","textarea"]),Oi=nr(["img","input","textarea","hr","iframe","video","audio","object","embed"]),Bi=nr(["table"]),Pi=ii,Di=e=>!Pi(e)&&(Ai(e)?!Ti(e.parentNode):Oi(e)||Ri(e)||Bi(e)||Li(e)),Li=e=>!(e=>Jo(e)&&"true"===e.getAttribute("unselectable"))(e)&&Ni(e),Mi=(e,t)=>Di(e)&&((e,t)=>{for(let n=e.parentNode;n&&n!==t;n=n.parentNode){if(Li(n))return!1;if(ki(n))return!0}return!0})(e,t),Ii=Jo,Fi=Di,Ui=or("display","block table"),zi=or("float","left right"),ji=((...e)=>t=>{for(let n=0;n<e.length;n++)if(!e[n](t))return!1;return!0})(Ii,Fi,O(zi)),Hi=O(or("white-space","pre pre-line pre-wrap")),$i=lr,Vi=gr,qi=ma.nodeIndex,Wi=(e,t)=>t<0&&Jo(e)&&e.hasChildNodes()?void 0:xi(e,t),Ki=e=>e?e.createRange():ma.DOM.createRng(),Yi=e=>m(e)&&/[\r\n\t ]/.test(e),Gi=e=>!!e.setStart&&!!e.setEnd,Xi=e=>{const t=e.startContainer,n=e.startOffset;if(Yi(e.toString())&&Hi(t.parentNode)&&lr(t)){const e=t.data;if(Yi(e[n-1])||Yi(e[n+1]))return!0}return!1},Zi=e=>0===e.left&&0===e.right&&0===e.top&&0===e.bottom,Qi=e=>{var t;let n;const o=e.getClientRects();return n=o.length>0?hi(o[0]):hi(e.getBoundingClientRect()),!Gi(e)&&Vi(e)&&Zi(n)?(e=>{const t=e.ownerDocument,n=Ki(t),o=t.createTextNode(qo),r=e.parentNode;r.insertBefore(o,e),n.setStart(o,0),n.setEnd(o,1);const s=hi(n.getBoundingClientRect());return r.removeChild(o),s})(e):Zi(n)&&Gi(e)&&null!==(t=(e=>{const t=e.startContainer,n=e.endContainer,o=e.startOffset,r=e.endOffset;if(t===n&&lr(n)&&0===o&&1===r){const t=e.cloneRange();return t.setEndAfter(n),Qi(t)}return null})(e))&&void 0!==t?t:n},Ji=(e,t)=>{const n=bi(e,t);return n.width=1,n.right=n.left+1,n},el=(e,t,n)=>{const o=()=>(n||(n=(e=>{const t=[],n=e=>{var n,o;0!==e.height&&(t.length>0&&(n=e,o=t[t.length-1],n.left===o.left&&n.top===o.top&&n.bottom===o.bottom&&n.right===o.right)||t.push(e))},o=(e,t)=>{const o=Ki(e.ownerDocument);if(t<e.data.length){if(Si(e.data[t]))return;if(Si(e.data[t-1])&&(o.setStart(e,t),o.setEnd(e,t+1),!Xi(o)))return void n(Ji(Qi(o),!1))}t>0&&(o.setStart(e,t-1),o.setEnd(e,t),Xi(o)||n(Ji(Qi(o),!1))),t<e.data.length&&(o.setStart(e,t),o.setEnd(e,t+1),Xi(o)||n(Ji(Qi(o),!0)))},r=e.container(),s=e.offset();if($i(r))return o(r,s),t;if(Ii(r))if(e.isAtEnd()){const e=Wi(r,s);$i(e)&&o(e,e.data.length),ji(e)&&!Vi(e)&&n(Ji(Qi(e),!1))}else{const a=Wi(r,s);if($i(a)&&o(a,0),ji(a)&&e.isAtEnd())return n(Ji(Qi(a),!1)),t;const i=Wi(e.container(),e.offset()-1);ji(i)&&!Vi(i)&&(Ui(i)||Ui(a)||!ji(a))&&n(Ji(Qi(i),!1)),ji(a)&&n(Ji(Qi(a),!0))}return t})(el(e,t))),n);return{container:N(e),offset:N(t),toRange:()=>{const n=Ki(e.ownerDocument);return n.setStart(e,t),n.setEnd(e,t),n},getClientRects:o,isVisible:()=>o().length>0,isAtStart:()=>($i(e),0===t),isAtEnd:()=>$i(e)?t>=e.data.length:t>=e.childNodes.length,isEqual:n=>n&&e===n.container()&&t===n.offset(),getNode:n=>Wi(e,n?t-1:t)}};el.fromRangeStart=e=>el(e.startContainer,e.startOffset),el.fromRangeEnd=e=>el(e.endContainer,e.endOffset),el.after=e=>el(e.parentNode,qi(e)+1),el.before=e=>el(e.parentNode,qi(e)),el.isAbove=(e,t)=>It(le(t.getClientRects()),de(e.getClientRects()),yi).getOr(!1),el.isBelow=(e,t)=>It(de(t.getClientRects()),le(e.getClientRects()),Ci).getOr(!1),el.isAtStart=e=>!!e&&e.isAtStart(),el.isAtEnd=e=>!!e&&e.isAtEnd(),el.isTextPosition=e=>!!e&&lr(e.container()),el.isElementPosition=e=>!el.isTextPosition(e);const tl=(e,t)=>{lr(t)&&0===t.data.length&&e.remove(t)},nl=(e,t,n)=>{fr(n)?((e,t,n)=>{const o=I.from(n.firstChild),r=I.from(n.lastChild);t.insertNode(n),o.each((t=>tl(e,t.previousSibling))),r.each((t=>tl(e,t.nextSibling)))})(e,t,n):((e,t,n)=>{t.insertNode(n),tl(e,n.previousSibling),tl(e,n.nextSibling)})(e,t,n)},ol=lr,rl=rr,sl=ma.nodeIndex,al=e=>{const t=e.parentNode;return rl(t)?al(t):t},il=e=>e?Be(e.childNodes,((e,t)=>(rl(t)&&"BR"!==t.nodeName?e=e.concat(il(t)):e.push(t),e)),[]):[],ll=e=>t=>e===t,dl=e=>(ol(e)?"text()":e.nodeName.toLowerCase())+"["+(e=>{let t,n;t=il(al(e)),n=Pe(t,ll(e),e),t=t.slice(0,n+1);const o=Be(t,((e,n,o)=>(ol(n)&&ol(t[o-1])&&e++,e)),0);return t=Oe(t,nr([e.nodeName])),n=Pe(t,ll(e),e),n-o})(e)+"]",cl=(e,t)=>{let n,o=[],r=t.container(),s=t.offset();if(ol(r))n=((e,t)=>{let n=e;for(;(n=n.previousSibling)&&ol(n);)t+=n.data.length;return t})(r,s);else{const e=r.childNodes;s>=e.length?(n="after",s=e.length-1):n="before",r=e[s]}o.push(dl(r));let a=((e,t)=>{const n=[];for(let o=t.parentNode;o&&o!==e;o=o.parentNode)n.push(o);return n})(e,r);return a=Oe(a,O(rr)),o=o.concat(Te(a,(e=>dl(e)))),o.reverse().join("/")+","+n},ul=(e,t)=>{if(!t)return null;const n=t.split(","),o=n[0].split("/"),r=n.length>1?n[1]:"before",s=Be(o,((e,t)=>{const n=/([\w\-\(\)]+)\[([0-9]+)\]/.exec(t);return n?("text()"===n[1]&&(n[1]="#text"),((e,t,n)=>{let o=il(e);return o=Oe(o,((e,t)=>!ol(e)||!ol(o[t-1]))),o=Oe(o,nr([t])),o[n]})(e,n[1],parseInt(n[2],10))):null}),e);if(!s)return null;if(!ol(s)&&s.parentNode){let e;return e="after"===r?sl(s)+1:sl(s),el(s.parentNode,e)}return((e,t)=>{let n=e,o=0;for(;ol(n);){const r=n.data.length;if(t>=o&&t<=o+r){e=n,t-=o;break}if(!ol(n.nextSibling)){e=n,t=r;break}o+=r,n=n.nextSibling}return ol(e)&&t>e.data.length&&(t=e.data.length),el(e,t)})(s,parseInt(r,10))},ml=br,fl=(e,t,n,o,r)=>{const s=r?o.startContainer:o.endContainer;let a=r?o.startOffset:o.endOffset;const i=[],l=e.getRoot();if(lr(s))i.push(n?((e,t,n)=>{let o=e(t.data.slice(0,n)).length;for(let n=t.previousSibling;n&&lr(n);n=n.previousSibling)o+=e(n.data).length;return o})(t,s,a):a);else{let t=0;const o=s.childNodes;a>=o.length&&o.length&&(t=1,a=Math.max(0,o.length-1)),i.push(e.nodeIndex(o[a],n)+t)}for(let t=s;t&&t!==l;t=t.parentNode)i.push(e.nodeIndex(t,n));return i},gl=(e,t,n)=>{let o=0;return Dt.each(e.select(t),(e=>"all"===e.getAttribute("data-mce-bogus")?void 0:e!==n&&void o++)),o},pl=(e,t)=>{let n=t?e.startContainer:e.endContainer,o=t?e.startOffset:e.endOffset;if(Jo(n)&&"TR"===n.nodeName){const r=n.childNodes;n=r[Math.min(t?o:o-1,r.length-1)],n&&(o=t?0:n.childNodes.length,t?e.setStart(n,o):e.setEnd(n,o))}},hl=e=>(pl(e,!0),pl(e,!1),e),bl=(e,t)=>{if(Jo(e)&&(e=xi(e,t),ml(e)))return e;if(ii(e)){lr(e)&&si(e)&&(e=e.parentNode);let t=e.previousSibling;if(ml(t))return t;if(t=e.nextSibling,ml(t))return t}},vl=(e,t,n)=>{const o=n.getNode(),r=n.getRng();if("IMG"===o.nodeName||ml(o)){const e=o.nodeName;return{name:e,index:gl(n.dom,e,o)}}const s=(e=>bl(e.startContainer,e.startOffset)||bl(e.endContainer,e.endOffset))(r);if(s){const e=s.tagName;return{name:e,index:gl(n.dom,e,s)}}return((e,t,n,o)=>{const r=t.dom,s=fl(r,e,n,o,!0),a=t.isForward(),i=gi(o)?{isFakeCaret:!0}:{};return t.isCollapsed()?{start:s,forward:a,...i}:{start:s,end:fl(r,e,n,o,!1),forward:a,...i}})(e,n,t,r)},yl=(e,t,n)=>{const o={"data-mce-type":"bookmark",id:t,style:"overflow:hidden;line-height:0px"};return n?e.create("span",o,"&#xFEFF;"):e.create("span",o)},Cl=(e,t)=>{const n=e.dom;let o=e.getRng();const r=n.uniqueId(),s=e.isCollapsed(),a=e.getNode(),i=a.nodeName,l=e.isForward();if("IMG"===i)return{name:i,index:gl(n,i,a)};const d=hl(o.cloneRange());if(!s){d.collapse(!1);const e=yl(n,r+"_end",t);nl(n,d,e)}o=hl(o),o.collapse(!0);const c=yl(n,r+"_start",t);return nl(n,o,c),e.moveToBookmark({id:r,keep:!0,forward:l}),{id:r,forward:l}},wl=T(vl,R,!0),El=e=>{const t=t=>t(e),n=N(e),o=()=>r,r={tag:!0,inner:e,fold:(t,n)=>n(e),isValue:M,isError:L,map:t=>_l.value(t(e)),mapError:o,bind:t,exists:t,forall:t,getOr:n,or:o,getOrThunk:n,orThunk:o,getOrDie:n,each:t=>{t(e)},toOptional:()=>I.some(e)};return r},xl=e=>{const t=()=>n,n={tag:!1,inner:e,fold:(t,n)=>t(e),isValue:L,isError:M,map:t,mapError:t=>_l.error(t(e)),bind:t,exists:L,forall:M,getOr:R,or:R,getOrThunk:P,orThunk:P,getOrDie:B(String(e)),each:_,toOptional:I.none};return n},_l={value:El,error:xl,fromOption:(e,t)=>e.fold((()=>xl(t)),El)},Sl=e=>{if(!p(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],n={};return q(e,((o,r)=>{const s=fe(o);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],i=o[a];if(void 0!==n[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!p(i))throw new Error("case arguments must be an array");t.push(a),n[a]=(...n)=>{const o=n.length;if(o!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+o);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,n)},match:e=>{const o=fe(e);if(t.length!==o.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+o.join(","));if(!ne(t,(e=>H(o,e))))throw new Error("Not all branches were specified when using match. Specified: "+o.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,n)},log:e=>{console.log(e,{constructors:t,constructor:a,params:n})}}}})),n};Sl([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const kl=e=>"inline-command"===e.type||"inline-format"===e.type,Nl=e=>"block-command"===e.type||"block-format"===e.type,Rl=e=>{var t;const n=t=>_l.error({message:t,pattern:e}),o=(t,o,r)=>{if(void 0!==e.format){let r;if(p(e.format)){if(!ne(e.format,m))return n(t+" pattern has non-string items in the `format` array");r=e.format}else{if(!m(e.format))return n(t+" pattern has non-string `format` parameter");r=[e.format]}return _l.value(o(r))}return void 0!==e.cmd?m(e.cmd)?_l.value(r(e.cmd,e.value)):n(t+" pattern has non-string `cmd` parameter"):n(t+" pattern is missing both `format` and `cmd` parameters")};if(!f(e))return n("Raw pattern is not an object");if(!m(e.start))return n("Raw pattern is missing `start` parameter");if(void 0!==e.end){if(!m(e.end))return n("Inline pattern has non-string `end` parameter");if(0===e.start.length&&0===e.end.length)return n("Inline pattern has empty `start` and `end` parameters");let t=e.start,r=e.end;return 0===r.length&&(r=t,t=""),o("Inline",(e=>({type:"inline-format",start:t,end:r,format:e})),((e,n)=>({type:"inline-command",start:t,end:r,cmd:e,value:n})))}if(void 0!==e.replacement)return m(e.replacement)?0===e.start.length?n("Replacement pattern has empty `start` parameter"):_l.value({type:"inline-command",start:"",end:e.start,cmd:"mceInsertContent",value:e.replacement}):n("Replacement pattern has non-string `replacement` parameter");{const r=null!==(t=e.trigger)&&void 0!==t?t:"space";return 0===e.start.length?n("Block pattern has empty `start` parameter"):o("Block",(t=>({type:"block-format",start:e.start,format:t[0],trigger:r})),((t,n)=>({type:"block-command",start:e.start,cmd:t,value:n,trigger:r})))}},Al=e=>Y(e,Nl),Tl=e=>Y(e,kl),Ol=(e,t)=>({...e,blockPatterns:Y(e.blockPatterns,(e=>((e,t)=>("block-command"===e.type||"block-format"===e.type)&&e.trigger===t)(e,t)))}),Bl=e=>{const t=(e=>{const t=[],n=[];return q(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{n.push(e)}))})),{errors:t,values:n}})(V(e,Rl));return q(t.errors,(e=>console.error(e.message,e.pattern))),t.values},Pl=(e,t,n)=>{e.dispatch(t,n)},Dl=(e,t,n,o)=>{e.dispatch("FormatApply",{format:t,node:n,vars:o})},Ll=(e,t,n,o)=>{e.dispatch("FormatRemove",{format:t,node:n,vars:o})},Ml=(e,t)=>e.dispatch("SetContent",t),Il=(e,t)=>e.dispatch("GetContent",t),Fl=(e,t)=>{e.dispatch("AutocompleterUpdateActiveRange",t)},Ul=(e,t)=>e.dispatch("PastePlainTextToggle",{state:t}),zl=xt().deviceType,jl=zl.isTouch(),Hl=ma.DOM,$l=e=>u(e,RegExp),Vl=e=>t=>t.options.get(e),ql=e=>m(e)||f(e),Wl=(e,t="")=>n=>{const o=m(n);if(o){if(-1!==n.indexOf("=")){const r=(e=>{const t=e.indexOf("=")>0?e.split(/[;,](?![^=;,]*(?:[;,]|$))/):e.split(",");return X(t,((e,t)=>{const n=t.split("="),o=n[0],r=n.length>1?n[1]:o;return e[We(o)]=We(r),e}),{})})(n);return{value:xe(r,e.id).getOr(t),valid:o}}return{value:n,valid:o}}return{valid:!1,message:"Must be a string."}},Kl=Vl("iframe_attrs"),Yl=Vl("doctype"),Gl=Vl("document_base_url"),Xl=Vl("body_id"),Zl=Vl("body_class"),Ql=Vl("content_security_policy"),Jl=Vl("br_in_pre"),ed=Vl("forced_root_block"),td=Vl("forced_root_block_attrs"),nd=Vl("newline_behavior"),od=Vl("br_newline_selector"),rd=Vl("no_newline_selector"),sd=Vl("keep_styles"),ad=Vl("end_container_on_empty_block"),id=Vl("automatic_uploads"),ld=Vl("images_reuse_filename"),dd=Vl("images_replace_blob_uris"),cd=Vl("icons"),ud=Vl("icons_url"),md=Vl("images_upload_url"),fd=Vl("images_upload_base_path"),gd=Vl("images_upload_credentials"),pd=Vl("images_upload_handler"),hd=Vl("content_css_cors"),bd=Vl("referrer_policy"),vd=Vl("language"),yd=Vl("language_url"),Cd=Vl("indent_use_margin"),wd=Vl("indentation"),Ed=Vl("content_css"),xd=Vl("content_style"),_d=Vl("font_css"),Sd=Vl("directionality"),kd=Vl("inline_boundaries_selector"),Nd=Vl("object_resizing"),Rd=Vl("resize_img_proportional"),Ad=Vl("placeholder"),Td=Vl("event_root"),Od=Vl("service_message"),Bd=Vl("theme"),Pd=Vl("theme_url"),Dd=Vl("model"),Ld=Vl("model_url"),Md=Vl("inline_boundaries"),Id=Vl("formats"),Fd=Vl("preview_styles"),Ud=Vl("format_empty_lines"),zd=Vl("format_noneditable_selector"),jd=Vl("custom_ui_selector"),Hd=Vl("inline"),$d=Vl("hidden_input"),Vd=Vl("submit_patch"),qd=Vl("add_form_submit_trigger"),Wd=Vl("add_unload_trigger"),Kd=Vl("custom_undo_redo_levels"),Yd=Vl("disable_nodechange"),Gd=Vl("readonly"),Xd=Vl("editable_root"),Zd=Vl("content_css_cors"),Qd=Vl("plugins"),Jd=Vl("external_plugins"),ec=Vl("block_unsupported_drop"),tc=Vl("visual"),nc=Vl("visual_table_class"),oc=Vl("visual_anchor_class"),rc=Vl("iframe_aria_text"),sc=Vl("setup"),ac=Vl("init_instance_callback"),ic=Vl("urlconverter_callback"),lc=Vl("auto_focus"),dc=Vl("browser_spellcheck"),cc=Vl("protect"),uc=Vl("paste_block_drop"),mc=Vl("paste_data_images"),fc=Vl("paste_preprocess"),gc=Vl("paste_postprocess"),pc=Vl("newdocument_content"),hc=Vl("paste_webkit_styles"),bc=Vl("paste_remove_styles_if_webkit"),vc=Vl("paste_merge_formats"),yc=Vl("smart_paste"),Cc=Vl("paste_as_text"),wc=Vl("paste_tab_spaces"),Ec=Vl("allow_html_data_urls"),xc=Vl("text_patterns"),_c=Vl("text_patterns_lookup"),Sc=Vl("noneditable_class"),kc=Vl("editable_class"),Nc=Vl("noneditable_regexp"),Rc=Vl("preserve_cdata"),Ac=Vl("highlight_on_focus"),Tc=Vl("xss_sanitization"),Oc=Vl("init_content_sync"),Bc=e=>Dt.explode(e.options.get("images_file_types")),Pc=Vl("table_tab_navigation"),Dc=Vl("details_initial_state"),Lc=Vl("details_serialized_state"),Mc=Vl("sandbox_iframes"),Ic=e=>e.options.get("sandbox_iframes_exclusions"),Fc=Vl("convert_unsafe_embeds"),Uc=Vl("license_key"),zc=Vl("api_key"),jc=Vl("disabled"),Hc=Jo,$c=lr,Vc=e=>{const t=e.parentNode;t&&t.removeChild(e)},qc=e=>{const t=ni(e);return{count:e.length-t.length,text:t}},Wc=e=>{let t;for(;-1!==(t=e.data.lastIndexOf(ei));)e.deleteData(t,1)},Kc=(e,t)=>(Gc(e),t),Yc=(e,t)=>el.isTextPosition(t)?((e,t)=>$c(e)&&t.container()===e?((e,t)=>{const n=qc(e.data.substr(0,t.offset())),o=qc(e.data.substr(t.offset()));return(n.text+o.text).length>0?(Wc(e),el(e,t.offset()-n.count)):t})(e,t):Kc(e,t))(e,t):((e,t)=>t.container()===e.parentNode?((e,t)=>{const n=t.container(),o=((e,t)=>{const n=j(e,t);return-1===n?I.none():I.some(n)})(ce(n.childNodes),e).map((e=>e<t.offset()?el(n,t.offset()-1):t)).getOr(t);return Gc(e),o})(e,t):Kc(e,t))(e,t),Gc=e=>{Hc(e)&&ii(e)&&(li(e)?e.removeAttribute("data-mce-caret"):Vc(e)),$c(e)&&(Wc(e),0===e.data.length&&Vc(e))},Xc=br,Zc=wr,Qc=yr,Jc=(e,t,n)=>{const o=bi(t.getBoundingClientRect(),n);let r,s;if("BODY"===e.tagName){const t=e.ownerDocument.documentElement;r=e.scrollLeft||t.scrollLeft,s=e.scrollTop||t.scrollTop}else{const t=e.getBoundingClientRect();r=e.scrollLeft-t.left,s=e.scrollTop-t.top}o.left+=r,o.right+=r,o.top+=s,o.bottom+=s,o.width=1;let a=t.offsetWidth-t.clientWidth;return a>0&&(n&&(a*=-1),o.left+=a,o.right+=a),o},eu=(e,t,n,o)=>{const r=Dr();let s,a;const i=ed(e),l=e.dom,d=()=>{(e=>{var t,n;const o=Uo(Cn(e),"*[contentEditable=false],video,audio,embed,object");for(let e=0;e<o.length;e++){const r=o[e].dom;let s=r.previousSibling;if(mi(s)){const e=s.data;1===e.length?null===(t=s.parentNode)||void 0===t||t.removeChild(s):s.deleteData(e.length-1,1)}s=r.nextSibling,ui(s)&&(1===s.data.length?null===(n=s.parentNode)||void 0===n||n.removeChild(s):s.deleteData(0,1))}})(t),a&&(Gc(a),a=null),r.on((e=>{l.remove(e.caret),r.clear()})),s&&(clearInterval(s),s=void 0)};return{show:(e,c)=>{let u;if(d(),Qc(c))return null;if(!n(c))return a=((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(ei),r=e.parentNode;if(t){const t=e.previousSibling;if(ri(t)){if(ii(t))return t;if(mi(t))return t.splitText(t.data.length-1)}null==r||r.insertBefore(o,e)}else{const t=e.nextSibling;if(ri(t)){if(ii(t))return t;if(ui(t))return t.splitText(1),t}e.nextSibling?null==r||r.insertBefore(o,e.nextSibling):null==r||r.appendChild(o)}return o})(c,e),u=c.ownerDocument.createRange(),nu(a.nextSibling)?(u.setStart(a,0),u.setEnd(a,0)):(u.setStart(a,1),u.setEnd(a,1)),u;{const n=((e,t,n)=>{var o;const r=(null!==(o=t.ownerDocument)&&void 0!==o?o:document).createElement(e);r.setAttribute("data-mce-caret",n?"before":"after"),r.setAttribute("data-mce-bogus","all"),r.appendChild(Qa().dom);const s=t.parentNode;return n?null==s||s.insertBefore(r,t):t.nextSibling?null==s||s.insertBefore(r,t.nextSibling):null==s||s.appendChild(r),r})(i,c,e),d=Jc(t,c,e);l.setStyle(n,"top",d.top),l.setStyle(n,"caret-color","transparent"),a=n;const m=l.create("div",{class:"mce-visual-caret","data-mce-bogus":"all"});l.setStyles(m,{...d}),l.add(t,m),r.set({caret:m,element:c,before:e}),e&&l.addClass(m,"mce-visual-caret-before"),s=window.setInterval((()=>{r.on((e=>{o()?l.toggleClass(e.caret,"mce-visual-caret-hidden"):l.addClass(e.caret,"mce-visual-caret-hidden")}))}),500),u=c.ownerDocument.createRange(),u.setStart(n,0),u.setEnd(n,0)}return u},hide:d,getCss:()=>".mce-visual-caret {position: absolute;background-color: black;background-color: currentcolor;}.mce-visual-caret-hidden {display: none;}*[data-mce-caret] {position: absolute;left: -1000px;right: auto;top: 0;margin: 0;padding: 0;}",reposition:()=>{r.on((e=>{const n=Jc(t,e.element,e.before);l.setStyles(e.caret,{...n})}))},destroy:()=>clearInterval(s)}},tu=()=>Tt.browser.isFirefox(),nu=e=>Xc(e)||Zc(e),ou=e=>(nu(e)||sr(e)&&tu())&&On(Cn(e)).exists(no),ru=hr,su=br,au=wr,iu=or("display","block table table-cell table-row table-caption list-item"),lu=ii,du=si,cu=Jo,uu=lr,mu=Di,fu=e=>1===e,gu=e=>-1===e,pu=(e,t)=>{let n;for(;n=e(t);)if(!du(n))return n;return null},hu=(e,t,n,o,r)=>{const s=new $o(e,o),a=su(e)||du(e);let i;if(gu(t)){if(a&&(i=pu(s.prev.bind(s),!0),n(i)))return i;for(;i=pu(s.prev.bind(s),r);)if(n(i))return i}if(fu(t)){if(a&&(i=pu(s.next.bind(s),!0),n(i)))return i;for(;i=pu(s.next.bind(s),r);)if(n(i))return i}return null},bu=(e,t)=>{for(;e&&e!==t;){if(iu(e))return e;e=e.parentNode}return null},vu=(e,t,n)=>bu(e.container(),n)===bu(t.container(),n),yu=(e,t)=>{if(!t)return I.none();const n=t.container(),o=t.offset();return cu(n)?I.from(n.childNodes[o+e]):I.none()},Cu=(e,t)=>{var n;const o=(null!==(n=t.ownerDocument)&&void 0!==n?n:document).createRange();return e?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),o},wu=(e,t,n)=>bu(t,e)===bu(n,e),Eu=(e,t,n)=>{const o=e?"previousSibling":"nextSibling";let r=n;for(;r&&r!==t;){let e=r[o];if(e&&lu(e)&&(e=e[o]),su(e)||au(e)){if(wu(t,e,r))return e;break}if(mu(e))break;r=r.parentNode}return null},xu=T(Cu,!0),_u=T(Cu,!1),Su=(e,t,n)=>{let o;const r=T(Eu,!0,t),s=T(Eu,!1,t),a=n.startContainer,i=n.startOffset;if(si(a)){const e=uu(a)?a.parentNode:a,t=e.getAttribute("data-mce-caret");if("before"===t&&(o=e.nextSibling,ou(o)))return xu(o);if("after"===t&&(o=e.previousSibling,ou(o)))return _u(o)}if(!n.collapsed)return n;if(lr(a)){if(lu(a)){if(1===e){if(o=s(a),o)return xu(o);if(o=r(a),o)return _u(o)}if(-1===e){if(o=r(a),o)return _u(o);if(o=s(a),o)return xu(o)}return n}if(mi(a)&&i>=a.data.length-1)return 1===e&&(o=s(a),o)?xu(o):n;if(ui(a)&&i<=1)return-1===e&&(o=r(a),o)?_u(o):n;if(i===a.data.length)return o=s(a),o?xu(o):n;if(0===i)return o=r(a),o?_u(o):n}return n},ku=(e,t)=>yu(e?0:-1,t).filter(su),Nu=(e,t,n)=>{const o=Su(e,t,n);return-1===e?el.fromRangeStart(o):el.fromRangeEnd(o)},Ru=e=>I.from(e.getNode()).map(Cn),Au=(e,t)=>{let n=t;for(;n=e(n);)if(n.isVisible())return n;return n},Tu=(e,t)=>{const n=vu(e,t);return!(n||!gr(e.getNode()))||n},Ou=br,Bu=lr,Pu=Jo,Du=gr,Lu=Di,Mu=e=>Oi(e)||(e=>!!Li(e)&&!X(ce(e.getElementsByTagName("*")),((e,t)=>e||ki(t)),!1))(e),Iu=Mi,Fu=(e,t)=>e.hasChildNodes()&&t<e.childNodes.length?e.childNodes[t]:null,Uu=(e,t)=>{if(fu(e)){if(Lu(t.previousSibling)&&!Bu(t.previousSibling))return el.before(t);if(Bu(t))return el(t,0)}if(gu(e)){if(Lu(t.nextSibling)&&!Bu(t.nextSibling))return el.after(t);if(Bu(t))return el(t,t.data.length)}return gu(e)?Du(t)?el.before(t):el.after(t):el.before(t)},zu=(e,t,n)=>{let o,r,s,a;if(!Pu(n)||!t)return null;if(t.isEqual(el.after(n))&&n.lastChild){if(a=el.after(n.lastChild),gu(e)&&Lu(n.lastChild)&&Pu(n.lastChild))return Du(n.lastChild)?el.before(n.lastChild):a}else a=t;const i=a.container();let l=a.offset();if(Bu(i)){if(gu(e)&&l>0)return el(i,--l);if(fu(e)&&l<i.length)return el(i,++l);o=i}else{if(gu(e)&&l>0&&(r=Fu(i,l-1),Lu(r)))return!Mu(r)&&(s=hu(r,e,Iu,r),s)?Bu(s)?el(s,s.data.length):el.after(s):Bu(r)?el(r,r.data.length):el.before(r);if(fu(e)&&l<i.childNodes.length&&(r=Fu(i,l),Lu(r)))return Du(r)?((e,t)=>{const n=t.nextSibling;return n&&Lu(n)?Bu(n)?el(n,0):el.before(n):zu(1,el.after(t),e)})(n,r):!Mu(r)&&(s=hu(r,e,Iu,r),s)?Bu(s)?el(s,0):el.before(s):Bu(r)?el(r,0):el.after(r);o=r||a.getNode()}if(o&&(fu(e)&&a.isAtEnd()||gu(e)&&a.isAtStart())&&(o=hu(o,e,M,n,!0),Iu(o,n)))return Uu(e,o);r=o?hu(o,e,Iu,n):o;const d=De(Y(((e,t)=>{const n=[];let o=e;for(;o&&o!==t;)n.push(o),o=o.parentNode;return n})(i,n),Ou));return!d||r&&d.contains(r)?r?Uu(e,r):null:(a=fu(e)?el.after(d):el.before(d),a)},ju=e=>({next:t=>zu(1,t,e),prev:t=>zu(-1,t,e)}),Hu=e=>el.isTextPosition(e)?0===e.offset():Di(e.getNode()),$u=e=>{if(el.isTextPosition(e)){const t=e.container();return e.offset()===t.data.length}return Di(e.getNode(!0))},Vu=(e,t)=>!el.isTextPosition(e)&&!el.isTextPosition(t)&&e.getNode()===t.getNode(!0),qu=(e,t,n)=>{const o=ju(t);return I.from(e?o.next(n):o.prev(n))},Wu=(e,t,n)=>qu(e,t,n).bind((o=>vu(n,o,t)&&((e,t,n)=>{return e?!Vu(t,n)&&(o=t,!(!el.isTextPosition(o)&&gr(o.getNode())))&&$u(t)&&Hu(n):!Vu(n,t)&&Hu(t)&&$u(n);var o})(e,n,o)?qu(e,t,o):I.some(o))),Ku=(e,t,n,o)=>Wu(e,t,n).bind((n=>o(n)?Ku(e,t,n,o):I.some(n))),Yu=(e,t)=>{const n=e?t.firstChild:t.lastChild;return lr(n)?I.some(el(n,e?0:n.data.length)):n?Di(n)?I.some(e?el.before(n):gr(o=n)?el.before(o):el.after(o)):((e,t,n)=>{const o=e?el.before(n):el.after(n);return qu(e,t,o)})(e,t,n):I.none();var o},Gu=T(qu,!0),Xu=T(qu,!1),Zu=T(Yu,!0),Qu=T(Yu,!1),Ju="_mce_caret",em=e=>Jo(e)&&e.id===Ju,tm=(e,t)=>{let n=t;for(;n&&n!==e;){if(em(n))return n;n=n.parentNode}return null},nm=e=>_e(e,"name"),om=e=>Dt.isArray(e.start),rm=e=>!(!nm(e)&&b(e.forward))||e.forward,sm=(e,t)=>(Jo(t)&&e.isBlock(t)&&!t.innerHTML&&(t.innerHTML='<br data-mce-bogus="1" />'),t),am=(e,t)=>Qu(e).fold(L,(e=>(t.setStart(e.container(),e.offset()),t.setEnd(e.container(),e.offset()),!0))),im=(e,t,n)=>!(!(e=>!e.hasChildNodes())(t)||!tm(e,t)||(((e,t)=>{var n;const o=(null!==(n=e.ownerDocument)&&void 0!==n?n:document).createTextNode(ei);e.appendChild(o),t.setStart(o,0),t.setEnd(o,0)})(t,n),0)),lm=(e,t,n,o)=>{const r=n[t?"start":"end"],s=e.getRoot();if(r){let e=s,n=r[0];for(let t=r.length-1;e&&t>=1;t--){const n=e.childNodes;if(im(s,e,o))return!0;if(r[t]>n.length-1)return!!im(s,e,o)||am(e,o);e=n[r[t]]}lr(e)&&(n=Math.min(r[0],e.data.length)),Jo(e)&&(n=Math.min(r[0],e.childNodes.length)),t?o.setStart(e,n):o.setEnd(e,n)}return!0},dm=e=>lr(e)&&e.data.length>0,cm=(e,t,n)=>{const o=e.get(n.id+"_"+t),r=null==o?void 0:o.parentNode,s=n.keep;if(o&&r){let a,i;if("start"===t?s?o.hasChildNodes()?(a=o.firstChild,i=1):dm(o.nextSibling)?(a=o.nextSibling,i=0):dm(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)+1):(a=r,i=e.nodeIndex(o)):s?o.hasChildNodes()?(a=o.firstChild,i=1):dm(o.previousSibling)?(a=o.previousSibling,i=o.previousSibling.data.length):(a=r,i=e.nodeIndex(o)):(a=r,i=e.nodeIndex(o)),!s){const r=o.previousSibling,s=o.nextSibling;let l;for(Dt.each(Dt.grep(o.childNodes),(e=>{lr(e)&&(e.data=e.data.replace(/\uFEFF/g,""))}));l=e.get(n.id+"_"+t);)e.remove(l,!0);if(lr(s)&&lr(r)&&!Tt.browser.isOpera()){const t=r.data.length;r.appendData(s.data),e.remove(s),a=r,i=t}}return I.some(el(a,i))}return I.none()},um=(e,t,n)=>((e,t,n=!1)=>2===t?vl(ni,n,e):3===t?(e=>{const t=e.getRng();return{start:cl(e.dom.getRoot(),el.fromRangeStart(t)),end:cl(e.dom.getRoot(),el.fromRangeEnd(t)),forward:e.isForward()}})(e):t?(e=>({rng:e.getRng(),forward:e.isForward()}))(e):Cl(e,!1))(e,t,n),mm=(e,t)=>{((e,t)=>{const n=e.dom;if(t){if(om(t))return((e,t)=>{const n=e.createRng();return lm(e,!0,t,n)&&lm(e,!1,t,n)?I.some({range:n,forward:rm(t)}):I.none()})(n,t);if((e=>m(e.start))(t))return((e,t)=>{const n=I.from(ul(e.getRoot(),t.start)),o=I.from(ul(e.getRoot(),t.end));return It(n,o,((n,o)=>{const r=e.createRng();return r.setStart(n.container(),n.offset()),r.setEnd(o.container(),o.offset()),{range:r,forward:rm(t)}}))})(n,t);if((e=>_e(e,"id"))(t))return((e,t)=>{const n=cm(e,"start",t),o=cm(e,"end",t);return It(n,o.or(n),((n,o)=>{const r=e.createRng();return r.setStart(sm(e,n.container()),n.offset()),r.setEnd(sm(e,o.container()),o.offset()),{range:r,forward:rm(t)}}))})(n,t);if(nm(t))return((e,t)=>I.from(e.select(t.name)[t.index]).map((t=>{const n=e.createRng();return n.selectNode(t),{range:n,forward:!0}})))(n,t);if((e=>_e(e,"rng"))(t))return I.some({range:t.rng,forward:rm(t)})}return I.none()})(e,t).each((({range:t,forward:n})=>{e.setRng(t,n)}))},fm=e=>Jo(e)&&"SPAN"===e.tagName&&"bookmark"===e.getAttribute("data-mce-type"),gm=(pm=qo,e=>pm===e);var pm;const hm=e=>""!==e&&-1!==" \f\n\r\t\v".indexOf(e),bm=e=>!hm(e)&&!gm(e)&&!Wo(e),vm=e=>{const t=[];if(e)for(let n=0;n<e.rangeCount;n++)t.push(e.getRangeAt(n));return t},ym=(e,t)=>{const n=Uo(t,"td[data-mce-selected],th[data-mce-selected]");return n.length>0?n:(e=>Y((e=>te(e,(e=>{const t=Ei(e);return t?[Cn(t)]:[]})))(e),Xa))(e)},Cm=e=>ym(vm(e.selection.getSel()),Cn(e.getBody())),wm=(e,t)=>Jn(e,"table",t),Em=e=>Un(e).fold(N([e]),(t=>[e].concat(Em(t)))),xm=e=>zn(e).fold(N([e]),(t=>"br"===$t(t)?Pn(t).map((t=>[e].concat(xm(t)))).getOr([]):[e].concat(xm(t)))),_m=(e,t)=>It((e=>{const t=e.startContainer,n=e.startOffset;return lr(t)?0===n?I.some(Cn(t)):I.none():I.from(t.childNodes[n]).map(Cn)})(t),(e=>{const t=e.endContainer,n=e.endOffset;return lr(t)?n===t.data.length?I.some(Cn(t)):I.none():I.from(t.childNodes[n-1]).map(Cn)})(t),((t,n)=>{const o=Q(Em(e),T(Sn,t)),r=Q(xm(e),T(Sn,n));return o.isSome()&&r.isSome()})).getOr(!1),Sm=(e,t,n,o)=>{const r=n,s=new $o(n,r),a=Ce(e.schema.getMoveCaretBeforeOnEnterElements(),((e,t)=>!H(["td","th","table"],t.toLowerCase())));let i=n;do{if(lr(i)&&0!==Dt.trim(i.data).length)return void(o?t.setStart(i,0):t.setEnd(i,i.data.length));if(a[i.nodeName])return void(o?t.setStartBefore(i):"BR"===i.nodeName?t.setEndBefore(i):t.setEndAfter(i))}while(i=o?s.next():s.prev());"BODY"===r.nodeName&&(o?t.setStart(r,0):t.setEnd(r,r.childNodes.length))},km=e=>{const t=e.selection.getSel();return C(t)&&t.rangeCount>0},Nm=(e,t)=>{const n=Cm(e);n.length>0?q(n,(n=>{const o=n.dom,r=e.dom.createRng();r.setStartBefore(o),r.setEndAfter(o),t(r,!0)})):t(e.selection.getRng(),!1)},Rm=(e,t,n)=>{const o=Cl(e,t);n(o),e.moveToBookmark(o)},Am=e=>E(null==e?void 0:e.nodeType),Tm=e=>Jo(e)&&!fm(e)&&!em(e)&&!rr(e),Om=(e,t,n)=>{const{selection:o,dom:r}=e,s=o.getNode(),a=br(s);Rm(o,!0,(()=>{t()})),a&&br(s)&&r.isChildOf(s,e.getBody())?e.selection.select(s):n(o.getStart())&&Bm(r,o)},Bm=(e,t)=>{var n,o;const r=t.getRng(),{startContainer:s,startOffset:a}=r;if(!((e,t)=>{if(Tm(t)&&!/^(TD|TH)$/.test(t.nodeName)){const n=e.getAttrib(t,"data-mce-selected"),o=parseInt(n,10);return!isNaN(o)&&o>0}return!1})(e,t.getNode())&&Jo(s)){const i=s.childNodes,l=e.getRoot();let d;if(a<i.length){const t=i[a];d=new $o(t,null!==(n=e.getParent(t,e.isBlock))&&void 0!==n?n:l)}else{const t=i[i.length-1];d=new $o(t,null!==(o=e.getParent(t,e.isBlock))&&void 0!==o?o:l),d.next(!0)}for(let n=d.current();n;n=d.next()){if("false"===e.getContentEditable(n))return;if(lr(n)&&!Mm(n))return r.setStart(n,0),void t.setRng(r)}}},Pm=(e,t,n)=>{if(e){const o=t?"nextSibling":"previousSibling";for(e=n?e:e[o];e;e=e[o])if(Jo(e)||!Mm(e))return e}},Dm=(e,t)=>!!e.getTextBlockElements()[t.nodeName.toLowerCase()]||Zr(e,t),Lm=(e,t,n)=>e.schema.isValidChild(t,n),Mm=(e,t=!1)=>{if(C(e)&&lr(e)){const n=t?e.data.replace(/ /g,"\xa0"):e.data;return Yo(n)}return!1},Im=(e,t)=>{const n=e.dom;return Tm(t)&&"false"===n.getContentEditable(t)&&((e,t)=>{const n="[data-mce-cef-wrappable]",o=zd(e),r=Xe(o)?n:`${n},${o}`;return xn(Cn(t),r)})(e,t)&&0===n.select('[contenteditable="true"]',t).length},Fm=(e,t)=>w(e)?e(t):(C(t)&&(e=e.replace(/%(\w+)/g,((e,n)=>t[n]||e))),e),Um=(e,t)=>(t=t||"",e=""+((e=e||"").nodeName||e),t=""+(t.nodeName||t),e.toLowerCase()===t.toLowerCase()),zm=(e,t)=>{if(y(e))return null;{let n=String(e);return"color"!==t&&"backgroundColor"!==t||(n=Ws(n)),"fontWeight"===t&&700===e&&(n="bold"),"fontFamily"===t&&(n=n.replace(/[\'\"]/g,"").replace(/,\s+/g,",")),n}},jm=(e,t,n)=>{const o=e.getStyle(t,n);return zm(o,n)},Hm=(e,t)=>{let n;return e.getParent(t,(t=>!!Jo(t)&&(n=e.getStyle(t,"text-decoration"),!!n&&"none"!==n))),n},$m=(e,t,n)=>e.getParents(t,n,e.getRoot()),Vm=(e,t,n)=>{const o=e.formatter.get(t);return C(o)&&$(o,n)},qm=e=>Se(e,"block"),Wm=e=>Se(e,"selector"),Km=e=>Se(e,"inline"),Ym=e=>Wm(e)&&!1!==e.expand&&!Km(e),Gm=e=>(e=>{const t=[];let n=e;for(;n;){if(lr(n)&&n.data!==ei||n.childNodes.length>1)return[];Jo(n)&&t.push(n),n=n.firstChild}return t})(e).length>0,Xm=e=>em(e.dom)&&Gm(e.dom),Zm=fm,Qm=$m,Jm=Mm,ef=Dm,tf=(e,t)=>{let n=t;for(;n;){if(Jo(n)&&e.getContentEditable(n))return"false"===e.getContentEditable(n)?n:t;n=n.parentNode}return t},nf=(e,t,n,o)=>{const r=t.data;if(e){for(let e=n;e>0;e--)if(o(r.charAt(e-1)))return e}else for(let e=n;e<r.length;e++)if(o(r.charAt(e)))return e;return-1},of=(e,t,n)=>nf(e,t,n,(e=>gm(e)||hm(e))),rf=(e,t,n)=>nf(e,t,n,bm),sf=(e,t,n,o,r,s)=>{let a;const i=e.getParent(n,(t=>vr(t)||e.isBlock(t))),l=C(i)?i:t,d=(t,n,o)=>{const s=za(e),i=r?s.backwards:s.forwards;return I.from(i(t,n,((e,t)=>Zm(e.parentNode)?-1:(a=e,o(r,e,t))),l))};return d(n,o,of).bind((e=>s?d(e.container,e.offset+(r?-1:0),rf):I.some(e))).orThunk((()=>a?I.some({container:a,offset:r?0:a.length}):I.none()))},af=(e,t,n,o,r)=>{const s=o[r];lr(o)&&Xe(o.data)&&s&&(o=s);const a=Qm(e,o);for(let o=0;o<a.length;o++)for(let r=0;r<t.length;r++){const s=t[r];if((!C(s.collapsed)||s.collapsed===n.collapsed)&&Wm(s)&&e.is(a[o],s.selector))return a[o]}return o},lf=(e,t,n,o)=>{var r;let s=n;const a=e.getRoot(),i=t[0];if(qm(i)&&(s=i.wrapper?null:e.getParent(n,i.block,a)),!s){const t=null!==(r=e.getParent(n,"LI,TD,TH,SUMMARY"))&&void 0!==r?r:a;s=e.getParent(lr(n)?n.parentNode:n,(t=>t!==a&&ef(e.schema,t)),t)}if(s&&qm(i)&&i.wrapper&&(s=Qm(e,s,"ul,ol").reverse()[0]||s),!s)for(s=n;s&&s[o]&&!e.isBlock(s[o])&&(s=s[o],!Um(s,"br")););return s||n},df=(e,t,n,o)=>{const r=n.parentNode;return!C(n[o])&&(!(r!==t&&!y(r)&&!e.isBlock(r))||df(e,t,r,o))},cf=(e,t,n,o,r,s)=>{let a=n;const i=r?"previousSibling":"nextSibling",l=e.getRoot();if(lr(n)&&!Jm(n)&&(r?o>0:o<n.data.length))return n;for(;a;){if(vr(a))return n;if(!t[0].block_expand&&e.isBlock(a))return s?a:n;for(let t=a[i];t;t=t[i]){const n=lr(t)&&!df(e,l,t,i);if(!Zm(t)&&(!gr(d=t)||!d.getAttribute("data-mce-bogus")||d.nextSibling)&&!Jm(t,n))return a}if(a===l||a.parentNode===l){n=a;break}a=a.parentNode}var d;return n},uf=e=>Zm(e.parentNode)||Zm(e),mf=(e,t,n,o={})=>{const{includeTrailingSpace:r=!1,expandToBlock:s=!0}=o,a=e.getParent(t.commonAncestorContainer,(e=>vr(e))),i=C(a)?a:e.getRoot();let{startContainer:l,startOffset:d,endContainer:c,endOffset:u}=t;const m=n[0];return Jo(l)&&l.hasChildNodes()&&(l=xi(l,d),lr(l)&&(d=0)),Jo(c)&&c.hasChildNodes()&&(c=xi(c,t.collapsed?u:u-1),lr(c)&&(u=c.data.length)),l=tf(e,l),c=tf(e,c),uf(l)&&(l=Zm(l)?l:l.parentNode,l=t.collapsed?l.previousSibling||l:l.nextSibling||l,lr(l)&&(d=t.collapsed?l.length:0)),uf(c)&&(c=Zm(c)?c:c.parentNode,c=t.collapsed?c.nextSibling||c:c.previousSibling||c,lr(c)&&(u=t.collapsed?0:c.length)),t.collapsed&&(sf(e,i,l,d,!0,r).each((({container:e,offset:t})=>{l=e,d=t})),sf(e,i,c,u,!1,r).each((({container:e,offset:t})=>{c=e,u=t}))),(Km(m)||m.block_expand)&&(Km(m)&&lr(l)&&0!==d||(l=cf(e,n,l,d,!0,s)),Km(m)&&lr(c)&&u!==c.data.length||(c=cf(e,n,c,u,!1,s))),Ym(m)&&(l=af(e,n,t,l,"previousSibling"),c=af(e,n,t,c,"nextSibling")),(qm(m)||Wm(m))&&(l=lf(e,n,l,"previousSibling"),c=lf(e,n,c,"nextSibling"),qm(m)&&(e.isBlock(l)||(l=cf(e,n,l,d,!0,s),lr(l)&&(d=0)),e.isBlock(c)||(c=cf(e,n,c,u,!1,s),lr(c)&&(u=c.data.length)))),Jo(l)&&l.parentNode&&(d=e.nodeIndex(l),l=l.parentNode),Jo(c)&&c.parentNode&&(u=e.nodeIndex(c)+1,c=c.parentNode),{startContainer:l,startOffset:d,endContainer:c,endOffset:u}},ff=(e,t,n)=>{var o;const r=t.startOffset,s=xi(t.startContainer,r),a=t.endOffset,i=xi(t.endContainer,a-1),l=e=>{const t=e[0];lr(t)&&t===s&&r>=t.data.length&&e.splice(0,1);const n=e[e.length-1];return 0===a&&e.length>0&&n===i&&lr(n)&&e.splice(e.length-1,1),e},d=(e,t,n)=>{const o=[];for(;e&&e!==n;e=e[t])o.push(e);return o},c=(t,n)=>e.getParent(t,(e=>e.parentNode===n),n),u=(e,t,o)=>{const r=o?"nextSibling":"previousSibling";for(let s=e,a=s.parentNode;s&&s!==t;s=a){a=s.parentNode;const t=d(s===e?s:s[r],r);t.length&&(o||t.reverse(),n(l(t)))}};if(s===i)return n(l([s]));const m=null!==(o=e.findCommonAncestor(s,i))&&void 0!==o?o:e.getRoot();if(e.isChildOf(s,i))return u(s,m,!0);if(e.isChildOf(i,s))return u(i,m);const f=c(s,m)||s,g=c(i,m)||i;u(s,f,!0);const p=d(f===s?f:f.nextSibling,"nextSibling",g===i?g.nextSibling:g);p.length&&n(l(p)),u(i,g)},gf=['pre[class*=language-][contenteditable="false"]',"figure.image","div[data-ephox-embed-iri]","div.tiny-pageembed","div.mce-toc","div[data-mce-toc]","div.mce-footnotes"],pf=(e,t,n,o,r,s)=>{const{uid:a=t,...i}=n;mn(e,Ea()),Jt(e,`${_a()}`,a),Jt(e,`${xa()}`,o);const{attributes:l={},classes:d=[]}=r(a,i);if(en(e,l),((e,t)=>{q(t,(t=>{mn(e,t)}))})(e,d),s){d.length>0&&Jt(e,`${ka()}`,d.join(","));const t=fe(l);t.length>0&&Jt(e,`${Na()}`,t.join(","))}},hf=(e,t,n,o,r)=>{const s=vn("span",e);return pf(s,t,n,o,r,!1),s},bf=(e,t,n,o,r,s)=>{const a=[],i=hf(e.getDoc(),n,s,o,r),l=Dr(),d=()=>{l.clear()},c=e=>{q(e,u)},u=t=>{switch(((e,t,n,o)=>On(t).fold((()=>"skipping"),(r=>"br"===o||(e=>Yt(e)&&Ha(e)===ei)(t)?"valid":(e=>Kt(e)&&pn(e,Ea()))(t)?"existing":em(t.dom)?"caret":$(gf,(e=>xn(t,e)))?"valid-block":Lm(e,n,o)&&Lm(e,$t(r),n)?"valid":"invalid-child")))(e,t,"span",$t(t))){case"invalid-child":{d();const e=In(t);c(e),d();break}case"valid-block":d(),pf(t,n,s,o,r,!0);break;case"valid":{const e=l.get().getOrThunk((()=>{const e=Ia(i);return a.push(e),l.set(e),e}));yo(t,e);break}}};return ff(e.dom,t,(e=>{d(),(e=>{const t=V(e,Cn);c(t)})(e)})),a},vf=e=>{const t=(()=>{const e={};return{register:(t,n)=>{e[t]={name:t,settings:n}},lookup:t=>xe(e,t).map((e=>e.settings)),getNames:()=>fe(e)}})();((e,t)=>{const n=xa(),o=e=>I.from(e.attr(n)).bind(t.lookup),r=e=>{var t,n;e.attr(_a(),null),e.attr(xa(),null),e.attr(Sa(),null);const o=I.from(e.attr(Na())).map((e=>e.split(","))).getOr([]),r=I.from(e.attr(ka())).map((e=>e.split(","))).getOr([]);q(o,(t=>e.attr(t,null)));const s=null!==(n=null===(t=e.attr("class"))||void 0===t?void 0:t.split(" "))&&void 0!==n?n:[],a=re(s,[Ea()].concat(r));e.attr("class",a.length>0?a.join(" "):null),e.attr(ka(),null),e.attr(Na(),null)};e.serializer.addTempAttr(Sa()),e.serializer.addAttributeFilter(n,(e=>{for(const t of e)o(t).each((e=>{!1===e.persistent&&("span"===t.name?t.unwrap():r(t))}))}))})(e,t);const n=((e,t)=>{const n=Br({}),o=()=>({listeners:[],previous:Dr()}),r=(e,t)=>{s(e,(e=>(t(e),e)))},s=(e,t)=>{const r=n.get(),s=t(xe(r,e).getOrThunk(o));r[e]=s,n.set(r)},a=(t,n)=>{q(Oa(e,t),(e=>{n?Jt(e,Sa(),"true"):rn(e,Sa())}))},i=wa((()=>{const n=ae(t.getNames());q(n,(t=>{s(t,(n=>{const o=n.previous.get();return Aa(e,I.some(t)).fold((()=>{o.each((e=>{(e=>{r(e,(t=>{q(t.listeners,(t=>t(!1,e)))}))})(t),n.previous.clear(),a(e,!1)}))}),(({uid:e,name:t,elements:s})=>{Lt(o,e)||(o.each((e=>a(e,!1))),((e,t,n)=>{r(e,(o=>{q(o.listeners,(o=>o(!0,e,{uid:t,nodes:V(n,(e=>e.dom))})))}))})(t,e,s),n.previous.set(e),a(e,!0))})),{previous:n.previous,listeners:n.listeners}}))}))}),30);return e.on("remove",(()=>{i.cancel()})),e.on("NodeChange",(()=>{i.throttle()})),{addListener:(e,t)=>{s(e,(e=>({previous:e.previous,listeners:e.listeners.concat([t])})))}}})(e,t),o=Zt("span"),r=e=>{q(e,(e=>{o(e)?xo(e):(e=>{gn(e,Ea()),rn(e,`${_a()}`),rn(e,`${xa()}`),rn(e,`${Sa()}`);const t=nn(e,`${Na()}`).map((e=>e.split(","))).getOr([]),n=nn(e,`${ka()}`).map((e=>e.split(","))).getOr([]);var o;q(t,(t=>rn(e,t))),o=e,q(n,(e=>{gn(o,e)})),rn(e,`${ka()}`),rn(e,`${Na()}`)})(e)}))};return{register:(e,n)=>{t.register(e,n)},annotate:(n,o)=>{t.lookup(n).each((t=>{((e,t,n,o)=>{e.undoManager.transact((()=>{const r=e.selection,s=r.getRng(),a=Cm(e).length>0,i=La("mce-annotation");if(s.collapsed&&!a&&((e,t)=>{const n=mf(e.dom,t,[{inline:"span"}]);t.setStart(n.startContainer,n.startOffset),t.setEnd(n.endContainer,n.endOffset),e.selection.setRng(t)})(e,s),r.getRng().collapsed&&!a){const s=hf(e.getDoc(),i,o,t,n.decorate);ko(s,qo),r.getRng().insertNode(s.dom),r.select(s.dom)}else Rm(r,!1,(()=>{Nm(e,(r=>{bf(e,r,i,t,n.decorate,o)}))}))}))})(e,n,t,o)}))},annotationChanged:(e,t)=>{n.addListener(e,t)},remove:t=>{Aa(e,I.some(t)).each((({elements:t})=>{const n=e.selection.getBookmark();r(t),e.selection.moveToBookmark(n)}))},removeAll:t=>{const n=e.selection.getBookmark();pe(Ba(e,t),((e,t)=>{r(e)})),e.selection.moveToBookmark(n)},getAll:t=>{const n=Ba(e,t);return he(n,(e=>V(e,(e=>e.dom))))}}},yf=e=>({getBookmark:T(um,e),moveToBookmark:T(mm,e)});yf.isBookmarkNode=fm;const Cf=(e,t,n)=>!n.collapsed&&$(n.getClientRects(),(n=>((e,t,n)=>t>=e.left&&t<=e.right&&n>=e.top&&n<=e.bottom)(n,e,t))),wf=()=>Cn(document),Ef=(e,t=!1)=>e.dom.focus({preventScroll:t}),xf=e=>{const t=$n(e).dom;return e.dom===t.activeElement},_f=(e=wf())=>I.from(e.dom.activeElement).map(Cn),Sf=(e,t,n,o)=>({start:e,soffset:t,finish:n,foffset:o}),kf=Sl([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Nf={before:kf.before,on:kf.on,after:kf.after,cata:(e,t,n,o)=>e.fold(t,n,o),getStart:e=>e.fold(R,R,R)},Rf=Sl([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Af={domRange:Rf.domRange,relative:Rf.relative,exact:Rf.exact,exactFromRange:e=>Rf.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Cn(e.startContainer),relative:(e,t)=>Nf.getStart(e),exact:(e,t,n,o)=>e}))(e);return An(t)},range:Sf},Tf=(e,t)=>{const n=Yt(t)?Ha(t).length:In(t).length+1;return e>n?n:e<0?0:e},Of=e=>Af.range(e.start,Tf(e.soffset,e.start),e.finish,Tf(e.foffset,e.finish)),Bf=(e,t)=>!Qo(t.dom)&&(kn(e,t)||Sn(e,t)),Pf=e=>t=>Bf(e,t.start)&&Bf(e,t.finish),Df=e=>Af.range(Cn(e.startContainer),e.startOffset,Cn(e.endContainer),e.endOffset),Lf=e=>{const t=document.createRange();try{return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),I.some(t)}catch(e){return I.none()}},Mf=e=>{const t=(e=>e.inline||Tt.browser.isFirefox())(e)?(n=Cn(e.getBody()),(e=>{const t=e.getSelection();return(t&&0!==t.rangeCount?I.from(t.getRangeAt(0)):I.none()).map(Df)})(An(n).dom).filter(Pf(n))):I.none();var n;e.bookmark=t.isSome()?t:e.bookmark},If=e=>(e.bookmark?e.bookmark:I.none()).bind((t=>{return n=Cn(e.getBody()),o=t,I.from(o).filter(Pf(n)).map(Of);var n,o})).bind(Lf),Ff={isEditorUIElement:e=>{const t=e.className.toString();return-1!==t.indexOf("tox-")||-1!==t.indexOf("mce-")}},Uf={setEditorTimeout:(e,t,n)=>((e,t)=>(E(t)||(t=0),window.setTimeout(e,t)))((()=>{e.removed||t()}),n),setEditorInterval:(e,t,n)=>{const o=((e,t)=>(E(t)||(t=0),window.setInterval(e,t)))((()=>{e.removed?window.clearInterval(o):t()}),n);return o}};let zf;const jf=ma.DOM,Hf=e=>{const t=e.classList;return void 0!==t&&(t.contains("tox-edit-area")||t.contains("tox-edit-area__iframe")||t.contains("mce-content-body"))},$f=(e,t)=>{const n=jd(e),o=jf.getParent(t,(t=>(e=>Jo(e)&&Ff.isEditorUIElement(e))(t)||!!n&&e.dom.is(t,n)));return null!==o},Vf=e=>{try{const t=$n(Cn(e.getElement()));return _f(t).fold((()=>document.body),(e=>e.dom))}catch(e){return document.body}},qf=(e,t)=>{const n=t.editor;(e=>{const t=Ca((()=>{Mf(e)}),0);e.on("init",(()=>{e.inline&&((e,t)=>{const n=()=>{t.throttle()};ma.DOM.bind(document,"mouseup",n),e.on("remove",(()=>{ma.DOM.unbind(document,"mouseup",n)}))})(e,t),((e,t)=>{((e,t)=>{e.on("mouseup touchend",(e=>{t.throttle()}))})(e,t),e.on("keyup NodeChange AfterSetSelectionRange",(t=>{(e=>"nodechange"===e.type&&e.selectionChange)(t)||Mf(e)}))})(e,t)})),e.on("remove",(()=>{t.cancel()}))})(n);const o=(e,t)=>{Ac(e)&&!0!==e.inline&&t(Cn(e.getContainer()),"tox-edit-focus")};n.on("focusin",(()=>{const t=e.focusedEditor;Hf(Vf(n))&&o(n,mn),t!==n&&(t&&t.dispatch("blur",{focusedEditor:n}),e.setActive(n),e.focusedEditor=n,n.dispatch("focus",{blurredEditor:t}),n.focus(!0))})),n.on("focusout",(()=>{Uf.setEditorTimeout(n,(()=>{const t=e.focusedEditor;Hf(Vf(n))&&t===n||o(n,gn),$f(n,Vf(n))||t!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null)}))})),zf||(zf=t=>{const n=e.activeEditor;n&&Wn(t).each((t=>{const o=t;o.ownerDocument===document&&(o===document.body||$f(n,o)||e.focusedEditor!==n||(n.dispatch("blur",{focusedEditor:null}),e.focusedEditor=null))}))},jf.bind(document,"focusin",zf))},Wf=(e,t)=>{e.focusedEditor===t.editor&&(e.focusedEditor=null),!e.activeEditor&&zf&&(jf.unbind(document,"focusin",zf),zf=null)},Kf=(e,t)=>{((e,t)=>(e=>e.collapsed?I.from(xi(e.startContainer,e.startOffset)).map(Cn):I.none())(t).bind((t=>Ga(t)?I.some(t):kn(e,t)?I.none():I.some(e))))(Cn(e.getBody()),t).bind((e=>Zu(e.dom))).fold((()=>{e.selection.normalize()}),(t=>e.selection.setRng(t.toRange())))},Yf=e=>{if(e.setActive)try{e.setActive()}catch(t){e.focus()}else e.focus()},Gf=e=>e.inline?(e=>{const t=e.getBody();return t&&(n=Cn(t),xf(n)||(o=n,_f($n(o)).filter((e=>o.dom.contains(e.dom)))).isSome());var n,o})(e):(e=>C(e.iframeElement)&&xf(Cn(e.iframeElement)))(e),Xf=e=>Gf(e)||(e=>{const t=$n(Cn(e.getElement()));return _f(t).filter((t=>!Hf(t.dom)&&$f(e,t.dom))).isSome()})(e),Zf=e=>e.editorManager.setActive(e),Qf={BACKSPACE:8,DELETE:46,DOWN:40,ENTER:13,ESC:27,LEFT:37,RIGHT:39,SPACEBAR:32,TAB:9,UP:38,PAGE_UP:33,PAGE_DOWN:34,END:35,HOME:36,modifierPressed:e=>e.shiftKey||e.ctrlKey||e.altKey||Qf.metaKeyPressed(e),metaKeyPressed:e=>Tt.os.isMacOS()||Tt.os.isiOS()?e.metaKey:e.ctrlKey&&!e.altKey},Jf="data-mce-selected",eg=Math.abs,tg=Math.round,ng={nw:[0,0,-1,-1],ne:[1,0,1,-1],se:[1,1,1,1],sw:[0,1,-1,1]},og=(e,t)=>{const n=t.dom,o=t.getDoc(),r=document,s=t.getBody();let a,i,l,d,c,u,m,f,g,p,h,b,v,y,w;const E=e=>C(e)&&(pr(e)||n.is(e,"figure.image")),x=e=>wr(e)||n.hasClass(e,"mce-preview-object"),_=e=>{const n=e.target;((e,t)=>{if((e=>"longpress"===e.type||0===e.type.indexOf("touch"))(e)){const n=e.touches[0];return E(e.target)&&!Cf(n.clientX,n.clientY,t)}return E(e.target)&&!Cf(e.clientX,e.clientY,t)})(e,t.selection.getRng())&&!e.isDefaultPrevented()&&t.selection.select(n)},S=e=>n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?[e,e.firstElementChild]:n.is(e,"figure.image")?[e.querySelector("img")]:[e],k=e=>{const o=Nd(t);return!(!o||t.mode.isReadOnly())&&"false"!==e.getAttribute("data-mce-resize")&&e!==t.getBody()&&(n.hasClass(e,"mce-preview-object")&&C(e.firstElementChild)?xn(Cn(e.firstElementChild),o):xn(Cn(e),o))},N=(e,o,r)=>{if(C(r)){const s=S(e);q(s,(e=>{e.style[o]||!t.schema.isValid(e.nodeName.toLowerCase(),o)?n.setStyle(e,o,r):n.setAttrib(e,o,""+r)}))}},R=(e,t,n)=>{N(e,"width",t),N(e,"height",n)},A=e=>{let o,r,c,C,_;o=e.screenX-u,r=e.screenY-m,b=o*d[2]+f,v=r*d[3]+g,b=b<5?5:b,v=v<5?5:v,c=(E(a)||x(a))&&!1!==Rd(t)?!Qf.modifierPressed(e):Qf.modifierPressed(e),c&&(eg(o)>eg(r)?(v=tg(b*p),b=tg(v/p)):(b=tg(v/p),v=tg(b*p))),R(i,b,v),C=d.startPos.x+o,_=d.startPos.y+r,C=C>0?C:0,_=_>0?_:0,n.setStyles(l,{left:C,top:_,display:"block"}),l.innerHTML=b+" &times; "+v,o=s.scrollWidth-y,r=s.scrollHeight-w,o+r!==0&&n.setStyles(l,{left:C-o,top:_-r}),h||(((e,t,n,o,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:n,height:o,origin:r})})(t,a,f,g,"corner-"+d.name),h=!0)},T=()=>{const e=h;h=!1,e&&(N(a,"width",b),N(a,"height",v)),n.unbind(o,"mousemove",A),n.unbind(o,"mouseup",T),r!==o&&(n.unbind(r,"mousemove",A),n.unbind(r,"mouseup",T)),n.remove(i),n.remove(l),n.remove(c),O(a),e&&(((e,t,n,o,r)=>{e.dispatch("ObjectResized",{target:t,width:n,height:o,origin:r})})(t,a,b,v,"corner-"+d.name),n.setAttrib(a,"style",n.getAttrib(a,"style"))),t.nodeChanged()},O=e=>{M();const h=n.getPos(e,s),C=h.x,E=h.y,_=e.getBoundingClientRect(),N=_.width||_.right-_.left,O=_.height||_.bottom-_.top;a!==e&&(P(),a=e,b=v=0);const B=t.dispatch("ObjectSelected",{target:e});k(e)&&!B.isDefaultPrevented()?pe(ng,((e,t)=>{let h=n.get("mceResizeHandle"+t);h&&n.remove(h),h=n.add(s,"div",{id:"mceResizeHandle"+t,"data-mce-bogus":"all",class:"mce-resizehandle",unselectable:!0,style:"cursor:"+t+"-resize; margin:0; padding:0"}),n.bind(h,"mousedown",(h=>{h.stopImmediatePropagation(),h.preventDefault(),(h=>{const b=S(a)[0];u=h.screenX,m=h.screenY,f=b.clientWidth,g=b.clientHeight,p=g/f,d=e,d.name=t,d.startPos={x:N*e[0]+C,y:O*e[1]+E},y=s.scrollWidth,w=s.scrollHeight,c=n.add(s,"div",{class:"mce-resize-backdrop","data-mce-bogus":"all"}),n.setStyles(c,{position:"fixed",left:"0",top:"0",width:"100%",height:"100%"}),i=((e,t)=>{if(x(t))return e.create("img",{src:Tt.transparentSrc});if(sr(t)){const n=$e(d.name,"n")?le:de,o=t.cloneNode(!0);return n(e.select("tr",o)).each((t=>{const n=e.select("td,th",t);e.setStyle(t,"height",null),q(n,(t=>e.setStyle(t,"height",null)))})),o}return t.cloneNode(!0)})(n,a),n.addClass(i,"mce-clonedresizable"),n.setAttrib(i,"data-mce-bogus","all"),i.contentEditable="false",n.setStyles(i,{left:C,top:E,margin:0}),R(i,N,O),i.removeAttribute(Jf),s.appendChild(i),n.bind(o,"mousemove",A),n.bind(o,"mouseup",T),r!==o&&(n.bind(r,"mousemove",A),n.bind(r,"mouseup",T)),l=n.add(s,"div",{class:"mce-resize-helper","data-mce-bogus":"all"},f+" &times; "+g)})(h)})),e.elm=h,n.setStyles(h,{left:N*e[0]+C-h.offsetWidth/2,top:O*e[1]+E-h.offsetHeight/2})})):P(!1)},B=Ca(O,0),P=(e=!0)=>{B.cancel(),M(),a&&e&&a.removeAttribute(Jf),pe(ng,((e,t)=>{const o=n.get("mceResizeHandle"+t);o&&(n.unbind(o),n.remove(o))}))},D=(e,t)=>n.isChildOf(e,t),L=o=>{if(h||t.removed||t.composing)return;const r="mousedown"===o.type?o.target:e.getNode(),a=to(Cn(r),"table,img,figure.image,hr,video,span.mce-preview-object,details").map((e=>e.dom)).filter((e=>n.isEditable(e.parentElement)||"IMG"===e.nodeName&&n.isEditable(e))).getOrUndefined(),i=C(a)?n.getAttrib(a,Jf,"1"):"1";if(q(n.select(`img[${Jf}],hr[${Jf}]`),(e=>{e.removeAttribute(Jf)})),C(a)&&D(a,s)&&Xf(t)){I();const t=e.getStart(!0);if(D(t,a)&&D(e.getEnd(!0),a))return n.setAttrib(a,Jf,i),void B.throttle(a)}P()},M=()=>{pe(ng,(e=>{e.elm&&(n.unbind(e.elm),delete e.elm)}))},I=()=>{try{t.getDoc().execCommand("enableObjectResizing",!1,"false")}catch(e){}};return t.on("init",(()=>{I(),t.on("NodeChange ResizeEditor ResizeWindow ResizeContent drop",L),t.on("keyup compositionend",(e=>{a&&"TABLE"===a.nodeName&&L(e)})),t.on("hide blur",P),t.on("contextmenu longpress",_,!0)})),t.on("remove",M),{isResizable:k,showResizeRect:O,hideResizeRect:P,updateResizeRect:L,destroy:()=>{B.cancel(),a=i=c=null}}},rg=(e,t,n)=>{const o=e.document.createRange();var r;return r=o,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,n)=>{e.setEnd(t.dom,n)}),(t=>{e.setEndAfter(t.dom)}))})(o,n),o},sg=(e,t,n,o,r)=>{const s=e.document.createRange();return s.setStart(t.dom,n),s.setEnd(o.dom,r),s},ag=Sl([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),ig=(e,t,n)=>t(Cn(n.startContainer),n.startOffset,Cn(n.endContainer),n.endOffset);ag.ltr,ag.rtl;const lg=(e,t)=>{const n=$t(e);return"input"===n?Nf.after(e):H(["br","img"],n)?0===t?Nf.before(e):Nf.after(e):Nf.on(e,t)},dg=(e,t)=>{const n=e.fold(Nf.before,lg,Nf.after),o=t.fold(Nf.before,lg,Nf.after);return Af.relative(n,o)},cg=(e,t,n,o)=>{const r=lg(e,t),s=lg(n,o);return Af.relative(r,s)},ug=(e,t)=>{const n=(t||document).createDocumentFragment();return q(e,(e=>{n.appendChild(e.dom)})),Cn(n)},mg=e=>{const t=Af.getWin(e).dom,n=(e,n,o,r)=>sg(t,e,n,o,r),o=(e=>e.match({domRange:e=>{const t=Cn(e.startContainer),n=Cn(e.endContainer);return cg(t,e.startOffset,n,e.endOffset)},relative:dg,exact:cg}))(e);return((e,t)=>{const n=((e,t)=>t.match({domRange:e=>({ltr:N(e),rtl:I.none}),relative:(t,n)=>({ltr:Le((()=>rg(e,t,n))),rtl:Le((()=>I.some(rg(e,n,t))))}),exact:(t,n,o,r)=>({ltr:Le((()=>sg(e,t,n,o,r))),rtl:Le((()=>I.some(sg(e,o,r,t,n))))})}))(e,t);return((e,t)=>{const n=t.ltr();return n.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>ag.rtl(Cn(e.endContainer),e.endOffset,Cn(e.startContainer),e.startOffset))).getOrThunk((()=>ig(0,ag.ltr,n))):ig(0,ag.ltr,n)})(0,n)})(t,o).match({ltr:n,rtl:n})},fg=(e,t,n)=>((e,t,n)=>((e,t,n)=>e.caretPositionFromPoint?((e,t,n)=>{var o;return I.from(null===(o=e.caretPositionFromPoint)||void 0===o?void 0:o.call(e,t,n)).bind((t=>{if(null===t.offsetNode)return I.none();const n=e.createRange();return n.setStart(t.offsetNode,t.offset),n.collapse(),I.some(n)}))})(e,t,n):e.caretRangeFromPoint?((e,t,n)=>{var o;return I.from(null===(o=e.caretRangeFromPoint)||void 0===o?void 0:o.call(e,t,n))})(e,t,n):I.none())(e.document,t,n).map((e=>Sf(Cn(e.startContainer),e.startOffset,Cn(e.endContainer),e.endOffset))))(e,t,n),gg=(e,t,n)=>{const o=An(Cn(n));return fg(o.dom,e,t).map((e=>{const t=n.createRange();return t.setStart(e.start.dom,e.soffset),t.setEnd(e.finish.dom,e.foffset),t})).getOrUndefined()},pg=(e,t)=>C(e)&&C(t)&&e.startContainer===t.startContainer&&e.startOffset===t.startOffset&&e.endContainer===t.endContainer&&e.endOffset===t.endOffset,hg=(e,t,n)=>null!==((e,t,n)=>{let o=e;for(;o&&o!==t;){if(n(o))return o;o=o.parentNode}return null})(e,t,n),bg=(e,t,n)=>hg(e,t,(e=>e.nodeName===n)),vg=(e,t)=>ii(e)&&!hg(e,t,em),yg=(e,t,n)=>{const o=t.parentNode;if(o){const r=new $o(t,e.getParent(o,e.isBlock)||e.getRoot());let s;for(;s=r[n?"prev":"next"]();)if(gr(s))return!0}return!1},Cg=(e,t,n,o,r)=>{const s=e.getRoot(),a=e.schema.getNonEmptyElements(),i=r.parentNode;let l,d;if(!i)return I.none();const c=e.getParent(i,e.isBlock)||s;if(o&&gr(r)&&t&&e.isEmpty(c))return I.some(el(i,e.nodeIndex(r)));const u=new $o(r,c);for(;d=u[o?"prev":"next"]();){if("false"===e.getContentEditableParent(d)||vg(d,s))return I.none();if(lr(d)&&d.data.length>0)return bg(d,s,"A")?I.none():I.some(el(d,o?d.data.length:0));if(e.isBlock(d)||a[d.nodeName.toLowerCase()])return I.none();l=d}return ur(l)?I.none():n&&l?I.some(el(l,0)):I.none()},wg=(e,t,n,o)=>{const r=e.getRoot();let s,a=!1,i=n?o.startContainer:o.endContainer,l=n?o.startOffset:o.endOffset;const d=Jo(i)&&l===i.childNodes.length,c=e.schema.getNonEmptyElements();let u=n;if(ii(i))return I.none();if(Jo(i)&&l>i.childNodes.length-1&&(u=!1),mr(i)&&(i=r,l=0),i===r){if(u&&(s=i.childNodes[l>0?l-1:0],s)){if(ii(s))return I.none();if(c[s.nodeName]||sr(s))return I.none()}if(i.hasChildNodes()){if(l=Math.min(!u&&l>0?l-1:l,i.childNodes.length-1),i=i.childNodes[l],l=lr(i)&&d?i.data.length:0,!t&&i===r.lastChild&&sr(i))return I.none();if(((e,t)=>{let n=t;for(;n&&n!==e;){if(br(n))return!0;n=n.parentNode}return!1})(r,i)||ii(i))return I.none();if(xr(i))return I.none();if(i.hasChildNodes()&&!sr(i)){s=i;const t=new $o(i,r);do{if(br(s)||ii(s)){a=!1;break}if(lr(s)&&s.data.length>0){l=u?0:s.data.length,i=s,a=!0;break}if(c[s.nodeName.toLowerCase()]&&!Cr(s)){l=e.nodeIndex(s),i=s.parentNode,u||l++,a=!0;break}}while(s=u?t.next():t.prev())}}}return t&&(lr(i)&&0===l&&Cg(e,d,t,!0,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),Jo(i)&&(s=i.childNodes[l],s||(s=i.childNodes[l-1]),!s||!gr(s)||(e=>{var t;return"A"===(null===(t=e.previousSibling)||void 0===t?void 0:t.nodeName)})(s)||yg(e,s,!1)||yg(e,s,!0)||Cg(e,d,t,!0,s).each((e=>{i=e.container(),l=e.offset(),a=!0})))),u&&!t&&lr(i)&&l===i.data.length&&Cg(e,d,t,!1,i).each((e=>{i=e.container(),l=e.offset(),a=!0})),a&&i?I.some(el(i,l)):I.none()},Eg=(e,t)=>{const n=t.collapsed,o=t.cloneRange(),r=el.fromRangeStart(t);return wg(e,n,!0,o).each((e=>{n&&el.isAbove(r,e)||o.setStart(e.container(),e.offset())})),n||wg(e,n,!1,o).each((e=>{o.setEnd(e.container(),e.offset())})),n&&o.collapse(!0),pg(t,o)?I.none():I.some(o)},xg=(e,t)=>e.splitText(t),_g=e=>{let t=e.startContainer,n=e.startOffset,o=e.endContainer,r=e.endOffset;if(t===o&&lr(t)){if(n>0&&n<t.data.length)if(o=xg(t,n),t=o.previousSibling,r>n){r-=n;const e=xg(o,r).previousSibling;t=o=e,r=e.data.length,n=0}else r=0}else if(lr(t)&&n>0&&n<t.data.length&&(t=xg(t,n),n=0),lr(o)&&r>0&&r<o.data.length){const e=xg(o,r).previousSibling;o=e,r=e.data.length}return{startContainer:t,startOffset:n,endContainer:o,endOffset:r}},Sg=e=>({walk:(t,n)=>ff(e,t,n),split:_g,expand:(t,n={type:"word"})=>{if("word"===n.type){const n=mf(e,t,[{inline:"span"}],{includeTrailingSpace:!1,expandToBlock:!1}),o=e.createRng();return o.setStart(n.startContainer,n.startOffset),o.setEnd(n.endContainer,n.endOffset),o}return t},normalize:t=>Eg(e,t).fold(L,(e=>(t.setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),!0)))});Sg.compareRanges=pg,Sg.getCaretRangeFromPoint=gg,Sg.getSelectedNode=Ei,Sg.getNode=xi;const kg=(e=>{const t=t=>{const n=(e=>{const t=e.dom;return Yn(e)?t.getBoundingClientRect().height:t.offsetHeight})(t);if(n<=0||null===n){const n=co(t,e);return parseFloat(n)||0}return n},n=(e,t)=>X(t,((t,n)=>{const o=co(e,n),r=void 0===o?0:parseInt(o,10);return isNaN(r)?t:t+r}),0);return{set:(t,n)=>{if(!E(n)&&!n.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+n);const o=t.dom;so(o)&&(o.style[e]=n+"px")},get:t,getOuter:t,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}})("height"),Ng=(e,t)=>e.view(t).fold(N([]),(t=>{const n=e.owner(t),o=Ng(e,n);return[t].concat(o)}));var Rg=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?I.none():I.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Cn)},owner:e=>Rn(e)});const Ag=e=>"textarea"===$t(e),Tg=(e,t)=>{const n=(e=>{const t=e.dom.ownerDocument,n=t.body,o=t.defaultView,r=t.documentElement;if(n===e.dom)return To(n.offsetLeft,n.offsetTop);const s=Oo(null==o?void 0:o.pageYOffset,r.scrollTop),a=Oo(null==o?void 0:o.pageXOffset,r.scrollLeft),i=Oo(r.clientTop,n.clientTop),l=Oo(r.clientLeft,n.clientLeft);return Bo(e).translate(a-l,s-i)})(e),o=(e=>kg.get(e))(e);return{element:e,bottom:n.top+o,height:o,pos:n,cleanup:t}},Og=(e,t,n,o)=>{Lg(e,((r,s)=>Pg(e,t,n,o)),n)},Bg=(e,t,n,o,r)=>{const s={elm:o.element.dom,alignToTop:r};((e,t)=>e.dispatch("ScrollIntoView",t).isDefaultPrevented())(e,s)||(n(e,t,Po(t).top,o,r),((e,t)=>{e.dispatch("AfterScrollIntoView",t)})(e,s))},Pg=(e,t,n,o)=>{const r=Cn(e.getBody()),s=Cn(e.getDoc());r.dom.offsetWidth;const a=((e,t)=>{const n=((e,t)=>{const n=In(e);if(0===n.length||Ag(e))return{element:e,offset:t};if(t<n.length&&!Ag(n[t]))return{element:n[t],offset:0};{const o=n[n.length-1];return Ag(o)?{element:e,offset:t}:"img"===$t(o)?{element:o,offset:1}:Yt(o)?{element:o,offset:Ha(o).length}:{element:o,offset:In(o).length}}})(e,t),o=bn('<span data-mce-bogus="all" style="display: inline-block;">\ufeff</span>');return po(n.element,o),Tg(o,(()=>Eo(o)))})(Cn(n.startContainer),n.startOffset);Bg(e,s,t,a,o),a.cleanup()},Dg=(e,t,n,o)=>{const r=Cn(e.getDoc());Bg(e,r,n,(e=>Tg(Cn(e),_))(t),o)},Lg=(e,t,n)=>{const o=n.startContainer,r=n.startOffset,s=n.endContainer,a=n.endOffset;t(Cn(o),Cn(s));const i=e.dom.createRng();i.setStart(o,r),i.setEnd(s,a),e.selection.setRng(n)},Mg=(e,t,n,o,r)=>{const s=t.pos;if(o)Do(s.left,Math.max(0,s.top-30),r);else{const o=s.top-n+t.height+30;Do(-e.getBody().getBoundingClientRect().left,o,r)}},Ig=(e,t,n,o,r,s)=>{const a=o+n,i=r.pos.top,l=r.bottom,d=l-i>=o;i<n?Mg(e,r,o,!1!==s,t):i>a?Mg(e,r,o,d?!1!==s:!0===s,t):l>a&&!d&&Mg(e,r,o,!0===s,t)},Fg=(e,t,n,o,r)=>{const s=An(t).dom.innerHeight;Ig(e,t,n,s,o,r)},Ug=(e,t,n,o,r)=>{const s=An(t).dom.innerHeight;Ig(e,t,n,s,o,r);const a=(e=>{const t=wf(),n=Po(t),o=((e,t)=>{const n=t.owner(e);return Ng(t,n)})(e,Rg),r=Bo(e),s=G(o,((e,t)=>{const n=Bo(t);return{left:e.left+n.left,top:e.top+n.top}}),{left:0,top:0});return To(s.left+r.left+n.left,s.top+r.top+n.top)})(o.element),i=Io(window);a.top<i.y?Lo(o.element,!1!==r):a.top>i.bottom&&Lo(o.element,!0===r)},zg=(e,t,n)=>Og(e,Fg,t,n),jg=(e,t,n)=>Dg(e,t,Fg,n),Hg=(e,t,n)=>Og(e,Ug,t,n),$g=(e,t,n)=>Dg(e,t,Ug,n),Vg=(e,t,n)=>{(e.inline?zg:Hg)(e,t,n)},qg=(e,t)=>t.collapsed?e.isEditable(t.startContainer):e.isEditable(t.startContainer)&&e.isEditable(t.endContainer),Wg=(e,t,n,o,r)=>{const s=n?t.startContainer:t.endContainer,a=n?t.startOffset:t.endOffset;return I.from(s).map(Cn).map((e=>o&&t.collapsed?e:Fn(e,r(e,a)).getOr(e))).bind((e=>Kt(e)?I.some(e):Tn(e).filter(Kt))).map((e=>e.dom)).getOr(e)},Kg=(e,t,n=!1)=>Wg(e,t,!0,n,((e,t)=>Math.min(jn(e),t))),Yg=(e,t,n=!1)=>Wg(e,t,!1,n,((e,t)=>t>0?t-1:t)),Gg=(e,t)=>{const n=e;for(;e&&lr(e)&&0===e.length;)e=t?e.nextSibling:e.previousSibling;return e||n},Xg=(e,t)=>V(t,(t=>{const n=e.dispatch("GetSelectionRange",{range:t});return n.range!==t?n.range:t})),Zg={"#text":3,"#comment":8,"#cdata":4,"#pi":7,"#doctype":10,"#document-fragment":11},Qg=(e,t,n)=>{const o=n?"lastChild":"firstChild",r=n?"prev":"next";if(e[o])return e[o];if(e!==t){let n=e[r];if(n)return n;for(let o=e.parent;o&&o!==t;o=o.parent)if(n=o[r],n)return n}},Jg=e=>{var t;const n=null!==(t=e.value)&&void 0!==t?t:"";if(!Yo(n))return!1;const o=e.parent;return!o||"span"===o.name&&!o.attr("style")||!/^[ ]+$/.test(n)},ep=e=>{const t="a"===e.name&&!e.attr("href")&&e.attr("id");return e.attr("name")||e.attr("id")&&!e.firstChild||e.attr("data-mce-bookmark")||t};class tp{static create(e,t){const n=new tp(e,Zg[e]||1);return t&&pe(t,((e,t)=>{n.attr(t,e)})),n}constructor(e,t){this.name=e,this.type=t,1===t&&(this.attributes=[],this.attributes.map={})}replace(e){const t=this;return e.parent&&e.remove(),t.insert(e,t),t.remove(),t}attr(e,t){const n=this;if(!m(e))return C(e)&&pe(e,((e,t)=>{n.attr(t,e)})),n;const o=n.attributes;if(o){if(void 0!==t){if(null===t){if(e in o.map){delete o.map[e];let t=o.length;for(;t--;)if(o[t].name===e)return o.splice(t,1),n}return n}if(e in o.map){let n=o.length;for(;n--;)if(o[n].name===e){o[n].value=t;break}}else o.push({name:e,value:t});return o.map[e]=t,n}return o.map[e]}}clone(){const e=this,t=new tp(e.name,e.type),n=e.attributes;if(n){const e=[];e.map={};for(let t=0,o=n.length;t<o;t++){const o=n[t];"id"!==o.name&&(e[e.length]={name:o.name,value:o.value},e.map[o.name]=o.value)}t.attributes=e}return t.value=e.value,t}wrap(e){const t=this;return t.parent&&(t.parent.insert(e,t),e.append(t)),t}unwrap(){const e=this;for(let t=e.firstChild;t;){const n=t.next;e.insert(t,e,!0),t=n}e.remove()}remove(){const e=this,t=e.parent,n=e.next,o=e.prev;return t&&(t.firstChild===e?(t.firstChild=n,n&&(n.prev=null)):o&&(o.next=n),t.lastChild===e?(t.lastChild=o,o&&(o.next=null)):n&&(n.prev=o),e.parent=e.next=e.prev=null),e}append(e){const t=this;e.parent&&e.remove();const n=t.lastChild;return n?(n.next=e,e.prev=n,t.lastChild=e):t.lastChild=t.firstChild=e,e.parent=t,e}insert(e,t,n){e.parent&&e.remove();const o=t.parent||this;return n?(t===o.firstChild?o.firstChild=e:t.prev&&(t.prev.next=e),e.prev=t.prev,e.next=t,t.prev=e):(t===o.lastChild?o.lastChild=e:t.next&&(t.next.prev=e),e.next=t.next,e.prev=t,t.next=e),e.parent=o,e}getAll(e){const t=this,n=[];for(let o=t.firstChild;o;o=Qg(o,t))o.name===e&&n.push(o);return n}children(){const e=[];for(let t=this.firstChild;t;t=t.next)e.push(t);return e}empty(){const e=this;if(e.firstChild){const t=[];for(let n=e.firstChild;n;n=Qg(n,e))t.push(n);let n=t.length;for(;n--;){const e=t[n];e.parent=e.firstChild=e.lastChild=e.next=e.prev=null}}return e.firstChild=e.lastChild=null,e}isEmpty(e,t={},n){var o;const r=this;let s=r.firstChild;if(ep(r))return!1;if(s)do{if(1===s.type){if(s.attr("data-mce-bogus"))continue;if(e[s.name])return!1;if(ep(s))return!1}if(8===s.type)return!1;if(3===s.type&&!Jg(s))return!1;if(3===s.type&&s.parent&&t[s.parent.name]&&Yo(null!==(o=s.value)&&void 0!==o?o:""))return!1;if(n&&n(s))return!1}while(s=Qg(s,r));return!0}walk(e){return Qg(this,null,e)}}const np=Dt.makeMap("NOSCRIPT STYLE SCRIPT XMP IFRAME NOEMBED NOFRAMES PLAINTEXT"," "),op=e=>m(e.nodeValue)&&e.nodeValue.includes(ei),rp=e=>(0===e.length?"":`${V(e,(e=>`[${e}]`)).join(",")},`)+'[data-mce-bogus="all"]',sp=e=>document.createTreeWalker(e,NodeFilter.SHOW_COMMENT,(e=>op(e)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP)),ap=e=>document.createTreeWalker(e,NodeFilter.SHOW_TEXT,(e=>{if(op(e)){const t=e.parentNode;return t&&_e(np,t.nodeName)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}return NodeFilter.FILTER_SKIP})),ip=e=>null!==sp(e).nextNode(),lp=e=>null!==ap(e).nextNode(),dp=(e,t)=>null!==t.querySelector(rp(e)),cp=(e,t)=>{q(((e,t)=>t.querySelectorAll(rp(e)))(e,t),(t=>{const n=Cn(t);"all"===tn(n,"data-mce-bogus")?Eo(n):q(e,(e=>{on(n,e)&&rn(n,e)}))}))},up=e=>{let t=e.nextNode();for(;null!==t;)t.nodeValue=null,t=e.nextNode()},mp=S(up,sp),fp=S(up,ap),gp=(e,t)=>{const n=[{condition:T(dp,t),action:T(cp,t)},{condition:ip,action:mp},{condition:lp,action:fp}];let o=e,r=!1;return q(n,(({condition:t,action:n})=>{t(o)&&(r||(o=e.cloneNode(!0),r=!0),n(o))})),o},pp=e=>{const t=Uo(e,"[data-mce-bogus]");q(t,(e=>{"all"===tn(e,"data-mce-bogus")?Eo(e):qa(e)?(po(e,yn(Vo)),Eo(e)):xo(e)}))},hp=e=>{const t=Uo(e,"input");q(t,(e=>{rn(e,"name")}))},bp=(e,t,n)=>{let o;return o="raw"===t.format?Dt.trim(ni(gp(n,e.serializer.getTempAttrs()).innerHTML)):"text"===t.format?((e,t)=>{const n=e.getDoc(),o=$n(Cn(e.getBody())),r=vn("div",n);Jt(r,"data-mce-bogus","all"),lo(r,{position:"fixed",left:"-9999999px",top:"0"}),ko(r,t.innerHTML),pp(r),hp(r);const s=(e=>Hn(e)?e:Cn(Rn(e).dom.body))(o);vo(s,r);const a=ni(r.dom.innerText);return Eo(r),a})(e,n):"tree"===t.format?e.serializer.serialize(n,t):((e,t)=>{const n=ed(e),o=new RegExp(`^(<${n}[^>]*>(&nbsp;|&#160;|\\s|\xa0|<br \\/>|)<\\/${n}>[\r\n]*|<br \\/>[\r\n]*)$`);return t.replace(o,"")})(e,e.serializer.serialize(n,t)),"text"!==t.format&&!Za(Cn(n))&&m(o)?Dt.trim(o):o},vp=Dt.makeMap,yp=e=>{const t=[],n=(e=e||{}).indent,o=vp(e.indent_before||""),r=vp(e.indent_after||""),s=ws.getEncodeFunc(e.entity_encoding||"raw",e.entities),a="xhtml"!==e.element_format;return{start:(e,i,l)=>{if(n&&o[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}if(t.push("<",e),i)for(let e=0,n=i.length;e<n;e++){const n=i[e];t.push(" ",n.name,'="',s(n.value,!0),'"')}if(t[t.length]=!l||a?">":" />",l&&n&&r[e]&&t.length>0){const e=t[t.length-1];e.length>0&&"\n"!==e&&t.push("\n")}},end:e=>{let o;t.push("</",e,">"),n&&r[e]&&t.length>0&&(o=t[t.length-1],o.length>0&&"\n"!==o&&t.push("\n"))},text:(e,n)=>{e.length>0&&(t[t.length]=n?e:s(e))},cdata:e=>{t.push("<![CDATA[",e,"]]>")},comment:e=>{t.push("\x3c!--",e,"--\x3e")},pi:(e,o)=>{o?t.push("<?",e," ",s(o),"?>"):t.push("<?",e,"?>"),n&&t.push("\n")},doctype:e=>{t.push("<!DOCTYPE",e,">",n?"\n":"")},reset:()=>{t.length=0},getContent:()=>t.join("").replace(/\n$/,"")}},Cp=(e={},t=Fs())=>{const n=yp(e);return e.validate=!("validate"in e)||e.validate,{serialize:o=>{const r=e.validate,s={3:e=>{var t;n.text(null!==(t=e.value)&&void 0!==t?t:"",e.raw)},8:e=>{var t;n.comment(null!==(t=e.value)&&void 0!==t?t:"")},7:e=>{n.pi(e.name,e.value)},10:e=>{var t;n.doctype(null!==(t=e.value)&&void 0!==t?t:"")},4:e=>{var t;n.cdata(null!==(t=e.value)&&void 0!==t?t:"")},11:e=>{let t=e;if(t=t.firstChild)do{a(t)}while(t=t.next)}};n.reset();const a=e=>{var o;const i=s[e.type];if(i)i(e);else{const s=e.name,i=s in t.getVoidElements();let l=e.attributes;if(r&&l&&l.length>1){const n=[];n.map={};const o=t.getElementRule(e.name);if(o){for(let e=0,t=o.attributesOrder.length;e<t;e++){const t=o.attributesOrder[e];if(t in l.map){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}for(let e=0,t=l.length;e<t;e++){const t=l[e].name;if(!(t in n.map)){const e=l.map[t];n.map[t]=e,n.push({name:t,value:e})}}l=n}}if(n.start(s,l,i),Mr(s))m(e.value)&&n.text(e.value,!0),n.end(s);else if(!i){let t=e.firstChild;if(t){"pre"!==s&&"textarea"!==s||3!==t.type||"\n"!==(null===(o=t.value)||void 0===o?void 0:o[0])||n.text("\n",!0);do{a(t)}while(t=t.next)}n.end(s)}}};return 1!==o.type||e.inner?3===o.type?s[3](o):s[11](o):a(o),n.getContent()}}},wp=new Set;q(["margin","margin-left","margin-right","margin-top","margin-bottom","padding","padding-left","padding-right","padding-top","padding-bottom","border","border-width","border-style","border-color","background","background-attachment","background-clip","background-image","background-origin","background-position","background-repeat","background-size","float","position","left","right","top","bottom","z-index","display","transform","width","max-width","min-width","height","max-height","min-height","overflow","overflow-x","overflow-y","text-overflow","vertical-align","transition","transition-delay","transition-duration","transition-property","transition-timing-function"],(e=>{wp.add(e)}));const Ep=new Set;q(["background-color"],(e=>{Ep.add(e)}));const xp=["font","text-decoration","text-emphasis"],_p=(e,t)=>fe(((e,t)=>e.parseStyle(e.getAttrib(t,"style")))(e,t)),Sp=(e,t)=>$(_p(e,t),(e=>(e=>wp.has(e))(e))),kp=(e,t,n)=>I.from(n.container()).filter(lr).exists((o=>{const r=e?0:-1;return t(o.data.charAt(n.offset()+r))})),Np=T(kp,!0,hm),Rp=T(kp,!1,hm),Ap=e=>{const t=e.container();return lr(t)&&(0===t.data.length||ti(t.data)&&yf.isBookmarkNode(t.parentNode))},Tp=(e,t)=>n=>yu(e?0:-1,n).filter(t).isSome(),Op=e=>pr(e)&&"block"===co(Cn(e),"display"),Bp=e=>br(e)&&!(e=>Jo(e)&&"all"===e.getAttribute("data-mce-bogus"))(e),Pp=Tp(!0,Op),Dp=Tp(!1,Op),Lp=Tp(!0,wr),Mp=Tp(!1,wr),Ip=Tp(!0,sr),Fp=Tp(!1,sr),Up=Tp(!0,Bp),zp=Tp(!1,Bp),jp=(e,t)=>((e,t,n)=>kn(t,e)?Bn(e,(e=>n(e)||Sn(e,t))).slice(0,-1):[])(e,t,L),Hp=(e,t)=>[e].concat(jp(e,t)),$p=(e,t,n)=>Ku(e,t,n,Ap),Vp=(e,t,n)=>Q(Hp(Cn(t.container()),e),(e=>t=>e.isBlock($t(t)))(n)),qp=(e,t,n,o)=>$p(e,t.dom,n).forall((e=>Vp(t,n,o).fold((()=>!vu(e,n,t.dom)),(o=>!vu(e,n,t.dom)&&kn(o,Cn(e.container())))))),Wp=(e,t,n,o)=>Vp(t,n,o).fold((()=>$p(e,t.dom,n).forall((e=>!vu(e,n,t.dom)))),(t=>$p(e,t.dom,n).isNone())),Kp=T(Wp,!1),Yp=T(Wp,!0),Gp=T(qp,!1),Xp=T(qp,!0),Zp=e=>Ru(e).exists(qa),Qp=(e,t,n,o)=>{const r=Y(Hp(Cn(n.container()),t),(e=>o.isBlock($t(e)))),s=le(r).getOr(t);return qu(e,s.dom,n).filter(Zp)},Jp=(e,t,n)=>Ru(t).exists(qa)||Qp(!0,e,t,n).isSome(),eh=(e,t,n)=>(e=>I.from(e.getNode(!0)).map(Cn))(t).exists(qa)||Qp(!1,e,t,n).isSome(),th=T(Qp,!1),nh=T(Qp,!0),oh=e=>el.isTextPosition(e)&&!e.isAtStart()&&!e.isAtEnd(),rh=(e,t,n)=>{const o=Y(Hp(Cn(t.container()),e),(e=>n.isBlock($t(e))));return le(o).getOr(e)},sh=(e,t,n)=>oh(t)?Rp(t):Rp(t)||Xu(rh(e,t,n).dom,t).exists(Rp),ah=(e,t,n)=>oh(t)?Np(t):Np(t)||Gu(rh(e,t,n).dom,t).exists(Np),ih=e=>Ru(e).bind((e=>Zn(e,Kt))).exists((e=>(e=>H(["pre","pre-wrap"],e))(co(e,"white-space")))),lh=(e,t)=>n=>{return o=new $o(n,e)[t](),C(o)&&br(o)&&iu(o);var o},dh=(e,t,n)=>!ih(t)&&(((e,t,n)=>((e,t)=>Xu(e.dom,t).isNone())(e,t)||((e,t)=>Gu(e.dom,t).isNone())(e,t)||Kp(e,t,n)||Yp(e,t,n)||eh(e,t,n)||Jp(e,t,n))(e,t,n)||sh(e,t,n)||ah(e,t,n)),ch=(e,t,n)=>!ih(t)&&(Kp(e,t,n)||Gp(e,t,n)||eh(e,t,n)||sh(e,t,n)||((e,t)=>{const n=Xu(e.dom,t).getOr(t),o=lh(e.dom,"prev");return t.isAtStart()&&(o(t.container())||o(n.container()))})(e,t)),uh=(e,t,n)=>!ih(t)&&(Yp(e,t,n)||Xp(e,t,n)||Jp(e,t,n)||ah(e,t,n)||((e,t)=>{const n=Gu(e.dom,t).getOr(t),o=lh(e.dom,"next");return t.isAtEnd()&&(o(t.container())||o(n.container()))})(e,t)),mh=(e,t,n)=>ch(e,t,n)||uh(e,(e=>{const t=e.container(),n=e.offset();return lr(t)&&n<t.data.length?el(t,n+1):e})(t),n),fh=(e,t)=>gm(e.charAt(t)),gh=(e,t)=>hm(e.charAt(t)),ph=(e,t,n,o)=>{const r=t.data,s=el(t,0);return n||!fh(r,0)||mh(e,s,o)?!!(n&&gh(r,0)&&ch(e,s,o))&&(t.data=qo+r.slice(1),!0):(t.data=" "+r.slice(1),!0)},hh=(e,t,n,o)=>{const r=t.data,s=el(t,r.length-1);return n||!fh(r,r.length-1)||mh(e,s,o)?!!(n&&gh(r,r.length-1)&&uh(e,s,o))&&(t.data=r.slice(0,-1)+qo,!0):(t.data=r.slice(0,-1)+" ",!0)},bh=(e,t,n)=>{const o=t.container();if(!lr(o))return I.none();if((e=>{const t=e.container();return lr(t)&&He(t.data,qo)})(t)){const r=ph(e,o,!1,n)||(e=>{const t=e.data,n=(e=>{const t=e.split("");return V(t,((e,n)=>gm(e)&&n>0&&n<t.length-1&&bm(t[n-1])&&bm(t[n+1])?" ":e)).join("")})(t);return n!==t&&(e.data=n,!0)})(o)||hh(e,o,!1,n);return Ft(r,t)}if(mh(e,t,n)){const r=ph(e,o,!0,n)||hh(e,o,!0,n);return Ft(r,t)}return I.none()},vh=(e,t,n,o)=>{if(0===n)return;const r=Cn(e),s=Xn(r,(e=>o.isBlock($t(e)))).getOr(r),a=e.data.slice(t,t+n),i=t+n>=e.data.length&&uh(s,el(e,e.data.length),o),l=0===t&&ch(s,el(e,0),o);e.replaceData(t,n,Xo(a,4,l,i))},yh=(e,t,n)=>{const o=e.data.slice(t),r=o.length-Ke(o).length;vh(e,t,r,n)},Ch=(e,t,n)=>{const o=e.data.slice(0,t),r=o.length-Ye(o).length;vh(e,t-r,r,n)},wh=(e,t,n,o,r=!0)=>{const s=Ye(e.data).length,a=r?e:t,i=r?t:e;return r?a.appendData(i.data):a.insertData(0,i.data),Eo(Cn(i)),o&&yh(a,s,n),a},Eh=(e,t)=>((e,t)=>{const n=e.container(),o=e.offset();return!el.isTextPosition(e)&&n===t.parentNode&&o>el.before(t).offset()})(t,e)?el(t.container(),t.offset()-1):t,xh=e=>{return Di(e.previousSibling)?I.some((t=e.previousSibling,lr(t)?el(t,t.data.length):el.after(t))):e.previousSibling?Qu(e.previousSibling):I.none();var t},_h=e=>{return Di(e.nextSibling)?I.some((t=e.nextSibling,lr(t)?el(t,0):el.before(t))):e.nextSibling?Zu(e.nextSibling):I.none();var t},Sh=(e,t,n)=>((e,t,n)=>e?((e,t)=>_h(t).orThunk((()=>xh(t))).orThunk((()=>((e,t)=>Gu(e,el.after(t)).orThunk((()=>Xu(e,el.before(t)))))(e,t))))(t,n):((e,t)=>xh(t).orThunk((()=>_h(t))).orThunk((()=>((e,t)=>I.from(t.previousSibling?t.previousSibling:t.parentNode).bind((t=>Xu(e,el.before(t)))).orThunk((()=>Gu(e,el.after(t)))))(e,t))))(t,n))(e,t,n).map(T(Eh,n)),kh=(e,t,n)=>{n.fold((()=>{e.focus()}),(n=>{e.selection.setRng(n.toRange(),t)}))},Nh=(e,t)=>t&&_e(e.schema.getBlockElements(),$t(t)),Rh=(e,t,n,o=!0,r=!1)=>{const s=Sh(t,e.getBody(),n.dom),a=Xn(n,T(Nh,e),(i=e.getBody(),e=>e.dom===i));var i;const l=((e,t,n,o)=>{const r=Pn(e).filter(Yt),s=Dn(e).filter(Yt);return Eo(e),(a=r,i=s,l=t,d=(e,t,r)=>{const s=e.dom,a=t.dom,i=s.data.length;return wh(s,a,n,o),r.container()===a?el(s,i):r},a.isSome()&&i.isSome()&&l.isSome()?I.some(d(a.getOrDie(),i.getOrDie(),l.getOrDie())):I.none()).orThunk((()=>(o&&(r.each((e=>Ch(e.dom,e.dom.length,n))),s.each((e=>yh(e.dom,0,n)))),t)));var a,i,l,d})(n,s,e.schema,((e,t)=>_e(e.schema.getTextInlineElements(),$t(t)))(e,n));e.dom.isEmpty(e.getBody())?(e.setContent(""),e.selection.setCursorLocation()):a.bind((t=>((e,t,n)=>{if(Tr(e,t)){const e=bn('<br data-mce-bogus="1">');return n?q(In(t),(e=>{Xm(e)||Eo(e)})):wo(t),vo(t,e),I.some(el.before(e.dom))}return I.none()})(e.schema,t,r))).fold((()=>{o&&kh(e,t,l)}),(n=>{o&&kh(e,t,I.some(n))}))},Ah=/[\u0591-\u07FF\uFB1D-\uFDFF\uFE70-\uFEFC]/,Th=(e,t)=>xn(Cn(t),kd(e))&&!Zr(e.schema,t)&&e.dom.isEditable(t),Oh=e=>{var t;return"rtl"===ma.DOM.getStyle(e,"direction",!0)||(e=>Ah.test(e))(null!==(t=e.textContent)&&void 0!==t?t:"")},Bh=(e,t,n)=>{const o=((e,t,n)=>Y(ma.DOM.getParents(n.container(),"*",t),e))(e,t,n);return I.from(o[o.length-1])},Ph=(e,t)=>{const n=t.container(),o=t.offset();return e?ai(n)?lr(n.nextSibling)?el(n.nextSibling,0):el.after(n):di(t)?el(n,o+1):t:ai(n)?lr(n.previousSibling)?el(n.previousSibling,n.previousSibling.data.length):el.before(n):ci(t)?el(n,o-1):t},Dh=T(Ph,!0),Lh=T(Ph,!1),Mh=(e,t)=>{const n=e=>e.stopImmediatePropagation();e.on("beforeinput input",n,!0),e.getDoc().execCommand(t),e.off("beforeinput input",n)},Ih=e=>Mh(e,"Delete"),Fh=e=>Mh(e,"ForwardDelete"),Uh=e=>Wa(e)||Ya(e),zh=(e,t)=>kn(e,t)?Zn(t,Uh,(e=>t=>Lt(Tn(t),e,Sn))(e)):I.none(),jh=(e,t=!0)=>{e.dom.isEmpty(e.getBody())&&e.setContent("",{no_selection:!t})},Hh=(e,t,n)=>It(Zu(n),Qu(n),((o,r)=>{const s=Ph(!0,o),a=Ph(!1,r),i=Ph(!1,t);return e?Gu(n,i).exists((e=>e.isEqual(a)&&t.isEqual(s))):Xu(n,i).exists((e=>e.isEqual(s)&&t.isEqual(a)))})).getOr(!0),$h=e=>{var t;return(8===Vt(t=e)||"#comment"===$t(t)?Pn(e):zn(e)).bind($h).orThunk((()=>I.some(e)))},Vh=(e,t,n,o=!0)=>{var r;t.deleteContents();const s=$h(n).getOr(n),a=Cn(null!==(r=e.dom.getParent(s.dom,e.dom.isBlock))&&void 0!==r?r:n.dom);if(a.dom===e.getBody()?jh(e,o):Tr(e.schema,a,{checkRootAsContent:!1})&&(Ja(a),o&&e.selection.setCursorLocation(a.dom,0)),!Sn(n,a)){const t=Lt(Tn(a),n)?[]:Tn(i=a).map(In).map((e=>Y(e,(e=>!Sn(i,e))))).getOr([]);q(t.concat(In(n)),(t=>{Sn(t,a)||kn(t,a)||!Tr(e.schema,t)||Eo(t)}))}var i},qh=e=>Uo(e,"td,th"),Wh=(e,t)=>wm(Cn(e),t),Kh=(e,t)=>({start:e,end:t}),Yh=Sl([{singleCellTable:["rng","cell"]},{fullTable:["table"]},{partialTable:["cells","outsideDetails"]},{multiTable:["startTableCells","endTableCells","betweenRng"]}]),Gh=(e,t)=>to(Cn(e),"td,th",t),Xh=e=>!Sn(e.start,e.end),Zh=(e,t)=>wm(e.start,t).bind((n=>wm(e.end,t).bind((e=>Ft(Sn(n,e),n))))),Qh=e=>t=>Zh(t,e).map((e=>((e,t,n)=>({rng:e,table:t,cells:n}))(t,e,qh(e)))),Jh=(e,t,n,o)=>{if(n.collapsed||!e.forall(Xh))return I.none();if(t.isSameTable){const t=e.bind(Qh(o));return I.some({start:t,end:t})}{const e=Gh(n.startContainer,o),t=Gh(n.endContainer,o),r=e.bind((e=>t=>wm(t,e).bind((e=>de(qh(e)).map((e=>Kh(t,e))))))(o)).bind(Qh(o)),s=t.bind((e=>t=>wm(t,e).bind((e=>le(qh(e)).map((e=>Kh(e,t))))))(o)).bind(Qh(o));return I.some({start:r,end:s})}},eb=(e,t)=>J(e,(e=>Sn(e,t))),tb=e=>It(eb(e.cells,e.rng.start),eb(e.cells,e.rng.end),((t,n)=>e.cells.slice(t,n+1))),nb=(e,t)=>{const{startTable:n,endTable:o}=t,r=e.cloneRange();return n.each((e=>r.setStartAfter(e.dom))),o.each((e=>r.setEndBefore(e.dom))),r},ob=(e,t)=>{const n=(e=>t=>Sn(e,t))(e),o=((e,t)=>{const n=Gh(e.startContainer,t),o=Gh(e.endContainer,t);return It(n,o,Kh)})(t,n),r=((e,t)=>{const n=Wh(e.startContainer,t),o=Wh(e.endContainer,t),r=n.isSome(),s=o.isSome(),a=It(n,o,Sn).getOr(!1);return(e=>It(e.startTable,e.endTable,((t,n)=>{const o=Ho(t,(e=>Sn(e,n))),r=Ho(n,(e=>Sn(e,t)));return o||r?{...e,startTable:o?I.none():e.startTable,endTable:r?I.none():e.endTable,isSameTable:!1,isMultiTable:!1}:e})).getOr(e))({startTable:n,endTable:o,isStartInTable:r,isEndInTable:s,isSameTable:a,isMultiTable:!a&&r&&s})})(t,n);return((e,t,n)=>e.exists((e=>((e,t)=>!Xh(e)&&Zh(e,t).exists((e=>{const t=e.dom.rows;return 1===t.length&&1===t[0].cells.length})))(e,n)&&_m(e.start,t))))(o,t,n)?o.map((e=>Yh.singleCellTable(t,e.start))):r.isMultiTable?((e,t,n,o)=>Jh(e,t,n,o).bind((({start:e,end:o})=>{const r=e.bind(tb).getOr([]),s=o.bind(tb).getOr([]);if(r.length>0&&s.length>0){const e=nb(n,t);return I.some(Yh.multiTable(r,s,e))}return I.none()})))(o,r,t,n):((e,t,n,o)=>Jh(e,t,n,o).bind((({start:e,end:t})=>e.or(t))).bind((e=>{const{isSameTable:o}=t,r=tb(e).getOr([]);if(o&&e.cells.length===r.length)return I.some(Yh.fullTable(e.table));if(r.length>0){if(o)return I.some(Yh.partialTable(r,I.none()));{const e=nb(n,t);return I.some(Yh.partialTable(r,I.some({...t,rng:e})))}}return I.none()})))(o,r,t,n)},rb=e=>q(e,(e=>{rn(e,"contenteditable"),Ja(e)})),sb=(e,t,n,o)=>{const r=n.cloneRange();o?(r.setStart(n.startContainer,n.startOffset),r.setEndAfter(t.dom.lastChild)):(r.setStartBefore(t.dom.firstChild),r.setEnd(n.endContainer,n.endOffset)),db(e,r,t,!1).each((e=>e()))},ab=e=>{const t=Cm(e),n=Cn(e.selection.getNode());yr(n.dom)&&Tr(e.schema,n)?e.selection.setCursorLocation(n.dom,0):e.selection.collapse(!0),t.length>1&&$(t,(e=>Sn(e,n)))&&Jt(n,"data-mce-selected","1")},ib=(e,t,n)=>I.some((()=>{const o=e.selection.getRng(),r=n.bind((({rng:n,isStartInTable:r})=>{const s=((e,t)=>I.from(e.dom.getParent(t,e.dom.isBlock)).map(Cn))(e,r?n.endContainer:n.startContainer);n.deleteContents(),((e,t,n)=>{n.each((n=>{t?Eo(n):(Ja(n),e.selection.setCursorLocation(n.dom,0))}))})(e,r,s.filter(T(Tr,e.schema)));const a=r?t[0]:t[t.length-1];return sb(e,a,o,r),Tr(e.schema,a)?I.none():I.some(r?t.slice(1):t.slice(0,-1))})).getOr(t);rb(r),ab(e)})),lb=(e,t,n,o)=>I.some((()=>{const r=e.selection.getRng(),s=t[0],a=n[n.length-1];sb(e,s,r,!0),sb(e,a,r,!1);const i=Tr(e.schema,s)?t:t.slice(1),l=Tr(e.schema,a)?n:n.slice(0,-1);rb(i.concat(l)),o.deleteContents(),ab(e)})),db=(e,t,n,o=!0)=>I.some((()=>{Vh(e,t,n,o)})),cb=(e,t)=>I.some((()=>Rh(e,!1,t))),ub=(e,t)=>Q(Hp(t,e),Xa),mb=(e,t)=>Q(Hp(t,e),Zt("caption")),fb=(e,t)=>I.some((()=>{Ja(t),e.selection.setCursorLocation(t.dom,0)})),gb=(e,t)=>e?Ip(t):Fp(t),pb=(e,t,n)=>{const o=Cn(e.getBody());return mb(o,n).fold((()=>((e,t,n,o)=>{const r=el.fromRangeStart(e.selection.getRng());return ub(n,o).bind((o=>Tr(e.schema,o,{checkRootAsContent:!1})?fb(e,o):((e,t,n,o,r)=>Wu(n,e.getBody(),r).bind((e=>ub(t,Cn(e.getNode())).bind((e=>Sn(e,o)?I.none():I.some(_))))))(e,n,t,o,r)))})(e,t,o,n).orThunk((()=>Ft(((e,t)=>{const n=el.fromRangeStart(e.selection.getRng());return gb(t,n)||qu(t,e.getBody(),n).exists((e=>gb(t,e)))})(e,t),_)))),(n=>((e,t,n,o)=>{const r=el.fromRangeStart(e.selection.getRng());return Tr(e.schema,o)?fb(e,o):((e,t,n,o,r)=>Wu(n,e.getBody(),r).fold((()=>I.some(_)),(s=>((e,t,n,o)=>Zu(e.dom).bind((r=>Qu(e.dom).map((e=>t?n.isEqual(r)&&o.isEqual(e):n.isEqual(e)&&o.isEqual(r))))).getOr(!0))(o,n,r,s)?((e,t)=>fb(e,t))(e,o):((e,t,n)=>mb(e,Cn(n.getNode())).fold((()=>I.some(_)),(e=>Ft(!Sn(e,t),_))))(t,o,s))))(e,n,t,o,r)})(e,t,o,n)))},hb=(e,t)=>{const n=Cn(e.selection.getStart(!0)),o=Cm(e);return e.selection.isCollapsed()&&0===o.length?pb(e,t,n):((e,t,n)=>{const o=Cn(e.getBody()),r=e.selection.getRng();return 0!==n.length?ib(e,n,I.none()):((e,t,n,o)=>mb(t,o).fold((()=>((e,t,n)=>ob(t,n).bind((t=>t.fold(T(db,e),T(cb,e),T(ib,e),T(lb,e)))))(e,t,n)),(t=>((e,t)=>fb(e,t))(e,t))))(e,o,r,t)})(e,n,o)},bb=(e,t)=>{let n=t;for(;n&&n!==e;){if(hr(n)||br(n))return n;n=n.parentNode}return null},vb=["data-ephox-","data-mce-","data-alloy-","data-snooker-","_"],yb=Dt.each,Cb=e=>{const t=e.dom,n=new Set(e.serializer.getTempAttrs()),o=e=>$(vb,(t=>$e(e,t)))||n.has(e);return{compare:(e,n)=>{if(e.nodeName!==n.nodeName||e.nodeType!==n.nodeType)return!1;const r=e=>{const n={};return yb(t.getAttribs(e),(r=>{const s=r.nodeName.toLowerCase();"style"===s||o(s)||(n[s]=t.getAttrib(e,s))})),n},s=(e,t)=>{for(const n in e)if(_e(e,n)){const o=t[n];if(v(o))return!1;if(e[n]!==o)return!1;delete t[n]}for(const e in t)if(_e(t,e))return!1;return!0};if(Jo(e)&&Jo(n)){if(!s(r(e),r(n)))return!1;if(!s(t.parseStyle(t.getAttrib(e,"style")),t.parseStyle(t.getAttrib(n,"style"))))return!1}return!fm(e)&&!fm(n)},isAttributeInternal:o}},wb=e=>["h1","h2","h3","h4","h5","h6"].includes(e.name),Eb=(e,t,n,o)=>{const r=n.name;for(let t=0,s=e.length;t<s;t++){const s=e[t];if(s.name===r){const e=o.nodes[r];e?e.nodes.push(n):o.nodes[r]={filter:s,nodes:[n]}}}if(n.attributes)for(let e=0,r=t.length;e<r;e++){const r=t[e],s=r.name;if(s in n.attributes.map){const e=o.attributes[s];e?e.nodes.push(n):o.attributes[s]={filter:r,nodes:[n]}}}},xb=(e,t)=>{const n=(e,n)=>{pe(e,(e=>{const o=ce(e.nodes);q(e.filter.callbacks,(r=>{for(let t=o.length-1;t>=0;t--){const r=o[t];(n?void 0!==r.attr(e.filter.name):r.name===e.filter.name)&&!y(r.parent)||o.splice(t,1)}o.length>0&&r(o,e.filter.name,t)}))}))};n(e.nodes,!1),n(e.attributes,!0)},_b=(e,t,n,o={})=>{const r=((e,t,n)=>{const o={nodes:{},attributes:{}};return n.firstChild&&(n=>{let r=n;for(;r=r.walk();)Eb(e,t,r,o)})(n),o})(e,t,n);xb(r,o)},Sb=(e,t,n,o)=>{if((e.pad_empty_with_br||t.insert)&&n(o)){const e=new tp("br",1);t.insert&&e.attr("data-mce-bogus","1"),o.empty().append(e)}else o.empty().append(new tp("#text",3)).value=qo},kb=(e,t)=>{const n=null==e?void 0:e.firstChild;return C(n)&&n===e.lastChild&&n.name===t},Nb=(e,t,n,o)=>o.isEmpty(t,n,(t=>((e,t)=>{const n=e.getElementRule(t.name);return!0===(null==n?void 0:n.paddEmpty)})(e,t))),Rb=e=>{let t;for(let n=e;n;n=n.parent){const e=n.attr("contenteditable");if("false"===e)break;"true"===e&&(t=n)}return I.from(t)},Ab=(e,t,n=e.parent)=>{if(t.getSpecialElements()[e.name])e.empty().remove();else{const o=e.children();for(const e of o)n&&!t.isValidChild(n.name,e.name)&&Ab(e,t,n);e.unwrap()}},Tb=(e,t,n,o=_)=>{const r=t.getTextBlockElements(),s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=Dt.makeMap("tr,td,th,tbody,thead,tfoot,table,summary"),l=new Set,d=e=>e!==n&&!i[e.name];for(let n=0;n<e.length;n++){const i=e[n];let c,u,m;if(!i.parent||l.has(i))continue;if(r[i.name]&&"li"===i.parent.name){let e=i.next;for(;e&&r[e.name];)e.name="li",l.add(e),i.parent.insert(e,i.parent),e=e.next;i.unwrap();continue}const f=[i];for(c=i.parent;c&&!t.isValidChild(c.name,i.name)&&d(c);c=c.parent)f.push(c);if(c&&f.length>1)if(Ob(t,i,c))Ab(i,t);else{f.reverse(),u=f[0].clone(),o(u);let e=u;for(let n=0;n<f.length-1;n++){t.isValidChild(e.name,f[n].name)&&n>0?(m=f[n].clone(),o(m),e.append(m)):m=e;for(let e=f[n].firstChild;e&&e!==f[n+1];){const t=e.next;m.append(e),e=t}e=m}Nb(t,s,a,u)?c.insert(i,f[0],!0):(c.insert(u,f[0],!0),c.insert(i,u)),c=f[0],(Nb(t,s,a,c)||kb(c,"br"))&&c.empty().remove()}else if(i.parent){if("li"===i.name){let e=i.prev;if(e&&("ul"===e.name||"ol"===e.name)){e.append(i);continue}if(e=i.next,e&&("ul"===e.name||"ol"===e.name)&&e.firstChild){e.insert(i,e.firstChild,!0);continue}const t=new tp("ul",1);o(t),i.wrap(t);continue}if(t.isValidChild(i.parent.name,"div")&&t.isValidChild("div",i.name)){const e=new tp("div",1);o(e),i.wrap(e)}else Ab(i,t)}}},Ob=(e,t,n=t.parent)=>!(!n||(!e.children[t.name]||e.isValidChild(n.name,t.name))&&("a"!==t.name||!(e=>{let t=e;for(;t;){if("a"===t.name)return!0;t=t.parent}return!1})(n))&&(!(e=>"summary"===e.name)(n)||!wb(t)||(null==n?void 0:n.firstChild)===t&&(null==n?void 0:n.lastChild)===t)),Bb=e=>e.collapsed?e:(e=>{const t=el.fromRangeStart(e),n=el.fromRangeEnd(e),o=e.commonAncestorContainer;return qu(!1,o,n).map((r=>!vu(t,n,o)&&vu(t,r,o)?((e,t,n,o)=>{const r=document.createRange();return r.setStart(e,t),r.setEnd(n,o),r})(t.container(),t.offset(),r.container(),r.offset()):e)).getOr(e)})(e),Pb=(e,t)=>{let n=t.firstChild,o=t.lastChild;return n&&"meta"===n.name&&(n=n.next),o&&"mce_marker"===o.attr("id")&&(o=o.prev),((e,t)=>{const n=e.getNonEmptyElements();return C(t)&&(t.isEmpty(n)||((e,t)=>e.getBlockElements()[t.name]&&(e=>C(e.firstChild)&&e.firstChild===e.lastChild)(t)&&(e=>"br"===e.name||e.value===qo)(t.firstChild))(e,t))})(e,o)&&(o=null==o?void 0:o.prev),!(!n||n!==o||"ul"!==n.name&&"ol"!==n.name)},Db=e=>{return e.length>0&&(!(n=e[e.length-1]).firstChild||C(null==(t=n)?void 0:t.firstChild)&&t.firstChild===t.lastChild&&(e=>e.data===qo||gr(e))(t.firstChild))?e.slice(0,-1):e;var t,n},Lb=(e,t)=>{const n=e.getParent(t,e.isBlock);return n&&"LI"===n.nodeName?n:null},Mb=(e,t)=>{const n=el.after(e),o=ju(t).prev(n);return o?o.toRange():null},Ib=(e,t,n,o)=>{const r=((e,t,n)=>{const o=t.serialize(n);return(e=>{var t,n;const o=e.firstChild,r=e.lastChild;return o&&"META"===o.nodeName&&(null===(t=o.parentNode)||void 0===t||t.removeChild(o)),r&&"mce_marker"===r.id&&(null===(n=r.parentNode)||void 0===n||n.removeChild(r)),e})(e.createFragment(o))})(t,e,o),s=Lb(t,n.startContainer),a=Db((i=r.firstChild,Y(null!==(l=null==i?void 0:i.childNodes)&&void 0!==l?l:[],(e=>"LI"===e.nodeName))));var i,l;const d=t.getRoot(),c=e=>{const o=el.fromRangeStart(n),r=ju(t.getRoot()),a=1===e?r.prev(o):r.next(o),i=null==a?void 0:a.getNode();return!i||Lb(t,i)!==s};return s?c(1)?((e,t,n)=>{const o=e.parentNode;return o&&Dt.each(t,(t=>{o.insertBefore(t,e)})),((e,t)=>{const n=el.before(e),o=ju(t).next(n);return o?o.toRange():null})(e,n)})(s,a,d):c(2)?((e,t,n,o)=>(o.insertAfter(t.reverse(),e),Mb(t[0],n)))(s,a,d,t):((e,t,n,o)=>{const r=((e,t)=>{const n=t.cloneRange(),o=t.cloneRange();return n.setStartBefore(e),o.setEndAfter(e),[n.cloneContents(),o.cloneContents()]})(e,o),s=e.parentNode;return s&&(s.insertBefore(r[0],e),Dt.each(t,(t=>{s.insertBefore(t,e)})),s.insertBefore(r[1],e),s.removeChild(e)),Mb(t[t.length-1],n)})(s,a,d,n):null},Fb=["pre"],Ub=yr,zb=(e,t,n)=>{var o,r;const s=e.selection,a=e.dom,i=e.parser,l=n.merge,d=Cp({validate:!0},e.schema),c='<span id="mce_marker" data-mce-type="bookmark">&#xFEFF;</span>';n.preserve_zwsp||(t=ni(t)),-1===t.indexOf("{$caret}")&&(t+="{$caret}"),t=t.replace(/\{\$caret\}/,c);let u=s.getRng();const m=u.startContainer,f=e.getBody();m===f&&s.isCollapsed()&&a.isBlock(f.firstChild)&&((e,t)=>C(t)&&!e.schema.getVoidElements()[t.nodeName])(e,f.firstChild)&&a.isEmpty(f.firstChild)&&(u=a.createRng(),u.setStart(f.firstChild,0),u.setEnd(f.firstChild,0),s.setRng(u)),s.isCollapsed()||(e=>{const t=e.dom,n=Bb(e.selection.getRng());e.selection.setRng(n);const o=t.getParent(n.startContainer,Ub);((e,t,n)=>!!C(n)&&n===e.getParent(t.endContainer,Ub)&&_m(Cn(n),t))(t,n,o)?db(e,n,Cn(o)):n.startContainer===n.endContainer&&n.endOffset-n.startOffset==1&&lr(n.startContainer.childNodes[n.startOffset])?n.deleteContents():e.getDoc().execCommand("Delete",!1)})(e);const g=s.getNode(),p={context:g.nodeName.toLowerCase(),data:n.data,insert:!0},h=i.parse(t,p);if(!0===n.paste&&Pb(e.schema,h)&&((e,t)=>!!Lb(e,t))(a,g))return u=Ib(d,a,s.getRng(),h),u&&s.setRng(u),t;!0===n.paste&&((e,t,n,o)=>{var r;const s=t.firstChild,a=t.lastChild,i=s===("bookmark"===a.attr("data-mce-type")?a.prev:a),l=H(Fb,s.name);if(i&&l){const t="false"!==s.attr("contenteditable"),a=(null===(r=e.getParent(n,e.isBlock))||void 0===r?void 0:r.nodeName.toLowerCase())===s.name,i=I.from(bb(o,n)).forall(hr);return t&&a&&i}return!1})(a,h,g,e.getBody())&&(null===(o=h.firstChild)||void 0===o||o.unwrap()),(e=>{let t=e;for(;t=t.walk();)1===t.type&&t.attr("data-mce-fragment","1")})(h);let b=h.lastChild;if(b&&"mce_marker"===b.attr("id")){const t=b;for(b=b.prev;b&&"table"!==b.name;b=b.walk(!0))if(3===b.type||!a.isBlock(b.name)){b.parent&&e.schema.isValidChild(b.parent.name,"span")&&b.parent.insert(t,b,"br"===b.name);break}}if(e._selectionOverrides.showBlockCaretContainer(g),p.invalid||((e,t,n)=>{var o;return $(n.children(),wb)&&"SUMMARY"===(null===(o=e.getParent(t,e.isBlock))||void 0===o?void 0:o.nodeName)})(a,g,h)){e.selection.setContent(c);let n,o=s.getNode();const l=e.getBody();for(mr(o)?o=n=l:n=o;n&&n!==l;)o=n,n=n.parentNode;t=o===l?l.innerHTML:a.getOuterHTML(o);const u=i.parse(t),m=(e=>{for(let t=e;t;t=t.walk())if("mce_marker"===t.attr("id"))return I.some(t);return I.none()})(u),f=m.bind(Rb).getOr(u);m.each((e=>e.replace(h)));const g=h.children(),p=null!==(r=h.parent)&&void 0!==r?r:u;h.unwrap();const b=Y(g,(t=>Ob(e.schema,t,p)));Tb(b,e.schema,f),_b(i.getNodeFilters(),i.getAttributeFilters(),u),t=d.serialize(u),o===l?a.setHTML(l,t):a.setOuterHTML(o,t)}else t=d.serialize(h),((e,t,n)=>{var o;"all"===n.getAttribute("data-mce-bogus")?null===(o=n.parentNode)||void 0===o||o.insertBefore(e.dom.createFragment(t),n):((e,t)=>{if(e.isBlock(t)&&e.isEditable(t)){const e=t.childNodes;return 1===e.length&&gr(e[0])||0===e.length}return!1})(e.dom,n)?e.dom.setHTML(n,t):e.selection.setContent(t,{no_events:!0})})(e,t,g);var v;return((e,t)=>{const n=e.schema.getTextInlineElements(),o=e.dom;if(t){const t=e.getBody(),r=Cb(e),s="*[data-mce-fragment]",a=o.select(s);Dt.each(a,(e=>{const a=e=>C(n[e.nodeName.toLowerCase()]),i=e=>1===e.childNodes.length;if(!Sp(o,l=e)&&!((e,t)=>Sp(e,t)&&$(_p(e,t),(e=>(e=>Ep.has(e))(e))))(o,l)&&a(e)&&i(e)){const n=_p(o,e),l=(e,t)=>ne(e,(e=>H(t,e))),d=t=>i(e)&&o.is(t,s)&&a(t)&&(t.nodeName===e.nodeName&&l(n,_p(o,t))||d(t.children[0])),c=n=>C(n)&&n!==t&&(r.compare(e,n)||c(n.parentElement)),u=n=>C(n)&&n!==t&&o.is(n,s)&&(((e,t,n)=>{const o=_p(e,t),r=_p(e,n),s=o=>{var r,s;const a=null!==(r=e.getStyle(t,o))&&void 0!==r?r:"",i=null!==(s=e.getStyle(n,o))&&void 0!==s?s:"";return Ge(a)&&Ge(i)&&a!==i};return $(o,(e=>{const t=t=>$(t,(t=>t===e));if(!t(r)&&t(xp)){const e=Y(r,(e=>$(xp,(t=>$e(e,t)))));return $(e,s)}return s(e)}))})(o,e,n)||u(n.parentElement));(d(e.children[0])||c(e.parentElement)&&!u(e.parentElement))&&o.remove(e,!0)}var l}))}})(e,l),((e,t)=>{var n,o,r;let s;const a=e.dom,i=e.selection;if(!t)return;i.scrollIntoView(t);const l=bb(e.getBody(),t);if(l&&"false"===a.getContentEditable(l))return a.remove(t),void i.select(l);let d=a.createRng();const c=t.previousSibling;if(lr(c)){d.setStart(c,null!==(o=null===(n=c.nodeValue)||void 0===n?void 0:n.length)&&void 0!==o?o:0);const e=t.nextSibling;lr(e)&&(c.appendData(e.data),null===(r=e.parentNode)||void 0===r||r.removeChild(e))}else d.setStartBefore(t),d.setEndBefore(t);const u=a.getParent(t,a.isBlock);if(a.remove(t),u&&a.isEmpty(u)){const t=Ub(u);wo(Cn(u)),d.setStart(u,0),d.setEnd(u,0),t||(e=>!!e.getAttribute("data-mce-fragment"))(u)||!(s=(t=>{let n=el.fromRangeStart(t);return n=ju(e.getBody()).next(n),null==n?void 0:n.toRange()})(d))?a.add(u,a.create("br",t?{}:{"data-mce-bogus":"1"})):(d=s,a.remove(u))}i.setRng(d)})(e,a.get("mce_marker")),v=e.getBody(),Dt.each(v.getElementsByTagName("*"),(e=>{e.removeAttribute("data-mce-fragment")})),((e,t,n)=>{I.from(e.getParent(t,"td,th")).map(Cn).each((e=>((e,t)=>{zn(e).each((n=>{Pn(n).each((o=>{t.isBlock($t(e))&&qa(n)&&t.isBlock($t(o))&&Eo(n)}))}))})(e,n)))})(a,s.getStart(),e.schema),((e,t,n)=>{const o=Bn(Cn(n),(e=>Sn(e,Cn(t))));ie(o,o.length-2).filter(Kt).fold((()=>Wr(e,t)),(t=>Wr(e,t.dom)))})(e.schema,e.getBody(),s.getStart()),t},jb=e=>e instanceof tp,Hb=(e,t,n)=>{e.dom.setHTML(e.getBody(),t),!0!==n&&(e=>{Gf(e)&&Zu(e.getBody()).each((t=>{const n=t.getNode(),o=sr(n)?Zu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e)},$b=e=>w(e)?e:L,Vb=(e,t,n)=>{const o=t(e),r=$b(n);return o.orThunk((()=>r(e)?I.none():((e,t,n)=>{let o=e.dom;const r=$b(n);for(;o.parentNode;){o=o.parentNode;const e=Cn(o),n=t(e);if(n.isSome())return n;if(r(e))break}return I.none()})(e,t,r)))},qb=Um,Wb=(e,t,n)=>{const o=e.formatter.get(n);if(o)for(let n=0;n<o.length;n++){const r=o[n];if(Wm(r)&&!1===r.inherit&&e.dom.is(t,r.selector))return!0}return!1},Kb=(e,t,n,o,r)=>{const s=e.dom.getRoot();if(t===s)return!1;const a=e.dom.getParent(t,(t=>!!Wb(e,t,n)||t.parentNode===s||!!Xb(e,t,n,o,!0)));return!!Xb(e,a,n,o,r)},Yb=(e,t,n)=>!(!Km(n)||!qb(t,n.inline))||!(!qm(n)||!qb(t,n.block))||!!Wm(n)&&Jo(t)&&e.is(t,n.selector),Gb=(e,t,n,o,r,s)=>{const a=n[o],i="attributes"===o;if(w(n.onmatch))return n.onmatch(t,n,o);if(a)if(Ne(a)){for(let n=0;n<a.length;n++)if(i?e.getAttrib(t,a[n]):jm(e,t,a[n]))return!0}else for(const o in a)if(_e(a,o)){const l=i?e.getAttrib(t,o):jm(e,t,o),d=Fm(a[o],s),c=y(l)||Xe(l);if(c&&y(d))continue;if(r&&c&&!n.exact)return!1;if((!r||n.exact)&&!qb(l,zm(d,o)))return!1}return!0},Xb=(e,t,n,o,r)=>{const s=e.formatter.get(n),a=e.dom;if(s&&Jo(t))for(let n=0;n<s.length;n++){const i=s[n];if(Yb(e.dom,t,i)&&Gb(a,t,i,"attributes",r,o)&&Gb(a,t,i,"styles",r,o)){const n=i.classes;if(n)for(let r=0;r<n.length;r++)if(!e.dom.hasClass(t,Fm(n[r],o)))return;return i}}},Zb=(e,t,n,o,r)=>{if(o)return Kb(e,o,t,n,r);if(o=e.selection.getNode(),Kb(e,o,t,n,r))return!0;const s=e.selection.getStart();return!(s===o||!Kb(e,s,t,n,r))},Qb=ei,Jb=e=>{if(e){const t=new $o(e,e);for(let e=t.current();e;e=t.next())if(lr(e))return e}return null},ev=e=>{const t=vn("span");return en(t,{id:Ju,"data-mce-bogus":"1","data-mce-type":"format-caret"}),e&&vo(t,yn(Qb)),t},tv=(e,t,n)=>{const o=e.dom,r=e.selection;if(Gm(t))Rh(e,!1,Cn(t),n,!0);else{const e=r.getRng(),n=o.getParent(t,o.isBlock),s=e.startContainer,a=e.startOffset,i=e.endContainer,l=e.endOffset,d=(e=>{const t=Jb(e);return t&&t.data.charAt(0)===Qb&&t.deleteData(0,1),t})(t);o.remove(t,!0),s===d&&a>0&&e.setStart(d,a-1),i===d&&l>0&&e.setEnd(d,l-1),n&&o.isEmpty(n)&&Ja(Cn(n)),r.setRng(e)}},nv=(e,t,n)=>{const o=e.dom,r=e.selection;if(t)tv(e,t,n);else if(!(t=tm(e.getBody(),r.getStart())))for(;t=o.get(Ju);)tv(e,t,n)},ov=(e,t)=>(e.appendChild(t),t),rv=(e,t)=>{var n;const o=G(e,((e,t)=>ov(e,t.cloneNode(!1))),t),r=null!==(n=o.ownerDocument)&&void 0!==n?n:document;return ov(o,r.createTextNode(Qb))},sv=e=>$a(e,Ha(e).replace(new RegExp(`${qo}$`)," ")),av=(e,t)=>{const n=()=>{null===t||e.dom.isEmpty(t)||Pn(Cn(t)).each((e=>{Yt(e)?sv(e):Qn(e,(e=>Yt(e))).each((e=>{Yt(e)&&sv(e)}))}))};e.once("input",(t=>{t.data&&!hm(t.data)&&(t.isComposing?e.once("compositionend",(()=>{n()})):n())}))},iv=(e,t,n,o)=>{const a=e.dom,i=e.selection;let l=!1;const d=e.formatter.get(t);if(!d)return;const c=i.getRng(),u=c.startContainer,m=c.startOffset;let f=u;lr(u)&&(m!==u.data.length&&(l=!0),f=f.parentNode);const g=[];let h;for(;f;){if(Xb(e,f,t,n,o)){h=f;break}f.nextSibling&&(l=!0),g.push(f),f=f.parentNode}if(h)if(l){const r=i.getBookmark();c.collapse(!0);let s=mf(a,c,d,{includeTrailingSpace:!0});s=_g(s),e.formatter.remove(t,n,s,o),i.moveToBookmark(r)}else{const l=tm(e.getBody(),h),d=C(l)?a.getParents(h.parentNode,M,l):[],c=ev(!1).dom;((e,t,n)=>{var o,r;const s=e.dom,a=s.getParent(n,T(Dm,e.schema));a&&s.isEmpty(a)?null===(o=n.parentNode)||void 0===o||o.replaceChild(t,n):((e=>{const t=Uo(e,"br"),n=Y((e=>{const t=[];let n=e.dom;for(;n;)t.push(Cn(n)),n=n.lastChild;return t})(e).slice(-1),qa);t.length===n.length&&q(n,Eo)})(Cn(n)),s.isEmpty(n)?null===(r=n.parentNode)||void 0===r||r.replaceChild(t,n):s.insertAfter(t,n))})(e,c,null!=l?l:h);const u=((e,t,n,o,a,i)=>{const l=e.formatter,d=e.dom,c=Y(fe(l.get()),(e=>e!==o&&!He(e,"removeformat"))),u=((e,t,n)=>X(n,((n,o)=>{const r=((e,t)=>Vm(e,t,(e=>{const t=e=>w(e)||e.length>1&&"%"===e.charAt(0);return $(["styles","attributes"],(n=>xe(e,n).exists((e=>{const n=p(e)?e:Ee(e);return $(n,t)}))))})))(e,o);return e.formatter.matchNode(t,o,{},r)?n.concat([o]):n}),[]))(e,n,c);if(Y(u,(t=>!((e,t,n)=>{const o=["inline","block","selector","attributes","styles","classes"],a=e=>Ce(e,((e,t)=>$(o,(e=>e===t))));return Vm(e,t,(t=>{const o=a(t);return Vm(e,n,(e=>{const t=a(e);return((e,t,n=s)=>r(n).eq(e,t))(o,t)}))}))})(e,t,o))).length>0){const e=n.cloneNode(!1);return d.add(t,e),l.remove(o,a,e,i),d.remove(e),I.some(e)}return I.none()})(e,c,h,t,n,o),m=rv([...g,...u.toArray(),...d],c);l&&tv(e,l,C(l)),i.setCursorLocation(m,1),av(e,c),a.isEmpty(h)&&a.remove(h)}},lv=e=>{const t=ev(!1),n=rv(e,t.dom);return{caretContainer:t,caretPosition:el(n,0)}},dv=(e,t)=>{const{caretContainer:n,caretPosition:o}=lv(t);return po(Cn(e),n),Eo(Cn(e)),o},cv=(e,t)=>{if(em(t.dom))return!1;const n=e.schema.getTextInlineElements();return _e(n,$t(t))&&!em(t.dom)&&!rr(t.dom)},uv={},mv=nr(["pre"]);(e=>{uv[e]||(uv[e]=[]),uv[e].push((e=>{if(!e.selection.getRng().collapsed){const t=e.selection.getSelectedBlocks(),n=Y(Y(t,mv),(e=>t=>{const n=t.previousSibling;return mv(n)&&H(e,n)})(t));q(n,(e=>{((e,t)=>{const n=Cn(t),o=Rn(n).dom;Eo(n),Co(Cn(e),[vn("br",o),vn("br",o),...In(n)])})(e.previousSibling,e)}))}}))})("pre");const fv=["fontWeight","fontStyle","color","fontSize","fontFamily"],gv=(e,t)=>{const n=e.get(t);return p(n)?Q(n,(e=>Km(e)&&"span"===e.inline&&(e=>f(e.styles)&&$(fe(e.styles),(e=>H(fv,e))))(e))):I.none()},pv=(e,t)=>Xu(t,el.fromRangeStart(e)).isNone(),hv=(e,t)=>!1===Gu(t,el.fromRangeEnd(e)).exists((e=>!gr(e.getNode())||Gu(t,e).isSome())),bv=e=>t=>Er(t)&&e.isEditable(t),vv=e=>Y(e.getSelectedBlocks(),bv(e.dom)),yv=Dt.each,Cv=e=>Jo(e)&&!fm(e)&&!em(e)&&!rr(e),wv=(e,t)=>{for(let n=e;n;n=n[t]){if(lr(n)&&Ge(n.data))return e;if(Jo(n)&&!fm(n))return n}return e},Ev=(e,t,n)=>{const o=Cb(e),r=er(t)&&e.dom.isEditable(t),s=er(n)&&e.dom.isEditable(n);if(r&&s){const r=wv(t,"previousSibling"),s=wv(n,"nextSibling");if(o.compare(r,s)){for(let e=r.nextSibling;e&&e!==s;){const t=e;e=e.nextSibling,r.appendChild(t)}return e.dom.remove(s),Dt.each(Dt.grep(s.childNodes),(e=>{r.appendChild(e)})),r}}return n},xv=(e,t,n,o)=>{var r;if(o&&!1!==t.merge_siblings){const t=null!==(r=Ev(e,Pm(o),o))&&void 0!==r?r:o;Ev(e,t,Pm(t,!0))}},_v=(e,t,n)=>{yv(e.childNodes,(e=>{Cv(e)&&(t(e)&&n(e),e.hasChildNodes()&&_v(e,t,n))}))},Sv=(e,t)=>n=>!(!n||!jm(e,n,t)),kv=(e,t,n)=>o=>{e.setStyle(o,t,n),""===o.getAttribute("style")&&o.removeAttribute("style"),((e,t)=>{"SPAN"===t.nodeName&&0===e.getAttribs(t).length&&e.remove(t,!0)})(e,o)},Nv=Sl([{keep:[]},{rename:["name"]},{removed:[]}]),Rv=/^(src|href|style)$/,Av=Dt.each,Tv=Um,Ov=(e,t,n)=>e.isChildOf(t,n)&&t!==n&&!e.isBlock(n),Bv=(e,t,n)=>{let o=t[n?"startContainer":"endContainer"],r=t[n?"startOffset":"endOffset"];if(Jo(o)){const e=o.childNodes.length-1;!n&&r&&r--,o=o.childNodes[r>e?e:r]}return lr(o)&&n&&r>=o.data.length&&(o=new $o(o,e.getBody()).next()||o),lr(o)&&!n&&0===r&&(o=new $o(o,e.getBody()).prev()||o),o},Pv=(e,t)=>{const n=t?"firstChild":"lastChild",o=e[n];return(e=>/^(TR|TH|TD)$/.test(e.nodeName))(e)&&o?"TR"===e.nodeName&&o[n]||o:e},Dv=(e,t,n,o)=>{var r;const s=e.create(n,o);return null===(r=t.parentNode)||void 0===r||r.insertBefore(s,t),s.appendChild(t),s},Lv=(e,t,n,o,r)=>{const s=Cn(t),a=Cn(e.create(o,r)),i=n?Mn(s):Ln(s);return Co(a,i),n?(po(s,a),bo(a,s)):(ho(s,a),vo(a,s)),a.dom},Mv=(e,t,n)=>{const o=t.parentNode;let r;const s=e.dom,a=ed(e);qm(n)&&o===s.getRoot()&&(n.list_block&&Tv(t,n.list_block)||q(ce(t.childNodes),(t=>{Lm(e,a,t.nodeName.toLowerCase())?r?r.appendChild(t):(r=Dv(s,t,a),s.setAttribs(r,td(e))):r=null}))),(e=>Wm(e)&&Km(e)&&Lt(xe(e,"mixed"),!0))(n)&&!Tv(n.inline,t)||s.remove(t,!0)},Iv=(e,t,n)=>E(e)?{name:t,value:null}:{name:e,value:Fm(t,n)},Fv=(e,t)=>{""===e.getAttrib(t,"style")&&(t.removeAttribute("style"),t.removeAttribute("data-mce-style"))},Uv=(e,t,n,o,r)=>{let s=!1;Av(n.styles,((a,i)=>{const{name:l,value:d}=Iv(i,a,o),c=zm(d,l);(n.remove_similar||h(d)||!Jo(r)||Tv(jm(e,r,l),c))&&e.setStyle(t,l,""),s=!0})),s&&Fv(e,t)},zv=(e,t,n,o,r)=>{const s=e.dom,a=Cb(e),i=e.schema;if(Km(t)&&Gr(i,t.inline)&&Zr(i,o)&&o.parentElement===e.getBody())return Mv(e,o,t),Nv.removed();if(!t.ceFalseOverride&&o&&"false"===s.getContentEditableParent(o))return Nv.keep();if(o&&!Yb(s,o,t)&&!((e,t)=>t.links&&"A"===e.nodeName)(o,t))return Nv.keep();const l=o,d=t.preserve_attributes;if(Km(t)&&"all"===t.remove&&p(d)){const e=Y(s.getAttribs(l),(e=>H(d,e.name.toLowerCase())));if(s.removeAllAttribs(l),q(e,(e=>s.setAttrib(l,e.name,e.value))),e.length>0)return Nv.rename("span")}if("all"!==t.remove){Uv(s,l,t,n,r),Av(t.attributes,((e,o)=>{const{name:a,value:i}=Iv(o,e,n);if(t.remove_similar||h(i)||!Jo(r)||Tv(s.getAttrib(r,a),i)){if("class"===a){const e=s.getAttrib(l,a);if(e){let t="";if(q(e.split(/\s+/),(e=>{/mce\-\w+/.test(e)&&(t+=(t?" ":"")+e)})),t)return void s.setAttrib(l,a,t)}}if(Rv.test(a)&&l.removeAttribute("data-mce-"+a),"style"===a&&nr(["li"])(l)&&"none"===s.getStyle(l,"list-style-type"))return l.removeAttribute(a),void s.setStyle(l,"list-style-type","none");"class"===a&&l.removeAttribute("className"),l.removeAttribute(a)}})),Av(t.classes,(e=>{e=Fm(e,n),Jo(r)&&!s.hasClass(r,e)||s.removeClass(l,e)}));const e=s.getAttribs(l);for(let t=0;t<e.length;t++){const n=e[t].nodeName;if(!a.isAttributeInternal(n))return Nv.keep()}}return"none"!==t.remove?(Mv(e,l,t),Nv.removed()):Nv.keep()},jv=(e,t,n,o)=>zv(e,t,n,o,o).fold(N(o),(t=>(e.dom.createFragment().appendChild(o),e.dom.rename(o,t))),N(null)),Hv=(e,t,n,o,r)=>{(o||e.selection.isEditable())&&((e,t,n,o,r)=>{const s=e.formatter.get(t),a=s[0],i=e.dom,l=e.selection,d=o=>{const i=((e,t,n,o,r)=>{let s;return t.parentNode&&q($m(e.dom,t.parentNode).reverse(),(t=>{if(!s&&Jo(t)&&"_start"!==t.id&&"_end"!==t.id){const a=Xb(e,t,n,o,r);a&&!1!==a.split&&(s=t)}})),s})(e,o,t,n,r);return((e,t,n,o,r,s,a,i)=>{var l,d;let c,u;const m=e.dom;if(n){const s=n.parentNode;for(let n=o.parentNode;n&&n!==s;n=n.parentNode){let o=m.clone(n,!1);for(let n=0;n<t.length&&(o=jv(e,t[n],i,o),null!==o);n++);o&&(c&&o.appendChild(c),u||(u=o),c=o)}a.mixed&&m.isBlock(n)||(o=null!==(l=m.split(n,o))&&void 0!==l?l:o),c&&u&&(null===(d=r.parentNode)||void 0===d||d.insertBefore(c,r),u.appendChild(r),Km(a)&&xv(e,a,0,c))}return o})(e,s,i,o,o,0,a,n)},c=t=>$(s,(o=>$v(e,o,n,t,t))),u=t=>{const n=ce(t.childNodes),o=c(t)||$(s,(e=>Yb(i,t,e))),r=t.parentNode;if(!o&&C(r)&&Ym(a)&&c(r),a.deep&&n.length)for(let e=0;e<n.length;e++)u(n[e]);q(["underline","line-through","overline"],(n=>{Jo(t)&&e.dom.getStyle(t,"text-decoration")===n&&t.parentNode&&Hm(i,t.parentNode)===n&&$v(e,{deep:!1,exact:!0,inline:"span",styles:{textDecoration:n}},void 0,t)}))},m=e=>{const t=i.get(e?"_start":"_end");if(t){let n=t[e?"firstChild":"lastChild"];return(e=>fm(e)&&Jo(e)&&("_start"===e.id||"_end"===e.id))(n)&&(n=n[e?"firstChild":"lastChild"]),lr(n)&&0===n.data.length&&(n=e?t.previousSibling||t.nextSibling:t.nextSibling||t.previousSibling),i.remove(t,!0),n}return null},f=t=>{let n,o,r=mf(i,t,s,{includeTrailingSpace:t.collapsed});if(a.split){if(r=_g(r),n=Bv(e,r,!0),o=Bv(e,r),n!==o){if(n=Pv(n,!0),o=Pv(o,!1),Ov(i,n,o)){const e=I.from(n.firstChild).getOr(n);return d(Lv(i,e,!0,"span",{id:"_start","data-mce-type":"bookmark"})),void m(!0)}if(Ov(i,o,n)){const e=I.from(o.lastChild).getOr(o);return d(Lv(i,e,!1,"span",{id:"_end","data-mce-type":"bookmark"})),void m(!1)}n=Dv(i,n,"span",{id:"_start","data-mce-type":"bookmark"}),o=Dv(i,o,"span",{id:"_end","data-mce-type":"bookmark"});const e=i.createRng();e.setStartAfter(n),e.setEndBefore(o),ff(i,e,(e=>{q(e,(e=>{fm(e)||fm(e.parentNode)||d(e)}))})),d(n),d(o),n=m(!0),o=m()}else n=o=d(n);r.startContainer=n.parentNode?n.parentNode:n,r.startOffset=i.nodeIndex(n),r.endContainer=o.parentNode?o.parentNode:o,r.endOffset=i.nodeIndex(o)+1}ff(i,r,(e=>{q(e,u)}))};if(o){if(Am(o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),f(e)}else f(o);Ll(e,t,o,n)}else l.isCollapsed()&&Km(a)&&!Cm(e).length?iv(e,t,n,r):(Om(e,(()=>Nm(e,f)),(o=>Km(a)&&Zb(e,t,n,o))),e.nodeChanged()),((e,t,n)=>{"removeformat"===t?q(vv(e.selection),(t=>{q(fv,(n=>e.dom.setStyle(t,n,""))),Fv(e.dom,t)})):gv(e.formatter,t).each((t=>{q(vv(e.selection),(o=>Uv(e.dom,o,t,n,null)))}))})(e,t,n),Ll(e,t,o,n)})(e,t,n,o,r)},$v=(e,t,n,o,r)=>zv(e,t,n,o,r).fold(L,(t=>(e.dom.rename(o,t),!0)),M),Vv=Dt.each,qv=Dt.each,Wv=(e,t,n,o)=>{if(qv(n.styles,((n,r)=>{e.setStyle(t,r,Fm(n,o))})),n.styles){const n=e.getAttrib(t,"style");n&&e.setAttrib(t,"data-mce-style",n)}},Kv=(e,t,n,o)=>{const r=e.formatter.get(t),s=r[0],a=!o&&e.selection.isCollapsed(),i=e.dom,l=e.selection,d=(e,t=s)=>{w(t.onformat)&&t.onformat(e,t,n,o),Wv(i,e,t,n),qv(t.attributes,((t,o)=>{i.setAttrib(e,o,Fm(t,n))})),qv(t.classes,(t=>{const o=Fm(t,n);i.hasClass(e,o)||i.addClass(e,o)}))},c=(e,t)=>{let n=!1;return qv(e,(e=>!(!Wm(e)||("false"!==i.getContentEditable(t)||e.ceFalseOverride)&&(!C(e.collapsed)||e.collapsed===a)&&i.is(t,e.selector)&&!em(t)&&(d(t,e),n=!0,1)))),n},u=e=>{if(m(e)){const t=i.create(e);return d(t),t}return null},f=(o,a,i)=>{const l=[];let m=!0;const f=s.inline||s.block,g=u(f);ff(o,a,(a=>{let u;const p=a=>{let h=!1,b=m,v=!1;const y=a.parentNode,w=y.nodeName.toLowerCase(),E=o.getContentEditable(a);C(E)&&(b=m,m="true"===E,h=!0,v=Im(e,a));const x=m&&!h;if(gr(a)&&!((e,t,n,o)=>{if(Ud(e)&&Km(t)&&n.parentNode){const t=Ms(e.schema),r=jo(Cn(n),(e=>em(e.dom)));return Se(t,o)&&Ar(e.schema,n.parentNode,{skipBogus:!1,includeZwsp:!0})&&!r}return!1})(e,s,a,w))return u=null,void(qm(s)&&o.remove(a));if((o=>(e=>qm(e)&&!0===e.wrapper)(s)&&Xb(e,o,t,n))(a))u=null;else{if(((t,n,o)=>{const r=(e=>qm(e)&&!0!==e.wrapper)(s)&&Dm(e.schema,t)&&Lm(e,n,f);return o&&r})(a,w,x)){const e=o.rename(a,f);return d(e),l.push(e),void(u=null)}if(Wm(s)){let e=c(r,a);if(!e&&C(y)&&Ym(s)&&(e=c(r,y)),!Km(s)||e)return void(u=null)}C(g)&&((t,n,r,a)=>{const l=t.nodeName.toLowerCase(),d=Lm(e,f,l)&&Lm(e,n,f),c=!i&&lr(t)&&ti(t.data),u=em(t),m=!Km(s)||!o.isBlock(t);return(r||a)&&d&&!c&&!u&&m})(a,w,x,v)?(u||(u=o.clone(g,!1),y.insertBefore(u,a),l.push(u)),v&&h&&(m=b),u.appendChild(a)):(u=null,q(ce(a.childNodes),p),h&&(m=b),u=null)}};q(a,p)})),!0===s.links&&q(l,(e=>{const t=e=>{"A"===e.nodeName&&d(e,s),q(ce(e.childNodes),t)};t(e)})),q(l,(a=>{const i=(e=>{let t=0;return q(e.childNodes,(e=>{(e=>C(e)&&lr(e)&&0===e.length)(e)||fm(e)||t++})),t})(a);!(l.length>1)&&o.isBlock(a)||0!==i?(Km(s)||qm(s)&&s.wrapper)&&(s.exact||1!==i||(a=(e=>{const t=Q(e.childNodes,Tm).filter((e=>"false"!==o.getContentEditable(e)&&Yb(o,e,s)));return t.map((t=>{const n=o.clone(t,!1);return d(n),o.replace(n,e,!0),o.remove(t,!0),n})).getOr(e)})(a)),((e,t,n,o)=>{Vv(t,(t=>{Km(t)&&Vv(e.dom.select(t.inline,o),(o=>{Cv(o)&&$v(e,t,n,o,t.exact?o:null)})),((e,t,n)=>{if(t.clear_child_styles){const o=t.links?"*:not(a)":"*";yv(e.select(o,n),(n=>{Cv(n)&&e.isEditable(n)&&yv(t.styles,((t,o)=>{e.setStyle(n,o,"")}))}))}})(e.dom,t,o)}))})(e,r,n,a),((e,t,n,o,r)=>{const s=r.parentNode;Xb(e,s,n,o)&&$v(e,t,o,r)||t.merge_with_parents&&s&&e.dom.getParent(s,(s=>!!Xb(e,s,n,o)&&($v(e,t,o,r),!0)))})(e,s,t,n,a),((e,t,n,o)=>{if(t.styles&&t.styles.backgroundColor){const r=Sv(e,"fontSize");_v(o,(t=>r(t)&&e.isEditable(t)),kv(e,"backgroundColor",Fm(t.styles.backgroundColor,n)))}})(o,s,n,a),((e,t,n,o)=>{const r=t=>{if(er(t)&&Jo(t.parentNode)&&e.isEditable(t)){const n=Hm(e,t.parentNode);e.getStyle(t,"color")&&n?e.setStyle(t,"text-decoration",n):e.getStyle(t,"text-decoration")===n&&e.setStyle(t,"text-decoration",null)}};t.styles&&(t.styles.color||t.styles.textDecoration)&&(Dt.walk(o,r,"childNodes"),r(o))})(o,s,0,a),((e,t,n,o)=>{if(Km(t)&&("sub"===t.inline||"sup"===t.inline)){const n=Sv(e,"fontSize");_v(o,(t=>n(t)&&e.isEditable(t)),kv(e,"fontSize",""));const r=Y(e.select("sup"===t.inline?"sub":"sup",o),e.isEditable);e.remove(r,!0)}})(o,s,0,a),xv(e,s,0,a)):o.remove(a,!0)}))},g=Am(o)?o:l.getNode();if("false"===i.getContentEditable(g)&&!Im(e,g))return c(r,o=g),void Dl(e,t,o,n);if(s){if(o)if(Am(o)){if(!c(r,o)){const e=i.createRng();e.setStartBefore(o),e.setEndAfter(o),f(i,mf(i,e,r),!0)}}else f(i,o,!0);else a&&Km(s)&&!Cm(e).length?((e,t,n)=>{let o;const r=e.selection,s=e.formatter.get(t);if(!s)return;const a=r.getRng();let i=a.startOffset;const l=a.startContainer.nodeValue;o=tm(e.getBody(),r.getStart());const d=/[^\s\u00a0\u00ad\u200b\ufeff]/;if(l&&i>0&&i<l.length&&d.test(l.charAt(i))&&d.test(l.charAt(i-1))){const o=r.getBookmark();a.collapse(!0);let i=mf(e.dom,a,s);i=_g(i),e.formatter.apply(t,n,i),r.moveToBookmark(o)}else{let s=o?Jb(o):null;o&&(null==s?void 0:s.data)===Qb||(c=e.getDoc(),u=ev(!0).dom,o=c.importNode(u,!0),s=o.firstChild,a.insertNode(o),i=1,av(e,o)),e.formatter.apply(t,n,o),r.setCursorLocation(s,i)}var c,u})(e,t,n):(l.setRng(Bb(l.getRng())),Om(e,(()=>{Nm(e,((e,t)=>{const n=t?e:mf(i,e,r);f(i,n,!1)}))}),M),e.nodeChanged()),gv(e.formatter,t).each((t=>{q((e=>Y((e=>{const t=e.getSelectedBlocks(),n=e.getRng();if(e.isCollapsed())return[];if(1===t.length)return pv(n,t[0])&&hv(n,t[0])?t:[];{const e=le(t).filter((e=>pv(n,e))).toArray(),o=de(t).filter((e=>hv(n,e))).toArray(),r=t.slice(1,-1);return e.concat(r).concat(o)}})(e),bv(e.dom)))(e.selection),(e=>Wv(i,e,t,n)))}));((e,t)=>{_e(uv,e)&&q(uv[e],(e=>{e(t)}))})(t,e)}Dl(e,t,o,n)},Yv=(e,t,n,o)=>{(o||e.selection.isEditable())&&Kv(e,t,n,o)},Gv=e=>_e(e,"vars"),Xv=e=>e.selection.getStart(),Zv=(e,t,n,o,r)=>Z(t,(t=>{const s=e.formatter.matchNode(t,n,null!=r?r:{},o);return!v(s)}),(t=>!!Wb(e,t,n)||!o&&C(e.formatter.matchNode(t,n,r,!0)))),Qv=(e,t)=>{const n=null!=t?t:Xv(e);return Y($m(e.dom,n),(e=>Jo(e)&&!rr(e)))},Jv=(e,t,n)=>{const o=Qv(e,t);pe(n,((n,r)=>{const s=n=>{const s=Zv(e,o,r,n.similar,Gv(n)?n.vars:void 0),a=s.isSome();if(n.state.get()!==a){n.state.set(a);const e=s.getOr(t);Gv(n)?n.callback(a,{node:e,format:r,parents:o}):q(n.callbacks,(t=>t(a,{node:e,format:r,parents:o})))}};q([n.withSimilar,n.withoutSimilar],s),q(n.withVars,s)}))},ey=Dt.explode,ty=()=>{const e={};return{addFilter:(t,n)=>{q(ey(t),(t=>{_e(e,t)||(e[t]={name:t,callbacks:[]}),e[t].callbacks.push(n)}))},getFilters:()=>Ee(e),removeFilter:(t,n)=>{q(ey(t),(t=>{if(_e(e,t))if(C(n)){const o=e[t],r=Y(o.callbacks,(e=>e!==n));r.length>0?o.callbacks=r:delete e[t]}else delete e[t]}))}}},ny=(e,t,n)=>{var o;const r=Ks();t.convert_fonts_to_spans&&((e,t,n)=>{e.addNodeFilter("font",(e=>{q(e,(e=>{const o=t.parse(e.attr("style")),r=e.attr("color"),s=e.attr("face"),a=e.attr("size");r&&(o.color=r),s&&(o["font-family"]=s),a&&Ze(a).each((e=>{o["font-size"]=n[e-1]})),e.name="span",e.attr("style",t.serialize(o)),(e=>{q(["color","face","size"],(t=>{e.attr(t,null)}))})(e)}))}))})(e,r,Dt.explode(null!==(o=t.font_size_legacy_values)&&void 0!==o?o:"")),((e,t,n)=>{e.addNodeFilter("strike",(e=>{const o="html4"!==t.type;q(e,(e=>{if(o)e.name="s";else{const t=n.parse(e.attr("style"));t["text-decoration"]="line-through",e.name="span",e.attr("style",n.serialize(t))}}))}))})(e,n,r)},oy=e=>{const[t,...n]=e.split(","),o=n.join(","),r=/data:([^/]+\/[^;]+)(;.+)?/.exec(t);if(r){const e=";base64"===r[2],t=(e=>{try{return decodeURIComponent(e)}catch(t){return e}})(o),n=e?(e=>{const t=/([a-z0-9+\/=\s]+)/i.exec(e);return t?t[1]:""})(t):t;return I.some({type:r[1],data:n,base64Encoded:e})}return I.none()},ry=(e,t,n=!0)=>{let o=t;if(n)try{o=atob(t)}catch(e){return I.none()}const r=new Uint8Array(o.length);for(let e=0;e<r.length;e++)r[e]=o.charCodeAt(e);return I.some(new Blob([r],{type:e}))},sy=e=>new Promise(((t,n)=>{const o=new FileReader;o.onloadend=()=>{t(o.result)},o.onerror=()=>{var e;n(null===(e=o.error)||void 0===e?void 0:e.message)},o.readAsDataURL(e)}));let ay=0;const iy=(e,t,n)=>oy(e).bind((({data:e,type:o,base64Encoded:r})=>{if(t&&!r)return I.none();{const t=r?e:btoa(e);return n(t,o)}})),ly=(e,t,n)=>{const o=e.create("blobid"+ay++,t,n);return e.add(o),o},dy=(e,t,n=!1)=>iy(t,n,((t,n)=>I.from(e.getByData(t,n)).orThunk((()=>ry(n,t).map((n=>ly(e,n,t))))))),cy=/^(?:(?:(?:[A-Za-z][A-Za-z\d.+-]{0,14}:\/\/(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?|www\.|[-;:&=+$,.\w]+@)([A-Za-z\d-]+(?:\.[A-Za-z\d-]+)*))(?::\d+)?(?:\/(?:[-.~*+=!;:'%@$(),\/\w]*[-~*+=%@$()\/\w])?)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+)?)?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+)?)?)$/,uy=e=>I.from(e.match(cy)).bind((e=>ie(e,1))).map((e=>$e(e,"www.")?e.substring(4):e)),my=(e,t)=>{I.from(e.attr("src")).bind(uy).forall((e=>!H(t,e)))&&e.attr("sandbox","")},fy=(e,t)=>$e(e,`${t}/`),{entries:gy,setPrototypeOf:py,isFrozen:hy,getPrototypeOf:by,getOwnPropertyDescriptor:vy}=Object;let{freeze:yy,seal:Cy,create:wy}=Object,{apply:Ey,construct:xy}="undefined"!=typeof Reflect&&Reflect;// eslint-disable-line import/no-mutable-exports
yy||(yy=function(e){return e}),Cy||(Cy=function(e){return e}),Ey||(Ey=function(e,t,n){return e.apply(t,n)}),xy||(xy=function(e,t){return new e(...t)});const _y=Uy(Array.prototype.forEach),Sy=Uy(Array.prototype.lastIndexOf),ky=Uy(Array.prototype.pop),Ny=Uy(Array.prototype.push),Ry=Uy(Array.prototype.splice),Ay=Uy(String.prototype.toLowerCase),Ty=Uy(String.prototype.toString),Oy=Uy(String.prototype.match),By=Uy(String.prototype.replace),Py=Uy(String.prototype.indexOf),Dy=Uy(String.prototype.trim),Ly=Uy(Object.prototype.hasOwnProperty),My=Uy(RegExp.prototype.test),Iy=(Fy=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return xy(Fy,t)});
/**
     * Creates a new function that constructs an instance of the given constructor function with the provided arguments.
     *
     * @param func - The constructor function to be wrapped and called.
     * @returns A new function that constructs an instance of the given constructor function with the provided arguments.
     */
var Fy;
/**
     * Add properties to a lookup table
     *
     * @param set - The set to which elements will be added.
     * @param array - The array containing elements to be added to the set.
     * @param transformCaseFunc - An optional function to transform the case of each element before adding to the set.
     * @returns The modified set with added elements.
     */
/**
     * Creates a new function that calls the given function with a specified thisArg and arguments.
     *
     * @param func - The function to be wrapped and called.
     * @returns A new function that calls the given function with a specified thisArg and arguments.
     */
function Uy(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return Ey(e,t,o)}}function zy(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Ay;py&&
// Make 'in' and truthy checks like Boolean(set.constructor)
// independent of any properties defined on Object.prototype.
// Prevent prototype setters from intercepting set as a this value.
py(e,null);let o=t.length;for(;o--;){let r=t[o];if("string"==typeof r){const e=n(r);e!==r&&(
// Config presets (e.g. tags.js, attrs.js) are immutable.
hy(t)||(t[o]=e),r=e)}e[r]=!0}return e}
/**
     * Clean up an array to harden against CSPP
     *
     * @param array - The array to be cleaned.
     * @returns The cleaned version of the array
     */function jy(e){for(let t=0;t<e.length;t++)Ly(e,t)||(e[t]=null);return e}
/**
     * Shallow clone an object
     *
     * @param object - The object to be cloned.
     * @returns A new object that copies the original.
     */function Hy(e){const t=wy(null);for(const[n,o]of gy(e))Ly(e,n)&&(Array.isArray(o)?t[n]=jy(o):o&&"object"==typeof o&&o.constructor===Object?t[n]=Hy(o):t[n]=o);return t}
/**
     * This method automatically checks if the prop is function or getter and behaves accordingly.
     *
     * @param object - The object to look up the getter function in its prototype chain.
     * @param prop - The property name for which to find the getter function.
     * @returns The getter function found in the prototype chain or a fallback function.
     */function $y(e,t){for(;null!==e;){const n=vy(e,t);if(n){if(n.get)return Uy(n.get);if("function"==typeof n.value)return Uy(n.value)}e=by(e)}return function(){return null}}const Vy=yy(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),qy=yy(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Wy=yy(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Ky=yy(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Yy=yy(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Gy=yy(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),Xy=yy(["#text"]),Zy=yy(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Qy=yy(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Jy=yy(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),eC=yy(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),tC=Cy(/\{\{[\w\W]*|[\w\W]*\}\}/gm),nC=Cy(/<%[\w\W]*|[\w\W]*%>/gm),oC=Cy(/\$\{[\w\W]*/gm),rC=Cy(/^data-[\-\w.\u00B7-\uFFFF]+$/),sC=Cy(/^aria-[\-\w]+$/),aC=Cy(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),iC=Cy(/^(?:\w+script|data):/i),lC=Cy(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),dC=Cy(/^html$/i),cC=Cy(/^[a-z][.\w]*(-[.\w]+)+$/i);var uC=Object.freeze({__proto__:null,ARIA_ATTR:sC,ATTR_WHITESPACE:lC,CUSTOM_ELEMENT:cC,DATA_ATTR:rC,DOCTYPE_NAME:dC,ERB_EXPR:nC,IS_ALLOWED_URI:aC,IS_SCRIPT_OR_DATA:iC,MUSTACHE_EXPR:tC,TMPLIT_EXPR:oC});
/* eslint-disable @typescript-eslint/indent */
// https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType
const mC=function(){return"undefined"==typeof window?null:window};var fC=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:mC();const n=t=>e(t);if(n.version="3.2.4",n.removed=[],!t||!t.document||9!==t.document.nodeType||!t.Element)
// Not running in a browser, provide a factory function
// so that you can pass your own Window
return n.isSupported=!1,n;let{document:o}=t;const r=o,s=r.currentScript,{DocumentFragment:a,HTMLTemplateElement:i,Node:l,Element:d,NodeFilter:c,NamedNodeMap:u=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:m,DOMParser:f,trustedTypes:g}=t,p=d.prototype,h=$y(p,"cloneNode"),b=$y(p,"remove"),v=$y(p,"nextSibling"),y=$y(p,"childNodes"),C=$y(p,"parentNode");
// As per issue #47, the web-components registry is inherited by a
// new document created via createHTMLDocument. As per the spec
// (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)
// a new empty registry is used when creating a template contents owner
// document, so we use that as our parent document to ensure nothing
// is inherited.
if("function"==typeof i){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let w,E="";const{implementation:x,createNodeIterator:_,createDocumentFragment:S,getElementsByTagName:k}=o,{importNode:N}=r;let R={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};
/**
       * Expose whether this browser supports running the full DOMPurify.
       */n.isSupported="function"==typeof gy&&"function"==typeof C&&x&&void 0!==x.createHTMLDocument;const{MUSTACHE_EXPR:A,ERB_EXPR:T,TMPLIT_EXPR:O,DATA_ATTR:B,ARIA_ATTR:P,IS_SCRIPT_OR_DATA:D,ATTR_WHITESPACE:L,CUSTOM_ELEMENT:M}=uC;let{IS_ALLOWED_URI:I}=uC,F=null;
/**
       * We consider the elements and attributes below to be safe. Ideally
       * don't add any new ones but feel free to remove unwanted ones.
       */
/* allowed element names */const U=zy({},[...Vy,...qy,...Wy,...Yy,...Xy]);
/* Allowed attribute names */let z=null;const j=zy({},[...Zy,...Qy,...Jy,...eC]);
/*
       * Configure how DOMPurify should handle custom elements and their attributes as well as customized built-in elements.
       * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)
       * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)
       * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.
       */let H=Object.seal(wy(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),$=null,V=null,q=!0,W=!0,K=!1,Y=!0,G=!1,X=!0,Z=!1,Q=!1,J=!1,ee=!1,te=!1,ne=!1,oe=!0,re=!1,se=!0,ae=!1,ie={},le=null;
/* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */const de=zy({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);
/* Tags that are safe for data: URIs */let ce=null;const ue=zy({},["audio","video","img","source","image","track"]);
/* Attributes safe for values like "javascript:" */let me=null;const fe=zy({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",pe="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml";
/* Document namespace */
let be=he,ve=!1,ye=null;const Ce=zy({},[ge,pe,he],Ty);let we=zy({},["mi","mo","mn","ms","mtext"]),Ee=zy({},["annotation-xml"]);
// Certain elements are allowed in both SVG and HTML
// namespace. We need to specify them explicitly
// so that they don't get erroneously deleted from
// HTML namespace.
const xe=zy({},["title","style","font","a","script"]);
/* Parsing of strict XHTML documents */let _e=null;const Se=["application/xhtml+xml","text/html"];let ke=null,Ne=null;
/* Keep a reference to config to pass to hooks */
/* Ideally, do not touch anything below this line */
/* ______________________________________________ */
const Re=o.createElement("form"),Ae=function(e){return e instanceof RegExp||e instanceof Function},Te=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Ne||Ne!==e){if(
/* Shield configuration object from tampering */
e&&"object"==typeof e||(e={})
/* Shield configuration object from prototype pollution */,e=Hy(e),_e=
// eslint-disable-next-line unicorn/prefer-includes
-1===Se.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,
// HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.
ke="application/xhtml+xml"===_e?Ty:Ay,
/* Set configuration parameters */
F=Ly(e,"ALLOWED_TAGS")?zy({},e.ALLOWED_TAGS,ke):U,z=Ly(e,"ALLOWED_ATTR")?zy({},e.ALLOWED_ATTR,ke):j,ye=Ly(e,"ALLOWED_NAMESPACES")?zy({},e.ALLOWED_NAMESPACES,Ty):Ce,me=Ly(e,"ADD_URI_SAFE_ATTR")?zy(Hy(fe),e.ADD_URI_SAFE_ATTR,ke):fe,ce=Ly(e,"ADD_DATA_URI_TAGS")?zy(Hy(ue),e.ADD_DATA_URI_TAGS,ke):ue,le=Ly(e,"FORBID_CONTENTS")?zy({},e.FORBID_CONTENTS,ke):de,$=Ly(e,"FORBID_TAGS")?zy({},e.FORBID_TAGS,ke):{},V=Ly(e,"FORBID_ATTR")?zy({},e.FORBID_ATTR,ke):{},ie=!!Ly(e,"USE_PROFILES")&&e.USE_PROFILES,q=!1!==e.ALLOW_ARIA_ATTR,// Default true
W=!1!==e.ALLOW_DATA_ATTR,// Default true
K=e.ALLOW_UNKNOWN_PROTOCOLS||!1,// Default false
Y=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,// Default true
G=e.SAFE_FOR_TEMPLATES||!1,// Default false
X=!1!==e.SAFE_FOR_XML,// Default true
Z=e.WHOLE_DOCUMENT||!1,// Default false
ee=e.RETURN_DOM||!1,// Default false
te=e.RETURN_DOM_FRAGMENT||!1,// Default false
ne=e.RETURN_TRUSTED_TYPE||!1,// Default false
J=e.FORCE_BODY||!1,// Default false
oe=!1!==e.SANITIZE_DOM,// Default true
re=e.SANITIZE_NAMED_PROPS||!1,// Default false
se=!1!==e.KEEP_CONTENT,// Default true
ae=e.IN_PLACE||!1,// Default false
I=e.ALLOWED_URI_REGEXP||aC,be=e.NAMESPACE||he,we=e.MATHML_TEXT_INTEGRATION_POINTS||we,Ee=e.HTML_INTEGRATION_POINTS||Ee,H=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(H.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ae(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(H.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(H.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),G&&(W=!1),te&&(ee=!0)
/* Parse profile info */,ie&&(F=zy({},Xy),z=[],!0===ie.html&&(zy(F,Vy),zy(z,Zy)),!0===ie.svg&&(zy(F,qy),zy(z,Qy),zy(z,eC)),!0===ie.svgFilters&&(zy(F,Wy),zy(z,Qy),zy(z,eC)),!0===ie.mathMl&&(zy(F,Yy),zy(z,Jy),zy(z,eC)))
/* Merge configuration parameters */,e.ADD_TAGS&&(F===U&&(F=Hy(F)),zy(F,e.ADD_TAGS,ke)),e.ADD_ATTR&&(z===j&&(z=Hy(z)),zy(z,e.ADD_ATTR,ke)),e.ADD_URI_SAFE_ATTR&&zy(me,e.ADD_URI_SAFE_ATTR,ke),e.FORBID_CONTENTS&&(le===de&&(le=Hy(le)),zy(le,e.FORBID_CONTENTS,ke))
/* Add #text in case KEEP_CONTENT is set to true */,se&&(F["#text"]=!0)
/* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */,Z&&zy(F,["html","head","body"])
/* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */,F.table&&(zy(F,["tbody"]),delete $.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw Iy('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw Iy('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');
// Overwrite existing TrustedTypes policy.
w=e.TRUSTED_TYPES_POLICY,
// Sign local variables required by `sanitize`.
E=w.createHTML("")}else
// Uninitialized policy, attempt to initialize the internal dompurify policy.
void 0===w&&(w=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;
// Allow the callers to control the unique policy name
// by adding a data-tt-policy-suffix to the script element with the DOMPurify.
// Policy creation with duplicate names throws in Trusted Types.
let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){
// Policy creation failed (most likely another DOMPurify script has
// already run). Skip creating the policy, as this will only cause errors
// if TT are enforced.
return console.warn("TrustedTypes policy "+r+" could not be created."),null}}(g,s)),
// If creating the internal policy succeeded sign internal variables.
null!==w&&"string"==typeof E&&(E=w.createHTML(""));
// Prevent further manipulation of configuration.
// Not available in IE8, Safari 5, etc.
yy&&yy(e),Ne=e}},Oe=zy({},[...qy,...Wy,...Ky]),Be=zy({},[...Yy,...Gy]),Pe=function(e){Ny(n.removed,{element:e});try{
// eslint-disable-next-line unicorn/prefer-dom-node-remove
C(e).removeChild(e)}catch(t){b(e)}},De=function(e,t){try{Ny(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){Ny(n.removed,{attribute:null,from:t})}
// We void attribute values for unremovable "is" attributes
if(t.removeAttribute(e),"is"===e)if(ee||te)try{Pe(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Le=function(e){
/* Create a HTML document */
let t=null,n=null;if(J)e="<remove></remove>"+e;else{
/* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */
const t=Oy(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===_e&&be===he&&(
// Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)
e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=w?w.createHTML(e):e;
/*
         * Use the DOMParser API by default, fallback later if needs be
         * DOMParser not work for svg when has multiple root element.
         */if(be===he)try{t=(new f).parseFromString(r,_e)}catch(e){}
/* Use createHTMLDocument in case DOMParser is not available */if(!t||!t.documentElement){t=x.createDocument(be,"template",null);try{t.documentElement.innerHTML=ve?E:r}catch(e){
// Syntax error if dirtyPayload is invalid xml
}}const s=t.body||t.documentElement;
/* Work on whole document or just its body */
return e&&n&&s.insertBefore(o.createTextNode(n),s.childNodes[0]||null),be===he?k.call(t,Z?"html":"body")[0]:Z?t.documentElement:s},Me=function(e){return _.call(e.ownerDocument||e,e,
// eslint-disable-next-line no-bitwise
c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT|c.SHOW_PROCESSING_INSTRUCTION|c.SHOW_CDATA_SECTION,null)},Ie=function(e){return e instanceof m&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof u)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},Fe=function(e){return"function"==typeof l&&e instanceof l};function Ue(e,t,o){_y(e,(e=>{e.call(n,t,o,Ne)}))}
/**
       * _sanitizeElements
       *
       * @protect nodeName
       * @protect textContent
       * @protect removeChild
       * @param currentNode to check for permission to exist
       * @return true if node was killed, false if left alive
       */const ze=function(e){let t=null;
/* Execute a hook if present */
/* Check if element is clobbered or can clobber */
if(Ue(R.beforeSanitizeElements,e,null),Ie(e))return Pe(e),!0;
/* Now let's check the element's type and name */const o=ke(e.nodeName);
/* Execute a hook if present */
/* Detect mXSS attempts abusing namespace confusion */
if(Ue(R.uponSanitizeElement,e,{tagName:o,allowedTags:F}),e.hasChildNodes()&&!Fe(e.firstElementChild)&&My(/<[/\w]/g,e.innerHTML)&&My(/<[/\w]/g,e.textContent))return Pe(e),!0;
/* Remove any occurrence of processing instructions */if(7===e.nodeType)return Pe(e),!0;
/* Remove any kind of possibly harmful comments */if(X&&8===e.nodeType&&My(/<[/\w]/g,e.data))return Pe(e),!0;
/* Remove element if anything forbids its presence */if(!F[o]||$[o]){
/* Check if we have a custom element to handle */
if(!$[o]&&He(o)){if(H.tagNameCheck instanceof RegExp&&My(H.tagNameCheck,o))return!1;if(H.tagNameCheck instanceof Function&&H.tagNameCheck(o))return!1}
/* Keep content except for bad-listed elements */if(se&&!le[o]){const t=C(e)||e.parentNode,n=y(e)||e.childNodes;if(n&&t)for(let o=n.length-1;o>=0;--o){const r=h(n[o],!0);r.__removalCount=(e.__removalCount||0)+1,t.insertBefore(r,v(e))}}return Pe(e),!0}
/* Check whether element has a valid namespace */return e instanceof d&&!function(e){let t=C(e);
// In JSDOM, if we're inside shadow DOM, then parentNode
// can be null. We just simulate parent in this case.
t&&t.tagName||(t={namespaceURI:be,tagName:"template"});const n=Ay(e.tagName),o=Ay(t.tagName);return!!ye[e.namespaceURI]&&(e.namespaceURI===pe?
// The only way to switch from HTML namespace to SVG
// is via <svg>. If it happens via any other tag, then
// it should be killed.
t.namespaceURI===he?"svg"===n:
// The only way to switch from MathML to SVG is via`
// svg if parent is either <annotation-xml> or MathML
// text integration points.
t.namespaceURI===ge?"svg"===n&&("annotation-xml"===o||we[o]):Boolean(Oe[n]):e.namespaceURI===ge?
// The only way to switch from HTML namespace to MathML
// is via <math>. If it happens via any other tag, then
// it should be killed.
t.namespaceURI===he?"math"===n:
// The only way to switch from SVG to MathML is via
// <math> and HTML integration points
t.namespaceURI===pe?"math"===n&&Ee[o]:Boolean(Be[n]):e.namespaceURI===he?
// The only way to switch from SVG to HTML is via
// HTML integration points, and from MathML to HTML
// is via MathML text integration points
!(t.namespaceURI===pe&&!Ee[o])&&!(t.namespaceURI===ge&&!we[o])&&!Be[n]&&(xe[n]||!Oe[n]):!("application/xhtml+xml"!==_e||!ye[e.namespaceURI]))}(e)?(Pe(e),!0):
/* Make sure that older browsers don't get fallback-tag mXSS */
"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!My(/<\/no(script|embed|frames)/i,e.innerHTML)?(
/* Sanitize element content to be template-safe */
G&&3===e.nodeType&&(
/* Get the element's text content */
t=e.textContent,_y([A,T,O],(e=>{t=By(t,e," ")})),e.textContent!==t&&(Ny(n.removed,{element:e.cloneNode()}),e.textContent=t))
/* Execute a hook if present */,Ue(R.afterSanitizeElements,e,null),!1):(Pe(e),!0)},je=function(e,t,n){
/* Make sure attribute cannot clobber */
if(oe&&("id"===t||"name"===t)&&(n in o||n in Re))return!1;
/* Allow valid data-* attributes: At least one character after "-"
            (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)
            XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)
            We don't need to check the value; it's always URI safe. */if(W&&!V[t]&&My(B,t));else if(q&&My(P,t));else if(!z[t]||V[t]){if(
// First condition does a very basic check if a) it's basically a valid custom element tagname AND
// b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
// and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck
!(He(e)&&(H.tagNameCheck instanceof RegExp&&My(H.tagNameCheck,e)||H.tagNameCheck instanceof Function&&H.tagNameCheck(e))&&(H.attributeNameCheck instanceof RegExp&&My(H.attributeNameCheck,t)||H.attributeNameCheck instanceof Function&&H.attributeNameCheck(t))||
// Alternative, second condition checks if it's an `is`-attribute, AND
// the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck
"is"===t&&H.allowCustomizedBuiltInElements&&(H.tagNameCheck instanceof RegExp&&My(H.tagNameCheck,n)||H.tagNameCheck instanceof Function&&H.tagNameCheck(n))))return!1;
/* Check value is safe. First, is attr inert? If so, is safe */}else if(me[t]);else if(My(I,By(n,L,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Py(n,"data:")||!ce[e])if(K&&!My(D,By(n,L,"")));else if(n)return!1;return!0},He=function(e){return"annotation-xml"!==e&&Oy(e,M)},$e=function(e){
/* Execute a hook if present */
Ue(R.beforeSanitizeAttributes,e,null);const{attributes:t}=e;
/* Check if we have attributes; if not we might have a text node */if(!t||Ie(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:z,forceKeepAttr:void 0};let r=t.length;
/* Go backwards over all attributes; safely remove bad ones */for(;r--;){const s=t[r],{name:a,namespaceURI:i,value:l}=s,d=ke(a);let c="value"===a?l:Dy(l);const u=c;
/* Execute a hook if present */
/* Work around a security issue with comments inside attributes */
if(o.attrName=d,o.attrValue=c,o.keepAttr=!0,o.forceKeepAttr=void 0,// Allows developers to see this is a property they can set
Ue(R.uponSanitizeAttribute,e,o),c=o.attrValue,
/* Full DOM Clobbering protection via namespace isolation,
           * Prefix id and name attributes with `user-content-`
           */
!re||"id"!==d&&"name"!==d||(
// Remove the attribute with this value
De(a,e),
// Prefix the value and later re-create the attribute with the sanitized value
c="user-content-"+c),X&&My(/((--!?|])>)|<\/(style|title)/i,c)){De(a,e);continue}
/* Did the hooks approve of the attribute? */if(o.forceKeepAttr)continue;
/* Remove attribute */
/* Did the hooks approve of the attribute? */if(!o.keepAttr){De(a,e);continue}
/* Work around a security issue in jQuery 3.0 */if(!Y&&My(/\/>/i,c)){De(a,e);continue}
/* Sanitize attribute content to be template-safe */G&&_y([A,T,O],(e=>{c=By(c,e," ")}))
/* Is `value` valid for this attribute? */;const m=ke(e.nodeName);if(je(m,d,c)){
/* Handle attributes that require Trusted Types */
if(w&&"object"==typeof g&&"function"==typeof g.getAttributeType)if(i);else switch(g.getAttributeType(m,d)){case"TrustedHTML":c=w.createHTML(c);break;case"TrustedScriptURL":c=w.createScriptURL(c)}
/* Handle invalid data-* attribute set by try-catching it */if(c!==u)try{i?e.setAttributeNS(i,a,c):
/* Fallback to setAttribute() for browser-unrecognized namespaces e.g. "x-schema". */
e.setAttribute(a,c),Ie(e)?Pe(e):ky(n.removed)}catch(e){}}else De(a,e)}
/* Execute a hook if present */Ue(R.afterSanitizeAttributes,e,null)},Ve=function e(t){let n=null;const o=Me(t);
/* Execute a hook if present */for(Ue(R.beforeSanitizeShadowDOM,t,null);n=o.nextNode();)
/* Execute a hook if present */
Ue(R.uponSanitizeShadowNode,n,null),
/* Sanitize tags and elements */
ze(n),
/* Check attributes next */
$e(n),
/* Deep shadow DOM detected */
n.content instanceof a&&e(n.content);
/* Execute a hook if present */Ue(R.afterSanitizeShadowDOM,t,null)};
/**
       * _isValidAttribute
       *
       * @param lcTag Lowercase tag name of containing element.
       * @param lcName Lowercase attribute name.
       * @param value Attribute value.
       * @return Returns true if `value` is valid, otherwise false.
       */
// eslint-disable-next-line complexity
// eslint-disable-next-line complexity
return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,s=null,i=null,d=null;
/* Stringify, in case dirty is an object */
if(
/* Make sure we have a string to sanitize.
          DO NOT return early, as this will return the wrong type if
          the user has requested a DOM object rather than a string */
ve=!e,ve&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Fe(e)){if("function"!=typeof e.toString)throw Iy("toString is not a function");if("string"!=typeof(e=e.toString()))throw Iy("dirty is not a string, aborting")}
/* Return dirty HTML if DOMPurify cannot run */if(!n.isSupported)return e;
/* Assign config vars */if(Q||Te(t)
/* Clean up removed elements */,n.removed=[],
/* Check if dirty is correctly typed for IN_PLACE */
"string"==typeof e&&(ae=!1),ae){
/* Do some early pre-sanitization to avoid unsafe root nodes */
if(e.nodeName){const t=ke(e.nodeName);if(!F[t]||$[t])throw Iy("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof l)
/* If dirty is a DOM element, append to an empty document to avoid
             elements being stripped by the parser */
o=Le("\x3c!----\x3e"),s=o.ownerDocument.importNode(e,!0),1===s.nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?
/* Node is already a body, use as is */
o=s:
// eslint-disable-next-line unicorn/prefer-dom-node-append
o.appendChild(s);else{
/* Exit directly if we have nothing to do */
if(!ee&&!G&&!Z&&
// eslint-disable-next-line unicorn/prefer-includes
-1===e.indexOf("<"))return w&&ne?w.createHTML(e):e;
/* Initialize the document to work on */
/* Check we have a DOM node from the data */
if(o=Le(e),!o)return ee?null:ne?E:""}
/* Remove first element node (ours) if FORCE_BODY is set */o&&J&&Pe(o.firstChild)
/* Get node iterator */;const c=Me(ae?e:o);
/* Now start iterating over the created document */for(;i=c.nextNode();)
/* Sanitize tags and elements */
ze(i),
/* Check attributes next */
$e(i),
/* Shadow DOM detected, sanitize it */
i.content instanceof a&&Ve(i.content);
/* If we sanitized `dirty` in-place, return it. */if(ae)return e;
/* Return sanitized string or DOM */if(ee){if(te)for(d=S.call(o.ownerDocument);o.firstChild;)
// eslint-disable-next-line unicorn/prefer-dom-node-append
d.appendChild(o.firstChild);else d=o;return(z.shadowroot||z.shadowrootmode)&&(
/*
              AdoptNode() is not used because internal state is not reset
              (e.g. the past names map of a HTMLFormElement), this is safe
              in theory but we would rather not risk another attack vector.
              The state that is cloned by importNode() is explicitly defined
              by the specs.
            */
d=N.call(r,d,!0)),d}let u=Z?o.outerHTML:o.innerHTML;
/* Serialize doctype if allowed */return Z&&F["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&My(dC,o.ownerDocument.doctype.name)&&(u="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+u)
/* Sanitize final string template-safe */,G&&_y([A,T,O],(e=>{u=By(u,e," ")})),w&&ne?w.createHTML(u):u},n.setConfig=function(){Te(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Q=!0},n.clearConfig=function(){Ne=null,Q=!1},n.isValidAttribute=function(e,t,n){
/* Initialize shared config vars if necessary. */
Ne||Te({});const o=ke(e),r=ke(t);return je(o,r,n)},n.addHook=function(e,t){"function"==typeof t&&Ny(R[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=Sy(R[e],t);return-1===n?void 0:Ry(R[e],n,1)[0]}return ky(R[e])},n.removeHooks=function(e){R[e]=[]},n.removeAllHooks=function(){R={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const gC=Dt.each,pC=Dt.trim,hC=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],bC={ftp:21,http:80,https:443,mailto:25},vC=["img","video"],yC=(e,t,n)=>{const o=(e=>{try{return decodeURIComponent(e)}catch(t){return unescape(e)}})(t).replace(/\s/g,"");return!e.allow_script_urls&&(!!/((java|vb)script|mhtml):/i.test(o)||!e.allow_html_data_urls&&(/^data:image\//i.test(o)?((e,t)=>C(e)?!e:!C(t)||!H(vC,t))(e.allow_svg_data_urls,n)&&/^data:image\/svg\+xml/i.test(o):/^data:/i.test(o)))};class CC{static parseDataUri(e){let t;const n=decodeURIComponent(e).split(","),o=/data:([^;]+)/.exec(n[0]);return o&&(t=o[1]),{type:t,data:n[1]}}static isDomSafe(e,t,n={}){if(n.allow_script_urls)return!0;{const o=ws.decode(e).replace(/[\s\u0000-\u001F]+/g,"");return!yC(n,o,t)}}static getDocumentBaseUrl(e){var t;let n;return n=0!==e.protocol.indexOf("http")&&"file:"!==e.protocol?null!==(t=e.href)&&void 0!==t?t:"":e.protocol+"//"+e.host+e.pathname,/^[^:]+:\/\/\/?[^\/]+\//.test(n)&&(n=n.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(n)||(n+="/")),n}constructor(e,t={}){this.path="",this.directory="",e=pC(e),this.settings=t;const n=t.base_uri,o=this;if(/^([\w\-]+):([^\/]{2})/i.test(e)||/^\s*#/.test(e))return void(o.source=e);const r=0===e.indexOf("//");if(0!==e.indexOf("/")||r||(e=(n&&n.protocol||"http")+"://mce_host"+e),!/^[\w\-]*:?\/\//.test(e)){const t=n?n.path:new CC(document.location.href).directory;if(""===(null==n?void 0:n.protocol))e="//mce_host"+o.toAbsPath(t,e);else{const r=/([^#?]*)([#?]?.*)/.exec(e);r&&(e=(n&&n.protocol||"http")+"://mce_host"+o.toAbsPath(t,r[1])+r[2])}}e=e.replace(/@@/g,"(mce_at)");const s=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*):?([^:@\/]*))?@)?(\[[a-zA-Z0-9:.%]+\]|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/.exec(e);s&&gC(hC,((e,t)=>{let n=s[t];n&&(n=n.replace(/\(mce_at\)/g,"@@")),o[e]=n})),n&&(o.protocol||(o.protocol=n.protocol),o.userInfo||(o.userInfo=n.userInfo),o.port||"mce_host"!==o.host||(o.port=n.port),o.host&&"mce_host"!==o.host||(o.host=n.host),o.source=""),r&&(o.protocol="")}setPath(e){const t=/^(.*?)\/?(\w+)?$/.exec(e);t&&(this.path=t[0],this.directory=t[1],this.file=t[2]),this.source="",this.getURI()}toRelative(e){if("./"===e)return e;const t=new CC(e,{base_uri:this});if("mce_host"!==t.host&&this.host!==t.host&&t.host||this.port!==t.port||this.protocol!==t.protocol&&""!==t.protocol)return t.getURI();const n=this.getURI(),o=t.getURI();if(n===o||"/"===n.charAt(n.length-1)&&n.substr(0,n.length-1)===o)return n;let r=this.toRelPath(this.path,t.path);return t.query&&(r+="?"+t.query),t.anchor&&(r+="#"+t.anchor),r}toAbsolute(e,t){const n=new CC(e,{base_uri:this});return n.getURI(t&&this.isSameOrigin(n))}isSameOrigin(e){if(this.host==e.host&&this.protocol==e.protocol){if(this.port==e.port)return!0;const t=this.protocol?bC[this.protocol]:null;if(t&&(this.port||t)==(e.port||t))return!0}return!1}toRelPath(e,t){let n,o,r=0,s="";const a=e.substring(0,e.lastIndexOf("/")).split("/"),i=t.split("/");if(a.length>=i.length)for(n=0,o=a.length;n<o;n++)if(n>=i.length||a[n]!==i[n]){r=n+1;break}if(a.length<i.length)for(n=0,o=i.length;n<o;n++)if(n>=a.length||a[n]!==i[n]){r=n+1;break}if(1===r)return t;for(n=0,o=a.length-(r-1);n<o;n++)s+="../";for(n=r-1,o=i.length;n<o;n++)s+=n!==r-1?"/"+i[n]:i[n];return s}toAbsPath(e,t){let n=0;const o=/\/$/.test(t)?"/":"",r=e.split("/"),s=t.split("/"),a=[];gC(r,(e=>{e&&a.push(e)}));const i=[];for(let e=s.length-1;e>=0;e--)0!==s[e].length&&"."!==s[e]&&(".."!==s[e]?n>0?n--:i.push(s[e]):n++);const l=a.length-n;let d;return d=l<=0?oe(i).join("/"):a.slice(0,l).join("/")+"/"+oe(i).join("/"),0!==d.indexOf("/")&&(d="/"+d),o&&d.lastIndexOf("/")!==d.length-1&&(d+=o),d}getURI(e=!1){let t;return this.source&&!e||(t="",e||(this.protocol?t+=this.protocol+"://":t+="//",this.userInfo&&(t+=this.userInfo+"@"),this.host&&(t+=this.host),this.port&&(t+=":"+this.port)),this.path&&(t+=this.path),this.query&&(t+="?"+this.query),this.anchor&&(t+="#"+this.anchor),this.source=t),this.source}}const wC=Dt.makeMap("src,href,data,background,action,formaction,poster,xlink:href"),EC="data-mce-type";let xC=0;const _C=(e,t,n,o,r)=>{var s,a,i,l;const d=t.validate,c=n.getSpecialElements();8===e.nodeType&&!t.allow_conditional_comments&&/^\[if/i.test(null!==(s=e.nodeValue)&&void 0!==s?s:"")&&(e.nodeValue=" "+e.nodeValue);const u=null!==(a=null==r?void 0:r.tagName)&&void 0!==a?a:e.nodeName.toLowerCase();if("html"!==o&&n.isValid(o))return void(C(r)&&(r.allowedTags[u]=!0));if(1!==e.nodeType||"body"===u)return;const f=Cn(e),g=on(f,EC),p=tn(f,"data-mce-bogus");if(!g&&m(p))return void("all"===p?Eo(f):xo(f));const h=n.getElementRule(u);if(!d||h){if(C(r)&&(r.allowedTags[u]=!0),d&&h&&!g){if(q(null!==(i=h.attributesForced)&&void 0!==i?i:[],(e=>{Jt(f,e.name,"{$uid}"===e.value?"mce_"+xC++:e.value)})),q(null!==(l=h.attributesDefault)&&void 0!==l?l:[],(e=>{on(f,e.name)||Jt(f,e.name,"{$uid}"===e.value?"mce_"+xC++:e.value)})),h.attributesRequired&&!$(h.attributesRequired,(e=>on(f,e))))return void xo(f);if(h.removeEmptyAttrs&&(e=>{const t=e.dom.attributes;return null==t||0===t.length})(f))return void xo(f);h.outputName&&h.outputName!==u&&((e,t)=>{const n=((e,t)=>{const n=vn(t),o=sn(e);return en(n,o),n})(e,t);ho(e,n);const o=In(e);Co(n,o),Eo(e)})(f,h.outputName)}}else _e(c,u)?Eo(f):xo(f)},SC=(e,t,n,o,r,s)=>"html"!==n&&!Mr(o)||!(r in wC&&yC(e,s,o))&&(!e.validate||t.isValid(o,r)||$e(r,"data-")||$e(r,"aria-")),kC=(e,t)=>e.hasAttribute(EC)&&("id"===t||"class"===t||"style"===t),NC=(e,t)=>e in t.getBoolAttrs(),RC=(e,t,n,o)=>{const{attributes:r}=e;for(let s=r.length-1;s>=0;s--){const a=r[s],i=a.name,l=a.value;SC(t,n,o,e.tagName.toLowerCase(),i,l)||kC(e,i)?NC(i,n)&&e.setAttribute(i,i):e.removeAttribute(i)}},AC=(e,t,n)=>{const o=fC();return o.addHook("uponSanitizeElement",((o,r)=>{_C(o,e,t,n.track(o),r)})),o.addHook("uponSanitizeAttribute",((o,r)=>{((e,t,n,o,r)=>{const s=e.tagName.toLowerCase(),{attrName:a,attrValue:i}=r;r.keepAttr=SC(t,n,o,s,a,i),r.keepAttr?(r.allowedAttributes[a]=!0,NC(a,n)&&(r.attrValue=a),t.allow_svg_data_urls&&$e(i,"data:image/svg+xml")&&(r.forceKeepAttr=!0)):kC(e,a)&&(r.forceKeepAttr=!0)})(o,e,t,n.current(),r)})),o},TC=(e,t)=>{const n=fC(),o=t.allow_mathml_annotation_encodings,r=p(o)&&o.length>0;n.addHook("uponSanitizeElement",((e,n)=>{var s;const a=null!==(s=n.tagName)&&void 0!==s?s:e.nodeName.toLowerCase();((e,n)=>r&&"semantics"===n?I.some(!0):"annotation"===n?I.some(Jo(e)&&(e=>{const t=e.getAttribute("encoding");return r&&m(t)&&H(o,t)})(e)):p(t.extended_mathml_elements)&&t.extended_mathml_elements.includes(n)?I.from(!0):I.none())(e,a).each((o=>{n.allowedTags[a]=o,!o&&t.sanitize&&Jo(e)&&e.remove()}))})),n.addHook("uponSanitizeAttribute",((e,n)=>{p(t.extended_mathml_attributes)&&t.extended_mathml_attributes.includes(n.attrName)&&(n.forceKeepAttr=!0)})),n.sanitize(e,{IN_PLACE:!0,USE_PROFILES:{mathMl:!0}})},OC=e=>t=>{const n=Fr(t);if("svg"===n)(e=>{const t=["type","href","role","arcrole","title","show","actuate","label","from","to"].map((e=>`xlink:${e}`)),n={IN_PLACE:!0,USE_PROFILES:{html:!0,svg:!0,svgFilters:!0},ALLOWED_ATTR:t};fC().sanitize(e,n)})(t);else{if("math"!==n)throw new Error("Not a namespace element");TC(t,e)}},BC=Dt.makeMap,PC=Dt.extend,DC=(e,t,n,o)=>{const r=e.name,s=r in n&&"title"!==r&&"textarea"!==r&&"noscript"!==r,a=t.childNodes;for(let t=0,r=a.length;t<r;t++){const r=a[t],i=new tp(r.nodeName.toLowerCase(),r.nodeType);if(Jo(r)){const e=r.attributes;for(let t=0,n=e.length;t<n;t++){const n=e[t];i.attr(n.name,n.value)}Mr(i.name)&&(o(r),i.value=r.innerHTML)}else lr(r)?(i.value=r.data,s&&(i.raw=!0)):(ur(r)||dr(r)||cr(r))&&(i.value=r.data);Mr(i.name)||DC(i,r,n,o),e.append(i)}},LC=(e={},t=Fs())=>{const n=ty(),o=ty(),r={validate:!0,root_name:"body",sanitize:!0,...e},s=new DOMParser,a=((e,t)=>{const n=(()=>{const e=Dr(),t=()=>e.get().map(Fr).getOr("html");return{track:n=>(Ir(n)?e.set(n):e.get().exists((e=>!e.contains(n)))&&e.clear(),t()),current:t,reset:()=>{e.clear()}}})();if(e.sanitize){const o=AC(e,t,n),r=(t,r)=>{o.sanitize(t,((e,t)=>{const n={IN_PLACE:!0,ALLOW_UNKNOWN_PROTOCOLS:!0,ALLOWED_TAGS:["#comment","#cdata-section","body"],ALLOWED_ATTR:[],SAFE_FOR_XML:!1};return n.PARSER_MEDIA_TYPE=t,e.allow_script_urls?n.ALLOWED_URI_REGEXP=/.*/:e.allow_html_data_urls&&(n.ALLOWED_URI_REGEXP=/^(?!(\w+script|mhtml):)/i),n})(e,r)),o.removed=[],n.reset()};return{sanitizeHtmlElement:r,sanitizeNamespaceElement:OC(e)}}return{sanitizeHtmlElement:(o,r)=>{const s=document.createNodeIterator(o,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT|NodeFilter.SHOW_TEXT);let a;for(;a=s.nextNode();){const o=n.track(a);_C(a,e,t,o),Jo(a)&&RC(a,e,t,o)}n.reset()},sanitizeNamespaceElement:_}})(r,t),i=n.addFilter,l=n.getFilters,d=n.removeFilter,c=o.addFilter,u=o.getFilters,f=o.removeFilter,g=(e,n)=>{const o=m(n.attr(EC)),r=1===n.type&&!_e(e,n.name)&&!Jr(t,n)&&!Mr(n.name);return 3===n.type||r&&!o},p={schema:t,addAttributeFilter:c,getAttributeFilters:u,removeAttributeFilter:f,addNodeFilter:i,getNodeFilters:l,removeNodeFilter:d,parse:(e,n={})=>{var o;const i=r.validate,d=null!==(o=n.context)&&void 0!==o?o:r.root_name,c=((e,n,o="html")=>{const r="xhtml"===o?"application/xhtml+xml":"text/html",i=_e(t.getSpecialElements(),n.toLowerCase()),l=i?`<${n}>${e}</${n}>`:e,d=s.parseFromString("xhtml"===o?`<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>${l}</body></html>`:/^[\s]*<head/i.test(e)||/^[\s]*<html/i.test(e)||/^[\s]*<!DOCTYPE/i.test(e)?`<html>${l}</html>`:`<body>${l}</body>`,r).body;return a.sanitizeHtmlElement(d,r),i?d.firstChild:d})(e,d,n.format);Wr(t,c);const m=new tp(d,11);DC(m,c,t.getSpecialElements(),a.sanitizeNamespaceElement),c.innerHTML="";const[f,p]=((e,t,n,o)=>{const r=n.validate,s=t.getNonEmptyElements(),a=t.getWhitespaceElements(),i=PC(BC("script,style,head,html,body,title,meta,param"),t.getBlockElements()),l=Ms(t),d=/[ \t\r\n]+/g,c=/^[ \t\r\n]+/,u=/[ \t\r\n]+$/,m=e=>{let t=e.parent;for(;C(t);){if(t.name in a)return!0;t=t.parent}return!1},f=n=>n.name in i||Jr(t,n)||Mr(n.name)&&n.parent===e,g=(t,n)=>{const r=n?t.prev:t.next;return!C(r)&&!y(t.parent)&&f(t.parent)&&(t.parent!==e||!0===o.isRootContent)};return[e=>{var t;if(3===e.type&&!m(e)){let n=null!==(t=e.value)&&void 0!==t?t:"";n=n.replace(d," "),(((e,t)=>C(e)&&(t(e)||"br"===e.name))(e.prev,f)||g(e,!0))&&(n=n.replace(c,"")),0===n.length||" "===n&&e.prev&&8===e.prev.type&&e.next&&8===e.next.type?e.remove():e.value=n}},e=>{var i;if(1===e.type){const i=t.getElementRule(e.name);if(r&&i){const r=Nb(t,s,a,e);i.paddInEmptyBlock&&r&&(e=>{let n=e;for(;C(n);){if(n.name in l)return Nb(t,s,a,n);n=n.parent}return!1})(e)?Sb(n,o,f,e):i.removeEmpty&&r?f(e)?e.remove():e.unwrap():i.paddEmpty&&(r||(e=>{var t;return kb(e,"#text")&&(null===(t=null==e?void 0:e.firstChild)||void 0===t?void 0:t.value)===qo})(e))&&Sb(n,o,f,e)}}else if(3===e.type&&!m(e)){let t=null!==(i=e.value)&&void 0!==i?i:"";(e.next&&f(e.next)||g(e,!1))&&(t=t.replace(u,"")),0===t.length?e.remove():e.value=t}}]})(m,t,r,n),h=[],b=i?e=>((e,n)=>{Ob(t,e)&&n.push(e)})(e,h):_,v={nodes:{},attributes:{}},w=e=>Eb(l(),u(),e,v);if(((e,t,n)=>{const o=[];for(let n=e,r=n;n;r=n,n=n.walk()){const s=n;q(t,(e=>e(s))),y(s.parent)&&s!==e?n=r:o.push(s)}for(let e=o.length-1;e>=0;e--){const t=o[e];q(n,(e=>e(t)))}})(m,[f,w],[p,b]),h.reverse(),i&&h.length>0)if(n.context){const{pass:e,fail:o}=K(h,(e=>e.parent===m));Tb(o,t,m,w),n.invalid=e.length>0}else Tb(h,t,m,w);const E=((e,t)=>{var n;const o=null!==(n=t.forced_root_block)&&void 0!==n?n:e.forced_root_block;return!1===o?"":!0===o?"p":o})(r,n);return E&&("body"===m.name||n.isRootContent)&&((e,n)=>{const o=PC(BC("script,style,head,html,body,title,meta,param"),t.getBlockElements()),s=/^[ \t\r\n]+/,a=/[ \t\r\n]+$/;let i=e.firstChild,l=null;const d=e=>{var t,n;e&&(i=e.firstChild,i&&3===i.type&&(i.value=null===(t=i.value)||void 0===t?void 0:t.replace(s,"")),i=e.lastChild,i&&3===i.type&&(i.value=null===(n=i.value)||void 0===n?void 0:n.replace(a,"")))};if(t.isValidChild(e.name,n.toLowerCase())){for(;i;){const t=i.next;g(o,i)?(l||(l=new tp(n,1),l.attr(r.forced_root_block_attrs),e.insert(l,i)),l.append(i)):(d(l),l=null),i=t}d(l)}})(m,E),n.invalid||xb(v,n),m}};return((e,t)=>{var n,o;const r=e.schema;e.addAttributeFilter("href",(e=>{let n=e.length;const o=e=>{const t=e?Dt.trim(e):"";return/\b(noopener)\b/g.test(t)?t:(e=>e.split(" ").filter((e=>e.length>0)).concat(["noopener"]).sort().join(" "))(t)};if(!t.allow_unsafe_link_target)for(;n--;){const t=e[n];"a"===t.name&&"_blank"===t.attr("target")&&t.attr("rel",o(t.attr("rel")))}})),t.allow_html_in_named_anchor||e.addAttributeFilter("id,name",(e=>{let t,n,o,r,s=e.length;for(;s--;)if(r=e[s],"a"===r.name&&r.firstChild&&!r.attr("href"))for(o=r.parent,t=r.lastChild;t&&o;)n=t.prev,o.insert(t,r),t=n})),t.fix_list_elements&&e.addNodeFilter("ul,ol",(e=>{let t,n,o=e.length;for(;o--;)if(t=e[o],n=t.parent,n&&("ul"===n.name||"ol"===n.name))if(t.prev&&"li"===t.prev.name)t.prev.append(t);else{const e=new tp("li",1);e.attr("style","list-style-type: none"),t.wrap(e)}}));const s=r.getValidClasses();t.validate&&s&&e.addAttributeFilter("class",(e=>{var t;let n=e.length;for(;n--;){const o=e[n],r=null!==(t=o.attr("class"))&&void 0!==t?t:"",a=Dt.explode(r," ");let i="";for(let e=0;e<a.length;e++){const t=a[e];let n=!1,r=s["*"];r&&r[t]&&(n=!0),r=s[o.name],!n&&r&&r[t]&&(n=!0),n&&(i&&(i+=" "),i+=t)}i.length||(i=null),o.attr("class",i)}})),((e,t)=>{const{blob_cache:n}=t;if(n){const t=e=>{const t=e.attr("src");(e=>e.attr("src")===Tt.transparentSrc||C(e.attr("data-mce-placeholder")))(e)||(e=>C(e.attr("data-mce-bogus")))(e)||y(t)||dy(n,t,!0).each((t=>{e.attr("src",t.blobUri())}))};e.addAttributeFilter("src",(e=>q(e,t)))}})(e,t);const a=null!==(n=t.sandbox_iframes)&&void 0!==n&&n,i=me(null!==(o=t.sandbox_iframes_exclusions)&&void 0!==o?o:[]);t.convert_unsafe_embeds&&e.addNodeFilter("object,embed",(e=>q(e,(e=>{e.replace((({type:e,src:t,width:n,height:o}={},r,s)=>{const a=(e=>v(e)?"iframe":fy(e,"image")?"img":fy(e,"video")?"video":fy(e,"audio")?"audio":"iframe")(e),i=new tp(a,1);return i.attr("audio"===a?{src:t}:{src:t,width:n,height:o}),"audio"!==a&&"video"!==a||i.attr("controls",""),"iframe"===a&&r&&my(i,s),i})({type:e.attr("type"),src:"object"===e.name?e.attr("data"):e.attr("src"),width:e.attr("width"),height:e.attr("height")},a,i))})))),a&&e.addNodeFilter("iframe",(e=>q(e,(e=>my(e,i)))))})(p,r),((e,t,n)=>{t.inline_styles&&ny(e,t,n)})(p,r,t),p},MC=(e,t,n)=>{const o=(e=>jb(e)?Cp({validate:!1}).serialize(e):e)(e),r=t(o);if(r.isDefaultPrevented())return r;if(jb(e)){if(r.content!==o){const t=LC({validate:!1,forced_root_block:!1,...n}).parse(r.content,{context:e.name});return{...r,content:t}}return{...r,content:e}}return r},IC=e=>({sanitize:Tc(e),sandbox_iframes:Mc(e),sandbox_iframes_exclusions:Ic(e)}),FC=(e,t)=>{if(t.no_events)return _l.value(t);{const n=((e,t)=>e.dispatch("BeforeGetContent",t))(e,t);return n.isDefaultPrevented()?_l.error(Il(e,{content:"",...n}).content):_l.value(n)}},UC=(e,t,n)=>{if(n.no_events)return t;{const o=MC(t,(t=>Il(e,{...n,content:t})),IC(e));return o.content}},zC=(e,t)=>{if(t.no_events)return _l.value(t);{const n=MC(t.content,(n=>((e,t)=>e.dispatch("BeforeSetContent",t))(e,{...t,content:n})),IC(e));return n.isDefaultPrevented()?(Ml(e,n),_l.error(void 0)):_l.value(n)}},jC=(e,t,n)=>{n.no_events||Ml(e,{...n,content:t})},HC=(e,t,n)=>({element:e,width:t,rows:n}),$C=(e,t)=>({element:e,cells:t}),VC=(e,t)=>({x:e,y:t}),qC=(e,t)=>nn(e,t).bind(Ze).getOr(1),WC=(e,t,n)=>{const o=e.rows;return!!(o[n]?o[n].cells:[])[t]},KC=e=>X(e,((e,t)=>t.cells.length>e?t.cells.length:e),0),YC=(e,t)=>{const n=e.rows;for(let e=0;e<n.length;e++){const o=n[e].cells;for(let n=0;n<o.length;n++)if(Sn(o[n],t))return I.some(VC(n,e))}return I.none()},GC=(e,t,n,o,r)=>{const s=[],a=e.rows;for(let e=n;e<=r;e++){const n=a[e].cells,r=t<o?n.slice(t,o+1):n.slice(o,t+1);s.push($C(a[e].element,r))}return s},XC=e=>((e,t)=>{const n=Ia(e.element),o=vn("tbody");return Co(o,t),vo(n,o),n})(e,(e=>V(e.rows,(e=>{const t=V(e.cells,(e=>{const t=Fa(e);return rn(t,"colspan"),rn(t,"rowspan"),t})),n=Ia(e.element);return Co(n,t),n})))(e)),ZC=(e,t,n)=>{const o=Cn(t.commonAncestorContainer),r=Hp(o,e),s=Y(r,(e=>n.isWrapper($t(e)))),a=((e,t)=>Q(e,(e=>"li"===$t(e)&&_m(e,t))).fold(N([]),(t=>(e=>Q(e,(e=>"ul"===$t(e)||"ol"===$t(e))))(e).map((e=>{const t=vn($t(e)),n=Ce(fo(e),((e,t)=>$e(t,"list-style")));return lo(t,n),[vn("li"),t]})).getOr([]))))(r,t),i=s.concat(a.length?a:(e=>Ya(e)?Tn(e).filter(Ka).fold(N([]),(t=>[e,t])):Ka(e)?[e]:[])(o));return V(i,Ia)},QC=()=>ug([]),JC=(e,t)=>((e,t)=>Jn(t,"table",T(Sn,e)))(e,t[0]).bind((e=>{const n=t[0],o=t[t.length-1],r=(e=>{const t=HC(Ia(e),0,[]);return q(Uo(e,"tr"),((e,n)=>{q(Uo(e,"td,th"),((o,r)=>{((e,t,n,o,r)=>{const s=qC(r,"rowspan"),a=qC(r,"colspan"),i=e.rows;for(let e=n;e<n+s;e++){i[e]||(i[e]=$C(Fa(o),[]));for(let o=t;o<t+a;o++)i[e].cells[o]=e===n&&o===t?r:Ia(r)}})(t,((e,t,n)=>{for(;WC(e,t,n);)t++;return t})(t,r,n),n,e,o)}))})),HC(t.element,KC(t.rows),t.rows)})(e);return((e,t,n)=>YC(e,t).bind((t=>YC(e,n).map((n=>((e,t,n)=>{const o=t.x,r=t.y,s=n.x,a=n.y,i=r<a?GC(e,o,r,s,a):GC(e,o,a,s,r);return HC(e.element,KC(i),i)})(e,t,n))))))(r,n,o).map((e=>ug([XC(e)])))})).getOrThunk(QC),ew=(e,t,n)=>{const o=ym(t,e);return o.length>0?JC(e,o):((e,t,n)=>t.length>0&&t[0].collapsed?QC():((e,t,n)=>((e,t)=>{const n=X(t,((e,t)=>(vo(t,e),t)),e);return t.length>0?ug([n]):n})(Cn(t.cloneContents()),ZC(e,t,n)))(e,t[0],n))(e,t,n)},tw=(e,t)=>t>=0&&t<e.length&&hm(e.charAt(t)),nw=e=>ni(e.innerText),ow=e=>Jo(e)?e.outerHTML:lr(e)?ws.encodeRaw(e.data,!1):ur(e)?"\x3c!--"+e.data+"--\x3e":"",rw=(e,t)=>(((e,t)=>{let n=0;q(e,(e=>{0===e[0]?n++:1===e[0]?(((e,t,n)=>{const o=(e=>{let t;const n=document.createElement("div"),o=document.createDocumentFragment();for(e&&(n.innerHTML=e);t=n.firstChild;)o.appendChild(t);return o})(t);if(e.hasChildNodes()&&n<e.childNodes.length){const t=e.childNodes[n];e.insertBefore(o,t)}else e.appendChild(o)})(t,e[1],n),n++):2===e[0]&&((e,t)=>{if(e.hasChildNodes()&&t<e.childNodes.length){const n=e.childNodes[t];e.removeChild(n)}})(t,n)}))})(((e,t)=>{const n=e.length+t.length+2,o=new Array(n),r=new Array(n),s=(n,o,r,a,l)=>{const d=i(n,o,r,a);if(null===d||d.start===o&&d.diag===o-a||d.end===n&&d.diag===n-r){let s=n,i=r;for(;s<o||i<a;)s<o&&i<a&&e[s]===t[i]?(l.push([0,e[s]]),++s,++i):o-n>a-r?(l.push([2,e[s]]),++s):(l.push([1,t[i]]),++i)}else{s(n,d.start,r,d.start-d.diag,l);for(let t=d.start;t<d.end;++t)l.push([0,e[t]]);s(d.end,o,d.end-d.diag,a,l)}},a=(n,o,r,s)=>{let a=n;for(;a-o<s&&a<r&&e[a]===t[a-o];)++a;return((e,t,n)=>({start:e,end:t,diag:n}))(n,a,o)},i=(n,s,i,l)=>{const d=s-n,c=l-i;if(0===d||0===c)return null;const u=d-c,m=c+d,f=(m%2==0?m:m+1)/2;let g,p,h,b,v;for(o[1+f]=n,r[1+f]=s+1,g=0;g<=f;++g){for(p=-g;p<=g;p+=2){for(h=p+f,p===-g||p!==g&&o[h-1]<o[h+1]?o[h]=o[h+1]:o[h]=o[h-1]+1,b=o[h],v=b-n+i-p;b<s&&v<l&&e[b]===t[v];)o[h]=++b,++v;if(u%2!=0&&u-g<=p&&p<=u+g&&r[h-u]<=o[h])return a(r[h-u],p+n-i,s,l)}for(p=u-g;p<=u+g;p+=2){for(h=p+f-u,p===u-g||p!==u+g&&r[h+1]<=r[h-1]?r[h]=r[h+1]-1:r[h]=r[h-1],b=r[h]-1,v=b-n+i-p;b>=n&&v>=i&&e[b]===t[v];)r[h]=b--,v--;if(u%2==0&&-g<=p&&p<=g&&r[h]<=o[h+u])return a(r[h],p+n-i,s,l)}}return null},l=[];return s(0,e.length,0,t.length,l),l})(V(ce(t.childNodes),ow),e),t),t),sw=Le((()=>document.implementation.createHTMLDocument("undo"))),aw=e=>{const t=e.serializer.getTempAttrs(),n=gp(e.getBody(),t);return(e=>null!==e.querySelector("iframe"))(n)?{type:"fragmented",fragments:Y(V(ce(n.childNodes),S(ni,ow)),(e=>e.length>0)),content:"",bookmark:null,beforeBookmark:null}:{type:"complete",fragments:null,content:ni(n.innerHTML),bookmark:null,beforeBookmark:null}},iw=(e,t,n)=>{const o=n?t.beforeBookmark:t.bookmark;"fragmented"===t.type?rw(t.fragments,e.getBody()):e.setContent(t.content,{format:"raw",no_selection:!C(o)||!om(o)||!o.isFakeCaret}),o&&(e.selection.moveToBookmark(o),e.selection.scrollIntoView())},lw=e=>"fragmented"===e.type?e.fragments.join(""):e.content,dw=e=>{const t=vn("body",sw());return ko(t,lw(e)),q(Uo(t,"*[data-mce-bogus]"),xo),So(t)},cw=(e,t)=>!(!e||!t)&&(!!((e,t)=>lw(e)===lw(t))(e,t)||((e,t)=>dw(e)===dw(t))(e,t)),uw=e=>0===e.get(),mw=(e,t,n)=>{uw(n)&&(e.typing=t)},fw=(e,t)=>{e.typing&&(mw(e,!1,t),e.add())},gw=e=>({init:{bindEvents:_},undoManager:{beforeChange:(t,n)=>((e,t,n)=>{uw(t)&&n.set(wl(e.selection))})(e,t,n),add:(t,n,o,r,s,a)=>((e,t,n,o,r,s,a)=>{const i=aw(e),l=Dt.extend(s||{},i);if(!uw(o)||e.removed)return null;const d=t.data[n.get()];if(e.dispatch("BeforeAddUndo",{level:l,lastLevel:d,originalEvent:a}).isDefaultPrevented())return null;if(d&&cw(d,l))return null;t.data[n.get()]&&r.get().each((e=>{t.data[n.get()].beforeBookmark=e}));const c=Kd(e);if(c&&t.data.length>c){for(let e=0;e<t.data.length-1;e++)t.data[e]=t.data[e+1];t.data.length--,n.set(t.data.length)}l.bookmark=wl(e.selection),n.get()<t.data.length-1&&(t.data.length=n.get()+1),t.data.push(l),n.set(t.data.length-1);const u={level:l,lastLevel:d,originalEvent:a};return n.get()>0?(e.setDirty(!0),e.dispatch("AddUndo",u),e.dispatch("change",u)):e.dispatch("AddUndo",u),l})(e,t,n,o,r,s,a),undo:(t,n,o)=>((e,t,n,o)=>{let r;return t.typing&&(t.add(),t.typing=!1,mw(t,!1,n)),o.get()>0&&(o.set(o.get()-1),r=t.data[o.get()],iw(e,r,!0),e.setDirty(!0),e.dispatch("Undo",{level:r})),r})(e,t,n,o),redo:(t,n)=>((e,t,n)=>{let o;return t.get()<n.length-1&&(t.set(t.get()+1),o=n[t.get()],iw(e,o,!1),e.setDirty(!0),e.dispatch("Redo",{level:o})),o})(e,t,n),clear:(t,n)=>((e,t,n)=>{t.data=[],n.set(0),t.typing=!1,e.dispatch("ClearUndos")})(e,t,n),reset:e=>(e=>{e.clear(),e.add()})(e),hasUndo:(t,n)=>((e,t,n)=>n.get()>0||t.typing&&t.data[0]&&!cw(aw(e),t.data[0]))(e,t,n),hasRedo:(e,t)=>((e,t)=>t.get()<e.data.length-1&&!e.typing)(e,t),transact:(e,t,n)=>((e,t,n)=>(fw(e,t),e.beforeChange(),e.ignore(n),e.add()))(e,t,n),ignore:(e,t)=>((e,t)=>{try{e.set(e.get()+1),t()}finally{e.set(e.get()-1)}})(e,t),extra:(t,n,o,r)=>((e,t,n,o,r)=>{if(t.transact(o)){const o=t.data[n.get()].bookmark,s=t.data[n.get()-1];iw(e,s,!0),t.transact(r)&&(t.data[n.get()-1].beforeBookmark=o)}})(e,t,n,o,r)},formatter:{match:(t,n,o,r)=>Zb(e,t,n,o,r),matchAll:(t,n)=>((e,t,n)=>{const o=[],r={},s=e.selection.getStart();return e.dom.getParent(s,(s=>{for(let a=0;a<t.length;a++){const i=t[a];!r[i]&&Xb(e,s,i,n)&&(r[i]=!0,o.push(i))}}),e.dom.getRoot()),o})(e,t,n),matchNode:(t,n,o,r)=>Xb(e,t,n,o,r),canApply:t=>((e,t)=>{const n=e.formatter.get(t),o=e.dom;if(n&&e.selection.isEditable()){const t=e.selection.getStart(),r=$m(o,t);for(let e=n.length-1;e>=0;e--){const t=n[e];if(!Wm(t))return!0;for(let e=r.length-1;e>=0;e--)if(o.is(r[e],t.selector))return!0}}return!1})(e,t),closest:t=>((e,t)=>{const n=t=>Sn(t,Cn(e.getBody()));return I.from(e.selection.getStart(!0)).bind((o=>Vb(Cn(o),(n=>ue(t,(t=>((t,n)=>Xb(e,t.dom,n)?I.some(n):I.none())(n,t)))),n))).getOrNull()})(e,t),apply:(t,n,o)=>Yv(e,t,n,o),remove:(t,n,o,r)=>Hv(e,t,n,o,r),toggle:(t,n,o)=>((e,t,n,o)=>{const r=e.formatter.get(t);r&&(!Zb(e,t,n,o)||"toggle"in r[0]&&!r[0].toggle?Yv(e,t,n,o):Hv(e,t,n,o))})(e,t,n,o),formatChanged:(t,n,o,r,s)=>((e,t,n,o,r,s)=>(((e,t,n,o,r,s)=>{const a=t.get();q(n.split(","),(t=>{const n=xe(a,t).getOrThunk((()=>{const e={withSimilar:{state:Br(!1),similar:!0,callbacks:[]},withoutSimilar:{state:Br(!1),similar:!1,callbacks:[]},withVars:[]};return a[t]=e,e})),i=()=>{const n=Qv(e);return Zv(e,n,t,r,s).isSome()};if(v(s)){const e=r?n.withSimilar:n.withoutSimilar;e.callbacks.push(o),1===e.callbacks.length&&e.state.set(i())}else n.withVars.push({state:Br(i()),similar:r,vars:s,callback:o})})),t.set(a)})(e,t,n,o,r,s),{unbind:()=>((e,t,n)=>{const o=e.get();q(t.split(","),(e=>xe(o,e).each((t=>{o[e]={withSimilar:{...t.withSimilar,callbacks:Y(t.withSimilar.callbacks,(e=>e!==n))},withoutSimilar:{...t.withoutSimilar,callbacks:Y(t.withoutSimilar.callbacks,(e=>e!==n))},withVars:Y(t.withVars,(e=>e.callback!==n))}})))),e.set(o)})(t,n,o)}))(e,t,n,o,r,s)},editor:{getContent:t=>((e,t)=>I.from(e.getBody()).fold(N("tree"===t.format?new tp("body",11):""),(n=>bp(e,t,n))))(e,t),setContent:(t,n)=>((e,t,n)=>I.from(e.getBody()).map((o=>jb(t)?((e,t,n,o)=>{_b(e.parser.getNodeFilters(),e.parser.getAttributeFilters(),n);const r=Cp({validate:!1},e.schema).serialize(n),s=ni(Za(Cn(t))?r:Dt.trim(r));return Hb(e,s,o.no_selection),{content:n,html:s}})(e,o,t,n):((e,t,n,o)=>{if(0===(n=ni(n)).length||/^\s+$/.test(n)){const r='<br data-mce-bogus="1">';"TABLE"===t.nodeName?n="<tr><td>"+r+"</td></tr>":/^(UL|OL)$/.test(t.nodeName)&&(n="<li>"+r+"</li>");const s=ed(e);return e.schema.isValidChild(t.nodeName.toLowerCase(),s.toLowerCase())?(n=r,n=e.dom.createHTML(s,td(e),n)):n||(n=r),Hb(e,n,o.no_selection),{content:n,html:n}}{"raw"!==o.format&&(n=Cp({validate:!1},e.schema).serialize(e.parser.parse(n,{isRootContent:!0,insert:!0})));const r=Za(Cn(t))?n:Dt.trim(n);return Hb(e,r,o.no_selection),{content:r,html:r}}})(e,o,t,n))).getOr({content:t,html:jb(n.content)?"":n.content}))(e,t,n),insertContent:(t,n)=>zb(e,t,n),addVisual:t=>((e,t)=>{const n=e.dom,o=C(t)?t:e.getBody();q(n.select("table,a",o),(t=>{switch(t.nodeName){case"TABLE":const o=nc(e),r=n.getAttrib(t,"border");r&&"0"!==r||!e.hasVisual?n.removeClass(t,o):n.addClass(t,o);break;case"A":if(!n.getAttrib(t,"href")){const o=n.getAttrib(t,"name")||t.id,r=oc(e);o&&e.hasVisual?n.addClass(t,r):n.removeClass(t,r)}}})),e.dispatch("VisualAid",{element:t,hasVisual:e.hasVisual})})(e,t)},selection:{getContent:(t,n)=>((e,t,n={})=>{const o=((e,t)=>({...e,format:t,get:!0,selection:!0,getInner:!0}))(n,t);return FC(e,o).fold(R,(t=>{const n=((e,t)=>{if("text"===t.format)return(e=>I.from(e.selection.getRng()).map((t=>{var n;const o=I.from(e.dom.getParent(t.commonAncestorContainer,e.dom.isBlock)),r=e.getBody(),s=(e=>e.map((e=>e.nodeName)).getOr("div").toLowerCase())(o),a=Cn(t.cloneContents());pp(a),hp(a);const i=e.dom.add(r,s,{"data-mce-bogus":"all",style:"overflow: hidden; opacity: 0;"},a.dom),l=nw(i),d=ni(null!==(n=i.textContent)&&void 0!==n?n:"");if(e.dom.remove(i),tw(d,0)||tw(d,d.length-1)){const e=o.getOr(r),t=nw(e),n=t.indexOf(l);return-1===n?l:(tw(t,n-1)?" ":"")+l+(tw(t,n+l.length)?" ":"")}return l})).getOr(""))(e);{const n=((e,t)=>{const n=e.selection.getRng(),o=e.dom.create("body"),r=e.selection.getSel(),s=Xg(e,vm(r)),a=t.contextual?ew(Cn(e.getBody()),s,e.schema).dom:n.cloneContents();return a&&o.appendChild(a),e.selection.serializer.serialize(o,t)})(e,t);return"tree"===t.format?n:e.selection.isCollapsed()?"":n}})(e,t);return UC(e,n,t)}))})(e,t,n)},autocompleter:{addDecoration:_,removeDecoration:_},raw:{getModel:()=>I.none()}}),pw=e=>_e(e.plugins,"rtc"),hw=e=>e.rtcInstance?e.rtcInstance:gw(e),bw=e=>{const t=e.rtcInstance;if(t)return t;throw new Error("Failed to get RTC instance not yet initialized.")},vw=e=>bw(e).init.bindEvents(),yw=e=>0===e.dom.length?(Eo(e),I.none()):I.some(e),Cw=(e,t,n,o,r)=>{e.bind((e=>((o?Ch:yh)(e.dom,o?e.dom.length:0,r),t.filter(Yt).map((t=>((e,t,n,o,r)=>{const s=e.dom,a=t.dom,i=o?s.length:a.length;o?(wh(s,a,r,!1,!o),n.setStart(a,i)):(wh(a,s,r,!1,!o),n.setEnd(a,i))})(e,t,n,o,r)))))).orThunk((()=>{const e=((e,t)=>e.filter((e=>yf.isBookmarkNode(e.dom))).bind(t?Dn:Pn))(t,o).or(t).filter(Yt);return e.map((e=>((e,t,n)=>{Tn(e).each((o=>{const r=e.dom;t&&ch(o,el(r,0),n)?yh(r,0,n):!t&&uh(o,el(r,r.length),n)&&Ch(r,r.length,n)}))})(e,o,r)))}))},ww=(e,t,n)=>{if(_e(e,t)){const o=Y(e[t],(e=>e!==n));0===o.length?delete e[t]:e[t]=o}};const Ew=e=>!(!e||!e.ownerDocument)&&kn(Cn(e.ownerDocument),Cn(e)),xw=(e,t,n,o)=>{let r,s;const{selectorChangedWithUnbind:a}=((e,t)=>{let n,o;const r=(t,n)=>Q(n,(n=>e.is(n,t))),s=t=>e.getParents(t,void 0,e.getRoot());return{selectorChangedWithUnbind:(e,a)=>(n||(n={},o={},t.on("NodeChange",(e=>{const t=e.element,a=s(t),i={};pe(n,((e,t)=>{r(t,a).each((n=>{o[t]||(q(e,(e=>{e(!0,{node:n,selector:t,parents:a})})),o[t]=e),i[t]=e}))})),pe(o,((e,n)=>{i[n]||(delete o[n],q(e,(e=>{e(!1,{node:t,selector:n,parents:a})})))}))}))),n[e]||(n[e]=[]),n[e].push(a),r(e,s(t.selection.getStart())).each((()=>{o[e]=n[e]})),{unbind:()=>{ww(n,e,a),ww(o,e,a)}})}})(e,o),i=(e,t)=>((e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,selection:!0,content:t}))(n,t);zC(e,o).each((t=>{const n=((e,t)=>{if("raw"!==t.format){const n=e.selection.getRng(),o=e.dom.getParent(n.commonAncestorContainer,e.dom.isBlock),r=o?{context:o.nodeName.toLowerCase()}:{},s=e.parser.parse(t.content,{forced_root_block:!1,...r,...t});return Cp({validate:!1},e.schema).serialize(s)}return t.content})(e,t),o=e.selection.getRng();((e,t,n)=>{const o=I.from(t.firstChild).map(Cn),r=I.from(t.lastChild).map(Cn);e.deleteContents(),e.insertNode(t);const s=o.bind(Pn).filter(Yt).bind(yw),a=r.bind(Dn).filter(Yt).bind(yw);Cw(s,o,e,!0,n),Cw(a,r,e,!1,n),e.collapse(!1)})(o,o.createContextualFragment(n),e.schema),e.selection.setRng(o),Vg(e,o),jC(e,n,t)}))})(o,e,t),l=e=>{const t=c();t.collapse(!!e),u(t)},d=()=>t.getSelection?t.getSelection():t.document.selection,c=()=>{let n;const a=(e,t,n)=>{try{return t.compareBoundaryPoints(e,n)}catch(e){return-1}},i=t.document;if(C(o.bookmark)&&!Gf(o)){const e=If(o);if(e.isSome())return e.map((e=>Xg(o,[e])[0])).getOr(i.createRange())}try{const e=d();e&&!Qo(e.anchorNode)&&(n=e.rangeCount>0?e.getRangeAt(0):i.createRange(),n=Xg(o,[n])[0])}catch(e){}if(n||(n=i.createRange()),mr(n.startContainer)&&n.collapsed){const t=e.getRoot();n.setStart(t,0),n.setEnd(t,0)}return r&&s&&(0===a(n.START_TO_START,n,r)&&0===a(n.END_TO_END,n,r)?n=s:(r=null,s=null)),n},u=(e,t)=>{if(!(e=>!!e&&Ew(e.startContainer)&&Ew(e.endContainer))(e))return;const n=d();if(e=o.dispatch("SetSelectionRange",{range:e,forward:t}).range,n){s=e;try{n.removeAllRanges(),n.addRange(e)}catch(e){}!1===t&&n.extend&&(n.collapse(e.endContainer,e.endOffset),n.extend(e.startContainer,e.startOffset)),r=n.rangeCount>0?n.getRangeAt(0):null}if(!e.collapsed&&e.startContainer===e.endContainer&&(null==n?void 0:n.setBaseAndExtent)&&e.endOffset-e.startOffset<2&&e.startContainer.hasChildNodes()){const t=e.startContainer.childNodes[e.startOffset];t&&"IMG"===t.nodeName&&(n.setBaseAndExtent(e.startContainer,e.startOffset,e.endContainer,e.endOffset),n.anchorNode===e.startContainer&&n.focusNode===e.endContainer||n.setBaseAndExtent(t,0,t,1))}o.dispatch("AfterSetSelectionRange",{range:e,forward:t})},m=()=>{const t=d(),n=null==t?void 0:t.anchorNode,o=null==t?void 0:t.focusNode;if(!t||!n||!o||Qo(n)||Qo(o))return!0;const r=e.createRng(),s=e.createRng();try{r.setStart(n,t.anchorOffset),r.collapse(!0),s.setStart(o,t.focusOffset),s.collapse(!0)}catch(e){return!0}return r.compareBoundaryPoints(r.START_TO_START,s)<=0},f={dom:e,win:t,serializer:n,editor:o,expand:(t={type:"word"})=>u(Sg(e).expand(c(),t)),collapse:l,setCursorLocation:(t,n)=>{const r=e.createRng();C(t)&&C(n)?(r.setStart(t,n),r.setEnd(t,n),u(r),l(!1)):(Sm(e,r,o.getBody(),!0),u(r))},getContent:e=>((e,t={})=>((e,t,n)=>bw(e).selection.getContent(t,n))(e,t.format?t.format:"html",t))(o,e),setContent:i,getBookmark:(e,t)=>g.getBookmark(e,t),moveToBookmark:e=>g.moveToBookmark(e),select:(t,n)=>(((e,t,n)=>I.from(t).bind((t=>I.from(t.parentNode).map((o=>{const r=e.nodeIndex(t),s=e.createRng();return s.setStart(o,r),s.setEnd(o,r+1),n&&(Sm(e,s,t,!0),Sm(e,s,t,!1)),s})))))(e,t,n).each(u),t),isCollapsed:()=>{const e=c(),t=d();return!(!e||e.item)&&(e.compareEndPoints?0===e.compareEndPoints("StartToEnd",e):!t||e.collapsed)},isEditable:()=>{if(o.mode.isReadOnly())return!1;const t=c(),n=o.getBody().querySelectorAll('[data-mce-selected="1"]');return n.length>0?ne(n,(t=>e.isEditable(t.parentElement))):qg(e,t)},isForward:m,setNode:t=>(i(e.getOuterHTML(t)),t),getNode:()=>((e,t)=>{if(!t)return e;let n=t.startContainer,o=t.endContainer;const r=t.startOffset,s=t.endOffset;let a=t.commonAncestorContainer;t.collapsed||(n===o&&s-r<2&&n.hasChildNodes()&&(a=n.childNodes[r]),lr(n)&&lr(o)&&(n=n.length===r?Gg(n.nextSibling,!0):n.parentNode,o=0===s?Gg(o.previousSibling,!1):o.parentNode,n&&n===o&&(a=n)));const i=lr(a)?a.parentNode:a;return er(i)?i:e})(o.getBody(),c()),getSel:d,setRng:u,getRng:c,getStart:e=>Kg(o.getBody(),c(),e),getEnd:e=>Yg(o.getBody(),c(),e),getSelectedBlocks:(t,n)=>((e,t,n,o)=>{const r=[],s=e.getRoot(),a=e.getParent(n||Kg(s,t,t.collapsed),e.isBlock),i=e.getParent(o||Yg(s,t,t.collapsed),e.isBlock);if(a&&a!==s&&r.push(a),a&&i&&a!==i){let t;const n=new $o(a,s);for(;(t=n.next())&&t!==i;)e.isBlock(t)&&r.push(t)}return i&&a!==i&&i!==s&&r.push(i),r})(e,c(),t,n),normalize:()=>{const t=c(),n=d();if(!(vm(n).length>1)&&km(o)){const n=Eg(e,t);return n.each((e=>{u(e,m())})),n.getOr(t)}return t},selectorChanged:(e,t)=>(a(e,t),f),selectorChangedWithUnbind:a,getScrollContainer:()=>{let t,n=e.getRoot();for(;n&&"BODY"!==n.nodeName;){if(n.scrollHeight>n.clientHeight){t=n;break}n=n.parentNode}return t},scrollIntoView:(e,t)=>{C(e)?((e,t,n)=>{(e.inline?jg:$g)(e,t,n)})(o,e,t):Vg(o,c(),t)},placeCaretAt:(e,t)=>u(gg(e,t,o.getDoc())),getBoundingClientRect:()=>{const e=c();return e.collapsed?el.fromRangeStart(e).getClientRects()[0]:e.getBoundingClientRect()},destroy:()=>{t=r=s=null,p.destroy()}},g=yf(f),p=og(f,o);return f.bookmarkManager=g,f.controlSelection=p,f},_w=(e,t,n)=>{-1===Dt.inArray(t,n)&&(e.addAttributeFilter(n,((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.push(n))},Sw=(e,t)=>{const n=["data-mce-selected"],o={entity_encoding:"named",remove_trailing_brs:!0,pad_empty_with_br:!1,...e},r=t&&t.dom?t.dom:ma.DOM,s=t&&t.schema?t.schema:Fs(o),a=LC(o,s);return((e,t,n)=>{e.addAttributeFilter("data-mce-tabindex",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];o.attr("tabindex",o.attr("data-mce-tabindex")),o.attr(t,null)}})),e.addAttributeFilter("src,href,style",((e,o)=>{const r="data-mce-"+o,s=t.url_converter,a=t.url_converter_scope;let i=e.length;for(;i--;){const t=e[i];let l=t.attr(r);void 0!==l?(t.attr(o,l.length>0?l:null),t.attr(r,null)):(l=t.attr(o),"style"===o?l=n.serializeStyle(n.parseStyle(l),t.name):s&&(l=s.call(a,l,o,t.name)),t.attr(o,l.length>0?l:null))}})),e.addAttributeFilter("class",(e=>{let t=e.length;for(;t--;){const n=e[t];let o=n.attr("class");o&&(o=o.replace(/(?:^|\s)mce-item-\w+(?!\S)/g,""),n.attr("class",o.length>0?o:null))}})),e.addAttributeFilter("data-mce-type",((e,t,n)=>{let o=e.length;for(;o--;){const t=e[o];if("bookmark"===t.attr("data-mce-type")&&!n.cleanup){const e=I.from(t.firstChild).exists((e=>{var t;return!ti(null!==(t=e.value)&&void 0!==t?t:"")}));e?t.unwrap():t.remove()}}})),e.addNodeFilter("script,style",((e,n)=>{var o;const r=e=>e.replace(/(<!--\[CDATA\[|\]\]-->)/g,"\n").replace(/^[\r\n]*|[\r\n]*$/g,"").replace(/^\s*((<!--)?(\s*\/\/)?\s*<!\[CDATA\[|(<!--\s*)?\/\*\s*<!\[CDATA\[\s*\*\/|(\/\/)?\s*<!--|\/\*\s*<!--\s*\*\/)\s*[\r\n]*/gi,"").replace(/\s*(\/\*\s*\]\]>\s*\*\/(-->)?|\s*\/\/\s*\]\]>(-->)?|\/\/\s*(-->)?|\]\]>|\/\*\s*-->\s*\*\/|\s*-->\s*)\s*$/g,"");let s=e.length;for(;s--;){const a=e[s],i=a.firstChild,l=null!==(o=null==i?void 0:i.value)&&void 0!==o?o:"";if("script"===n){const e=a.attr("type");e&&a.attr("type","mce-no/type"===e?null:e.replace(/^mce\-/,"")),"xhtml"===t.element_format&&i&&l.length>0&&(i.value="// <![CDATA[\n"+r(l)+"\n// ]]>")}else"xhtml"===t.element_format&&i&&l.length>0&&(i.value="\x3c!--\n"+r(l)+"\n--\x3e")}})),e.addNodeFilter("#comment",(e=>{let o=e.length;for(;o--;){const r=e[o],s=r.value;t.preserve_cdata&&0===(null==s?void 0:s.indexOf("[CDATA["))?(r.name="#cdata",r.type=4,r.value=n.decode(s.replace(/^\[CDATA\[|\]\]$/g,""))):0===(null==s?void 0:s.indexOf("mce:protected "))&&(r.name="#text",r.type=3,r.raw=!0,r.value=unescape(s).substr(14))}})),e.addNodeFilter("xml:namespace,input",((e,t)=>{let n=e.length;for(;n--;){const o=e[n];7===o.type?o.remove():1===o.type&&("input"!==t||o.attr("type")||o.attr("type","text"))}})),e.addAttributeFilter("data-mce-type",(t=>{q(t,(t=>{"format-caret"===t.attr("data-mce-type")&&(t.isEmpty(e.schema.getNonEmptyElements())?t.remove():t.unwrap())}))})),e.addAttributeFilter("data-mce-src,data-mce-href,data-mce-style,data-mce-selected,data-mce-expando,data-mce-block,data-mce-type,data-mce-resize,data-mce-placeholder",((e,t)=>{let n=e.length;for(;n--;)e[n].attr(t,null)})),t.remove_trailing_brs&&((e,t,n)=>{t.addNodeFilter("br",((t,o,r)=>{const s=Dt.extend({},n.getBlockElements()),a=n.getNonEmptyElements(),i=n.getWhitespaceElements();s.body=1;const l=e=>e.name in s||Jr(n,e);for(let o=0,d=t.length;o<d;o++){let d=t[o],c=d.parent;if(c&&l(c)&&d===c.lastChild){let t=d.prev;for(;t;){const e=t.name;if("span"!==e||"bookmark"!==t.attr("data-mce-type")){"br"===e&&(d=null);break}t=t.prev}if(d&&(d.remove(),Nb(n,a,i,c))){const t=n.getElementRule(c.name);t&&(t.removeEmpty?c.remove():t.paddEmpty&&Sb(e,r,l,c))}}else{let e=d;for(;c&&c.firstChild===e&&c.lastChild===e&&(e=c,!s[c.name]);)c=c.parent;if(e===c){const e=new tp("#text",3);e.value=qo,d.replace(e)}}}}))})(t,e,e.schema)})(a,o,r),{schema:s,addNodeFilter:a.addNodeFilter,addAttributeFilter:a.addAttributeFilter,serialize:(e,n={})=>{const i={format:"html",...n},l=((e,t,n)=>((e,t)=>C(e)&&e.hasEventListeners("PreProcess")&&!t.no_events)(e,n)?((e,t,n)=>{let o;const r=e.dom;let s=t.cloneNode(!0);const a=document.implementation;if(a.createHTMLDocument){const e=a.createHTMLDocument("");Dt.each("BODY"===s.nodeName?s.childNodes:[s],(t=>{e.body.appendChild(e.importNode(t,!0))})),s="BODY"!==s.nodeName?e.body.firstChild:e.body,o=r.doc,r.doc=e}return((e,t)=>{e.dispatch("PreProcess",t)})(e,{...n,node:s}),o&&(r.doc=o),s})(e,t,n):t)(t,e,i),d=((e,t,n)=>{const o=ni(n.getInner?t.innerHTML:e.getOuterHTML(t));return n.selection||Za(Cn(t))?o:Dt.trim(o)})(r,l,i),c=((e,t,n)=>{const o=n.selection?{forced_root_block:!1,...n}:n,r=e.parse(t,o);return(e=>{const t=e=>"br"===(null==e?void 0:e.name),n=e.lastChild;if(t(n)){const e=n.prev;t(e)&&(n.remove(),e.remove())}})(r),r})(a,d,i);return"tree"===i.format?c:((e,t,n,o,r)=>{const s=((e,t,n)=>Cp(e,t).serialize(n))(t,n,o);return((e,t,n)=>{if(!t.no_events&&e){const o=((e,t)=>e.dispatch("PostProcess",t))(e,{...t,content:n});return o.content}return n})(e,r,s)})(t,o,s,c,i)},addRules:s.addValidElements,setRules:s.setValidElements,addTempAttr:T(_w,a,n),getTempAttrs:N(n),getNodeFilters:a.getNodeFilters,getAttributeFilters:a.getAttributeFilters,removeNodeFilter:a.removeNodeFilter,removeAttributeFilter:a.removeAttributeFilter}},kw=(e,t)=>{const n=Sw(e,t);return{schema:n.schema,addNodeFilter:n.addNodeFilter,addAttributeFilter:n.addAttributeFilter,serialize:n.serialize,addRules:n.addRules,setRules:n.setRules,addTempAttr:n.addTempAttr,getTempAttrs:n.getTempAttrs,getNodeFilters:n.getNodeFilters,getAttributeFilters:n.getAttributeFilters,removeNodeFilter:n.removeNodeFilter,removeAttributeFilter:n.removeAttributeFilter}},Nw=(e,t,n={})=>{const o=((e,t)=>({format:"html",...e,set:!0,content:t}))(n,t);return zC(e,o).map((t=>{const n=((e,t,n)=>hw(e).editor.setContent(t,n))(e,t.content,t);return jC(e,n.html,t),n.content})).getOr(t)},Rw="autoresize_on_init,content_editable_state,padd_empty_with_br,block_elements,boolean_attributes,editor_deselector,editor_selector,elements,file_browser_callback_types,filepicker_validator_handler,force_hex_style_colors,force_p_newlines,gecko_spellcheck,images_dataimg_filter,media_scripts,mode,move_caret_before_on_enter_elements,non_empty_elements,self_closing_elements,short_ended_elements,special,spellchecker_select_languages,spellchecker_whitelist,tab_focus,tabfocus_elements,table_responsive_width,text_block_elements,text_inline_elements,toolbar_drawer,types,validate,whitespace_elements,paste_enable_default_filters,paste_filter_drop,paste_word_valid_elements,paste_retain_style_properties,paste_convert_word_fake_lists,template_cdate_classes,template_mdate_classes,template_selected_content_classes,template_preview_replace_values,template_replace_values,templates,template_cdate_format,template_mdate_format".split(","),Aw=[],Tw="bbcode,colorpicker,contextmenu,fullpage,legacyoutput,spellchecker,template,textcolor,rtc".split(","),Ow=[],Bw=(e,t)=>{const n=Y(t,(t=>_e(e,t)));return ae(n)},Pw=e=>{const t=Bw(e,Rw),n=e.forced_root_block;return!1!==n&&""!==n||t.push("forced_root_block (false only)"),ae(t)},Dw=e=>Bw(e,Aw),Lw=(e,t)=>{const n=Dt.makeMap(e.plugins," "),o=Y(t,(e=>_e(n,e)));return ae(o)},Mw=e=>Lw(e,Tw),Iw=e=>Lw(e,Ow.map((e=>e.name))),Fw=e=>Q(Ow,(t=>t.name===e)).fold((()=>e),(t=>t.replacedWith?`${e}, replaced by ${t.replacedWith}`:e)),Uw=ma.DOM,zw=e=>I.from(e).each((e=>e.destroy())),jw=(()=>{const e={};return{add:(t,n)=>{e[t]=n},get:t=>e[t]?e[t]:{icons:{}},has:t=>_e(e,t)}})(),Hw=ya.ModelManager,$w=(e,t)=>t.dom[e],Vw=(e,t)=>parseInt(co(t,e),10),qw=T($w,"clientWidth"),Ww=T($w,"clientHeight"),Kw=T(Vw,"margin-top"),Yw=T(Vw,"margin-left"),Gw=e=>{const t=[],n=()=>{const t=e.theme;return t&&t.getNotificationManagerImpl?t.getNotificationManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a NotificationManager implementation.")};return{open:e,close:e,getArgs:e}})()},o=()=>I.from(t[0]),r=()=>{o().each((e=>{e.reposition()}))},s=e=>{J(t,(t=>t===e)).each((e=>{t.splice(e,1)}))},a=(o,a=!0)=>e.removed||!(e=>{return(t=e.inline?e.getBody():e.getContentAreaContainer(),I.from(t).map(Cn)).map(Yn).getOr(!1);var t})(e)?{}:(a&&e.dispatch("BeforeOpenNotification",{notification:o}),Q(t,(e=>{return t=n().getArgs(e),r=o,!(t.type!==r.type||t.text!==r.text||t.progressBar||t.timeout||r.progressBar||r.timeout);var t,r})).getOrThunk((()=>{e.editorManager.setActive(e);const a=n().open(o,(()=>{s(a)}),(()=>Xf(e)));return(e=>{t.push(e)})(a),r(),e.dispatch("OpenNotification",{notification:{...a}}),a}))),i=N(t);return(e=>{e.on("SkinLoaded",(()=>{const t=Od(e);t&&a({text:t,type:"warning",timeout:0},!1),r()})),e.on("show ResizeEditor ResizeWindow NodeChange ToggleView FullscreenStateChanged",(()=>{requestAnimationFrame(r)})),e.on("remove",(()=>{q(t.slice(),(e=>{n().close(e)}))})),e.on("keydown",(e=>{var t;const n="f12"===(null===(t=e.key)||void 0===t?void 0:t.toLowerCase())||123===e.keyCode;e.altKey&&n&&(e.preventDefault(),o().map((e=>Cn(e.getEl()))).each((e=>Ef(e))))}))})(e),{open:a,close:()=>{o().each((e=>{n().close(e),s(e),r()}))},getNotifications:i}},Xw=ya.PluginManager,Zw=ya.ThemeManager,Qw=e=>{let t=[];const n=()=>{const t=e.theme;return t&&t.getWindowManagerImpl?t.getWindowManagerImpl():(()=>{const e=()=>{throw new Error("Theme did not provide a WindowManager implementation.")};return{open:e,openUrl:e,alert:e,confirm:e,close:e}})()},o=(e,t)=>(...n)=>t?t.apply(e,n):void 0,r=n=>{(t=>{e.dispatch("CloseWindow",{dialog:t})})(n),t=Y(t,(e=>e!==n)),0===t.length&&e.focus()},s=n=>{e.editorManager.setActive(e),Mf(e),e.ui.show();const o=n();return(n=>{t.push(n),(t=>{e.dispatch("OpenWindow",{dialog:t})})(n)})(o),o};return e.on("remove",(()=>{q(t,(e=>{n().close(e)}))})),{open:(e,t)=>s((()=>n().open(e,t,r))),openUrl:e=>s((()=>n().openUrl(e,r))),alert:(e,t,r)=>{const s=n();s.alert(e,o(r||s,t))},confirm:(e,t,r)=>{const s=n();s.confirm(e,o(r||s,t))},close:()=>{I.from(t[t.length-1]).each((e=>{n().close(e),r(e)}))}}},Jw=(e,t)=>{e.notificationManager.open({type:"error",text:t})},eE=(e,t)=>{e._skinLoaded?Jw(e,t):e.on("SkinLoaded",(()=>{Jw(e,t)}))},tE=(e,t,n)=>{Pl(e,t,{message:n}),console.error(n)},nE=(e,t,n)=>n?`Failed to load ${e}: ${n} from url ${t}`:`Failed to load ${e} url: ${t}`,oE=(e,...t)=>{const n=window.console;n&&(n.error?n.error(e,...t):n.log(e,...t))},rE=(e,t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},sE=(e,t,n)=>{pn(e,t)&&!n?gn(e,t):n&&mn(e,t)},aE=e=>{const t=Cn(e.getBody());sE(t,"mce-content-readonly",!0),e.selection.controlSelection.hideResizeRect(),e._selectionOverrides.hideFakeCaret(),(e=>{I.from(e.selection.getNode()).each((e=>{e.removeAttribute("data-mce-selected")}))})(e)},iE=e=>{const t=Cn(e.getBody());sE(t,"mce-content-readonly",!1),e.hasEditableRoot()&&ro(t,!0),((e,t)=>{rE(e,"StyleWithCSS",t),rE(e,"enableInlineTableEditing",t),rE(e,"enableObjectResizing",t)})(e,!1),Xf(e)&&e.focus(),(e=>{e.selection.setRng(e.selection.getRng())})(e),e.nodeChanged()},lE=e=>jc(e),dE="data-mce-contenteditable",cE=(e,t)=>{const n=Cn(e.getBody());t?(aE(e),ro(n,!1),q(Uo(n,'*[contenteditable="true"]'),(e=>{Jt(e,dE,"true"),ro(e,!1)}))):(q(Uo(n,`*[${dE}="true"]`),(e=>{rn(e,dE),ro(e,!0)})),iE(e))},uE=e=>{e.parser.addAttributeFilter("contenteditable",(t=>{lE(e)&&q(t,(e=>{e.attr(dE,e.attr("contenteditable")),e.attr("contenteditable","false")}))})),e.serializer.addAttributeFilter(dE,(t=>{lE(e)&&q(t,(e=>{e.attr("contenteditable",e.attr(dE))}))})),e.serializer.addTempAttr(dE)},mE=["copy"],fE=e=>"content/"+e+"/content.css",gE=(e,t)=>{const n=e.editorManager.baseURL+"/skins/content",o=`content${e.editorManager.suffix}.css`;return V(t,(t=>(e=>tinymce.Resource.has(fE(e)))(t)?t:(e=>/^[a-z0-9\-]+$/i.test(e))(t)&&!e.inline?`${n}/${t}/${o}`:e.documentBaseURI.toAbsolute(t)))},pE=(e,t)=>{const n={};return{findAll:(o,r=M)=>{const s=Y((e=>e?ce(e.getElementsByTagName("img")):[])(o),(t=>{const n=t.src;return!t.hasAttribute("data-mce-bogus")&&!t.hasAttribute("data-mce-placeholder")&&!(!n||n===Tt.transparentSrc)&&($e(n,"blob:")?!e.isUploaded(n)&&r(t):!!$e(n,"data:")&&r(t))})),a=V(s,(e=>{const o=e.src;if(_e(n,o))return n[o].then((t=>m(t)?t:{image:e,blobInfo:t.blobInfo}));{const r=((e,t)=>{const n=()=>Promise.reject("Invalid data URI");if($e(t,"blob:")){const s=e.getByUri(t);return C(s)?Promise.resolve(s):(o=t,$e(o,"blob:")?(e=>fetch(e).then((e=>e.ok?e.blob():Promise.reject())).catch((()=>Promise.reject({message:`Cannot convert ${e} to Blob. Resource might not exist or is inaccessible.`,uriType:"blob"}))))(o):$e(o,"data:")?(r=o,new Promise(((e,t)=>{oy(r).bind((({type:e,data:t,base64Encoded:n})=>ry(e,t,n))).fold((()=>t("Invalid data URI")),e)}))):Promise.reject("Unknown URI format")).then((t=>sy(t).then((o=>iy(o,!1,(n=>I.some(ly(e,t,n)))).getOrThunk(n)))))}var o,r;return $e(t,"data:")?dy(e,t).fold(n,(e=>Promise.resolve(e))):Promise.reject("Unknown image data format")})(t,o).then((t=>(delete n[o],{image:e,blobInfo:t}))).catch((e=>(delete n[o],e)));return n[o]=r,r}}));return Promise.all(a)}}},hE=()=>{let e={};const t=(e,t)=>({status:e,resultUri:t}),n=t=>t in e;return{hasBlobUri:n,getResultUri:t=>{const n=e[t];return n?n.resultUri:null},isPending:t=>!!n(t)&&1===e[t].status,isUploaded:t=>!!n(t)&&2===e[t].status,markPending:n=>{e[n]=t(1,null)},markUploaded:(n,o)=>{e[n]=t(2,o)},removeFailed:t=>{delete e[t]},destroy:()=>{e={}}}};let bE=0;const vE=(e,t)=>{const n={},o=(e,n)=>new Promise(((o,r)=>{const s=new XMLHttpRequest;s.open("POST",t.url),s.withCredentials=t.credentials,s.upload.onprogress=e=>{n(e.loaded/e.total*100)},s.onerror=()=>{r("Image upload failed due to a XHR Transport error. Code: "+s.status)},s.onload=()=>{if(s.status<200||s.status>=300)return void r("HTTP Error: "+s.status);const e=JSON.parse(s.responseText);var n,a;e&&m(e.location)?o((n=t.basePath,a=e.location,n?n.replace(/\/$/,"")+"/"+a.replace(/^\//,""):a)):r("Invalid JSON: "+s.responseText)};const a=new FormData;a.append("file",e.blob(),e.filename()),s.send(a)})),r=w(t.handler)?t.handler:o,s=(e,t)=>({url:t,blobInfo:e,status:!0}),a=(e,t)=>({url:"",blobInfo:e,status:!1,error:t}),i=(e,t)=>{Dt.each(n[e],(e=>{e(t)})),delete n[e]};return{upload:(l,d)=>t.url||r!==o?((t,o)=>(t=Dt.grep(t,(t=>!e.isUploaded(t.blobUri()))),Promise.all(Dt.map(t,(t=>e.isPending(t.blobUri())?(e=>{const t=e.blobUri();return new Promise((e=>{n[t]=n[t]||[],n[t].push(e)}))})(t):((t,n,o)=>(e.markPending(t.blobUri()),new Promise((r=>{let l,d;try{const c=()=>{l&&(l.close(),d=_)},u=n=>{c(),e.markUploaded(t.blobUri(),n),i(t.blobUri(),s(t,n)),r(s(t,n))},f=n=>{c(),e.removeFailed(t.blobUri()),i(t.blobUri(),a(t,n)),r(a(t,n))};d=e=>{e<0||e>100||I.from(l).orThunk((()=>I.from(o).map(P))).each((t=>{l=t,t.progressBar.value(e)}))},n(t,d).then(u,(e=>{f(m(e)?{message:e}:e)}))}catch(e){r(a(t,e))}}))))(t,r,o))))))(l,d):new Promise((e=>{e([])}))}},yE=e=>()=>e.notificationManager.open({text:e.translate("Image uploading..."),type:"info",timeout:-1,progressBar:!0}),CE=(e,t)=>vE(t,{url:md(e),basePath:fd(e),credentials:gd(e),handler:pd(e)}),wE=e=>{const t=(()=>{let e=[];const t=e=>{if(!e.blob||!e.base64)throw new Error("blob and base64 representations of the image are required for BlobInfo to be created");const t=e.id||"blobid"+bE+++(()=>{const e=()=>Math.round(4294967295*Pa()).toString(36);return"s"+(new Date).getTime().toString(36)+e()+e()+e()})(),n=e.name||t,o=e.blob;var r;return{id:N(t),name:N(n),filename:N(e.filename||n+"."+(r=o.type,{"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"}[r.toLowerCase()]||"dat")),blob:N(o),base64:N(e.base64),blobUri:N(e.blobUri||URL.createObjectURL(o)),uri:N(e.uri)}},n=t=>Q(e,t).getOrUndefined(),o=e=>n((t=>t.id()===e));return{create:(e,n,o,r,s)=>{if(m(e))return t({id:e,name:r,filename:s,blob:n,base64:o});if(f(e))return t(e);throw new Error("Unknown input type")},add:t=>{o(t.id())||e.push(t)},get:o,getByUri:e=>n((t=>t.blobUri()===e)),getByData:(e,t)=>n((n=>n.base64()===e&&n.blob().type===t)),findFirst:n,removeByUri:t=>{e=Y(e,(e=>e.blobUri()!==t||(URL.revokeObjectURL(e.blobUri()),!1)))},destroy:()=>{q(e,(e=>{URL.revokeObjectURL(e.blobUri())})),e=[]}}})();let n,o;const r=hE(),s=[],a=t=>n=>e.selection?t(n):[],i=(e,t,n)=>{let o=0;do{o=e.indexOf(t,o),-1!==o&&(e=e.substring(0,o)+n+e.substr(o+t.length),o+=n.length-t.length+1)}while(-1!==o);return e},l=(e,t,n)=>{const o=`src="${n}"${n===Tt.transparentSrc?' data-mce-placeholder="1"':""}`;return e=i(e,`src="${t}"`,o),i(e,'data-mce-src="'+t+'"','data-mce-src="'+n+'"')},d=(t,n)=>{q(e.undoManager.data,(e=>{"fragmented"===e.type?e.fragments=V(e.fragments,(e=>l(e,t,n))):e.content=l(e.content,t,n)}))},c=()=>(n||(n=CE(e,r)),p().then(a((o=>{const r=V(o,(e=>e.blobInfo));return n.upload(r,yE(e)).then(a((n=>{const r=[];let s=!1;const a=V(n,((n,a)=>{const{blobInfo:i,image:l}=o[a];let c=!1;return n.status&&dd(e)?(n.url&&!He(l.src,n.url)&&(s=!0),t.removeByUri(l.src),pw(e)||((t,n)=>{const o=e.convertURL(n,"src");var r;d(t.src,n),en(Cn(t),{src:ld(e)?(r=n,r+(-1===r.indexOf("?")?"?":"&")+(new Date).getTime()):n,"data-mce-src":o})})(l,n.url)):n.error&&(n.error.remove&&(d(l.src,Tt.transparentSrc),r.push(l),c=!0),((e,t)=>{eE(e,va.translate(["Failed to upload image: {0}",t]))})(e,n.error.message)),{element:l,status:n.status,uploadUri:n.url,blobInfo:i,removed:c}}));return r.length>0&&!pw(e)?e.undoManager.transact((()=>{q(_o(r),(n=>{const o=Tn(n);Eo(n),o.each((e=>t=>{((e,t)=>e.dom.isEmpty(t.dom)&&C(e.schema.getTextBlockElements()[$t(t)]))(e,t)&&vo(t,bn('<br data-mce-bogus="1" />'))})(e)),t.removeByUri(n.dom.src)}))})):s&&e.undoManager.dispatchChange(),a})))})))),u=()=>id(e)?c():Promise.resolve([]),g=e=>ne(s,(t=>t(e))),p=()=>(o||(o=pE(r,t)),o.findAll(e.getBody(),g).then(a((t=>{const n=Y(t,(t=>m(t)?(eE(e,t),!1):"blob"!==t.uriType));return pw(e)||q(n,(e=>{d(e.image.src,e.blobInfo.blobUri()),e.image.src=e.blobInfo.blobUri(),e.image.removeAttribute("data-mce-src")})),n})))),h=n=>n.replace(/src="(blob:[^"]+)"/g,((n,o)=>{const s=r.getResultUri(o);if(s)return'src="'+s+'"';let a=t.getByUri(o);return a||(a=X(e.editorManager.get(),((e,t)=>e||t.editorUpload&&t.editorUpload.blobCache.getByUri(o)),void 0)),a?'src="data:'+a.blob().type+";base64,"+a.base64()+'"':n}));return e.on("SetContent",(()=>{id(e)?u():p()})),e.on("RawSaveContent",(e=>{e.content=h(e.content)})),e.on("GetContent",(e=>{e.source_view||"raw"===e.format||"tree"===e.format||(e.content=h(e.content))})),e.on("PostRender",(()=>{e.parser.addNodeFilter("img",(e=>{q(e,(e=>{const n=e.attr("src");if(!n||t.getByUri(n))return;const o=r.getResultUri(n);o&&e.attr("src",o)}))}))})),{blobCache:t,addFilter:e=>{s.push(e)},uploadImages:c,uploadImagesAuto:u,scanForImages:p,destroy:()=>{t.destroy(),r.destroy(),o=n=null}}},EE={remove_similar:!0,inherit:!1},xE={selector:"td,th",...EE},_E={tablecellbackgroundcolor:{styles:{backgroundColor:"%value"},...xE},tablecellverticalalign:{styles:{"vertical-align":"%value"},...xE},tablecellbordercolor:{styles:{borderColor:"%value"},...xE},tablecellclass:{classes:["%value"],...xE},tableclass:{selector:"table",classes:["%value"],...EE},tablecellborderstyle:{styles:{borderStyle:"%value"},...xE},tablecellborderwidth:{styles:{borderWidth:"%value"},...xE}},SE=N(_E),kE=Dt.each,NE=ma.DOM,RE=e=>C(e)&&f(e),AE=(e,t)=>{const n=t&&t.schema||Fs({}),o=e=>{const t=m(e)?{name:e,classes:[],attrs:{}}:e,n=NE.create(t.name);return((e,t)=>{t.classes.length>0&&NE.addClass(e,t.classes.join(" ")),NE.setAttribs(e,t.attrs)})(n,t),n},r=(e,t,s)=>{let a;const i=t[0],l=RE(i)?i.name:void 0,d=((e,t)=>{const o=n.getElementRule(e.nodeName.toLowerCase()),r=null==o?void 0:o.parentsRequired;return!(!r||!r.length)&&(t&&H(r,t)?t:r[0])})(e,l);if(d)l===d?(a=i,t=t.slice(1)):a=d;else if(i)a=i,t=t.slice(1);else if(!s)return e;const c=a?o(a):NE.create("div");c.appendChild(e),s&&Dt.each(s,(t=>{const n=o(t);c.insertBefore(n,e)}));const u=RE(a)?a.siblings:void 0;return r(c,t,u)},s=NE.create("div");if(e.length>0){const t=e[0],n=o(t),a=RE(t)?t.siblings:void 0;s.appendChild(r(n,e.slice(1),a))}return s},TE=e=>{let t="div";const n={name:t,classes:[],attrs:{},selector:e=Dt.trim(e)};return"*"!==e&&(t=e.replace(/(?:([#\.]|::?)([\w\-]+)|(\[)([^\]]+)\]?)/g,((e,t,o,r,s)=>{switch(t){case"#":n.attrs.id=o;break;case".":n.classes.push(o);break;case":":-1!==Dt.inArray("checked disabled enabled read-only required".split(" "),o)&&(n.attrs[o]=o)}if("["===r){const e=s.match(/([\w\-]+)(?:\=\"([^\"]+))?/);e&&(n.attrs[e[1]]=e[2])}return""}))),n.name=t||"div",n},OE=(e,t)=>{let n="",o=Fd(e);if(""===o)return"";const r=e=>m(e)?e.replace(/%(\w+)/g,""):"",s=(t,n)=>NE.getStyle(null!=n?n:e.getBody(),t,!0);if(m(t)){const n=e.formatter.get(t);if(!n)return"";t=n[0]}if("preview"in t){const e=t.preview;if(!1===e)return"";o=e||o}let a,i=t.block||t.inline||"span";const l=(d=t.selector,m(d)?(d=(d=d.split(/\s*,\s*/)[0]).replace(/\s*(~\+|~|\+|>)\s*/g,"$1"),Dt.map(d.split(/(?:>|\s+(?![^\[\]]+\]))/),(e=>{const t=Dt.map(e.split(/(?:~\+|~|\+)/),TE),n=t.pop();return t.length&&(n.siblings=t),n})).reverse()):[]);var d;l.length>0?(l[0].name||(l[0].name=i),i=t.selector,a=AE(l,e)):a=AE([i],e);const c=NE.select(i,a)[0]||a.firstChild;kE(t.styles,((e,t)=>{const n=r(e);n&&NE.setStyle(c,t,n)})),kE(t.attributes,((e,t)=>{const n=r(e);n&&NE.setAttrib(c,t,n)})),kE(t.classes,(e=>{const t=r(e);NE.hasClass(c,t)||NE.addClass(c,t)})),e.dispatch("PreviewFormats"),NE.setStyles(a,{position:"absolute",left:-65535}),e.getBody().appendChild(a);const u=s("fontSize"),f=/px$/.test(u)?parseInt(u,10):0;return kE(o.split(" "),(e=>{let t=s(e,c);if(!("background-color"===e&&/transparent|rgba\s*\([^)]+,\s*0\)/.test(t)&&(t=s(e),"#ffffff"===Ws(t).toLowerCase())||"color"===e&&"#000000"===Ws(t).toLowerCase())){if("font-size"===e&&/em|%$/.test(t)){if(0===f)return;t=parseFloat(t)/(/%$/.test(t)?100:1)*f+"px"}"border"===e&&t&&(n+="padding:0 2px;"),n+=e+":"+t+";"}})),e.dispatch("AfterPreviewFormats"),NE.remove(a),n},BE=e=>{const t=(e=>{const t={},n=(e,o)=>{e&&(m(e)?(p(o)||(o=[o]),q(o,(e=>{v(e.deep)&&(e.deep=!Wm(e)),v(e.split)&&(e.split=!Wm(e)||Km(e)),v(e.remove)&&Wm(e)&&!Km(e)&&(e.remove="none"),Wm(e)&&Km(e)&&(e.mixed=!0,e.block_expand=!0),m(e.classes)&&(e.classes=e.classes.split(/\s+/))})),t[e]=o):pe(e,((e,t)=>{n(t,e)})))};return n((e=>{const t=e.dom,n=e.schema.type,o={valigntop:[{selector:"td,th",styles:{verticalAlign:"top"}}],valignmiddle:[{selector:"td,th",styles:{verticalAlign:"middle"}}],valignbottom:[{selector:"td,th",styles:{verticalAlign:"bottom"}}],alignleft:[{selector:"figure.image",collapsed:!1,classes:"align-left",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"left"},inherit:!1,preview:!1},{selector:"img,audio,video",collapsed:!1,styles:{float:"left"},preview:"font-family font-size"},{selector:".mce-placeholder",styles:{float:"left"},ceFalseOverride:!0},{selector:"table",collapsed:!1,styles:{marginLeft:"0px",marginRight:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"left"}}],aligncenter:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"center"},inherit:!1,preview:"font-family font-size"},{selector:"figure.image",collapsed:!1,classes:"align-center",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{display:"block",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:".mce-placeholder",styles:{display:"block",marginLeft:"auto",marginRight:"auto"},ceFalseOverride:!0},{selector:"table",collapsed:!1,styles:{marginLeft:"auto",marginRight:"auto"},preview:"font-family font-size"},{selector:".mce-preview-object",ceFalseOverride:!0,styles:{display:"table",marginLeft:"auto",marginRight:"auto"},preview:!1},{selector:"[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{marginLeft:"auto",marginRight:"auto"},preview:!1}],alignright:[{selector:"figure.image",collapsed:!1,classes:"align-right",ceFalseOverride:!0,preview:"font-family font-size"},{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"right"},inherit:!1,preview:"font-family font-size"},{selector:"img,audio,video",collapsed:!1,styles:{float:"right"},preview:"font-family font-size"},{selector:".mce-placeholder",styles:{float:"right"},ceFalseOverride:!0},{selector:"table",collapsed:!1,styles:{marginRight:"0px",marginLeft:"auto"},onformat:e=>{t.setStyle(e,"float",null)},preview:"font-family font-size"},{selector:".mce-preview-object,[data-ephox-embed-iri]",ceFalseOverride:!0,styles:{float:"right"},preview:!1}],alignjustify:[{selector:"figure,p,h1,h2,h3,h4,h5,h6,td,th,tr,div,ul,ol,li,pre",styles:{textAlign:"justify"},inherit:!1,preview:"font-family font-size"}],bold:[{inline:"strong",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontWeight:"bold"}},{inline:"b",remove:"all",preserve_attributes:["class","style"]}],italic:[{inline:"em",remove:"all",preserve_attributes:["class","style"]},{inline:"span",styles:{fontStyle:"italic"}},{inline:"i",remove:"all",preserve_attributes:["class","style"]}],underline:[{inline:"span",styles:{textDecoration:"underline"},exact:!0},{inline:"u",remove:"all",preserve_attributes:["class","style"]}],strikethrough:(()=>{const e={inline:"span",styles:{textDecoration:"line-through"},exact:!0},t={inline:"strike",remove:"all",preserve_attributes:["class","style"]},o={inline:"s",remove:"all",preserve_attributes:["class","style"]};return"html4"!==n?[o,e,t]:[e,o,t]})(),forecolor:{inline:"span",styles:{color:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},hilitecolor:{inline:"span",styles:{backgroundColor:"%value"},links:!0,remove_similar:!0,clear_child_styles:!0},fontname:{inline:"span",toggle:!1,styles:{fontFamily:"%value"},clear_child_styles:!0},fontsize:{inline:"span",toggle:!1,styles:{fontSize:"%value"},clear_child_styles:!0},lineheight:{selector:"h1,h2,h3,h4,h5,h6,p,li,td,th,div",styles:{lineHeight:"%value"}},fontsize_class:{inline:"span",attributes:{class:"%value"}},blockquote:{block:"blockquote",wrapper:!0,remove:"all"},subscript:{inline:"sub"},superscript:{inline:"sup"},code:{inline:"code"},samp:{inline:"samp"},link:{inline:"a",selector:"a",remove:"all",split:!0,deep:!0,onmatch:(e,t,n)=>Jo(e)&&e.hasAttribute("href"),onformat:(e,n,o)=>{Dt.each(o,((n,o)=>{t.setAttrib(e,o,n)}))}},lang:{inline:"span",clear_child_styles:!0,remove_similar:!0,attributes:{lang:"%value","data-mce-lang":e=>{var t;return null!==(t=null==e?void 0:e.customValue)&&void 0!==t?t:null}}},removeformat:[{selector:"b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small",remove:"all",split:!0,expand:!1,block_expand:!0,deep:!0},{selector:"span",attributes:["style","class"],remove:"empty",split:!0,expand:!1,deep:!0},{selector:"*",attributes:["style","class"],split:!1,expand:!1,deep:!0}]};return Dt.each("p h1 h2 h3 h4 h5 h6 div address pre dt dd".split(/\s/),(e=>{o[e]={block:e,remove:"all"}})),o})(e)),n(SE()),n(Id(e)),{get:e=>C(e)?t[e]:t,has:e=>_e(t,e),register:n,unregister:e=>(e&&t[e]&&delete t[e],t)}})(e),n=Br({});return(e=>{e.addShortcut("meta+b","","Bold"),e.addShortcut("meta+i","","Italic"),e.addShortcut("meta+u","","Underline");for(let t=1;t<=6;t++)e.addShortcut("access+"+t,"",["FormatBlock",!1,"h"+t]);e.addShortcut("access+7","",["FormatBlock",!1,"p"]),e.addShortcut("access+8","",["FormatBlock",!1,"div"]),e.addShortcut("access+9","",["FormatBlock",!1,"address"])})(e),(e=>{e.on("mouseup keydown",(t=>{var n;((e,t,n)=>{const o=e.selection,r=e.getBody();nv(e,null,n),8!==t&&46!==t||!o.isCollapsed()||o.getStart().innerHTML!==Qb||nv(e,tm(r,o.getStart()),!0),37!==t&&39!==t||nv(e,tm(r,o.getStart()),!0)})(e,t.keyCode,(n=e.selection.getRng().endContainer,lr(n)&&Ve(n.data,qo)))}))})(e),pw(e)||((e,t)=>{e.set({}),t.on("NodeChange",(n=>{Jv(t,n.element,e.get())})),t.on("FormatApply FormatRemove",(n=>{const o=I.from(n.node).map((e=>Am(e)?e:e.startContainer)).bind((e=>Jo(e)?I.some(e):I.from(e.parentElement))).getOrThunk((()=>Xv(t)));Jv(t,o,e.get())}))})(n,e),{get:t.get,has:t.has,register:t.register,unregister:t.unregister,apply:(t,n,o)=>{((e,t,n,o)=>{bw(e).formatter.apply(t,n,o)})(e,t,n,o)},remove:(t,n,o,r)=>{((e,t,n,o,r)=>{bw(e).formatter.remove(t,n,o,r)})(e,t,n,o,r)},toggle:(t,n,o)=>{((e,t,n,o)=>{bw(e).formatter.toggle(t,n,o)})(e,t,n,o)},match:(t,n,o,r)=>((e,t,n,o,r)=>bw(e).formatter.match(t,n,o,r))(e,t,n,o,r),closest:t=>((e,t)=>bw(e).formatter.closest(t))(e,t),matchAll:(t,n)=>((e,t,n)=>bw(e).formatter.matchAll(t,n))(e,t,n),matchNode:(t,n,o,r)=>((e,t,n,o,r)=>bw(e).formatter.matchNode(t,n,o,r))(e,t,n,o,r),canApply:t=>((e,t)=>bw(e).formatter.canApply(t))(e,t),formatChanged:(t,o,r,s)=>((e,t,n,o,r,s)=>bw(e).formatter.formatChanged(t,n,o,r,s))(e,n,t,o,r,s),getCssText:T(OE,e)}},PE=e=>{switch(e.toLowerCase()){case"undo":case"redo":case"mcefocus":return!0;default:return!1}},DE=e=>{const t=Dr(),n=Br(0),o=Br(0),r={data:[],typing:!1,beforeChange:()=>{((e,t,n)=>{bw(e).undoManager.beforeChange(t,n)})(e,n,t)},add:(s,a)=>((e,t,n,o,r,s,a)=>bw(e).undoManager.add(t,n,o,r,s,a))(e,r,o,n,t,s,a),dispatchChange:()=>{e.setDirty(!0);const t=aw(e);t.bookmark=wl(e.selection),e.dispatch("change",{level:t,lastLevel:ie(r.data,o.get()).getOrUndefined()})},undo:()=>((e,t,n,o)=>bw(e).undoManager.undo(t,n,o))(e,r,n,o),redo:()=>((e,t,n)=>bw(e).undoManager.redo(t,n))(e,o,r.data),clear:()=>{((e,t,n)=>{bw(e).undoManager.clear(t,n)})(e,r,o)},reset:()=>{((e,t)=>{bw(e).undoManager.reset(t)})(e,r)},hasUndo:()=>((e,t,n)=>bw(e).undoManager.hasUndo(t,n))(e,r,o),hasRedo:()=>((e,t,n)=>bw(e).undoManager.hasRedo(t,n))(e,r,o),transact:t=>((e,t,n,o)=>bw(e).undoManager.transact(t,n,o))(e,r,n,t),ignore:t=>{((e,t,n)=>{bw(e).undoManager.ignore(t,n)})(e,n,t)},extra:(t,n)=>{((e,t,n,o,r)=>{bw(e).undoManager.extra(t,n,o,r)})(e,r,o,t,n)}};return pw(e)||((e,t,n)=>{const o=Br(!1),r=e=>{mw(t,!1,n),t.add({},e)};e.on("init",(()=>{t.add()})),e.on("BeforeExecCommand",(e=>{const o=e.command;PE(o)||(fw(t,n),t.beforeChange())})),e.on("ExecCommand",(e=>{const t=e.command;PE(t)||r(e)})),e.on("ObjectResizeStart cut",(()=>{t.beforeChange()})),e.on("SaveContent ObjectResized blur",r),e.on("dragend",r),e.on("keyup",(n=>{const s=n.keyCode;if(n.isDefaultPrevented())return;const a=Tt.os.isMacOS()&&"Meta"===n.key;(s>=33&&s<=36||s>=37&&s<=40||45===s||n.ctrlKey||a)&&(r(),e.nodeChanged()),46!==s&&8!==s||e.nodeChanged(),o.get()&&t.typing&&!cw(aw(e),t.data[0])&&(e.isDirty()||e.setDirty(!0),e.dispatch("TypingUndo"),o.set(!1),e.nodeChanged())})),e.on("keydown",(e=>{const s=e.keyCode;if(e.isDefaultPrevented())return;if(s>=33&&s<=36||s>=37&&s<=40||45===s)return void(t.typing&&r(e));const a=e.ctrlKey&&!e.altKey||e.metaKey;if((s<16||s>20)&&224!==s&&91!==s&&!t.typing&&!a)return t.beforeChange(),mw(t,!0,n),t.add({},e),void o.set(!0);(Tt.os.isMacOS()?e.metaKey:e.ctrlKey&&!e.altKey)&&t.beforeChange()})),e.on("mousedown",(e=>{t.typing&&r(e)})),e.on("input",(e=>{var t;e.inputType&&("insertReplacementText"===e.inputType||"insertText"===(t=e).inputType&&null===t.data||(e=>"insertFromPaste"===e.inputType||"insertFromDrop"===e.inputType)(e))&&r(e)})),e.on("AddUndo Undo Redo ClearUndos",(t=>{t.isDefaultPrevented()||e.nodeChanged()}))})(e,r,n),(e=>{e.addShortcut("meta+z","","Undo"),e.addShortcut("meta+y,meta+shift+z","","Redo")})(e),r},LE=[9,27,Qf.HOME,Qf.END,19,20,44,144,145,33,34,45,16,17,18,91,92,93,Qf.DOWN,Qf.UP,Qf.LEFT,Qf.RIGHT].concat(Tt.browser.isFirefox()?[224]:[]),ME="data-mce-placeholder",IE=e=>"keydown"===e.type||"keyup"===e.type,FE=e=>{const t=e.keyCode;return t===Qf.BACKSPACE||t===Qf.DELETE},UE=(e,t)=>({from:e,to:t}),zE=(e,t)=>{const n=Cn(e),o=Cn(t.container());return zh(n,o).map((e=>((e,t)=>({block:e,position:t}))(e,t)))},jE=(e,t)=>Zn(t,(e=>Xa(e)||hr(e.dom)),(t=>Sn(t,e))).filter(Kt).getOr(e),HE=(e,t)=>{const n=((e,t)=>{const n=In(e);return J(n,(e=>t.isBlock($t(e)))).fold(N(n),(e=>n.slice(0,e)))})(e,t);return q(n,Eo),n},$E=(e,t,n)=>{const o=Hp(n,t);return Q(o.reverse(),(t=>Tr(e,t))).each(Eo)},VE=(e,t,n,o,r)=>{if(Tr(o,n))return Ja(n),Zu(n.dom);((e,t)=>0===Y(Ln(t),(t=>!Tr(e,t))).length)(o,r)&&Tr(o,t)&&po(r,vn("br"));const s=Xu(n.dom,el.before(r.dom));return q(HE(t,o),(e=>{po(r,e)})),$E(o,e,t),s},qE=(e,t,n,o)=>{if(Tr(o,n)){if(Tr(o,t)){const e=e=>{const t=(e,n)=>Un(e).fold((()=>n),(e=>((e,t)=>e.isInline($t(t)))(o,e)?t(e,n.concat(Ia(e))):n));return t(e,[])},r=G(e(n),((e,t)=>(yo(e,t),t)),Qa());wo(t),vo(t,r)}return Eo(n),Zu(t.dom)}const r=Qu(n.dom);return q(HE(t,o),(e=>{vo(n,e)})),$E(o,e,t),r},WE=(e,t)=>{Yu(e,t.dom).bind((e=>I.from(e.getNode()))).map(Cn).filter(qa).each(Eo)},KE=(e,t,n,o)=>(WE(!0,t),WE(!1,n),((e,t)=>kn(t,e)?((e,t)=>{const n=Hp(t,e);return I.from(n[n.length-1])})(t,e):I.none())(t,n).fold(T(qE,e,t,n,o),T(VE,e,t,n,o))),YE=(e,t,n,o,r)=>t?KE(e,o,n,r):KE(e,n,o,r),GE=(e,t)=>{const n=Cn(e.getBody()),o=((e,t,n,o)=>o.collapsed?((e,t,n,o)=>{const r=zE(t,el.fromRangeStart(o)),s=r.bind((o=>qu(n,t,o.position).bind((o=>zE(t,o).map((o=>((e,t,n,o)=>gr(o.position.getNode())&&!Tr(e,o.block)?Yu(!1,o.block.dom).bind((e=>e.isEqual(o.position)?qu(n,t,e).bind((e=>zE(t,e))):I.some(o))).getOr(o):o)(e,t,n,o)))))));return It(r,s,UE).filter((e=>(e=>!Sn(e.from.block,e.to.block))(e)&&((e,t)=>{const n=Cn(e);return Sn(jE(n,t.from.block),jE(n,t.to.block))})(t,e)&&(e=>!1===br(e.from.block.dom)&&!1===br(e.to.block.dom))(e)&&(e=>{const t=e=>Wa(e)||Yr(e.dom)||Ya(e);return t(e.from.block)&&t(e.to.block)})(e)&&(e=>!(kn(e.to.block,e.from.block)||kn(e.from.block,e.to.block)))(e)))})(e,t,n,o):I.none())(e.schema,n.dom,t,e.selection.getRng()).map((o=>()=>{YE(n,t,o.from.block,o.to.block,e.schema).each((t=>{e.selection.setRng(t.toRange())}))}));return o},XE=(e,t)=>{const n=Cn(t),o=T(Sn,e);return Xn(n,Xa,o).isSome()},ZE=e=>{const t=Cn(e.getBody());return((e,t)=>{const n=Xu(e.dom,el.fromRangeStart(t)).isNone(),o=Gu(e.dom,el.fromRangeEnd(t)).isNone();return!((e,t)=>XE(e,t.startContainer)||XE(e,t.endContainer))(e,t)&&n&&o})(t,e.selection.getRng())?(e=>I.some((()=>{e.setContent(""),e.selection.setCursorLocation()})))(e):((e,t,n)=>{const o=t.getRng();return It(zh(e,Cn(o.startContainer)),zh(e,Cn(o.endContainer)),((r,s)=>Sn(r,s)?I.none():I.some((()=>{o.deleteContents(),YE(e,!0,r,s,n).each((e=>{t.setRng(e.toRange())}))})))).getOr(I.none())})(t,e.selection,e.schema)},QE=(e,t)=>e.selection.isCollapsed()?I.none():ZE(e),JE=(e,t,n,o,r)=>I.from(t._selectionOverrides.showCaret(e,n,o,r)),ex=(e,t)=>e.dispatch("BeforeObjectSelected",{target:t}).isDefaultPrevented()?I.none():I.some((e=>{const t=e.ownerDocument.createRange();return t.selectNode(e),t})(t)),tx=(e,t,n)=>t.collapsed?((e,t,n)=>{const o=Su(1,e.getBody(),t),r=el.fromRangeStart(o),s=r.getNode();if(nu(s))return JE(1,e,s,!r.isAtEnd(),!1);const a=r.getNode(!0);if(nu(a))return JE(1,e,a,!1,!1);const i=bb(e.dom.getRoot(),r.getNode());return nu(i)?JE(1,e,i,!1,n):I.none()})(e,t,n).getOr(t):t,nx=e=>Up(e)||Lp(e),ox=e=>zp(e)||Mp(e),rx=(e,t,n,o,r,s)=>{JE(o,e,s.getNode(!r),r,!0).each((n=>{if(t.collapsed){const e=t.cloneRange();r?e.setEnd(n.startContainer,n.startOffset):e.setStart(n.endContainer,n.endOffset),e.deleteContents()}else t.deleteContents();e.selection.setRng(n)})),((e,t)=>{lr(t)&&0===t.data.length&&e.remove(t)})(e.dom,n)},sx=(e,t)=>((e,t)=>{const n=e.selection.getRng();if(!lr(n.commonAncestorContainer))return I.none();const o=t?1:-1,r=ju(e.getBody()),s=T(Au,t?r.next:r.prev),a=t?nx:ox,i=Nu(o,e.getBody(),n),l=s(i),d=l?Ph(t,l):l;if(!d||!Tu(i,d))return I.none();if(a(d))return I.some((()=>rx(e,n,i.getNode(),o,t,d)));const c=s(d);return c&&a(c)&&Tu(d,c)?I.some((()=>rx(e,n,i.getNode(),o,t,c))):I.none()})(e,t),ax=(e,t)=>{const n=e.getBody();return t?Zu(n).filter(Up):Qu(n).filter(zp)},ix=e=>{const t=e.selection.getRng();return!t.collapsed&&(ax(e,!0).exists((e=>e.isEqual(el.fromRangeStart(t))))||ax(e,!1).exists((e=>e.isEqual(el.fromRangeEnd(t)))))},lx=Sl([{remove:["element"]},{moveToElement:["element"]},{moveToPosition:["position"]}]),dx=(e,t,n,o)=>qu(t,e,n).bind((r=>{return s=r.getNode(),C(s)&&(Xa(Cn(s))||Ya(Cn(s)))||((e,t,n,o,r)=>{const s=t=>r.isInline(t.nodeName.toLowerCase())&&!vu(n,o,e);return ku(!t,n).fold((()=>ku(t,o).fold(L,s)),s)})(e,t,n,r,o)?I.none():t&&br(r.getNode())||!t&&br(r.getNode(!0))?((e,t,n,o,r)=>{const s=r.getNode(!n);return zh(Cn(t),Cn(o.getNode())).map((t=>Tr(e,t)?lx.remove(t.dom):lx.moveToElement(s))).orThunk((()=>I.some(lx.moveToElement(s))))})(o,e,t,n,r):t&&zp(n)||!t&&Up(n)?I.some(lx.moveToPosition(r)):I.none();var s})),cx=(e,t)=>I.from(bb(e.getBody(),t)),ux=(e,t)=>{const n=e.selection.getNode();return cx(e,n).filter(br).fold((()=>((e,t,n,o)=>{const r=Su(t?1:-1,e,n),s=el.fromRangeStart(r),a=Cn(e);return!t&&zp(s)?I.some(lx.remove(s.getNode(!0))):t&&Up(s)?I.some(lx.remove(s.getNode())):!t&&Up(s)&&eh(a,s,o)?th(a,s,o).map((e=>lx.remove(e.getNode()))):t&&zp(s)&&Jp(a,s,o)?nh(a,s,o).map((e=>lx.remove(e.getNode()))):((e,t,n,o)=>((e,t)=>{const n=t.getNode(!e),o=e?"after":"before";return Jo(n)&&n.getAttribute("data-mce-caret")===o})(t,n)?((e,t)=>y(t)?I.none():e&&br(t.nextSibling)?I.some(lx.moveToElement(t.nextSibling)):!e&&br(t.previousSibling)?I.some(lx.moveToElement(t.previousSibling)):I.none())(t,n.getNode(!t)).orThunk((()=>dx(e,t,n,o))):dx(e,t,n,o).bind((t=>((e,t,n)=>n.fold((e=>I.some(lx.remove(e))),(e=>I.some(lx.moveToElement(e))),(n=>vu(t,n,e)?I.none():I.some(lx.moveToPosition(n)))))(e,n,t))))(e,t,s,o)})(e.getBody(),t,e.selection.getRng(),e.schema).map((n=>()=>n.fold(((e,t)=>n=>(e._selectionOverrides.hideFakeCaret(),Rh(e,t,Cn(n)),!0))(e,t),((e,t)=>n=>{const o=t?el.before(n):el.after(n);return e.selection.setRng(o.toRange()),!0})(e,t),(e=>t=>(e.selection.setRng(t.toRange()),!0))(e))))),(()=>I.some(_)))},mx=e=>{const t=e.dom,n=e.selection,o=bb(e.getBody(),n.getNode());if(hr(o)&&t.isBlock(o)&&t.isEmpty(o)){const e=t.create("br",{"data-mce-bogus":"1"});t.setHTML(o,""),o.appendChild(e),n.setRng(el.before(e).toRange())}return!0},fx=(e,t)=>e.selection.isCollapsed()?ux(e,t):((e,t)=>{const n=e.selection.getNode();return br(n)&&!yr(n)?cx(e,n.parentNode).filter(br).fold((()=>I.some((()=>{var n;n=Cn(e.getBody()),q(Uo(n,".mce-offscreen-selection"),Eo),Rh(e,t,Cn(e.selection.getNode())),jh(e)}))),(()=>I.some(_))):ix(e)?I.some((()=>{Vh(e,e.selection.getRng(),Cn(e.getBody()))})):I.none()})(e,t),gx=e=>e.hasOwnProperty("text"),px=e=>e.hasOwnProperty("marker"),hx=(e,t)=>{const n=(e,n)=>{if(lr(e))return{text:e,offset:n};{const o=t(),r=e.childNodes;return n<r.length?(e.insertBefore(o,r[n]),{marker:o,before:!0}):(e.appendChild(o),{marker:o,before:!1})}},o=n(e.endContainer,e.endOffset);return{start:n(e.startContainer,e.startOffset),end:o}},bx=e=>{var t,n;const{start:o,end:r}=e,s=new window.Range;return gx(o)?s.setStart(o.text,o.offset):px(o)&&(o.before?s.setStartBefore(o.marker):s.setStartAfter(o.marker),null===(t=o.marker.parentNode)||void 0===t||t.removeChild(o.marker)),gx(r)?s.setEnd(r.text,r.offset):px(r)&&(r.before?s.setEndBefore(r.marker):s.setEndAfter(r.marker),null===(n=r.marker.parentNode)||void 0===n||n.removeChild(r.marker)),s},vx=(e,t)=>{var n;const o=e.dom,r=o.getParent(e.selection.getStart(),o.isBlock),s=o.getParent(e.selection.getEnd(),o.isBlock),a=e.getBody();if("div"===(null===(n=null==r?void 0:r.nodeName)||void 0===n?void 0:n.toLowerCase())&&r&&s&&r===a.firstChild&&s===a.lastChild&&!o.isEmpty(a)){const n=r.cloneNode(!1),o=()=>{if(t?Fh(e):Ih(e),a.firstChild!==r){const t=hx(e.selection.getRng(),(()=>document.createElement("span")));Array.from(a.childNodes).forEach((e=>n.appendChild(e))),a.appendChild(n),e.selection.setRng(bx(t))}};return I.some(o)}return I.none()},yx=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=el.fromRangeStart(e.selection.getRng());return qu(t,e.getBody(),n).filter((e=>t?Pp(e):Dp(e))).bind((e=>yu(t?0:-1,e))).map((t=>()=>e.selection.select(t)))})(e,t):I.none(),Cx=lr,wx=e=>Cx(e)&&e.data[0]===ei,Ex=e=>Cx(e)&&e.data[e.data.length-1]===ei,xx=e=>{var t;return(null!==(t=e.ownerDocument)&&void 0!==t?t:document).createTextNode(ei)},_x=(e,t)=>e?(e=>{var t;if(Cx(e.previousSibling))return Ex(e.previousSibling)||e.previousSibling.appendData(ei),e.previousSibling;if(Cx(e))return wx(e)||e.insertData(0,ei),e;{const n=xx(e);return null===(t=e.parentNode)||void 0===t||t.insertBefore(n,e),n}})(t):(e=>{var t,n;if(Cx(e.nextSibling))return wx(e.nextSibling)||e.nextSibling.insertData(0,ei),e.nextSibling;if(Cx(e))return Ex(e)||e.appendData(ei),e;{const o=xx(e);return e.nextSibling?null===(t=e.parentNode)||void 0===t||t.insertBefore(o,e.nextSibling):null===(n=e.parentNode)||void 0===n||n.appendChild(o),o}})(t),Sx=T(_x,!0),kx=T(_x,!1),Nx=(e,t)=>lr(e.container())?_x(t,e.container()):_x(t,e.getNode()),Rx=(e,t)=>{const n=t.get();return n&&e.container()===n&&ai(n)},Ax=(e,t)=>t.fold((t=>{Gc(e.get());const n=Sx(t);return e.set(n),I.some(el(n,n.length-1))}),(t=>Zu(t).map((t=>{if(Rx(t,e)){const t=e.get();return el(t,1)}{Gc(e.get());const n=Nx(t,!0);return e.set(n),el(n,1)}}))),(t=>Qu(t).map((t=>{if(Rx(t,e)){const t=e.get();return el(t,t.length-1)}{Gc(e.get());const n=Nx(t,!1);return e.set(n),el(n,n.length-1)}}))),(t=>{Gc(e.get());const n=kx(t);return e.set(n),I.some(el(n,1))})),Tx=(e,t)=>{for(let n=0;n<e.length;n++){const o=e[n].apply(null,t);if(o.isSome())return o}return I.none()},Ox=Sl([{before:["element"]},{start:["element"]},{end:["element"]},{after:["element"]}]),Bx=(e,t)=>bu(t,e)||e,Px=(e,t,n)=>{const o=Dh(n),r=Bx(t,o.container());return Bh(e,r,o).fold((()=>Gu(r,o).bind(T(Bh,e,r)).map((e=>Ox.before(e)))),I.none)},Dx=(e,t)=>null===tm(e,t),Lx=(e,t,n)=>Bh(e,t,n).filter(T(Dx,t)),Mx=(e,t,n)=>{const o=Lh(n);return Lx(e,t,o).bind((e=>Xu(e,o).isNone()?I.some(Ox.start(e)):I.none()))},Ix=(e,t,n)=>{const o=Dh(n);return Lx(e,t,o).bind((e=>Gu(e,o).isNone()?I.some(Ox.end(e)):I.none()))},Fx=(e,t,n)=>{const o=Lh(n),r=Bx(t,o.container());return Bh(e,r,o).fold((()=>Xu(r,o).bind(T(Bh,e,r)).map((e=>Ox.after(e)))),I.none)},Ux=e=>!Oh(jx(e)),zx=(e,t,n)=>Tx([Px,Mx,Ix,Fx],[e,t,n]).filter(Ux),jx=e=>e.fold(R,R,R,R),Hx=e=>e.fold(N("before"),N("start"),N("end"),N("after")),$x=e=>e.fold(Ox.before,Ox.before,Ox.after,Ox.after),Vx=e=>e.fold(Ox.start,Ox.start,Ox.end,Ox.end),qx=(e,t,n,o,r,s)=>It(Bh(t,n,o),Bh(t,n,r),((t,o)=>t!==o&&((e,t,n)=>{const o=bu(t,e),r=bu(n,e);return C(o)&&o===r})(n,t,o)?Ox.after(e?t:o):s)).getOr(s),Wx=(e,t)=>e.fold(M,(e=>{return o=t,!(Hx(n=e)===Hx(o)&&jx(n)===jx(o));var n,o})),Kx=(e,t)=>e?t.fold(S(I.some,Ox.start),I.none,S(I.some,Ox.after),I.none):t.fold(I.none,S(I.some,Ox.before),I.none,S(I.some,Ox.end)),Yx=(e,t,n)=>{const o=e?1:-1;return t.setRng(el(n.container(),n.offset()+o).toRange()),t.getSel().modify("move",e?"forward":"backward","word"),!0};var Gx;!function(e){e[e.Br=0]="Br",e[e.Block=1]="Block",e[e.Wrap=2]="Wrap",e[e.Eol=3]="Eol"}(Gx||(Gx={}));const Xx=(e,t)=>-1===e?oe(t):t,Zx=(e,t,n)=>1===e?t.next(n):t.prev(n),Qx=(e,t,n,o)=>gr(o.getNode(1===t))?Gx.Br:!1===vu(n,o)?Gx.Block:Gx.Wrap,Jx=(e,t,n,o)=>{const r=ju(n);let s=o;const a=[];for(;s;){const n=Zx(t,r,s);if(!n)break;if(gr(n.getNode(!1)))return 1===t?{positions:Xx(t,a).concat([n]),breakType:Gx.Br,breakAt:I.some(n)}:{positions:Xx(t,a),breakType:Gx.Br,breakAt:I.some(n)};if(n.isVisible()){if(e(s,n)){const e=Qx(0,t,s,n);return{positions:Xx(t,a),breakType:e,breakAt:I.some(n)}}a.push(n),s=n}else s=n}return{positions:Xx(t,a),breakType:Gx.Eol,breakAt:I.none()}},e_=(e,t,n,o)=>t(n,o).breakAt.map((o=>{const r=t(n,o).positions;return-1===e?r.concat(o):[o].concat(r)})).getOr([]),t_=(e,t)=>X(e,((e,n)=>e.fold((()=>I.some(n)),(o=>It(le(o.getClientRects()),le(n.getClientRects()),((e,r)=>{const s=Math.abs(t-e.left);return Math.abs(t-r.left)<=s?n:o})).or(e)))),I.none()),n_=(e,t)=>le(t.getClientRects()).bind((t=>t_(e,t.left))),o_=T(Jx,el.isAbove,-1),r_=T(Jx,el.isBelow,1),s_=T(e_,-1,o_),a_=T(e_,1,r_),i_=(e,t)=>o_(e,t).breakAt.isNone(),l_=(e,t)=>r_(e,t).breakAt.isNone(),d_=(e,t)=>n_(s_(e,t),t),c_=(e,t)=>n_(a_(e,t),t),u_=br,m_=(e,t)=>Math.abs(e.left-t),f_=(e,t)=>Math.abs(e.right-t),g_=(e,t)=>Be(e,((e,n)=>{const o=Math.min(m_(e,t),f_(e,t)),r=Math.min(m_(n,t),f_(n,t));return r===o&&Se(n,"node")&&u_(n.node)||r<o?n:e})),p_=e=>{const t=t=>V(t,(t=>{const n=hi(t);return n.node=e,n}));if(Jo(e))return t(e.getClientRects());if(lr(e)){const n=e.ownerDocument.createRange();return n.setStart(e,0),n.setEnd(e,e.data.length),t(n.getClientRects())}return[]},h_=e=>te(e,p_);var b_;!function(e){e[e.Up=-1]="Up",e[e.Down=1]="Down"}(b_||(b_={}));const v_=(e,t,n,o,r,s)=>{let a=0;const i=[],l=o=>{let s=h_([o]);e===b_.Up&&(s=s.reverse());for(let e=0;e<s.length;e++){const o=s[e];if(!n(o,d)){if(i.length>0&&t(o,De(i))&&a++,o.line=a,r(o))return!0;i.push(o)}}return!1},d=De(s.getClientRects());if(!d)return i;const c=s.getNode();return c&&(l(c),((e,t,n,o)=>{let r=o;for(;r=hu(r,e,Mi,t);)if(n(r))return})(e,o,l,c)),i},y_=T(v_,b_.Up,yi,Ci),C_=T(v_,b_.Down,Ci,yi),w_=e=>De(e.getClientRects()),E_=e=>t=>((e,t)=>t.line>e)(e,t),x_=e=>t=>((e,t)=>t.line===e)(e,t),__=(e,t)=>{e.selection.setRng(t),Vg(e,e.selection.getRng())},S_=(e,t,n)=>I.some(tx(e,t,n)),k_=(e,t,n,o,r,s)=>{const a=1===t,i=ju(e.getBody()),l=T(Au,a?i.next:i.prev),d=a?o:r;if(!n.collapsed){const o=Ei(n);if(s(o))return JE(t,e,o,-1===t,!1);if(ix(e)){const e=n.cloneRange();return e.collapse(-1===t),I.from(e)}}const c=Nu(t,e.getBody(),n);if(d(c))return ex(e,c.getNode(!a));let u=l(c);const m=gi(n);if(!u)return m?I.some(n):I.none();if(u=Ph(a,u),d(u))return JE(t,e,u.getNode(!a),a,!1);const f=l(u);return f&&d(f)&&Tu(u,f)?JE(t,e,f.getNode(!a),a,!1):m?S_(e,u.toRange(),!1):I.none()},N_=(e,t,n,o,r,s)=>{const a=Nu(t,e.getBody(),n),i=De(a.getClientRects()),l=t===b_.Down,d=e.getBody();if(!i)return I.none();if(ix(e)){const e=l?el.fromRangeEnd(n):el.fromRangeStart(n);return(l?c_:d_)(d,e).orThunk((()=>I.from(e))).map((e=>e.toRange()))}const c=(l?C_:y_)(d,E_(1),a),u=Y(c,x_(1)),m=i.left,f=g_(u,m);if(f&&s(f.node)){const n=Math.abs(m-f.left),o=Math.abs(m-f.right);return JE(t,e,f.node,n<o,!1)}let g;if(g=o(a)?a.getNode():r(a)?a.getNode(!0):Ei(n),g){const n=((e,t,n,o)=>{const r=ju(t);let s,a,i,l;const d=[];let c=0;e===b_.Down?(s=r.next,a=Ci,i=yi,l=el.after(o)):(s=r.prev,a=yi,i=Ci,l=el.before(o));const u=w_(l);do{if(!l.isVisible())continue;const e=w_(l);if(i(e,u))continue;d.length>0&&a(e,De(d))&&c++;const t=hi(e);if(t.position=l,t.line=c,n(t))return d;d.push(t)}while(l=s(l));return d})(t,d,E_(1),g);let o=g_(Y(n,x_(1)),m);if(o)return S_(e,o.position.toRange(),!1);if(o=De(Y(n,x_(0))),o)return S_(e,o.position.toRange(),!1)}return 0===u.length?R_(e,l).filter(l?r:o).map((t=>tx(e,t.toRange(),!1))):I.none()},R_=(e,t)=>{const n=e.selection.getRng(),o=t?el.fromRangeEnd(n):el.fromRangeStart(n),r=(s=o.container(),a=e.getBody(),Xn(Cn(s),(e=>ru(e.dom)),(e=>e.dom===a)).map((e=>e.dom)).getOr(a));var s,a;if(t){const e=r_(r,o);return de(e.positions)}{const e=o_(r,o);return le(e.positions)}},A_=(e,t,n)=>R_(e,t).filter(n).exists((t=>(e.selection.setRng(t.toRange()),!0))),T_=(e,t)=>{const n=e.dom.createRng();n.setStart(t.container(),t.offset()),n.setEnd(t.container(),t.offset()),e.selection.setRng(n)},O_=(e,t)=>{e?t.setAttribute("data-mce-selected","inline-boundary"):t.removeAttribute("data-mce-selected")},B_=(e,t,n)=>Ax(t,n).map((t=>(T_(e,t),n))),P_=(e,t,n)=>{const o=e.getBody(),r=((e,t,n)=>{const o=el.fromRangeStart(e);if(e.collapsed)return o;{const r=el.fromRangeEnd(e);return n?Xu(t,r).getOr(r):Gu(t,o).getOr(o)}})(e.selection.getRng(),o,n);return((e,t,n,o)=>{const r=Ph(e,o),s=zx(t,n,r);return zx(t,n,r).bind(T(Kx,e)).orThunk((()=>((e,t,n,o,r)=>{const s=Ph(e,r);return qu(e,n,s).map(T(Ph,e)).fold((()=>o.map($x)),(r=>zx(t,n,r).map(T(qx,e,t,n,s,r)).filter(T(Wx,o)))).filter(Ux)})(e,t,n,s,o)))})(n,T(Th,e),o,r).bind((n=>B_(e,t,n)))},D_=(e,t,n)=>!!Md(e)&&P_(e,t,n).isSome(),L_=(e,t,n)=>!!Md(t)&&((e,t)=>{const n=t.selection.getRng(),o=e?el.fromRangeEnd(n):el.fromRangeStart(n);return!!(e=>w(e.selection.getSel().modify))(t)&&(e&&di(o)?Yx(!0,t.selection,o):!(e||!ci(o))&&Yx(!1,t.selection,o))})(e,t),M_=e=>{const t=Br(null),n=T(Th,e);return e.on("NodeChange",(o=>{Md(e)&&(((e,t,n)=>{const o=V(Uo(Cn(t.getRoot()),'*[data-mce-selected="inline-boundary"]'),(e=>e.dom)),r=Y(o,e),s=Y(n,e);q(re(r,s),T(O_,!1)),q(re(s,r),T(O_,!0))})(n,e.dom,o.parents),((e,t)=>{const n=t.get();if(e.selection.isCollapsed()&&!e.composing&&n){const o=el.fromRangeStart(e.selection.getRng());el.isTextPosition(o)&&!(e=>di(e)||ci(e))(o)&&(T_(e,Yc(n,o)),t.set(null))}})(e,t),((e,t,n,o)=>{if(t.selection.isCollapsed()){const r=Y(o,e);q(r,(o=>{const r=el.fromRangeStart(t.selection.getRng());zx(e,t.getBody(),r).bind((e=>B_(t,n,e)))}))}})(n,e,t,o.parents))})),t},I_=T(L_,!0),F_=T(L_,!1),U_=(e,t,n)=>{if(Md(e)){const o=R_(e,t).getOrThunk((()=>{const n=e.selection.getRng();return t?el.fromRangeEnd(n):el.fromRangeStart(n)}));return zx(T(Th,e),e.getBody(),o).exists((t=>{const o=$x(t);return Ax(n,o).exists((t=>(T_(e,t),!0)))}))}return!1},z_=(e,t)=>n=>Ax(t,n).map((t=>()=>T_(e,t))),j_=(e,t,n,o)=>{const r=e.getBody(),s=T(Th,e);e.undoManager.ignore((()=>{e.selection.setRng(((e,t)=>{const n=document.createRange();return n.setStart(e.container(),e.offset()),n.setEnd(t.container(),t.offset()),n})(n,o)),Ih(e),zx(s,r,el.fromRangeStart(e.selection.getRng())).map(Vx).bind(z_(e,t)).each(D)})),e.nodeChanged()},H_=(e,t,n)=>{if(e.selection.isCollapsed()&&Md(e)){const o=el.fromRangeStart(e.selection.getRng());return((e,t,n,o)=>{const r=((e,t)=>bu(t,e)||e)(e.getBody(),o.container()),s=T(Th,e),a=zx(s,r,o);return a.bind((e=>n?e.fold(N(I.some(Vx(e))),I.none,N(I.some($x(e))),I.none):e.fold(I.none,N(I.some($x(e))),I.none,N(I.some(Vx(e)))))).map(z_(e,t)).getOrThunk((()=>{const i=Wu(n,r,o),l=i.bind((e=>zx(s,r,e)));return It(a,l,(()=>Bh(s,r,o).bind((t=>(e=>It(Zu(e),Qu(e),((t,n)=>{const o=Ph(!0,t),r=Ph(!1,n);return Gu(e,o).forall((e=>e.isEqual(r)))})).getOr(!0))(t)?I.some((()=>{Rh(e,n,Cn(t))})):I.none())))).getOrThunk((()=>l.bind((()=>i.map((r=>()=>{n?j_(e,t,o,r):j_(e,t,r,o)}))))))}))})(e,t,n,o)}return I.none()},$_=(e,t)=>{const n=Cn(e.getBody()),o=Cn(e.selection.getStart()),r=Hp(o,n);return J(r,t).fold(N(r),(e=>r.slice(0,e)))},V_=e=>1===jn(e),q_=(e,t)=>{const n=T(cv,e);return te(t,(e=>n(e)?[e.dom]:[]))},W_=e=>{const t=(e=>$_(e,(t=>e.schema.isBlock($t(t)))))(e);return q_(e,t)},K_=(e,t)=>{const n=Y((e=>$_(e,(t=>e.schema.isBlock($t(t))||(e=>jn(e)>1)(t))))(e),V_);return de(n).bind((o=>{const r=el.fromRangeStart(e.selection.getRng());return Hh(t,r,o.dom)&&!Xm(o)?I.some((()=>((e,t,n,o)=>{const r=q_(t,o);if(0===r.length)Rh(t,e,n);else{const e=dv(n.dom,r);t.selection.setRng(e.toRange())}})(t,e,o,n))):I.none()}))},Y_=(e,t)=>{const n=e.selection.getStart(),o=((e,t)=>{const n=t.parentElement;return gr(t)&&!h(n)&&e.dom.isEmpty(n)})(e,n)||Xm(Cn(n))?dv(n,t):((e,t)=>{const{caretContainer:n,caretPosition:o}=lv(t);return e.insertNode(n.dom),o})(e.selection.getRng(),t);e.selection.setRng(o.toRange())},G_=e=>lr(e.startContainer),X_=e=>{const t=e.selection.getRng();return(e=>0===e.startOffset&&G_(e))(t)&&((e,t)=>{const n=t.startContainer.parentElement;return!h(n)&&cv(e,Cn(n))})(e,t)&&(e=>(e=>(e=>{const t=e.startContainer.parentNode,n=e.endContainer.parentNode;return!h(t)&&!h(n)&&t.isEqualNode(n)})(e)&&(e=>{const t=e.endContainer;return e.endOffset===(lr(t)?t.length:t.childNodes.length)})(e))(e)||(e=>!e.endContainer.isEqualNode(e.commonAncestorContainer))(e))(t)},Z_=(e,t)=>e.selection.isCollapsed()?K_(e,t):(e=>{if(X_(e)){const t=W_(e);return I.some((()=>{Ih(e),((e,t)=>{const n=re(t,W_(e));n.length>0&&Y_(e,n)})(e,t)}))}return I.none()})(e),Q_=e=>((e=>{const t=e.selection.getRng();return t.collapsed&&(G_(t)||e.dom.isEmpty(t.startContainer))&&!(e=>{return t=Cn(e.selection.getStart()),n=e.schema,zo(t,(e=>em(e.dom)),(e=>n.isBlock($t(e))));var t,n})(e)})(e)&&Y_(e,[]),!0),J_=(e,t,n)=>C(n)?I.some((()=>{e._selectionOverrides.hideFakeCaret(),Rh(e,t,Cn(n))})):I.none(),eS=(e,t)=>e.selection.isCollapsed()?((e,t)=>{const n=t?Lp:Mp,o=Nu(t?1:-1,e.getBody(),e.selection.getRng());return n(o)?J_(e,t,o.getNode(!t)):I.from(Ph(t,o)).filter((e=>n(e)&&Tu(o,e))).bind((n=>J_(e,t,n.getNode(!t))))})(e,t):((e,t)=>{const n=e.selection.getNode();return wr(n)?J_(e,t,n):I.none()})(e,t),tS=e=>Ze(null!=e?e:"").getOr(0),nS=(e,t)=>(e||"table"===$t(t)?"margin":"padding")+("rtl"===co(t,"direction")?"-right":"-left"),oS=e=>{const t=sS(e);return!e.mode.isReadOnly()&&(t.length>1||((e,t)=>ne(t,(t=>{const n=nS(Cd(e),t),o=mo(t,n).map(tS).getOr(0);return"false"!==e.dom.getContentEditable(t.dom)&&o>0})))(e,t))},rS=e=>Ka(e)||Ya(e),sS=e=>Y(_o(e.selection.getSelectedBlocks()),(e=>!rS(e)&&!(e=>Tn(e).exists(rS))(e)&&Zn(e,(e=>hr(e.dom)||br(e.dom))).exists((e=>hr(e.dom))))),aS=(e,t)=>{var n,o;if(e.mode.isReadOnly())return;const{dom:r}=e,s=wd(e),a=null!==(o=null===(n=/[a-z%]+$/i.exec(s))||void 0===n?void 0:n[0])&&void 0!==o?o:"px",i=tS(s),l=Cd(e);q(sS(e),(e=>{((e,t,n,o,r,s)=>{const a=nS(n,Cn(s)),i=tS(e.getStyle(s,a));if("outdent"===t){const t=Math.max(0,i-o);e.setStyle(s,a,t?t+r:"")}else{const t=i+o+r;e.setStyle(s,a,t)}})(r,t,l,i,a,e.dom)}))},iS=e=>aS(e,"outdent"),lS=e=>{if(e.selection.isCollapsed()&&oS(e)){const t=e.dom,n=e.selection.getRng(),o=el.fromRangeStart(n),r=t.getParent(n.startContainer,t.isBlock);if(null!==r&&Kp(Cn(r),o,e.schema))return I.some((()=>iS(e)))}return I.none()},dS=(e,t,n)=>ue([lS,fx,sx,(e,n)=>H_(e,t,n),GE,hb,yx,eS,QE,Z_,vx],(t=>t(e,n))).filter((t=>e.selection.isEditable())),cS=e=>void 0===e.touches||1!==e.touches.length?I.none():I.some(e.touches[0]),uS=(e,t)=>_e(e,t.nodeName),mS=(e,t)=>!!lr(t)||!!Jo(t)&&!(uS(e.getBlockElements(),t)||fm(t)||Zr(e,t)||Ir(t)),fS=(e,t)=>{if(lr(t)){if(0===t.data.length)return!0;if(/^\s+$/.test(t.data))return!t.nextSibling||uS(e,t.nextSibling)||Ir(t.nextSibling)}return!1},gS=e=>e.dom.create(ed(e),td(e)),pS=(e,t,n)=>{const o=Cn(gS(e)),r=Qa();vo(o,r),n(t,o);const s=document.createRange();return s.setStartBefore(r.dom),s.setEndBefore(r.dom),s},hS=e=>t=>-1!==(" "+t.attr("class")+" ").indexOf(e),bS=(e,t,n)=>function(o){const r=arguments,s=r[r.length-2],a=s>0?t.charAt(s-1):"";if('"'===a)return o;if(">"===a){const e=t.lastIndexOf("<",s);if(-1!==e&&-1!==t.substring(e,s).indexOf('contenteditable="false"'))return o}return'<span class="'+n+'" data-mce-content="'+e.dom.encode(r[0])+'">'+e.dom.encode("string"==typeof r[1]?r[1]:r[0])+"</span>"},vS=(e,t)=>ne(e,(e=>{const n=t.match(e);return null!==n&&n[0].length===t.length})),yS=(e,t)=>{t.hasAttribute("data-mce-caret")&&(fi(t),e.selection.setRng(e.selection.getRng()),e.selection.scrollIntoView(t))},CS=(e,t)=>{const n=(e=>eo(Cn(e.getBody()),"*[data-mce-caret]").map((e=>e.dom)).getOrNull())(e);if(n)return"compositionstart"===t.type?(t.preventDefault(),t.stopPropagation(),void yS(e,n)):void(li(n)&&(yS(e,n),e.undoManager.add()))},wS=br,ES=(e,t,n)=>{const o=ju(e.getBody()),r=T(Au,1===t?o.next:o.prev);if(n.collapsed){const o=e.dom.getParent(n.startContainer,"PRE");if(!o)return;if(!r(el.fromRangeStart(n))){const n=Cn((e=>{const t=e.dom.create(ed(e));return t.innerHTML='<br data-mce-bogus="1">',t})(e));1===t?ho(Cn(o),n):po(Cn(o),n),e.selection.select(n.dom,!0),e.selection.collapse()}}},xS=(e,t)=>((e,t)=>{const n=t?1:-1,o=e.selection.getRng();return((e,t,n)=>k_(t,e,n,Up,zp,wS))(n,e,o).orThunk((()=>(ES(e,n,o),I.none())))})(e,((e,t)=>{const n=t?e.getEnd(!0):e.getStart(!0);return Oh(n)?!t:t})(e.selection,t)).exists((t=>(__(e,t),!0))),_S=(e,t)=>((e,t)=>{const n=t?1:-1,o=e.selection.getRng();return((e,t,n)=>N_(t,e,n,(e=>Up(e)||Ip(e)),(e=>zp(e)||Fp(e)),wS))(n,e,o).orThunk((()=>(ES(e,n,o),I.none())))})(e,t).exists((t=>(__(e,t),!0))),SS=(e,t)=>A_(e,t,t?zp:Up),kS=(e,t)=>ax(e,!t).map((n=>{const o=n.toRange(),r=e.selection.getRng();return t?o.setStart(r.startContainer,r.startOffset):o.setEnd(r.endContainer,r.endOffset),o})).exists((t=>(__(e,t),!0))),NS=e=>H(["figcaption"],$t(e)),RS=(e,t)=>!!e.selection.isCollapsed()&&((e,t)=>{const n=Cn(e.getBody()),o=el.fromRangeStart(e.selection.getRng());return((e,t,n)=>{const o=T(Sn,t);return Zn(Cn(e.container()),(e=>n.isBlock($t(e))),o).filter(NS)})(o,n,e.schema).exists((()=>{if(((e,t,n)=>t?l_(e.dom,n):i_(e.dom,n))(n,t,o)){const o=pS(e,n,t?vo:bo);return e.selection.setRng(o),!0}return!1}))})(e,t),AS=(e,t)=>((e,t)=>t?I.from(e.dom.getParent(e.selection.getNode(),"details")).map((t=>((e,t)=>{const n=e.selection.getRng(),o=el.fromRangeStart(n);return!(e.getBody().lastChild!==t||!l_(t,o)||(e.execCommand("InsertNewBlockAfter"),0))})(e,t))).getOr(!1):I.from(e.dom.getParent(e.selection.getNode(),"summary")).bind((t=>I.from(e.dom.getParent(t,"details")).map((n=>((e,t,n)=>{const o=e.selection.getRng(),r=el.fromRangeStart(o);return!(e.getBody().firstChild!==t||!i_(n,r)||(e.execCommand("InsertNewBlockBefore"),0))})(e,n,t))))).getOr(!1))(e,t),TS={shiftKey:!1,altKey:!1,ctrlKey:!1,metaKey:!1,keyCode:0},OS=(e,t)=>t.keyCode===e.keyCode&&t.shiftKey===e.shiftKey&&t.altKey===e.altKey&&t.ctrlKey===e.ctrlKey&&t.metaKey===e.metaKey,BS=(e,...t)=>()=>e.apply(null,t),PS=(e,t)=>Q(((e,t)=>te((e=>V(e,(e=>({...TS,...e}))))(e),(e=>OS(e,t)?[e]:[])))(e,t),(e=>e.action())),DS=(e,t)=>ue(((e,t)=>te((e=>V(e,(e=>({...TS,...e}))))(e),(e=>OS(e,t)?[e]:[])))(e,t),(e=>e.action())),LS=(e,t)=>{const n=t?1:-1,o=e.selection.getRng();return k_(e,n,o,Lp,Mp,wr).exists((t=>(__(e,t),!0)))},MS=(e,t)=>{const n=t?1:-1,o=e.selection.getRng();return N_(e,n,o,Lp,Mp,wr).exists((t=>(__(e,t),!0)))},IS=(e,t)=>A_(e,t,t?Mp:Lp),FS=Sl([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),US={...FS,none:e=>FS.none(e)},zS=(e,t,n)=>te(In(e),(e=>xn(e,t)?n(e)?[e]:[]:zS(e,t,n))),jS=(e,t)=>to(e,"table",t),HS=(e,t,n,o,r=M)=>{const s=1===o;if(!s&&n<=0)return US.first(e[0]);if(s&&n>=e.length-1)return US.last(e[e.length-1]);{const s=n+o,a=e[s];return r(a)?US.middle(t,a):HS(e,t,s,o,r)}},$S=(e,t)=>jS(e,t).bind((t=>{const n=zS(t,"th,td",M);return J(n,(t=>Sn(e,t))).map((e=>({index:e,all:n})))})),VS=["img","br"],qS=e=>{return(t=e,ja.getOption(t)).filter((e=>0!==e.trim().length||e.indexOf(qo)>-1)).isSome()||H(VS,$t(e))||(e=>Wt(e)&&"false"===tn(e,"contenteditable"))(e);var t},WS=(e,t,n,o,r)=>{const s=Uo(Cn(n),"td,th,caption").map((e=>e.dom)),a=Y(((e,t)=>te(t,(t=>{const n=((e,t)=>({left:e.left-t,top:e.top-t,right:e.right+-2,bottom:e.bottom+-2,width:e.width+t,height:e.height+t}))(hi(t.getBoundingClientRect()),-1);return[{x:n.left,y:e(n),cell:t},{x:n.right,y:e(n),cell:t}]})))(e,s),(e=>t(e,r)));return((e,t,n)=>X(e,((e,o)=>e.fold((()=>I.some(o)),(e=>{const r=Math.sqrt(Math.abs(e.x-t)+Math.abs(e.y-n)),s=Math.sqrt(Math.abs(o.x-t)+Math.abs(o.y-n));return I.some(s<r?o:e)}))),I.none()))(a,o,r).map((e=>e.cell))},KS=T(WS,(e=>e.bottom),((e,t)=>e.y<t)),YS=T(WS,(e=>e.top),((e,t)=>e.y>t)),GS=(e,t,n)=>{const o=e(t,n);return(e=>e.breakType===Gx.Wrap&&0===e.positions.length)(o)||!gr(n.getNode())&&(e=>e.breakType===Gx.Br&&1===e.positions.length)(o)?!((e,t,n)=>n.breakAt.exists((n=>e(t,n).breakAt.isSome())))(e,t,o):o.breakAt.isNone()},XS=T(GS,o_),ZS=T(GS,r_),QS=(e,t,n,o)=>{const r=e.selection.getRng(),s=t?1:-1;return!(!tu()||!((e,t,n)=>{const o=el.fromRangeStart(t);return Yu(!e,n).exists((e=>e.isEqual(o)))})(t,r,n)||(JE(s,e,n,!t,!1).each((t=>{__(e,t)})),0))},JS=(e,t,n)=>{const o=((e,t)=>{const n=t.getNode(e);return sr(n)?I.some(n):I.none()})(!!t,n),r=!1===t;o.fold((()=>__(e,n.toRange())),(o=>Yu(r,e.getBody()).filter((e=>e.isEqual(n))).fold((()=>__(e,n.toRange())),(n=>((e,t,n)=>{t.undoManager.transact((()=>{const o=e?ho:po,r=pS(t,Cn(n),o);__(t,r)}))})(t,e,o)))))},ek=(e,t,n,o)=>{const r=e.selection.getRng(),s=el.fromRangeStart(r),a=e.getBody();if(!t&&XS(o,s)){const o=((e,t,n)=>((e,t)=>le(t.getClientRects()).bind((t=>KS(e,t.left,t.top))).bind((e=>{return n_(Qu(n=e).map((e=>o_(n,e).positions.concat(e))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>t_(s_(e,el.before(t)),n.left))))).getOr(el.before(t)))(a,n,s);return JS(e,t,o),!0}if(t&&ZS(o,s)){const o=((e,t,n)=>((e,t)=>de(t.getClientRects()).bind((t=>YS(e,t.left,t.top))).bind((e=>{return n_(Zu(n=e).map((e=>[e].concat(r_(n,e).positions))).getOr([]),t);var n})))(t,n).orThunk((()=>le(n.getClientRects()).bind((n=>t_(a_(e,el.after(t)),n.left))))).getOr(el.after(t)))(a,n,s);return JS(e,t,o),!0}return!1},tk=(e,t,n)=>I.from(e.dom.getParent(e.selection.getNode(),"td,th")).bind((o=>I.from(e.dom.getParent(o,"table")).map((r=>n(e,t,r,o))))).getOr(!1),nk=(e,t)=>tk(e,t,QS),ok=(e,t)=>tk(e,t,ek),rk=(e,t,n)=>n.fold(I.none,I.none,((e,t)=>{return(n=t,Qn(n,qS)).map((e=>(e=>{const t=Af.exact(e,0,e,0);return mg(t)})(e)));var n}),(n=>e.mode.isReadOnly()||!sk(n)?I.none():(e.execCommand("mceTableInsertRowAfter"),ak(e,t,n)))),sk=e=>Zn(e,Zt("table")).exists(no),ak=(e,t,n)=>{return rk(e,t,(r=lk,$S(o=n,void 0).fold((()=>US.none(o)),(e=>HS(e.all,o,e.index,1,r)))));var o,r},ik=(e,t,n)=>{return rk(e,t,(r=lk,$S(o=n,void 0).fold((()=>US.none()),(e=>HS(e.all,o,e.index,-1,r)))));var o,r},lk=e=>no(e)||Ho(e,dk),dk=e=>Wt(e)&&no(e),ck=(e,t)=>{const n=["table","li","dl"],o=Cn(e.getBody()),r=e=>{const t=$t(e);return Sn(e,o)||H(n,t)},s=e.selection.getRng();return((e,t)=>((e,t,n=L)=>n(t)?I.none():H(e,$t(t))?I.some(t):Jn(t,e.join(","),(e=>xn(e,"table")||n(e))))(["td","th"],e,t))(Cn(t?s.endContainer:s.startContainer),r).map((n=>(jS(n,r).each((t=>{e.model.table.clearSelectedCells(t.dom)})),e.selection.collapse(!t),(t?ak:ik)(e,r,n).each((t=>{e.selection.setRng(t)})),!0))).getOr(!1)},uk=(e,t)=>({container:e,offset:t}),mk=ma.DOM,fk=e=>t=>e===t?-1:0,gk=(e,t,n)=>{if(lr(e)&&t>=0)return I.some(uk(e,t));{const o=za(mk);return I.from(o.backwards(e,t,fk(e),n)).map((e=>uk(e.container,e.container.data.length)))}},pk=(e,t,n)=>{if(!lr(e))return I.none();const o=e.data;if(t>=0&&t<=o.length)return I.some(uk(e,t));{const o=za(mk);return I.from(o.backwards(e,t,fk(e),n)).bind((e=>{const o=e.container.data;return pk(e.container,t+o.length,n)}))}},hk=(e,t,n)=>{if(!lr(e))return I.none();const o=e.data;if(t<=o.length)return I.some(uk(e,t));{const r=za(mk);return I.from(r.forwards(e,t,fk(e),n)).bind((e=>hk(e.container,t-o.length,n)))}},bk=(e,t,n,o,r)=>{const s=za(e,(e=>t=>e.isBlock(t)||H(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===e.getContentEditable(t))(e));return I.from(s.backwards(t,n,o,r))},vk=e=>""!==e&&-1!==" \xa0\ufeff\f\n\r\t\v".indexOf(e),yk=(e,t)=>e.substring(t.length),Ck=(e,t,n,o=!1)=>{if(!(r=t).collapsed||!lr(r.startContainer))return I.none();var r;const s={text:"",offset:0},a=e.getParent(t.startContainer,e.isBlock)||e.getRoot();return bk(e,t.startContainer,t.startOffset,((e,t,r)=>(s.text=r+s.text,s.offset+=t,((e,t,n,o=!1)=>{let r;const s=n.charAt(0);for(r=t-1;r>=0;r--){const a=e.charAt(r);if(!o&&vk(a))return I.none();if(s===a&&He(e,n,r,t))break}return I.some(r)})(s.text,s.offset,n,o).getOr(t))),a).bind((e=>{const o=t.cloneRange();if(o.setStart(e.container,e.offset),o.setEnd(t.endContainer,t.endOffset),o.collapsed)return I.none();const r=(e=>ni(e.toString().replace(/\u00A0/g," ")))(o);return 0!==r.lastIndexOf(n)?I.none():I.some({text:yk(r,n),range:o,trigger:n})}))},wk=e=>{if((e=>3===e.nodeType)(e))return uk(e,e.data.length);{const t=e.childNodes;return t.length>0?wk(t[t.length-1]):uk(e,t.length)}},Ek=(e,t)=>{const n=e.childNodes;return n.length>0&&t<n.length?Ek(n[t],0):n.length>0&&(e=>1===e.nodeType)(e)&&n.length===t?wk(n[n.length-1]):uk(e,t)},xk=(e,t,n,o={})=>{var r;const s=t(),a=null!==(r=e.selection.getRng().startContainer.nodeValue)&&void 0!==r?r:"",i=Y(s.lookupByTrigger(n.trigger),(t=>n.text.length>=t.minChars&&t.matches.getOrThunk((()=>(e=>t=>{const n=Ek(t.startContainer,t.startOffset);return!((e,t)=>{var n;const o=null!==(n=e.getParent(t.container,e.isBlock))&&void 0!==n?n:e.getRoot();return bk(e,t.container,t.offset,((e,t)=>0===t?-1:t),o).filter((e=>{const t=e.container.data.charAt(e.offset-1);return!vk(t)})).isSome()})(e,n)})(e.dom)))(n.range,a,n.text)));if(0===i.length)return I.none();const l=Promise.all(V(i,(e=>e.fetch(n.text,e.maxResults,o).then((t=>({matchText:n.text,items:t,columns:e.columns,onAction:e.onAction,highlightOn:e.highlightOn}))))));return I.some({lookupData:l,context:n})};var _k;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(_k||(_k={}));const Sk=(e,t,n)=>e.stype===_k.Error?t(e.serror):n(e.svalue),kk=e=>({stype:_k.Value,svalue:e}),Nk=e=>({stype:_k.Error,serror:e}),Rk=Sk,Ak=e=>f(e)&&fe(e).length>100?" removed due to size":JSON.stringify(e,null,2),Tk=(e,t)=>Nk([{path:e,getErrorInfo:t}]),Ok=(e,t)=>({extract:(n,o)=>xe(o,e).fold((()=>((e,t)=>Tk(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(n,e)),(e=>((e,t,n,o)=>xe(n,o).fold((()=>((e,t,n)=>Tk(e,(()=>'The chosen schema: "'+n+'" did not exist in branches: '+Ak(t))))(e,n,o)),(n=>n.extract(e.concat(["branch: "+o]),t))))(n,o,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+fe(t)}),Bk=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const n={};for(let o=0;o<t.length;o++){const r=t[o];for(const t in r)_e(r,t)&&(n[t]=e(n[t],r[t]))}return n},Pk=Bk(((e,t)=>g(e)&&g(t)?Pk(e,t):t)),Dk=Bk(((e,t)=>t)),Lk=e=>({tag:"defaultedThunk",process:N(e)}),Mk=e=>{const t=(e=>{const t=[],n=[];return q(e,(e=>{Sk(e,(e=>n.push(e)),(e=>t.push(e)))})),{values:t,errors:n}})(e);return t.errors.length>0?(n=t.errors,S(Nk,ee)(n)):kk(t.values);var n},Ik=(e,t,n)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return n(e.newKey,e.instantiator)}},Fk=e=>({extract:(t,n)=>{return o=e(n),r=e=>((e,t)=>Tk(e,N(t)))(t,e),o.stype===_k.Error?r(o.serror):o;var o,r},toString:N("val")}),Uk=Fk(kk),zk=(e,t,n,o)=>o(xe(e,t).getOrThunk((()=>n(e)))),jk=(e,t,n,o,r)=>{const s=e=>r.extract(t.concat([o]),e),a=e=>e.fold((()=>kk(I.none())),(e=>{const n=r.extract(t.concat([o]),e);return s=n,a=I.some,s.stype===_k.Value?{stype:_k.Value,svalue:a(s.svalue)}:s;var s,a}));switch(e.tag){case"required":return((e,t,n,o)=>xe(t,n).fold((()=>((e,t,n)=>Tk(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Ak(n))))(e,n,t)),o))(t,n,o,s);case"defaultedThunk":return zk(n,o,e.process,s);case"option":return((e,t,n)=>n(xe(e,t)))(n,o,a);case"defaultedOptionThunk":return((e,t,n,o)=>o(xe(e,t).map((t=>!0===t?n(e):t))))(n,o,e.process,a);case"mergeWithThunk":return zk(n,o,N({}),(t=>{const o=Pk(e.process(n),t);return s(o)}))}},Hk=e=>({extract:(t,n)=>((e,t,n)=>{const o={},r=[];for(const s of n)Ik(s,((n,s,a,i)=>{const l=jk(a,e,t,n,i);Rk(l,(e=>{r.push(...e)}),(e=>{o[s]=e}))}),((e,n)=>{o[e]=n(t)}));return r.length>0?Nk(r):kk(o)})(t,n,e),toString:()=>{const t=V(e,(e=>Ik(e,((e,t,n,o)=>e+" -> "+o.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),$k=e=>({extract:(t,n)=>{const o=V(n,((n,o)=>e.extract(t.concat(["["+o+"]"]),n)));return Mk(o)},toString:()=>"array("+e.toString()+")"}),Vk=S($k,Hk),qk=(e,t,n)=>{return o=((e,t,n)=>((e,t)=>e.stype===_k.Error?{stype:_k.Error,serror:t(e.serror)}:e)(t.extract([e],n),(e=>({input:n,errors:e}))))(e,t,n),Sk(o,_l.error,_l.value);var o},Wk=(e,t)=>Ok(e,he(t,Hk)),Kk=N(Uk),Yk=(e,t)=>Fk((n=>{const o=typeof n;return e(n)?kk(n):Nk(`Expected type: ${t} but got: ${o}`)})),Gk=Yk(E,"number"),Xk=Yk(m,"string"),Zk=Yk(b,"boolean"),Qk=Yk(w,"function"),Jk=(e,t,n,o)=>({tag:"field",key:e,newKey:t,presence:n,prop:o}),eN=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),tN=e=>{return t=t=>H(e,t)?_l.value(t):_l.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`),Fk((e=>t(e).fold(Nk,kk)));var t},nN=(e,t)=>Jk(e,e,{tag:"required",process:{}},t),oN=e=>nN(e,Xk),rN=(e,t)=>Jk(e,e,{tag:"required",process:{}},tN(t)),sN=e=>nN(e,Qk),aN=(e,t)=>Jk(e,e,{tag:"required",process:{}},$k(t)),iN=(e,t)=>Jk(e,e,{tag:"option",process:{}},t),lN=e=>iN(e,Xk),dN=(e,t,n)=>Jk(e,e,Lk(t),n),cN=(e,t)=>dN(e,t,Gk),uN=(e,t)=>dN(e,t,Xk),mN=(e,t,n)=>dN(e,t,tN(n)),fN=(e,t)=>dN(e,t,Zk),gN=(e,t)=>dN(e,t,Qk),pN=oN("type"),hN=sN("fetch"),bN=sN("onAction"),vN=gN("onSetup",(()=>_)),yN=lN("text"),CN=lN("icon"),wN=lN("tooltip"),EN=lN("label"),xN=fN("active",!1),_N=fN("enabled",!0),SN=fN("primary",!1),kN=e=>uN("type",e),NN=Hk([pN,oN("trigger"),cN("minChars",1),(e=>Jk(e,e,Lk(1),Kk()))("columns"),cN("maxResults",10),iN("matches",Qk),hN,bN,(RN=Xk,dN("highlightOn",[],$k(RN)))]);var RN;const AN=[_N,wN,CN,yN,vN,uN("context","mode:design")],TN=[xN].concat(AN),ON=[gN("predicate",L),mN("scope","node",["node","editor"]),mN("position","selection",["node","selection","line"])],BN=AN.concat([kN("contextformbutton"),uN("align","end"),SN,bN,eN("original",R)]),PN=TN.concat([kN("contextformbutton"),uN("align","end"),SN,bN,eN("original",R)]),DN=AN.concat([kN("contextformbutton")]),LN=TN.concat([kN("contextformtogglebutton")]),MN=[EN,aN("commands",Wk("type",{contextformbutton:BN,contextformtogglebutton:PN})),iN("launch",Wk("type",{contextformbutton:DN,contextformtogglebutton:LN})),gN("onInput",_),gN("onSetup",_)];Wk("type",{contextform:[...ON,...MN,rN("type",["contextform"]),gN("initValue",N("")),lN("placeholder")],contextsliderform:[...ON,...MN,rN("type",["contextsliderform"]),gN("initValue",N(0)),gN("min",N(0)),gN("max",N(100))],contextsizeinputform:[...ON,...MN,rN("type",["contextsizeinputform"]),gN("initValue",N({width:"",height:""}))]});const IN=AN.concat([kN("contexttoolbarbutton")]);var FN;Hk([kN("contexttoolbar"),(FN=IN,iN("launch",Hk(FN))),nN("items",(e=>{const t=R;return{extract:(n,o)=>{const r=[];for(const s of e){const e=s.extract(n,o);if(e.stype===_k.Value)return{stype:_k.Value,svalue:t(e.svalue)};r.push(e)}return Mk(r)},toString:()=>"oneOf("+V(e,(e=>e.toString())).join(", ")+")"}})([Xk,Vk([lN("name"),lN("label"),aN("items",Xk)])]))].concat(ON));const UN=e=>{const t=e.ui.registry.getAll().popups,n=he(t,(e=>{return(t=e,qk("Autocompleter",NN,t)).fold((e=>{throw new Error("Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:N("... (only showing first ten failures)")}]):e;return V(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})((t=e).errors).join("\n")+"\n\nInput object: "+Ak(t.input));var t}),R);var t})),o=ke(we(n,(e=>e.trigger))),r=Ee(n);return{dataset:n,triggers:o,lookupByTrigger:e=>Y(r,(t=>t.trigger===e))}},zN=e=>{const t=Dr(),n=Br(!1),o=t.isSet,r=()=>{o()&&((e=>{e.dispatch("AutocompleterEnd")})(e),n.set(!1),t.clear())},s=Le((()=>UN(e))),a=a=>{(n=>t.get().map((t=>Ck(e.dom,e.selection.getRng(),t.trigger,!0).bind((t=>xk(e,s,t,n))))).getOrThunk((()=>((e,t)=>{const n=t(),o=e.selection.getRng();return((e,t,n)=>ue(n.triggers,(n=>Ck(e,t,n))))(e.dom,o,n).bind((n=>xk(e,t,n)))})(e,s))))(a).fold(r,(r=>{(e=>{o()||t.set({trigger:e.trigger,matchLength:e.text.length})})(r.context),r.lookupData.then((o=>{t.get().map((s=>{const a=r.context;s.trigger===a.trigger&&(t.set({...s,matchLength:a.text.length}),n.get()?(Fl(e,{range:a.range}),((e,t)=>{e.dispatch("AutocompleterUpdate",t)})(e,{lookupData:o})):(n.set(!0),Fl(e,{range:a.range}),((e,t)=>{e.dispatch("AutocompleterStart",t)})(e,{lookupData:o})))}))}))}))},i=()=>t.get().bind((({trigger:t})=>{const o=e.selection.getRng();return Ck(e.dom,o,t,n.get()).filter((({range:e})=>((e,t)=>{const n=e.compareBoundaryPoints(window.Range.START_TO_START,t),o=e.compareBoundaryPoints(window.Range.END_TO_END,t);return n>=0&&o<=0})(o,e))).map((({range:e})=>e))}));e.addCommand("mceAutocompleterReload",((e,t)=>{const n=f(t)?t.fetchOptions:{};a(n)})),e.addCommand("mceAutocompleterClose",r),e.addCommand("mceAutocompleterRefreshActiveRange",(()=>{i().each((t=>{Fl(e,{range:t})}))})),e.editorCommands.addQueryStateHandler("mceAutoCompleterInRange",(()=>i().isSome())),((e,t)=>{const n=wa(t.load,50);e.on("input",(t=>{("insertCompositionText"!==t.inputType||e.composing)&&n.throttle()})),e.on("keydown",(e=>{const o=e.which;8===o?n.throttle():27===o?(n.cancel(),t.cancelIfNecessary()):38!==o&&40!==o||n.cancel()}),!0),e.on("remove",n.cancel)})(e,{cancelIfNecessary:r,load:a})},jN=xt().browser.isSafari(),HN=e=>Ja(Cn(e)),$N=(e,t)=>{var n;return 0===e.startOffset&&e.endOffset===(null===(n=t.textContent)||void 0===n?void 0:n.length)},VN=(e,t)=>I.from(e.getParent(t.container(),"details")),qN=(e,t)=>VN(e,t).isSome(),WN=(e,t)=>{const n=t.getNode();v(n)||e.selection.setCursorLocation(n,t.offset())},KN=(e,t,n)=>{const o=e.dom.getParent(t.container(),"details");if(o&&!o.open){const t=e.dom.select("summary",o)[0];t&&(n?Zu(t):Qu(t)).each((t=>WN(e,t)))}else WN(e,t)},YN=(e,t,n)=>{const{dom:o,selection:r}=e,s=e.getBody();if("character"===n){const n=el.fromRangeStart(r.getRng()),a=o.getParent(n.container(),o.isBlock),i=VN(o,n),l=a&&o.isEmpty(a),d=h(null==a?void 0:a.previousSibling),c=h(null==a?void 0:a.nextSibling);return!!(l&&(t?c:d)&&Wu(!t,s,n).exists((e=>qN(o,e)&&!Mt(i,VN(o,e)))))||Wu(t,s,n).fold(L,(n=>{const r=VN(o,n);if(qN(o,n)&&!Mt(i,r)){if(t||KN(e,n,!1),a&&l){if(t&&d)return!0;if(!t&&c)return!0;KN(e,n,t),e.dom.remove(a)}return!0}return!1}))}return!1},GN=(e,t,n,o)=>{const r=e.selection.getRng(),s=el.fromRangeStart(r),a=e.getBody();return"selection"===o?((e,t)=>{const n=t.startSummary.exists((t=>t.contains(e.startContainer))),o=t.startSummary.exists((t=>t.contains(e.endContainer))),r=t.startDetails.forall((e=>t.endDetails.forall((t=>e!==t))));return(n||o)&&!(n&&o)||r})(r,t):n?((e,t)=>t.startSummary.exists((t=>((e,t)=>Qu(t).exists((n=>gr(n.getNode())&&Xu(t,n).exists((t=>t.isEqual(e)))||n.isEqual(e))))(e,t))))(s,t)||((e,t,n)=>n.startDetails.exists((n=>Gu(e,t).forall((e=>!n.contains(e.container()))))))(a,s,t):((e,t)=>t.startSummary.exists((t=>((e,t)=>Zu(t).exists((t=>t.isEqual(e))))(e,t))))(s,t)||((e,t)=>t.startDetails.exists((n=>Xu(n,e).forall((n=>t.startSummary.exists((t=>!t.contains(e.container())&&t.contains(n.container()))))))))(s,t)},XN=(e,t,n)=>((e,t,n)=>((e,t)=>{const n=I.from(e.getParent(t.startContainer,"details")),o=I.from(e.getParent(t.endContainer,"details"));if(n.isSome()||o.isSome()){const t=n.bind((t=>I.from(e.select("summary",t)[0])));return I.some({startSummary:t,startDetails:n,endDetails:o})}return I.none()})(e.dom,e.selection.getRng()).fold((()=>YN(e,t,n)),(o=>GN(e,o,t,n)||YN(e,t,n))))(e,t,n)||jN&&((e,t,n)=>{const o=e.selection,r=o.getNode(),s=o.getRng(),a=el.fromRangeStart(s);return!!_r(r)&&("selection"===n&&$N(s,r)||Hh(t,a,r)?HN(r):e.undoManager.transact((()=>{const s=o.getSel();let{anchorNode:a,anchorOffset:i,focusNode:l,focusOffset:d}=null!=s?s:{};const c=()=>{C(a)&&C(i)&&C(l)&&C(d)&&(null==s||s.setBaseAndExtent(a,i,l,d))},u=(e,t)=>{q(e.childNodes,(e=>{Am(e)&&t.appendChild(e)}))},m=e.dom.create("span",{"data-mce-bogus":"1"});u(r,m),r.appendChild(m),c(),"word"!==n&&"line"!==n||null==s||s.modify("extend",t?"right":"left",n),!o.isCollapsed()&&$N(o.getRng(),m)?HN(r):(e.execCommand(t?"ForwardDelete":"Delete"),a=null==s?void 0:s.anchorNode,i=null==s?void 0:s.anchorOffset,l=null==s?void 0:s.focusNode,d=null==s?void 0:s.focusOffset,u(m,r),c()),e.dom.remove(m)})),!0)})(e,t,n)?I.some(_):I.none(),ZN=e=>(t,n,o={})=>{const r=t.getBody(),s={bubbles:!0,composed:!0,data:null,isComposing:!1,detail:0,view:null,target:r,currentTarget:r,eventPhase:Event.AT_TARGET,originalTarget:r,explicitOriginalTarget:r,isTrusted:!1,srcElement:r,cancelable:!1,preventDefault:_,inputType:n},a=Gs(new InputEvent(e));return t.dispatch(e,{...a,...s,...o})},QN=ZN("input"),JN=ZN("beforeinput"),eR=xt(),tR=eR.os,nR=tR.isMacOS()||tR.isiOS(),oR=eR.browser.isFirefox(),rR=(e,t)=>{const n=e.dom,o=e.schema.getMoveCaretBeforeOnEnterElements();if(!t)return;if(/^(LI|DT|DD)$/.test(t.nodeName)){const e=(e=>{for(;e;){if(Jo(e)||lr(e)&&e.data&&/[\r\n\s]/.test(e.data))return e;e=e.nextSibling}return null})(t.firstChild);e&&/^(UL|OL|DL)$/.test(e.nodeName)&&t.insertBefore(n.doc.createTextNode(qo),t.firstChild)}const r=n.createRng();if(t.normalize(),t.hasChildNodes()){const e=new $o(t,t);let n,s=t;for(;n=e.current();){if(lr(n)){r.setStart(n,0),r.setEnd(n,0);break}if(o[n.nodeName.toLowerCase()]){r.setStartBefore(n),r.setEndBefore(n);break}s=n,n=e.next()}n||(r.setStart(s,0),r.setEnd(s,0))}else gr(t)?t.nextSibling&&n.isBlock(t.nextSibling)?(r.setStartBefore(t),r.setEndBefore(t)):(r.setStartAfter(t),r.setEndAfter(t)):(r.setStart(t,0),r.setEnd(t,0));e.selection.setRng(r),Vg(e,r)},sR=(e,t)=>{const n=e.getRoot();let o,r=t;for(;r!==n&&r&&"false"!==e.getContentEditable(r);){if("true"===e.getContentEditable(r)){o=r;break}r=r.parentNode}return r!==n?o:n},aR=e=>I.from(e.dom.getParent(e.selection.getStart(!0),e.dom.isBlock)),iR=e=>{e.innerHTML='<br data-mce-bogus="1">'},lR=(e,t)=>{ed(e).toLowerCase()===t.tagName.toLowerCase()&&((e,t,n)=>{const o=e.dom;I.from(n.style).map(o.parseStyle).each((e=>{const n={...fo(Cn(t)),...e};o.setStyles(t,n)}));const r=I.from(n.class).map((e=>e.split(/\s+/))),s=I.from(t.className).map((e=>Y(e.split(/\s+/),(e=>""!==e))));It(r,s,((e,n)=>{const r=Y(n,(t=>!H(e,t))),s=[...e,...r];o.setAttrib(t,"class",s.join(" "))}));const a=["style","class"],i=Ce(n,((e,t)=>!H(a,t)));o.setAttribs(t,i)})(e,t,td(e))},dR=(e,t,n,o,r=!0,s,a)=>{const i=e.dom,l=e.schema,d=ed(e),c=n?n.nodeName.toUpperCase():"";let u=t;const m=l.getTextInlineElements();let f;f=s||"TABLE"===c||"HR"===c?i.create(s||d,a||{}):n.cloneNode(!1);let g=f;if(r){do{if(m[u.nodeName]){if(em(u)||fm(u))continue;const e=u.cloneNode(!1);i.setAttrib(e,"id",""),f.hasChildNodes()?(e.appendChild(f.firstChild),f.appendChild(e)):(g=e,f.appendChild(e))}}while((u=u.parentNode)&&u!==o)}else i.setAttrib(f,"style",null),i.setAttrib(f,"class",null);return lR(e,f),iR(g),f},cR=(e,t)=>{const n=null==e?void 0:e.parentNode;return C(n)&&n.nodeName===t},uR=e=>C(e)&&/^(OL|UL|LI)$/.test(e.nodeName),mR=e=>C(e)&&/^(LI|DT|DD)$/.test(e.nodeName),fR=e=>{const t=e.parentNode;return mR(t)?t:e},gR=(e,t,n)=>{let o=e[n?"firstChild":"lastChild"];for(;o&&!Jo(o);)o=o[n?"nextSibling":"previousSibling"];return o===t},pR=e=>X(we(fo(Cn(e)),((e,t)=>`${t}: ${e};`)),((e,t)=>e+t),""),hR=(e,t)=>t&&"A"===t.nodeName&&e.isEmpty(t),bR=(e,t)=>e.nodeName===t||e.previousSibling&&e.previousSibling.nodeName===t,vR=(e,t)=>C(t)&&e.isBlock(t)&&!/^(TD|TH|CAPTION|FORM)$/.test(t.nodeName)&&!/^(fixed|absolute)/i.test(t.style.position)&&e.isEditable(t.parentNode)&&"false"!==e.getContentEditable(t),yR=(e,t,n)=>lr(t)?e?1===n&&t.data.charAt(n-1)===ei?0:n:n===t.data.length-1&&t.data.charAt(n)===ei?t.data.length:n:n,CR={insert:(e,t)=>{let n,o,r,s,a=!1;const i=e.dom,l=e.schema.getNonEmptyElements(),d=e.selection.getRng(),c=ed(e),u=Cn(d.startContainer),f=Fn(u,d.startOffset),g=f.exists((e=>Wt(e)&&!no(e))),p=d.collapsed&&g,b=(t,o)=>dR(e,n,S,_,sd(e),t,o),v=e=>{const t=yR(e,n,o);if(lr(n)&&(e?t>0:t<n.data.length))return!1;if((n.parentNode===S||n===S)&&a&&!e)return!0;if(e&&Jo(n)&&n===S.firstChild)return!0;if(bR(n,"TABLE")||bR(n,"HR"))return(e=>"BR"===e.nodeName||e.nextSibling&&"BR"===e.nextSibling.nodeName)(n)?!e:a&&!e||!a&&e;const r=new $o(n,S);let s;for(lr(n)&&(e&&0===t?r.prev():e||t!==n.data.length||r.next());s=r.current();){if(Jo(s)){if(!s.getAttribute("data-mce-bogus")){const e=s.nodeName.toLowerCase();if(l[e]&&"br"!==e)return!1}}else if(lr(s)&&!Yo(s.data))return!1;e?r.prev():r.next()}return!0},w=()=>{let t;return t=/^(H[1-6]|PRE|FIGURE)$/.test(r)&&"HGROUP"!==k?b(c):b(),((e,t)=>{const n=ad(e);return!y(t)&&(m(n)?H(Dt.explode(n),t.nodeName.toLowerCase()):n)})(e,s)&&vR(i,s)&&i.isEmpty(S,void 0,{includeZwsp:!0})?t=i.split(s,S):i.insertAfter(t,S),rR(e,t),t};Eg(i,d).each((e=>{d.setStart(e.startContainer,e.startOffset),d.setEnd(e.endContainer,e.endOffset)})),n=d.startContainer,o=d.startOffset;const E=!(!t||!t.shiftKey),x=!(!t||!t.ctrlKey);Jo(n)&&n.hasChildNodes()&&!p&&(a=o>n.childNodes.length-1,n=n.childNodes[Math.min(o,n.childNodes.length-1)]||n,o=a&&lr(n)?n.data.length:0);const _=sR(i,n);if(!_||((e,t)=>{const n=e.dom.getParent(t,"ol,ul,dl");return null!==n&&"false"===e.dom.getContentEditableParent(n)})(e,n))return;E||(n=((e,t,n,o,r)=>{var s,a;const i=e.dom,l=null!==(s=sR(i,o))&&void 0!==s?s:i.getRoot();let d=i.getParent(o,i.isBlock);if(!d||!vR(i,d)){if(d=d||l,!d.hasChildNodes()){const o=i.create(t);return lR(e,o),d.appendChild(o),n.setStart(o,0),n.setEnd(o,0),o}let s,c=o;for(;c&&c.parentNode!==d;)c=c.parentNode;for(;c&&!i.isBlock(c);)s=c,c=c.previousSibling;const u=null===(a=null==s?void 0:s.parentElement)||void 0===a?void 0:a.nodeName;if(s&&u&&e.schema.isValidChild(u,t.toLowerCase())){const a=s.parentNode,l=i.create(t);for(lR(e,l),a.insertBefore(l,s),c=s;c&&!i.isBlock(c);){const e=c.nextSibling;l.appendChild(c),c=e}n.setStart(o,r),n.setEnd(o,r)}}return o})(e,c,d,n,o));let S=i.getParent(n,i.isBlock)||i.getRoot();s=C(null==S?void 0:S.parentNode)?i.getParent(S.parentNode,i.isBlock):null,r=S?S.nodeName.toUpperCase():"";const k=s?s.nodeName.toUpperCase():"";if("LI"!==k||x||(S=s,s=s.parentNode,r=k),Jo(s)&&((e,t,n)=>!t&&n.nodeName.toLowerCase()===ed(e)&&e.dom.isEmpty(n)&&((t,n)=>{let o=n;for(;o&&o!==t&&h(o.nextSibling);){const t=o.parentElement;if(!t||(r=t,!_e(e.schema.getTextBlockElements(),r.nodeName.toLowerCase())))return xr(t);o=t}var r;return!1})(e.getBody(),n))(e,E,S))return((e,t,n)=>{var o,r,s;const a=t(ed(e)),i=((e,t)=>e.dom.getParent(t,xr))(e,n);i&&(e.dom.insertAfter(a,i),rR(e,a),(null!==(s=null===(r=null===(o=n.parentElement)||void 0===o?void 0:o.childNodes)||void 0===r?void 0:r.length)&&void 0!==s?s:0)>1&&e.dom.remove(n))})(e,b,S);if(/^(LI|DT|DD)$/.test(r)&&Jo(s)&&i.isEmpty(S))return void((e,t,n,o,r)=>{const s=e.dom,a=e.selection.getRng(),i=n.parentNode;if(n===e.getBody()||!i)return;var l;uR(l=n)&&uR(l.parentNode)&&(r="LI");const d=mR(o)?pR(o):void 0;let c=mR(o)&&d?t(r,{style:pR(o)}):t(r);if(gR(n,o,!0)&&gR(n,o,!1))if(cR(n,"LI")){const e=fR(n);s.insertAfter(c,e),(e=>{var t;return(null===(t=e.parentNode)||void 0===t?void 0:t.firstChild)===e})(n)?s.remove(e):s.remove(n)}else s.replace(c,n);else if(gR(n,o,!0))cR(n,"LI")?(s.insertAfter(c,fR(n)),c.appendChild(s.doc.createTextNode(" ")),c.appendChild(n)):i.insertBefore(c,n),s.remove(o);else if(gR(n,o,!1))s.insertAfter(c,fR(n)),s.remove(o);else{n=fR(n);const e=a.cloneRange();e.setStartAfter(o),e.setEndAfter(n);const t=e.extractContents();if("LI"===r&&(e=>e.firstChild&&"LI"===e.firstChild.nodeName)(t)){const e=Y(V(c.children,Cn),O(Zt("br")));c=t.firstChild,s.insertAfter(t,n),q(e,(e=>bo(Cn(c),e))),d&&c.setAttribute("style",d)}else s.insertAfter(t,n),s.insertAfter(c,n);s.remove(o)}rR(e,c)})(e,b,s,S,c);if(!(p||S!==e.getBody()&&vR(i,S)))return;const N=S.parentNode;let R;if(p)R=b(c),f.fold((()=>{vo(u,Cn(R))}),(e=>{po(e,Cn(R))})),e.selection.setCursorLocation(R,0);else if(si(S))R=fi(S),i.isEmpty(S)&&iR(S),lR(e,R),rR(e,R);else if(v(!1))R=w();else if(v(!0)&&N){const t=el.fromRangeStart(d),n=Fp(t),o=Cn(S),r=eh(o,t,e.schema)?th(o,t,e.schema).bind((e=>I.from(e.getNode()))):I.none();R=N.insertBefore(b(),S);const s=bR(S,"HR")||n?R:r.getOr(S);rR(e,s)}else{const t=(e=>{const t=e.cloneRange();return t.setStart(e.startContainer,yR(!0,e.startContainer,e.startOffset)),t.setEnd(e.endContainer,yR(!1,e.endContainer,e.endOffset)),t})(d).cloneRange();t.setEndAfter(S);const n=t.extractContents();(e=>{q(Fo(Cn(e),Yt),(e=>{const t=e.dom;t.nodeValue=ni(t.data)}))})(n),(e=>{let t=e;do{lr(t)&&(t.data=t.data.replace(/^[\r\n]+/,"")),t=t.firstChild}while(t)})(n),R=n.firstChild,i.insertAfter(n,S),((e,t,n)=>{var o;const r=[];if(!n)return;let s=n;for(;s=s.firstChild;){if(e.isBlock(s))return;Jo(s)&&!t[s.nodeName.toLowerCase()]&&r.push(s)}let a=r.length;for(;a--;)s=r[a],(!s.hasChildNodes()||s.firstChild===s.lastChild&&""===(null===(o=s.firstChild)||void 0===o?void 0:o.nodeValue)||hR(e,s))&&e.remove(s)})(i,l,R),((e,t)=>{t.normalize();const n=t.lastChild;(!n||Jo(n)&&/^(left|right)$/gi.test(e.getStyle(n,"float",!0)))&&e.add(t,"br")})(i,S),i.isEmpty(S)&&iR(S),R.normalize(),i.isEmpty(R)?(i.remove(R),w()):(lR(e,R),rR(e,R))}i.setAttrib(R,"id",""),e.dispatch("NewBlock",{newBlock:R})},fakeEventName:"insertParagraph"},wR=(e,t,n)=>{const o=e.dom.createRng();n?(o.setStartBefore(t),o.setEndBefore(t)):(o.setStartAfter(t),o.setEndAfter(t)),e.selection.setRng(o),Vg(e,o)},ER=(e,t)=>{const n=vn("br");po(Cn(t),n),e.undoManager.add()},xR=(e,t)=>{_R(e.getBody(),t)||ho(Cn(t),vn("br"));const n=vn("br");ho(Cn(t),n),wR(e,n.dom,!1),e.undoManager.add()},_R=(e,t)=>{return n=el.after(t),!!gr(n.getNode())||Gu(e,el.after(t)).map((e=>gr(e.getNode()))).getOr(!1);var n},SR=e=>e&&"A"===e.nodeName&&"href"in e,kR=e=>e.fold(L,SR,SR,L),NR=(e,t)=>{t.fold(_,T(ER,e),T(xR,e),_)},RR={insert:(e,t)=>{const n=(e=>{const t=T(Th,e),n=el.fromRangeStart(e.selection.getRng());return zx(t,e.getBody(),n).filter(kR)})(e);n.isSome()?n.each(T(NR,e)):((e,t)=>{const n=e.selection,o=e.dom,r=n.getRng();let s,a=!1;Eg(o,r).each((e=>{r.setStart(e.startContainer,e.startOffset),r.setEnd(e.endContainer,e.endOffset)}));let i=r.startOffset,l=r.startContainer;if(Jo(l)&&l.hasChildNodes()){const e=i>l.childNodes.length-1;l=l.childNodes[Math.min(i,l.childNodes.length-1)]||l,i=e&&lr(l)?l.data.length:0}let d=o.getParent(l,o.isBlock);const c=d&&d.parentNode?o.getParent(d.parentNode,o.isBlock):null,u=c?c.nodeName.toUpperCase():"",m=!(!t||!t.ctrlKey);"LI"!==u||m||(d=c),lr(l)&&i>=l.data.length&&(((e,t,n)=>{const o=new $o(t,n);let r;const s=e.getNonEmptyElements();for(;r=o.next();)if(s[r.nodeName.toLowerCase()]||lr(r)&&r.length>0)return!0;return!1})(e.schema,l,d||o.getRoot())||(s=o.create("br"),r.insertNode(s),r.setStartAfter(s),r.setEndAfter(s),a=!0)),s=o.create("br"),nl(o,r,s),wR(e,s,a),e.undoManager.add()})(e,t)},fakeEventName:"insertLineBreak"},AR=(e,t)=>aR(e).filter((e=>t.length>0&&xn(Cn(e),t))).isSome(),TR=Sl([{br:[]},{block:[]},{none:[]}]),OR=(e,t)=>(e=>AR(e,rd(e)))(e),BR=e=>(t,n)=>(e=>aR(e).filter((e=>Ya(Cn(e)))).isSome())(t)===e,PR=(e,t)=>(n,o)=>{const r=(e=>aR(e).fold(N(""),(e=>e.nodeName.toUpperCase())))(n)===e.toUpperCase();return r===t},DR=e=>{const t=sR(e.dom,e.selection.getStart());return y(t)},LR=e=>PR("pre",e),MR=e=>(t,n)=>Jl(t)===e,IR=(e,t)=>(e=>AR(e,od(e)))(e),FR=(e,t)=>t,UR=e=>{const t=ed(e),n=sR(e.dom,e.selection.getStart());return C(n)&&e.schema.isValidChild(n.nodeName,t)},zR=e=>{const t=e.selection.getRng(),n=Cn(t.startContainer),o=Fn(n,t.startOffset).map((e=>Wt(e)&&!no(e)));return t.collapsed&&o.getOr(!0)},jR=(e,t)=>(n,o)=>X(e,((e,t)=>e&&t(n,o)),!0)?I.some(t):I.none(),HR=(e,t,n)=>{if(!t.mode.isReadOnly()){if(t.selection.isCollapsed()||(e=>{e.execCommand("delete")})(t),C(n)&&JN(t,e.fakeEventName).isDefaultPrevented())return;e.insert(t,n),C(n)&&QN(t,e.fakeEventName)}},$R=(e,t)=>{if(e.mode.isReadOnly())return;const n=()=>HR(RR,e,t),o=()=>HR(CR,e,t),r=((e,t)=>Tx([jR([OR],TR.none()),jR([LR(!0),DR],TR.none()),jR([PR("summary",!0)],TR.br()),jR([LR(!0),MR(!1),FR],TR.br()),jR([LR(!0),MR(!1)],TR.block()),jR([LR(!0),MR(!0),FR],TR.block()),jR([LR(!0),MR(!0)],TR.br()),jR([BR(!0),FR],TR.br()),jR([BR(!0)],TR.block()),jR([IR],TR.br()),jR([FR],TR.br()),jR([UR],TR.block()),jR([zR],TR.block())],[e,!(!t||!t.shiftKey)]).getOr(TR.none()))(e,t);switch(nd(e)){case"linebreak":r.fold(n,n,_);break;case"block":r.fold(o,o,_);break;case"invert":r.fold(o,n,_);break;default:r.fold(n,o,_)}},VR=xt(),qR=VR.os.isiOS()&&VR.browser.isSafari(),WR=(e,t)=>{var n;t.isDefaultPrevented()||(t.preventDefault(),(n=e.undoManager).typing&&(n.typing=!1,n.add()),e.undoManager.transact((()=>{$R(e,t)})))},KR=xt(),YR=e=>e.stopImmediatePropagation(),GR=e=>e.keyCode===Qf.PAGE_UP||e.keyCode===Qf.PAGE_DOWN,XR=(e,t,n)=>{n&&!e.get()?t.on("NodeChange",YR,!0):!n&&e.get()&&t.off("NodeChange",YR),e.set(n)},ZR=(e,t)=>e===t||e.contains(t),QR=(e,t)=>{const n=t.container(),o=t.offset();return lr(n)?(n.insertData(o,e),I.some(el(n,o+e.length))):Ru(t).map((n=>{const o=yn(e);return t.isAtEnd()?ho(n,o):po(n,o),el(o.dom,e.length)}))},JR=T(QR,qo),eA=T(QR," "),tA=e=>t=>{e.selection.setRng(t.toRange()),e.nodeChanged()},nA=e=>{const t=el.fromRangeStart(e.selection.getRng()),n=Cn(e.getBody());if(e.selection.isCollapsed()){const o=T(Th,e),r=el.fromRangeStart(e.selection.getRng());return zx(o,e.getBody(),r).bind((e=>t=>t.fold((t=>Xu(e.dom,el.before(t))),(e=>Zu(e)),(e=>Qu(e)),(t=>Gu(e.dom,el.after(t)))))(n)).map((o=>()=>((e,t,n)=>o=>dh(e,o,n)?JR(t):eA(t))(n,t,e.schema)(o).each(tA(e))))}return I.none()},oA=e=>{return Ft(Tt.browser.isFirefox()&&e.selection.isEditable()&&(t=e.dom,n=e.selection.getRng().startContainer,t.isEditable(t.getParent(n,"summary"))),(()=>{const t=Cn(e.getBody());e.selection.isCollapsed()||e.getDoc().execCommand("Delete"),((e,t,n)=>dh(e,t,n)?JR(t):eA(t))(t,el.fromRangeStart(e.selection.getRng()),e.schema).each(tA(e))}));var t,n},rA=e=>Pc(e)?[{keyCode:Qf.TAB,action:BS(ck,e,!0)},{keyCode:Qf.TAB,shiftKey:!0,action:BS(ck,e,!1)}]:[],sA=e=>{if(e.addShortcut("Meta+P","","mcePrint"),zN(e),pw(e))return Br(null);{const t=M_(e);return(e=>{e.on("beforeinput",(t=>{e.selection.isEditable()&&!$(t.getTargetRanges(),(t=>!((e,t)=>!ZR(e.getBody(),t.startContainer)||!ZR(e.getBody(),t.endContainer)||qg(e.dom,t))(e,t)))||t.preventDefault()}))})(e),(e=>{e.on("keyup compositionstart",T(CS,e))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=Tt.os.isMacOS()||Tt.os.isiOS();PS([{keyCode:Qf.RIGHT,action:BS(xS,e,!0)},{keyCode:Qf.LEFT,action:BS(xS,e,!1)},{keyCode:Qf.UP,action:BS(_S,e,!1)},{keyCode:Qf.DOWN,action:BS(_S,e,!0)},...o?[{keyCode:Qf.UP,action:BS(kS,e,!1),metaKey:!0,shiftKey:!0},{keyCode:Qf.DOWN,action:BS(kS,e,!0),metaKey:!0,shiftKey:!0}]:[],{keyCode:Qf.RIGHT,action:BS(nk,e,!0)},{keyCode:Qf.LEFT,action:BS(nk,e,!1)},{keyCode:Qf.UP,action:BS(ok,e,!1)},{keyCode:Qf.DOWN,action:BS(ok,e,!0)},{keyCode:Qf.UP,action:BS(ok,e,!1)},{keyCode:Qf.UP,action:BS(AS,e,!1)},{keyCode:Qf.DOWN,action:BS(AS,e,!0)},{keyCode:Qf.RIGHT,action:BS(LS,e,!0)},{keyCode:Qf.LEFT,action:BS(LS,e,!1)},{keyCode:Qf.UP,action:BS(MS,e,!1)},{keyCode:Qf.DOWN,action:BS(MS,e,!0)},{keyCode:Qf.RIGHT,action:BS(D_,e,t,!0)},{keyCode:Qf.LEFT,action:BS(D_,e,t,!1)},{keyCode:Qf.RIGHT,ctrlKey:!o,altKey:o,action:BS(I_,e,t)},{keyCode:Qf.LEFT,ctrlKey:!o,altKey:o,action:BS(F_,e,t)},{keyCode:Qf.UP,action:BS(RS,e,!1)},{keyCode:Qf.DOWN,action:BS(RS,e,!0)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{let n=!1;e.on("keydown",(o=>{n=o.keyCode===Qf.BACKSPACE,o.isDefaultPrevented()||((e,t,n)=>{const o=n.keyCode===Qf.BACKSPACE?"deleteContentBackward":"deleteContentForward",r=e.selection.isCollapsed(),s=r?"character":"selection",a=e=>r?e?"word":"line":"selection";DS([{keyCode:Qf.BACKSPACE,action:BS(lS,e)},{keyCode:Qf.BACKSPACE,action:BS(fx,e,!1)},{keyCode:Qf.DELETE,action:BS(fx,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(sx,e,!1)},{keyCode:Qf.DELETE,action:BS(sx,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(H_,e,t,!1)},{keyCode:Qf.DELETE,action:BS(H_,e,t,!0)},{keyCode:Qf.BACKSPACE,action:BS(hb,e,!1)},{keyCode:Qf.DELETE,action:BS(hb,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(XN,e,!1,s)},{keyCode:Qf.DELETE,action:BS(XN,e,!0,s)},...nR?[{keyCode:Qf.BACKSPACE,altKey:!0,action:BS(XN,e,!1,a(!0))},{keyCode:Qf.DELETE,altKey:!0,action:BS(XN,e,!0,a(!0))},{keyCode:Qf.BACKSPACE,metaKey:!0,action:BS(XN,e,!1,a(!1))}]:[{keyCode:Qf.BACKSPACE,ctrlKey:!0,action:BS(XN,e,!1,a(!0))},{keyCode:Qf.DELETE,ctrlKey:!0,action:BS(XN,e,!0,a(!0))}],{keyCode:Qf.BACKSPACE,action:BS(yx,e,!1)},{keyCode:Qf.DELETE,action:BS(yx,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(eS,e,!1)},{keyCode:Qf.DELETE,action:BS(eS,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(QE,e,!1)},{keyCode:Qf.DELETE,action:BS(QE,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(GE,e,!1)},{keyCode:Qf.DELETE,action:BS(GE,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(Z_,e,!1)},{keyCode:Qf.DELETE,action:BS(Z_,e,!0)},{keyCode:Qf.BACKSPACE,action:BS(vx,e,!1)},{keyCode:Qf.DELETE,action:BS(vx,e,!0)}],n).filter((t=>e.selection.isEditable())).each((t=>{n.preventDefault(),JN(e,o).isDefaultPrevented()||(t(),QN(e,o))}))})(e,t,o)})),e.on("keyup",(t=>{t.isDefaultPrevented()||((e,t,n)=>{PS([{keyCode:Qf.BACKSPACE,action:BS(mx,e)},{keyCode:Qf.DELETE,action:BS(mx,e)},...nR?[{keyCode:Qf.BACKSPACE,altKey:!0,action:BS(Q_,e)},{keyCode:Qf.DELETE,altKey:!0,action:BS(Q_,e)},...n?[{keyCode:oR?224:91,action:BS(Q_,e)}]:[]]:[{keyCode:Qf.BACKSPACE,ctrlKey:!0,action:BS(Q_,e)},{keyCode:Qf.DELETE,ctrlKey:!0,action:BS(Q_,e)}]],t)})(e,t,n),n=!1}))})(e,t),(e=>{let t=I.none();e.on("keydown",(n=>{n.keyCode===Qf.ENTER&&(qR&&(e=>{if(!e.collapsed)return!1;const t=e.startContainer;if(lr(t)){const n=/^[\uAC00-\uD7AF\u1100-\u11FF\u3130-\u318F\uA960-\uA97F\uD7B0-\uD7FF]$/,o=t.data.charAt(e.startOffset-1);return n.test(o)}return!1})(e.selection.getRng())?(e=>{t=I.some(e.selection.getBookmark()),e.undoManager.add()})(e):WR(e,n))})),e.on("keyup",(n=>{n.keyCode===Qf.ENTER&&t.each((()=>((e,n)=>{e.undoManager.undo(),t.fold(_,(t=>e.selection.moveToBookmark(t))),WR(e,n),t=I.none()})(e,n)))}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{DS([{keyCode:Qf.SPACEBAR,action:BS(nA,e)},{keyCode:Qf.SPACEBAR,action:BS(oA,e)}],t).each((n=>{t.preventDefault(),JN(e,"insertText",{data:" "}).isDefaultPrevented()||(n(),QN(e,"insertText",{data:" "}))}))})(e,t)}))})(e),(e=>{e.on("input",(t=>{t.isComposing||(e=>{const t=Cn(e.getBody());e.selection.isCollapsed()&&bh(t,el.fromRangeStart(e.selection.getRng()),e.schema).each((t=>{e.selection.setRng(t.toRange())}))})(e)}))})(e),(e=>{e.on("keydown",(t=>{t.isDefaultPrevented()||((e,t)=>{PS([...rA(e)],t).each((e=>{t.preventDefault()}))})(e,t)}))})(e),((e,t)=>{e.on("keydown",(n=>{n.isDefaultPrevented()||((e,t,n)=>{const o=Tt.os.isMacOS()||Tt.os.isiOS();PS([{keyCode:Qf.END,action:BS(SS,e,!0)},{keyCode:Qf.HOME,action:BS(SS,e,!1)},...o?[]:[{keyCode:Qf.HOME,action:BS(kS,e,!1),ctrlKey:!0,shiftKey:!0},{keyCode:Qf.END,action:BS(kS,e,!0),ctrlKey:!0,shiftKey:!0}],{keyCode:Qf.END,action:BS(IS,e,!0)},{keyCode:Qf.HOME,action:BS(IS,e,!1)},{keyCode:Qf.END,action:BS(U_,e,!0,t)},{keyCode:Qf.HOME,action:BS(U_,e,!1,t)}],n).each((e=>{n.preventDefault()}))})(e,t,n)}))})(e,t),((e,t)=>{if(KR.os.isMacOS())return;const n=Br(!1);e.on("keydown",(t=>{GR(t)&&XR(n,e,!0)})),e.on("keyup",(o=>{o.isDefaultPrevented()||((e,t,n)=>{PS([{keyCode:Qf.PAGE_UP,action:BS(U_,e,!1,t)},{keyCode:Qf.PAGE_DOWN,action:BS(U_,e,!0,t)}],n)})(e,t,o),GR(o)&&n.get()&&(XR(n,e,!1),e.nodeChanged())}))})(e,t),t}};class aA{constructor(e){let t;this.lastPath=[],this.editor=e;const n=this;"onselectionchange"in e.getDoc()||e.on("NodeChange click mouseup keyup focus",(n=>{const o=e.selection.getRng(),r={startContainer:o.startContainer,startOffset:o.startOffset,endContainer:o.endContainer,endOffset:o.endOffset};"nodechange"!==n.type&&pg(r,t)||e.dispatch("SelectionChange"),t=r})),e.on("contextmenu",(()=>{Mf(e),e.dispatch("SelectionChange")})),e.on("SelectionChange",(()=>{const t=e.selection.getStart(!0);t&&km(e)&&!n.isSameElementPath(t)&&e.dom.isChildOf(t,e.getBody())&&e.nodeChanged({selectionChange:!0})})),e.on("mouseup",(t=>{!t.isDefaultPrevented()&&km(e)&&("IMG"===e.selection.getNode().nodeName?Uf.setEditorTimeout(e,(()=>{e.nodeChanged()})):e.nodeChanged())}))}nodeChanged(e={}){const t=this.editor,n=t.selection;let o;if(t.initialized&&n&&!Yd(t)&&!jc(t)){const r=t.getBody();o=n.getStart(!0)||r,o.ownerDocument===t.getDoc()&&t.dom.isChildOf(o,r)||(o=r);const s=[];t.dom.getParent(o,(e=>e===r||(s.push(e),!1))),t.dispatch("NodeChange",{...e,element:o,parents:s})}}isSameElementPath(e){let t;const n=this.editor,o=oe(n.dom.getParents(e,M,n.getBody()));if(o.length===this.lastPath.length){for(t=o.length;t>=0&&o[t]===this.lastPath[t];t--);if(-1===t)return this.lastPath=o,!0}return this.lastPath=o,!1}}const iA=La("image"),lA=La("event"),dA=e=>t=>{t[lA]=e},cA=dA(0),uA=dA(2),mA=dA(1),fA=e=>{const t=e;return I.from(t[lA]).exists((e=>0===e))};const gA=La("mode"),pA=e=>t=>{t[gA]=e},hA=(e,t)=>pA(t)(e),bA=pA(0),vA=pA(2),yA=pA(1),CA=e=>t=>{const n=t;return I.from(n[gA]).exists((t=>t===e))},wA=CA(0),EA=CA(1),xA=["none","copy","link","move"],_A=["none","copy","copyLink","copyMove","link","linkMove","move","all","uninitialized"],SA=()=>{const e=new window.DataTransfer;let t="move",n="all";const o={get dropEffect(){return t},set dropEffect(e){H(xA,e)&&(t=e)},get effectAllowed(){return n},set effectAllowed(e){fA(o)&&H(_A,e)&&(n=e)},get items(){return((e,t)=>({...t,get length(){return t.length},add:(n,o)=>{if(wA(e)){if(!m(n))return t.add(n);if(!v(o))return t.add(n,o)}return null},remove:n=>{wA(e)&&t.remove(n)},clear:()=>{wA(e)&&t.clear()}}))(o,e.items)},get files(){return EA(o)?Object.freeze({length:0,item:e=>null}):e.files},get types(){return e.types},setDragImage:(t,n,r)=>{var s;wA(o)&&(s={image:t,x:n,y:r},o[iA]=s,e.setDragImage(t,n,r))},getData:t=>EA(o)?"":e.getData(t),setData:(t,n)=>{wA(o)&&e.setData(t,n)},clearData:t=>{wA(o)&&e.clearData(t)}};return bA(o),o},kA=(e,t)=>e.setData("text/html",t),NA="x-tinymce/html",RA=N(NA),AA="\x3c!-- "+NA+" --\x3e",TA=e=>AA+e,OA=e=>-1!==e.indexOf(AA),BA="%MCEPASTEBIN%",PA=e=>e.dom.get("mcepastebin"),DA=e=>C(e)&&"mcepastebin"===e.id,LA=e=>e===BA,MA=(e,t)=>(Dt.each(t,(t=>{e=u(t,RegExp)?e.replace(t,""):e.replace(t[0],t[1])})),e),IA=e=>MA(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/gi,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,(e,t,n)=>t||n?qo:" "],/<br class="Apple-interchange-newline">/g,/<br>$/i]),FA=(e,t)=>({content:e,cancelled:t}),UA=(e,t)=>(e.insertContent(t,{merge:vc(e),paste:!0}),!0),zA=e=>/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(e),jA=(e,t,n)=>!(e.selection.isCollapsed()||!zA(t))&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.execCommand("mceInsertLink",!1,t)})),!0))(e,t,n),HA=(e,t,n)=>!!((e,t)=>zA(t)&&$(Bc(e),(e=>Ve(t.toLowerCase(),`.${e.toLowerCase()}`))))(e,t)&&((e,t,n)=>(e.undoManager.extra((()=>{n(e,t)}),(()=>{e.insertContent('<img src="'+t+'">')})),!0))(e,t,n),$A=(()=>{let e=0;return()=>"mceclip"+e++})(),VA=e=>{const t=SA();return kA(t,e),vA(t),t},qA=(e,t,n,o,r)=>{const s=((e,t,n)=>((e,t,n)=>{const o=((e,t,n)=>e.dispatch("PastePreProcess",{content:t,internal:n}))(e,t,n),r=((e,t)=>{const n=LC({sanitize:Tc(e),sandbox_iframes:Mc(e),sandbox_iframes_exclusions:Ic(e),convert_unsafe_embeds:Fc(e)},e.schema);n.addNodeFilter("meta",(e=>{Dt.each(e,(e=>{e.remove()}))}));const o=n.parse(t,{forced_root_block:!1,isRootContent:!0});return Cp({validate:!0},e.schema).serialize(o)})(e,o.content);return e.hasEventListeners("PastePostProcess")&&!o.isDefaultPrevented()?((e,t,n)=>{const o=e.dom.create("div",{style:"display:none"},t),r=((e,t,n)=>e.dispatch("PastePostProcess",{node:t,internal:n}))(e,o,n);return FA(r.node.innerHTML,r.isDefaultPrevented())})(e,r,n):FA(r,o.isDefaultPrevented())})(e,t,n))(e,t,n);if(!s.cancelled){const t=s.content,n=()=>((e,t,n)=>{n||!yc(e)?UA(e,t):((e,t)=>{Dt.each([jA,HA,UA],(n=>!n(e,t,UA)))})(e,t)})(e,t,o);r?JN(e,"insertFromPaste",{dataTransfer:VA(t)}).isDefaultPrevented()||(n(),QN(e,"insertFromPaste")):n()}},WA=(e,t,n,o)=>{const r=n||OA(t);qA(e,(e=>e.replace(AA,""))(t),r,!1,o)},KA=(e,t,n)=>{const o=e.dom.encode(t).replace(/\r\n/g,"\n"),r=((e,t,n)=>{const o=e.split(/\n\n/),r=((e,t)=>{let n="<"+e;const o=we(t,((e,t)=>t+'="'+ws.encodeAllRaw(e)+'"'));return o.length&&(n+=" "+o.join(" ")),n+">"})(t,n),s="</"+t+">",a=V(o,(e=>e.split(/\n/).join("<br />")));return 1===a.length?a[0]:V(a,(e=>r+e+s)).join("")})(Xo(o,wc(e)),ed(e),td(e));qA(e,r,!1,!0,n)},YA=e=>{const t={};if(e&&e.types)for(let n=0;n<e.types.length;n++){const o=e.types[n];try{t[o]=e.getData(o)}catch(e){t[o]=""}}return t},GA=(e,t)=>t in e&&e[t].length>0,XA=e=>GA(e,"text/html")||GA(e,"text/plain"),ZA=(e,t,n)=>{const o="paste"===t.type?t.clipboardData:t.dataTransfer;var r;if(mc(e)&&o){const s=((e,t)=>{const n=t.items?te(ce(t.items),(e=>"file"===e.kind?[e.getAsFile()]:[])):[],o=t.files?ce(t.files):[];return Y(n.length>0?n:o,(e=>{const t=Bc(e);return e=>$e(e.type,"image/")&&$(t,(t=>(e=>{const t=e.toLowerCase(),n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"};return Dt.hasOwn(n,t)?"image/"+n[t]:"image/"+t})(t)===e.type))})(e))})(e,o);if(s.length>0)return t.preventDefault(),(r=s,Promise.all(V(r,(e=>sy(e).then((t=>({file:e,uri:t}))))))).then((t=>{n&&e.selection.setRng(n),q(t,(t=>{((e,t)=>{oy(t.uri).each((({data:n,type:o,base64Encoded:r})=>{const s=r?n:btoa(n),a=t.file,i=e.editorUpload.blobCache,l=i.getByData(s,o),d=null!=l?l:((e,t,n,o)=>{const r=$A(),s=ld(e)&&C(n.name),a=s?((e,t)=>{const n=t.match(/([\s\S]+?)(?:\.[a-z0-9.]+)$/i);return C(n)?e.dom.encode(n[1]):void 0})(e,n.name):r,i=s?n.name:void 0,l=t.create(r,n,o,a,i);return t.add(l),l})(e,i,a,s);WA(e,`<img src="${d.blobUri()}">`,!1,!0)}))})(e,t)}))})),!0}return!1},QA=(e,t,n,o,r)=>{let s=IA(n);const a=GA(t,RA())||OA(n),i=!a&&(e=>!/<(?:\/?(?!(?:div|p|br|span)>)\w+|(?:(?!(?:span style="white-space:\s?pre;?">)|br\s?\/>))\w+\s[^>]+)>/i.test(e))(s),l=zA(s);(LA(s)||!s.length||i&&!l)&&(o=!0),(o||l)&&(s=GA(t,"text/plain")&&i?t["text/plain"]:(e=>{const t=Fs(),n=LC({},t);let o="";const r=t.getVoidElements(),s=Dt.makeMap("script noscript style textarea video audio iframe object"," "),a=t.getBlockElements(),i=e=>{const n=e.name,l=e;if("br"!==n){if("wbr"!==n)if(r[n]&&(o+=" "),s[n])o+=" ";else{if(3===e.type&&(o+=e.value),!(e.name in t.getVoidElements())){let t=e.firstChild;if(t)do{i(t)}while(t=t.next)}a[n]&&l.next&&(o+="\n","p"===n&&(o+="\n"))}}else o+="\n"};return e=MA(e,[/<!\[[^\]]+\]>/g]),i(n.parse(e)),o})(s)),LA(s)||(o?KA(e,s,r):WA(e,s,a,r))},JA=(e,t,n)=>{((e,t,n)=>{let o;e.on("keydown",(e=>{(e=>Qf.metaKeyPressed(e)&&86===e.keyCode||e.shiftKey&&45===e.keyCode)(e)&&!e.isDefaultPrevented()&&(o=e.shiftKey&&86===e.keyCode)})),e.on("paste",(r=>{if(r.isDefaultPrevented()||(e=>{var t,n;return Tt.os.isAndroid()&&0===(null===(n=null===(t=e.clipboardData)||void 0===t?void 0:t.items)||void 0===n?void 0:n.length)})(r))return;const s="text"===n.get()||o;o=!1;const a=YA(r.clipboardData);!XA(a)&&ZA(e,r,t.getLastRng()||e.selection.getRng())||(GA(a,"text/html")?(r.preventDefault(),QA(e,a,a["text/html"],s,!0)):GA(a,"text/plain")&&GA(a,"text/uri-list")?(r.preventDefault(),QA(e,a,a["text/plain"],s,!0)):(t.create(),Uf.setEditorTimeout(e,(()=>{const n=t.getHtml();t.remove(),QA(e,a,n,s,!1)}),0)))}))})(e,t,n),(e=>{const t=e=>$e(e,"webkit-fake-url"),n=e=>$e(e,"data:");e.parser.addNodeFilter("img",((o,r,s)=>{if(!mc(e)&&(e=>{var t;return!0===(null===(t=e.data)||void 0===t?void 0:t.paste)})(s))for(const r of o){const o=r.attr("src");m(o)&&!r.attr("data-mce-object")&&o!==Tt.transparentSrc&&(t(o)||!Ec(e)&&n(o))&&r.remove()}}))})(e)},eT=(e,t,n,o)=>{((e,t,n)=>{if(!e)return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(RA(),t),!0}catch(e){return!1}})(e.clipboardData,t.html,t.text)?(e.preventDefault(),o()):n(t.html,o)},tT=e=>(t,n)=>{const{dom:o,selection:r}=e,s=o.create("div",{contenteditable:"false","data-mce-bogus":"all"}),a=o.create("div",{contenteditable:"true"},t);o.setStyles(s,{position:"fixed",top:"0",left:"-3000px",width:"1000px",overflow:"hidden"}),s.appendChild(a),o.add(e.getBody(),s);const i=r.getRng();a.focus();const l=o.createRng();l.selectNodeContents(a),r.setRng(l),Uf.setEditorTimeout(e,(()=>{r.setRng(i),o.remove(s),n()}),0)},nT=e=>({html:TA(e.selection.getContent({contextual:!0})),text:e.selection.getContent({format:"text"})}),oT=e=>!e.selection.isCollapsed()||(e=>!!e.dom.getParent(e.selection.getStart(),"td[data-mce-selected],th[data-mce-selected]",e.getBody()))(e),rT=(e,t)=>{var n,o;return Sg.getCaretRangeFromPoint(null!==(n=t.clientX)&&void 0!==n?n:0,null!==(o=t.clientY)&&void 0!==o?o:0,e.getDoc())},sT=(e,t)=>{e.focus(),t&&e.selection.setRng(t)},aT=/rgb\s*\(\s*([0-9]+)\s*,\s*([0-9]+)\s*,\s*([0-9]+)\s*\)/gi,iT=e=>Dt.trim(e).replace(aT,Ws).toLowerCase(),lT=(e,t,n)=>{const o=hc(e);if(n||"all"===o||!bc(e))return t;const r=o?o.split(/[, ]/):[];if(r&&"none"!==o){const n=e.dom,o=e.selection.getNode();t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,((e,t,s,a)=>{const i=n.parseStyle(n.decode(s)),l={};for(let e=0;e<r.length;e++){const t=i[r[e]];let s=t,a=n.getStyle(o,r[e],!0);/color/.test(r[e])&&(s=iT(s),a=iT(a)),a!==s&&(l[r[e]]=t)}const d=n.serializeStyle(l,"span");return d?t+' style="'+d+'"'+a:t+a}))}else t=t.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return t=t.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,((e,t,n,o)=>t+' style="'+n+'"'+o)),t},dT=e=>{const t=Br(!1),n=Br(Cc(e)?"text":"html"),o=(e=>{const t=Br(null);return{create:()=>((e,t)=>{const{dom:n,selection:o}=e,r=e.getBody();t.set(o.getRng());const s=n.add(e.getBody(),"div",{id:"mcepastebin",class:"mce-pastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: fixed; top: 50%; width: 10px; height: 10px; overflow: hidden; opacity: 0"},BA);Tt.browser.isFirefox()&&n.setStyle(s,"left","rtl"===n.getStyle(r,"direction",!0)?65535:-65535),n.bind(s,"beforedeactivate focusin focusout",(e=>{e.stopPropagation()})),s.focus(),o.select(s,!0)})(e,t),remove:()=>((e,t)=>{const n=e.dom;if(PA(e)){let o;const r=t.get();for(;o=PA(e);)n.remove(o),n.unbind(o);r&&e.selection.setRng(r)}t.set(null)})(e,t),getEl:()=>PA(e),getHtml:()=>(e=>{const t=e.dom,n=(e,n)=>{e.appendChild(n),t.remove(n,!0)},[o,...r]=Y(e.getBody().childNodes,DA);q(r,(e=>{n(o,e)}));const s=t.select("div[id=mcepastebin]",o);for(let e=s.length-1;e>=0;e--){const r=t.create("div");o.insertBefore(r,s[e]),n(r,s[e])}return o?o.innerHTML:""})(e),getLastRng:t.get}})(e);(e=>{(Tt.browser.isChromium()||Tt.browser.isSafari())&&((e,t)=>{e.on("PastePreProcess",(n=>{n.content=t(e,n.content,n.internal)}))})(e,lT)})(e),((e,t)=>{e.addCommand("mceTogglePlainTextPaste",(()=>{((e,t)=>{"text"===t.get()?(t.set("html"),Ul(e,!1)):(t.set("text"),Ul(e,!0)),e.focus()})(e,t)})),e.addCommand("mceInsertClipboardContent",((t,n)=>{n.html&&WA(e,n.html,n.internal,!1),n.text&&KA(e,n.text,!1)}))})(e,n),(e=>{const t=t=>n=>{t(e,n)},n=fc(e);w(n)&&e.on("PastePreProcess",t(n));const o=gc(e);w(o)&&e.on("PastePostProcess",t(o))})(e),e.addQueryStateHandler("mceTogglePlainTextPaste",(()=>"text"===n.get())),e.on("PreInit",(()=>{(e=>{e.on("cut",(e=>t=>{!t.isDefaultPrevented()&&oT(e)&&e.selection.isEditable()&&eT(t,nT(e),tT(e),(()=>{if(Tt.browser.isChromium()||Tt.browser.isFirefox()){const t=e.selection.getRng();Uf.setEditorTimeout(e,(()=>{e.selection.setRng(t),e.execCommand("Delete")}),0)}else e.execCommand("Delete")}))})(e)),e.on("copy",(e=>t=>{!t.isDefaultPrevented()&&oT(e)&&eT(t,nT(e),tT(e),_)})(e))})(e),((e,t)=>{uc(e)&&e.on("dragend dragover draggesture dragdrop drop drag",(e=>{e.preventDefault(),e.stopPropagation()})),mc(e)||e.on("drop",(e=>{const t=e.dataTransfer;t&&(e=>$(e.files,(e=>/^image\//.test(e.type))))(t)&&e.preventDefault()})),e.on("drop",(n=>{if(n.isDefaultPrevented())return;const o=rT(e,n);if(y(o))return;const r=YA(n.dataTransfer),s=GA(r,RA());if((!XA(r)||(e=>{const t=e["text/plain"];return!!t&&0===t.indexOf("file://")})(r))&&ZA(e,n,o))return;const a=r[RA()],i=a||r["text/html"]||r["text/plain"],l=((e,t,n,o)=>{const r=e.getParent(n,(e=>Zr(t,e)));if(!h(e.getParent(n,"summary")))return!0;if(r&&_e(o,"text/html")){const e=(new DOMParser).parseFromString(o["text/html"],"text/html").body;return!h(e.querySelector(r.nodeName.toLowerCase()))}return!1})(e.dom,e.schema,o.startContainer,r),d=t.get();d&&!l||i&&(n.preventDefault(),Uf.setEditorTimeout(e,(()=>{e.undoManager.transact((()=>{(a||d&&l)&&e.execCommand("Delete"),sT(e,o);const t=IA(i);r["text/html"]?WA(e,t,s,!0):KA(e,t,!0)}))})))})),e.on("dragstart",(e=>{t.set(!0)})),e.on("dragover dragend",(n=>{mc(e)&&!t.get()&&(n.preventDefault(),sT(e,rT(e,n))),"dragend"===n.type&&t.set(!1)})),(e=>{e.on("input",(t=>{const n=e=>h(e.querySelector("summary"));if("deleteByDrag"===t.inputType){const t=Y(e.dom.select("details"),n);q(t,(t=>{gr(t.firstChild)&&t.firstChild.remove();const n=e.dom.create("summary");n.appendChild(Qa().dom),t.prepend(n)}))}}))})(e)})(e,t),JA(e,o,n)}))},cT=gr,uT=lr,mT=e=>br(e.dom),fT=e=>t=>Sn(Cn(e),t),gT=(e,t)=>Zn(Cn(e),mT,fT(t)),pT=(e,t,n)=>{const o=new $o(e,t),r=n?o.next.bind(o):o.prev.bind(o);let s=e;for(let t=n?e:r();t&&!cT(t);t=r())Di(t)&&(s=t);return s},hT=e=>{const t=((e,t,n)=>{const o=el.fromRangeStart(e).getNode(),r=((e,t,n)=>Zn(Cn(e),(e=>(e=>hr(e.dom))(e)||n.isBlock($t(e))),fT(t)).getOr(Cn(t)).dom)(o,t,n),s=pT(o,r,!1),a=pT(o,r,!0),i=document.createRange();return gT(s,r).fold((()=>{uT(s)?i.setStart(s,0):i.setStartBefore(s)}),(e=>i.setStartBefore(e.dom))),gT(a,r).fold((()=>{uT(a)?i.setEnd(a,a.data.length):i.setEndAfter(a)}),(e=>i.setEndAfter(e.dom))),i})(e.selection.getRng(),e.getBody(),e.schema);e.selection.setRng(Bb(t))};var bT;!function(e){e.Before="before",e.After="after"}(bT||(bT={}));const vT=(e,t)=>Math.abs(e.left-t),yT=(e,t)=>Math.abs(e.right-t),CT=(e,t)=>(e=>X(e,((e,t)=>e.fold((()=>I.some(t)),(e=>{const n=Math.min(t.left,e.left),o=Math.min(t.top,e.top),r=Math.max(t.right,e.right),s=Math.max(t.bottom,e.bottom);return I.some({top:o,right:r,bottom:s,left:n,width:r-n,height:s-o})}))),I.none()))(Y(e,(e=>{return(n=t)>=(o=e).top&&n<=o.bottom;var n,o}))).fold((()=>[[],e]),(t=>{const{pass:n,fail:o}=K(e,(e=>((e,t)=>{const n=((e,t)=>Math.max(0,Math.min(e.bottom,t.bottom)-Math.max(e.top,t.top)))(e,t)/Math.min(e.height,t.height);return((e,t)=>e.top<t.bottom&&e.bottom>t.top)(e,t)&&n>.5})(e,t)));return[n,o]})),wT=(e,t,n)=>t>e.left&&t<e.right?0:Math.min(Math.abs(e.left-t),Math.abs(e.right-t)),ET=(e,t,n,o)=>{const r=e=>Di(e.node)?I.some(e):Jo(e.node)?ET(ce(e.node.childNodes),t,n,!1):I.none(),s=(e,s)=>{const a=ae(e,((e,o)=>s(e,t,n)-s(o,t,n)));return ue(a,r).map((e=>o&&!lr(e.node)&&a.length>1?((e,o,s)=>r(o).filter((o=>Math.abs(s(e,t,n)-s(o,t,n))<2&&lr(o.node))))(e,a[1],s).getOr(e):e))},[a,i]=CT(h_(e),n),{pass:l,fail:d}=K(i,(e=>e.top<n));return s(a,wT).orThunk((()=>s(d,wi))).orThunk((()=>s(l,wi)))},xT=(e,t,n)=>((e,t,n)=>{const o=Cn(e),r=Rn(o),s=wn(r,t,n).filter((e=>kn(o,e))).getOr(o);return((e,t,n,o)=>{const r=(t,s)=>{const a=Y(t.dom.childNodes,O((e=>Jo(e)&&e.classList.contains("mce-drag-container"))));return s.fold((()=>ET(a,n,o,!0)),(e=>{const t=Y(a,(t=>t!==e.dom));return ET(t,n,o,!0)})).orThunk((()=>(Sn(t,e)?I.none():On(t)).bind((e=>r(e,I.some(t))))))};return r(t,I.none())})(o,s,t,n)})(e,t,n).filter((e=>ou(e.node))).map((e=>((e,t)=>({node:e.node,position:vT(e,t)<yT(e,t)?bT.Before:bT.After}))(e,t))),_T=e=>{var t,n;const o=e.getBoundingClientRect(),r=e.ownerDocument,s=r.documentElement,a=r.defaultView;return{top:o.top+(null!==(t=null==a?void 0:a.scrollY)&&void 0!==t?t:0)-s.clientTop,left:o.left+(null!==(n=null==a?void 0:a.scrollX)&&void 0!==n?n:0)-s.clientLeft}},ST=e=>({target:e,srcElement:e}),kT=(e,t,n,o)=>{const r=((e,t)=>{const n=(e=>{const t=SA(),n=(e=>{const t=e;return I.from(t[gA])})(e);return vA(e),cA(t),t.dropEffect=e.dropEffect,t.effectAllowed=e.effectAllowed,(e=>{const t=e;return I.from(t[iA])})(e).each((e=>t.setDragImage(e.image,e.x,e.y))),q(e.types,(n=>{"Files"!==n&&t.setData(n,e.getData(n))})),q(e.files,(e=>t.items.add(e))),(e=>{const t=e;return I.from(t[lA])})(e).each((e=>{((e,t)=>{dA(t)(e)})(t,e)})),n.each((n=>{hA(e,n),hA(t,n)})),t})(e);return"dragstart"===t?(cA(n),bA(n)):"drop"===t?(uA(n),vA(n)):(mA(n),yA(n)),n})(n,e);return v(o)?((e,t,n)=>{const o=B("Function not supported on simulated event.");return{bubbles:!0,cancelBubble:!1,cancelable:!0,composed:!1,currentTarget:null,defaultPrevented:!1,eventPhase:0,isTrusted:!0,returnValue:!1,timeStamp:0,type:e,composedPath:o,initEvent:o,preventDefault:_,stopImmediatePropagation:_,stopPropagation:_,AT_TARGET:window.Event.AT_TARGET,BUBBLING_PHASE:window.Event.BUBBLING_PHASE,CAPTURING_PHASE:window.Event.CAPTURING_PHASE,NONE:window.Event.NONE,altKey:!1,button:0,buttons:0,clientX:0,clientY:0,ctrlKey:!1,layerX:0,layerY:0,metaKey:!1,movementX:0,movementY:0,offsetX:0,offsetY:0,pageX:0,pageY:0,relatedTarget:null,screenX:0,screenY:0,shiftKey:!1,x:0,y:0,detail:0,view:null,which:0,initUIEvent:o,initMouseEvent:o,getModifierState:o,dataTransfer:n,...ST(t)}})(e,t,r):((e,t,n,o)=>({...t,dataTransfer:o,type:e,...ST(n)}))(e,o,t,r)},NT=br,RT=((...e)=>t=>{for(let n=0;n<e.length;n++)if(e[n](t))return!0;return!1})(NT,hr),AT=(e,t,n,o)=>{const r=e.dom,s=t.cloneNode(!0);r.setStyles(s,{width:n,height:o}),r.setAttrib(s,"data-mce-selected",null);const a=r.create("div",{class:"mce-drag-container","data-mce-bogus":"all",unselectable:"on",contenteditable:"false"});return r.setStyles(a,{position:"absolute",opacity:.5,overflow:"hidden",border:0,padding:0,margin:0,width:n,height:o}),r.setStyles(s,{margin:0,boxSizing:"border-box"}),a.appendChild(s),a},TT=(e,t)=>n=>()=>{const o="left"===e?n.scrollX:n.scrollY;n.scroll({[e]:o+t,behavior:"smooth"})},OT=TT("left",-32),BT=TT("left",32),PT=TT("top",-32),DT=TT("top",32),LT=e=>{e&&e.parentNode&&e.parentNode.removeChild(e)},MT=(e,t,n,o,r)=>{"dragstart"===t&&kA(o,e.dom.getOuterHTML(n));const s=kT(t,n,o,r);return e.dispatch(t,s)},IT=(e,t)=>{const n=Ca(((e,n)=>((e,t,n)=>{e._selectionOverrides.hideFakeCaret(),xT(e.getBody(),t,n).fold((()=>e.selection.placeCaretAt(t,n)),(o=>{const r=e._selectionOverrides.showCaret(1,o.node,o.position===bT.Before,!1);r?e.selection.setRng(r):e.selection.placeCaretAt(t,n)}))})(t,e,n)),0);t.on("remove",n.cancel);const o=e;return r=>e.on((e=>{const s=Math.max(Math.abs(r.screenX-e.screenX),Math.abs(r.screenY-e.screenY));if(!e.dragging&&s>10){const n=MT(t,"dragstart",e.element,e.dataTransfer,r);if(C(n.dataTransfer)&&(e.dataTransfer=n.dataTransfer),n.isDefaultPrevented())return;e.dragging=!0,t.focus()}if(e.dragging){const s=r.currentTarget===t.getDoc().documentElement,l=((e,t)=>({pageX:t.pageX-e.relX,pageY:t.pageY+5}))(e,((e,t)=>{return n=(e=>e.inline?_T(e.getBody()):{left:0,top:0})(e),o=(e=>{const t=e.getBody();return e.inline?{left:t.scrollLeft,top:t.scrollTop}:{left:0,top:0}})(e),r=((e,t)=>{if(t.target.ownerDocument!==e.getDoc()){const n=_T(e.getContentAreaContainer()),o=(e=>{const t=e.getBody(),n=e.getDoc().documentElement,o={left:t.scrollLeft,top:t.scrollTop},r={left:t.scrollLeft||n.scrollLeft,top:t.scrollTop||n.scrollTop};return e.inline?o:r})(e);return{left:t.pageX-n.left+o.left,top:t.pageY-n.top+o.top}}return{left:t.pageX,top:t.pageY}})(e,t),{pageX:r.left-n.left+o.left,pageY:r.top-n.top+o.top};var n,o,r})(t,r));a=e.ghost,i=t.getBody(),a.parentNode!==i&&i.appendChild(a),((e,t,n,o,r,s,a,i,l,d,c,u)=>{let m=0,f=0;e.style.left=t.pageX+"px",e.style.top=t.pageY+"px",t.pageX+n>r&&(m=t.pageX+n-r),t.pageY+o>s&&(f=t.pageY+o-s),e.style.width=n-m+"px",e.style.height=o-f+"px";const g=l.clientHeight,p=l.clientWidth,h=a+l.getBoundingClientRect().top,b=i+l.getBoundingClientRect().left;c.on((e=>{e.intervalId.clear(),e.dragging&&u&&(a+8>=g?e.intervalId.set(DT(d)):a-8<=0?e.intervalId.set(PT(d)):i+8>=p?e.intervalId.set(BT(d)):i-8<=0?e.intervalId.set(OT(d)):h+16>=window.innerHeight?e.intervalId.set(DT(window)):h-16<=0?e.intervalId.set(PT(window)):b+16>=window.innerWidth?e.intervalId.set(BT(window)):b-16<=0&&e.intervalId.set(OT(window)))}))})(e.ghost,l,e.width,e.height,e.maxX,e.maxY,r.clientY,r.clientX,t.getContentAreaContainer(),t.getWin(),o,s),n.throttle(r.clientX,r.clientY)}var a,i}))},FT=(e,t,n)=>{e.on((e=>{e.intervalId.clear(),e.dragging&&n.fold((()=>MT(t,"dragend",e.element,e.dataTransfer)),(n=>MT(t,"dragend",e.element,e.dataTransfer,n)))})),UT(e)},UT=e=>{e.on((e=>{e.intervalId.clear(),LT(e.ghost)})),e.clear()},zT=e=>{const t=Dr(),n=ma.DOM,o=document,r=((e,t)=>n=>{if((e=>0===e.button)(n)){const o=Q(t.dom.getParents(n.target),RT).getOr(null);if(C(o)&&((e,t,n)=>NT(n)&&n!==t&&e.isEditable(n.parentElement))(t.dom,t.getBody(),o)){const r=t.dom.getPos(o),s=t.getBody(),a=t.getDoc().documentElement;e.set({element:o,dataTransfer:SA(),dragging:!1,screenX:n.screenX,screenY:n.screenY,maxX:(t.inline?s.scrollWidth:a.offsetWidth)-2,maxY:(t.inline?s.scrollHeight:a.offsetHeight)-2,relX:n.pageX-r.x,relY:n.pageY-r.y,width:o.offsetWidth,height:o.offsetHeight,ghost:AT(t,o,o.offsetWidth,o.offsetHeight),intervalId:Pr(100)})}}})(t,e),s=IT(t,e),a=((e,t)=>n=>{e.on((e=>{var o;if(e.intervalId.clear(),e.dragging){if(((e,t,n)=>!y(t)&&t!==n&&!e.dom.isChildOf(t,n)&&e.dom.isEditable(t))(t,(e=>{const t=e.getSel();if(C(t)){const e=t.getRangeAt(0).startContainer;return lr(e)?e.parentNode:e}return null})(t.selection),e.element)){const r=null!==(o=t.getDoc().elementFromPoint(n.clientX,n.clientY))&&void 0!==o?o:t.getBody();MT(t,"drop",r,e.dataTransfer,n).isDefaultPrevented()||t.undoManager.transact((()=>{((e,t)=>{const n=e.getParent(t.parentNode,e.isBlock);LT(t),n&&n!==e.getRoot()&&e.isEmpty(n)&&Ja(Cn(n))})(t.dom,e.element),(e=>{const t=e.getData("text/html");return""===t?I.none():I.some(t)})(e.dataTransfer).each((e=>t.insertContent(e))),t._selectionOverrides.hideFakeCaret()}))}MT(t,"dragend",t.getBody(),e.dataTransfer,n)}})),UT(e)})(t,e),i=((e,t)=>n=>FT(e,t,I.some(n)))(t,e);e.on("mousedown",r),e.on("mousemove",s),e.on("mouseup",a),n.bind(o,"mousemove",s),n.bind(o,"mouseup",i),e.on("remove",(()=>{n.unbind(o,"mousemove",s),n.unbind(o,"mouseup",i)})),e.on("keydown",(n=>{n.keyCode===Qf.ESC&&FT(t,e,I.none())}))},jT=br,HT=(e,t)=>bb(e.getBody(),t),$T=e=>{const t=e.selection,n=e.dom,o=e.getBody(),r=eu(e,o,n.isBlock,(()=>Gf(e))),s="sel-"+n.uniqueId(),a="data-mce-selected";let i;const l=e=>e!==o&&(jT(e)||wr(e))&&n.isChildOf(e,o)&&n.isEditable(e.parentNode),d=(n,o,s,a=!0)=>e.dispatch("ShowCaret",{target:o,direction:n,before:s}).isDefaultPrevented()?null:(a&&t.scrollIntoView(o,-1===n),r.show(s,o)),c=e=>ii(e)||ui(e)||mi(e),u=e=>c(e.startContainer)||c(e.endContainer),m=t=>{const o=e.schema.getVoidElements(),r=n.createRng(),s=t.startContainer,a=t.startOffset,i=t.endContainer,l=t.endOffset;return _e(o,s.nodeName.toLowerCase())?0===a?r.setStartBefore(s):r.setStartAfter(s):r.setStart(s,a),_e(o,i.nodeName.toLowerCase())?0===l?r.setEndBefore(i):r.setEndAfter(i):r.setEnd(i,l),r},f=(r,c)=>{if(!r)return null;if(r.collapsed){if(!u(r)){const e=c?1:-1,t=Nu(e,o,r),s=t.getNode(!c);if(C(s)){if(ou(s))return d(e,s,!!c&&!t.isAtEnd(),!1);if(ai(s)&&br(s.nextSibling)){const e=n.createRng();return e.setStart(s,0),e.setEnd(s,0),e}}const a=t.getNode(c);if(C(a)){if(ou(a))return d(e,a,!c&&!t.isAtEnd(),!1);if(ai(a)&&br(a.previousSibling)){const e=n.createRng();return e.setStart(a,1),e.setEnd(a,1),e}}}return null}let m=r.startContainer,f=r.startOffset;const g=r.endOffset;if(lr(m)&&0===f&&jT(m.parentNode)&&(m=m.parentNode,f=n.nodeIndex(m),m=m.parentNode),!Jo(m))return null;if(g===f+1&&m===r.endContainer){const o=m.childNodes[f];if(l(o))return(o=>{const r=o.cloneNode(!0),l=e.dispatch("ObjectSelected",{target:o,targetClone:r});if(l.isDefaultPrevented())return null;const d=((o,r)=>{const a=Cn(e.getBody()),i=e.getDoc(),l=eo(a,"#"+s).getOrThunk((()=>{const e=bn('<div data-mce-bogus="all" class="mce-offscreen-selection"></div>',i);return Jt(e,"id",s),vo(a,e),e})),d=n.createRng();wo(l),Co(l,[yn(qo,i),Cn(r),yn(qo,i)]),d.setStart(l.dom.firstChild,1),d.setEnd(l.dom.lastChild,0),lo(l,{top:n.getPos(o,e.getBody()).y+"px"}),Ef(l);const c=t.getSel();return c&&(c.removeAllRanges(),c.addRange(d)),d})(o,l.targetClone),c=Cn(o);return q(Uo(Cn(e.getBody()),`*[${a}]`),(e=>{Sn(c,e)||rn(e,a)})),n.getAttrib(o,a)||o.setAttribute(a,"1"),i=o,p(),d})(o)}return null},g=()=>{i&&i.removeAttribute(a),eo(Cn(e.getBody()),"#"+s).each(Eo),i=null},p=()=>{r.hide()};return pw(e)||(e.on("click",(t=>{n.isEditable(t.target)||(t.preventDefault(),e.focus())})),e.on("blur NewBlock",g),e.on("ResizeWindow FullscreenStateChanged",r.reposition),e.on("tap",(t=>{const n=t.target,o=HT(e,n);jT(o)?(t.preventDefault(),ex(e,o).each(f)):l(n)&&ex(e,n).each(f)}),!0),e.on("mousedown",(r=>{const s=r.target;if(s!==o&&"HTML"!==s.nodeName&&!n.isChildOf(s,o))return;if(!((e,t,n)=>{const o=Cn(e.getBody()),r=e.inline?o:Cn(Rn(o).dom.documentElement),s=((e,t,n,o)=>{const r=(e=>e.dom.getBoundingClientRect())(t);return{x:n-(e?r.left+t.dom.clientLeft+Yw(t):0),y:o-(e?r.top+t.dom.clientTop+Kw(t):0)}})(e.inline,r,t,n);return((e,t,n)=>{const o=qw(e),r=Ww(e);return t>=0&&n>=0&&t<=o&&n<=r})(r,s.x,s.y)})(e,r.clientX,r.clientY))return;g(),p();const a=HT(e,s);jT(a)?(r.preventDefault(),ex(e,a).each(f)):xT(o,r.clientX,r.clientY).each((n=>{var o;r.preventDefault(),(o=d(1,n.node,n.position===bT.Before,!1))&&t.setRng(o),er(a)?a.focus():e.getBody().focus()}))})),e.on("keypress",(e=>{Qf.modifierPressed(e)||jT(t.getNode())&&e.preventDefault()})),e.on("GetSelectionRange",(e=>{let t=e.range;if(i){if(!i.parentNode)return void(i=null);t=t.cloneRange(),t.selectNode(i),e.range=t}})),e.on("SetSelectionRange",(e=>{e.range=m(e.range);const t=f(e.range,e.forward);t&&(e.range=t)})),e.on("AfterSetSelectionRange",(e=>{const t=e.range,o=t.startContainer.parentElement;var r;u(t)||Jo(r=o)&&"mcepastebin"===r.id||p(),(e=>C(e)&&n.hasClass(e,"mce-offscreen-selection"))(o)||g()})),(e=>{zT(e),ec(e)&&(e=>{const t=t=>{if(!t.isDefaultPrevented()){const n=t.dataTransfer;n&&(H(n.types,"Files")||n.files.length>0)&&(t.preventDefault(),"drop"===t.type&&eE(e,"Dropped file type is not supported"))}},n=n=>{$f(e,n.target)&&t(n)},o=()=>{const o=ma.DOM,r=e.dom,s=document,a=e.inline?e.getBody():e.getDoc(),i=["drop","dragover"];q(i,(e=>{o.bind(s,e,n),r.bind(a,e,t)})),e.on("remove",(()=>{q(i,(e=>{o.unbind(s,e,n),r.unbind(a,e,t)}))}))};e.on("init",(()=>{Uf.setEditorTimeout(e,o,0)}))})(e)})(e),(e=>{const t=Ca((()=>{if(!e.removed&&e.getBody().contains(document.activeElement)){const t=e.selection.getRng();if(t.collapsed){const n=tx(e,t,!1);e.selection.setRng(n)}}}),0);e.on("focus",(()=>{t.throttle()})),e.on("blur",(()=>{t.cancel()}))})(e),(e=>{e.on("init",(()=>{e.on("focusin",(t=>{const n=t.target;if(wr(n)){const t=bb(e.getBody(),n),o=br(t)?t:n;e.selection.getNode()!==o&&ex(e,o).each((t=>e.selection.setRng(t)))}}))}))})(e)),{showCaret:d,showBlockCaretContainer:e=>{e.hasAttribute("data-mce-caret")&&(fi(e),t.scrollIntoView(e))},hideFakeCaret:p,destroy:()=>{r.destroy(),i=null}}},VT=(e,t)=>{let n=t;for(let t=e.previousSibling;lr(t);t=t.previousSibling)n+=t.data.length;return n},qT=(e,t,n,o,r)=>{if(lr(n)&&(o<0||o>n.data.length))return[];const s=r&&lr(n)?[VT(n,o)]:[o];let a=n;for(;a!==t&&a.parentNode;)s.push(e.nodeIndex(a,r)),a=a.parentNode;return a===t?s.reverse():[]},WT=(e,t,n,o,r,s,a=!1)=>({start:qT(e,t,n,o,a),end:qT(e,t,r,s,a)}),KT=(e,t)=>{const n=t.slice(),o=n.pop();return E(o)?X(n,((e,t)=>e.bind((e=>I.from(e.childNodes[t])))),I.some(e)).bind((e=>lr(e)&&(o<0||o>e.data.length)?I.none():I.some({node:e,offset:o}))):I.none()},YT=(e,t)=>KT(e,t.start).bind((({node:n,offset:o})=>KT(e,t.end).map((({node:e,offset:t})=>{const r=document.createRange();return r.setStart(n,o),r.setEnd(e,t),r})))),GT=(e,t,n)=>{if(t&&e.isEmpty(t)&&!n(t)){const o=t.parentNode;e.remove(t,lr(t.firstChild)&&Yo(t.firstChild.data)),GT(e,o,n)}},XT=(e,t,n,o=!0)=>{const r=t.startContainer.parentNode,s=t.endContainer.parentNode;t.deleteContents(),o&&!n(t.startContainer)&&(lr(t.startContainer)&&0===t.startContainer.data.length&&e.remove(t.startContainer),lr(t.endContainer)&&0===t.endContainer.data.length&&e.remove(t.endContainer),GT(e,r,n),r!==s&&GT(e,s,n))},ZT=(e,t)=>I.from(e.dom.getParent(t.startContainer,e.dom.isBlock)),QT=(e,t,n)=>{const o=e.dynamicPatternsLookup({text:n,block:t});return{...e,blockPatterns:Al(o).concat(e.blockPatterns),inlinePatterns:Tl(o).concat(e.inlinePatterns)}},JT=(e,t,n,o)=>{const r=e.createRng();return r.setStart(t,0),r.setEnd(n,o),r.toString()},eO=(e,t)=>e.create("span",{"data-mce-type":"bookmark",id:t}),tO=(e,t)=>{const n=e.createRng();return n.setStartAfter(t.start),n.setEndBefore(t.end),n},nO=(e,t,n)=>{const o=YT(e.getRoot(),n).getOrDie("Unable to resolve path range"),r=o.startContainer,s=o.endContainer,a=0===o.endOffset?s:s.splitText(o.endOffset),i=0===o.startOffset?r:r.splitText(o.startOffset),l=i.parentNode;return{prefix:t,end:a.parentNode.insertBefore(eO(e,t+"-end"),a),start:l.insertBefore(eO(e,t+"-start"),i)}},oO=(e,t,n)=>{GT(e,e.get(t.prefix+"-end"),n),GT(e,e.get(t.prefix+"-start"),n)},rO=e=>0===e.start.length,sO=(e,t,n,o)=>{const r=t.start;var s;return bk(e,o.container,o.offset,(s=r,(e,t)=>{const n=e.data.substring(0,t),o=n.lastIndexOf(s.charAt(s.length-1)),r=n.lastIndexOf(s);return-1!==r?r+s.length:-1!==o?o+1:-1}),n).bind((o=>{var s,a;const i=null!==(a=null===(s=n.textContent)||void 0===s?void 0:s.indexOf(r))&&void 0!==a?a:-1;if(-1!==i&&o.offset>=i+r.length){const t=e.createRng();return t.setStart(o.container,o.offset-r.length),t.setEnd(o.container,o.offset),I.some(t)}{const s=o.offset-r.length;return pk(o.container,s,n).map((t=>{const n=e.createRng();return n.setStart(t.container,t.offset),n.setEnd(o.container,o.offset),n})).filter((e=>e.toString()===r)).orThunk((()=>sO(e,t,n,uk(o.container,0))))}}))},aO=(e,t,n,o)=>{const r=e.dom,s=r.getRoot(),a=n.pattern,i=n.position.container,l=n.position.offset;return pk(i,l-n.pattern.end.length,t).bind((d=>{const c=WT(r,s,d.container,d.offset,i,l,o);if(rO(a))return I.some({matches:[{pattern:a,startRng:c,endRng:c}],position:d});{const i=iO(e,n.remainingPatterns,d.container,d.offset,t,o),l=i.getOr({matches:[],position:d}),u=l.position,m=((e,t,n,o,r,s=!1)=>{if(0===t.start.length&&!s){const t=e.createRng();return t.setStart(n,o),t.setEnd(n,o),I.some(t)}return gk(n,o,r).bind((n=>sO(e,t,r,n).bind((e=>{var t;if(s){if(e.endContainer===n.container&&e.endOffset===n.offset)return I.none();if(0===n.offset&&(null===(t=e.endContainer.textContent)||void 0===t?void 0:t.length)===e.endOffset)return I.none()}return I.some(e)}))))})(r,a,u.container,u.offset,t,i.isNone());return m.map((e=>{const t=((e,t,n,o=!1)=>WT(e,t,n.startContainer,n.startOffset,n.endContainer,n.endOffset,o))(r,s,e,o);return{matches:l.matches.concat([{pattern:a,startRng:t,endRng:c}]),position:uk(e.startContainer,e.startOffset)}}))}}))},iO=(e,t,n,o,r,s)=>{const a=e.dom;return gk(n,o,a.getRoot()).bind((i=>{const l=JT(a,r,n,o);for(let a=0;a<t.length;a++){const d=t[a];if(!Ve(l,d.end))continue;const c=t.slice();c.splice(a,1);const u=aO(e,r,{pattern:d,remainingPatterns:c,position:i},s);if(u.isNone()&&o>0)return iO(e,t,n,o-1,r,s);if(u.isSome())return u}return I.none()}))},lO=(e,t,n)=>{e.selection.setRng(n),"inline-format"===t.type?q(t.format,(t=>{e.formatter.apply(t)})):e.execCommand(t.cmd,!1,t.value)},dO=(e,t,n,o,r,s)=>{var a;return((e,t)=>{const n=ne(e,(e=>$(t,(t=>e.pattern.start===t.pattern.start&&e.pattern.end===t.pattern.end))));return e.length===t.length?n?e:t:e.length>t.length?e:t})(iO(e,r.inlinePatterns,n,o,t,s).fold((()=>[]),(e=>e.matches)),iO(e,(a=r.inlinePatterns,ae(a,((e,t)=>t.end.length-e.end.length))),n,o,t,s).fold((()=>[]),(e=>e.matches)))},cO=(e,t)=>{if(0===t.length)return;const n=e.dom,o=e.selection.getBookmark(),r=((e,t)=>{const n=La("mce_textpattern"),o=G(t,((t,o)=>{const r=nO(e,n+`_end${t.length}`,o.endRng);return t.concat([{...o,endMarker:r}])}),[]);return G(o,((t,r)=>{const s=o.length-t.length-1,a=rO(r.pattern)?r.endMarker:nO(e,n+`_start${s}`,r.startRng);return t.concat([{...r,startMarker:a}])}),[])})(n,t);q(r,(t=>{const o=n.getParent(t.startMarker.start,n.isBlock),r=e=>e===o;rO(t.pattern)?((e,t,n,o)=>{const r=tO(e.dom,n);XT(e.dom,r,o),lO(e,t,r)})(e,t.pattern,t.endMarker,r):((e,t,n,o,r)=>{const s=e.dom,a=tO(s,o),i=tO(s,n);XT(s,i,r),XT(s,a,r);const l={prefix:n.prefix,start:n.end,end:o.start},d=tO(s,l);lO(e,t,d)})(e,t.pattern,t.startMarker,t.endMarker,r),oO(n,t.endMarker,r),oO(n,t.startMarker,r)})),e.selection.moveToBookmark(o)},uO=(e,t,n)=>((e,t,n)=>{if(lr(e)&&0>=e.length)return I.some(uk(e,0));{const t=za(mk);return I.from(t.forwards(e,0,fk(e),n)).map((e=>uk(e.container,0)))}})(t,0,t).map((o=>{const r=o.container;return hk(r,n.start.length,t).each((n=>{const o=e.createRng();o.setStart(r,0),o.setEnd(n.container,n.offset),XT(e,o,(e=>e===t))})),r})),mO=e=>(t,n)=>{const o=t.dom,r=n.pattern,s=YT(o.getRoot(),n.range).getOrDie("Unable to resolve path range");return ZT(t,s).each((n=>{"block-format"===r.type?((e,t)=>{const n=t.get(e);return p(n)&&le(n).exists((e=>_e(e,"block")))})(r.format,t.formatter)&&t.undoManager.transact((()=>{e(t.dom,n,r),t.formatter.apply(r.format)})):"block-command"===r.type&&t.undoManager.transact((()=>{e(t.dom,n,r),t.execCommand(r.cmd,!1,r.value)}))})),!0},fO=e=>(t,n)=>{const o=(e=>ae(e,((e,t)=>t.start.length-e.start.length)))(t),r=n.replace(qo," ");return Q(o,(t=>e(t,n,r)))},gO=(e,t)=>(n,o,r,s,a)=>{var i;void 0===a&&(a=null!==(i=o.textContent)&&void 0!==i?i:"");const l=n.dom,d=ed(n);return l.is(o,d)?e(r.blockPatterns,a).map((e=>t&&Dt.trim(a).length===e.start.length?[]:[{pattern:e,range:WT(l,l.getRoot(),o,0,o,0,s)}])).getOr([]):[]},pO=mO(((e,t,n)=>{uO(e,t,n).each((e=>{const t=Cn(e),n=Ha(t);/^\s[^\s]/.test(n)&&$a(t,n.slice(1))}))})),hO=fO(((e,t,n)=>0===t.indexOf(e.start)||0===n.indexOf(e.start))),bO=gO(hO,!0),vO=mO(uO),yO=fO(((e,t,n)=>t===e.start||n===e.start)),CO=gO(yO,!1),wO=(e,t,n)=>{for(let o=0;o<e.length;o++)if(n(e[o],t))return!0;return!1},EO=e=>{const t=[",",".",";",":","!","?"],n=[32],o=()=>{return t=xc(e).filter((t=>"inline-command"!==t.type&&"block-command"!==t.type||e.queryCommandSupported(t.cmd))),n=_c(e),{inlinePatterns:Tl(t),blockPatterns:Al(t),dynamicPatternsLookup:n};var t,n},r=()=>(e=>e.options.isSet("text_patterns_lookup"))(e);e.on("keydown",(t=>{if(13===t.keyCode&&!Qf.modifierPressed(t)&&e.selection.isCollapsed()&&e.selection.isEditable()){const n=Ol(o(),"enter");(n.inlinePatterns.length>0||n.blockPatterns.length>0||r())&&((e,t)=>((e,t)=>{const n=e.selection.getRng();return ZT(e,n).map((o=>{var r;const s=Math.max(0,n.startOffset),a=QT(t,o,null!==(r=o.textContent)&&void 0!==r?r:"");return{inlineMatches:dO(e,o,n.startContainer,s,a,!0),blockMatches:bO(e,o,a,!0)}})).filter((({inlineMatches:e,blockMatches:t})=>t.length>0||e.length>0))})(e,t).fold(L,(({inlineMatches:t,blockMatches:n})=>(e.undoManager.add(),e.undoManager.extra((()=>{e.execCommand("mceInsertNewLine")}),(()=>{(e=>{e.insertContent(ei,{preserve_zwsp:!0})})(e),cO(e,t),((e,t)=>{if(0===t.length)return;const n=e.selection.getBookmark();q(t,(t=>pO(e,t))),e.selection.moveToBookmark(n)})(e,n);const o=e.selection.getRng(),r=gk(o.startContainer,o.startOffset,e.dom.getRoot());e.execCommand("mceInsertNewLine"),r.each((t=>{const n=t.container;n.data.charAt(t.offset-1)===Vo&&(n.deleteData(t.offset-1,1),GT(e.dom,n.parentNode,(t=>t===e.dom.getRoot())))}))})),!0))))(e,n)&&t.preventDefault()}}),!0),e.on("keydown",(t=>{if(32===t.keyCode&&e.selection.isCollapsed()&&e.selection.isEditable()){const n=Ol(o(),"space");(n.blockPatterns.length>0||r())&&((e,t)=>((e,t)=>{const n=e.selection.getRng();return ZT(e,n).map((o=>{const r=Math.max(0,n.startOffset),s=JT(e.dom,o,n.startContainer,r),a=QT(t,o,s);return CO(e,o,a,!1,s)})).filter((e=>e.length>0))})(e,t).fold(L,(t=>(e.undoManager.transact((()=>{((e,t)=>{q(t,(t=>vO(e,t)))})(e,t)})),!0))))(e,n)&&t.preventDefault()}}),!0);const s=()=>{if(e.selection.isCollapsed()&&e.selection.isEditable()){const t=Ol(o(),"space");(t.inlinePatterns.length>0||r())&&((e,t)=>{const n=e.selection.getRng();ZT(e,n).map((o=>{const r=Math.max(0,n.startOffset-1),s=JT(e.dom,o,n.startContainer,r),a=QT(t,o,s),i=dO(e,o,n.startContainer,r,a,!1);i.length>0&&e.undoManager.transact((()=>{cO(e,i)}))}))})(e,t)}};e.on("keyup",(e=>{wO(n,e,((e,t)=>e===t.keyCode&&!Qf.modifierPressed(t)))&&s()})),e.on("keypress",(n=>{wO(t,n,((e,t)=>e.charCodeAt(0)===t.charCode))&&Uf.setEditorTimeout(e,s)}))},xO=e=>{const t=Dt.each,n=Qf.BACKSPACE,o=Qf.DELETE,r=e.dom,s=e.selection,a=e.parser,i=Tt.browser,l=i.isFirefox(),d=i.isChromium()||i.isSafari(),c=Tt.deviceType.isiPhone()||Tt.deviceType.isiPad(),u=Tt.os.isMacOS()||Tt.os.isiOS(),m=(t,n)=>{try{e.getDoc().execCommand(t,!1,String(n))}catch(e){}},f=e=>e.isDefaultPrevented(),g=()=>{e.shortcuts.add("meta+a",null,"SelectAll")},p=()=>{e.inline||r.bind(e.getDoc(),"mousedown mouseup",(t=>{let n;if(t.target===e.getDoc().documentElement)if(n=s.getRng(),e.getBody().focus(),"mousedown"===t.type){if(ii(n.startContainer))return;s.placeCaretAt(t.clientX,t.clientY)}else s.setRng(n)}))},h=()=>{Range.prototype.getClientRects||e.on("mousedown",(t=>{if(!f(t)&&"HTML"===t.target.nodeName){const t=e.getBody();t.blur(),Uf.setEditorTimeout(e,(()=>{t.focus()}))}}))},b=()=>{const t=oc(e);e.on("click",(n=>{const o=n.target;/^(IMG|HR)$/.test(o.nodeName)&&r.isEditable(o)&&(n.preventDefault(),e.selection.select(o),e.nodeChanged()),"A"===o.nodeName&&r.hasClass(o,t)&&0===o.childNodes.length&&r.isEditable(o.parentNode)&&(n.preventDefault(),s.select(o))}))},v=()=>{e.on("keydown",(e=>{if(!f(e)&&e.keyCode===n&&s.isCollapsed()&&0===s.getRng().startOffset){const t=s.getNode().previousSibling;if(t&&t.nodeName&&"table"===t.nodeName.toLowerCase())return e.preventDefault(),!1}return!0}))},y=()=>{Gd(e)||e.on("BeforeExecCommand mousedown",(()=>{m("StyleWithCSS",!1),m("enableInlineTableEditing",!1),Nd(e)||m("enableObjectResizing",!1)}))},C=()=>{e.contentStyles.push("img:-moz-broken {-moz-force-broken-image-icon:1;min-width:24px;min-height:24px}")},w=()=>{e.inline||e.on("keydown",(()=>{document.activeElement===document.body&&e.getWin().focus()}))},E=()=>{e.inline||(e.contentStyles.push("body {min-height: 150px}"),e.on("click",(t=>{let n;"HTML"===t.target.nodeName&&(n=e.selection.getRng(),e.getBody().focus(),e.selection.setRng(n),e.selection.normalize(),e.nodeChanged())})))},x=()=>{u&&e.on("keydown",(t=>{!Qf.metaKeyPressed(t)||t.shiftKey||37!==t.keyCode&&39!==t.keyCode||(t.preventDefault(),e.selection.getSel().modify("move",37===t.keyCode?"backward":"forward","lineboundary"))}))},S=()=>{e.on("click",(e=>{let t=e.target;do{if("A"===t.tagName)return void e.preventDefault()}while(t=t.parentNode)})),e.contentStyles.push(".mce-content-body {-webkit-touch-callout: none}")},k=()=>{e.on("init",(()=>{e.dom.bind(e.getBody(),"submit",(e=>{e.preventDefault()}))}))},N=_;return pw(e)?(d&&(p(),b(),k(),g(),c&&(w(),E(),S())),l&&(h(),y(),C(),x())):(e.on("keydown",(t=>{if(f(t)||t.keyCode!==Qf.BACKSPACE)return;let n=s.getRng();const o=n.startContainer,a=n.startOffset,i=r.getRoot();let l=o;if(n.collapsed&&0===a){for(;l.parentNode&&l.parentNode.firstChild===l&&l.parentNode!==i;)l=l.parentNode;"BLOCKQUOTE"===l.nodeName&&(e.formatter.toggle("blockquote",void 0,l),n=r.createRng(),n.setStart(o,0),n.setEnd(o,0),s.setRng(n))}})),(()=>{const t=e=>{const t=r.create("body"),n=e.cloneContents();return t.appendChild(n),s.serializer.serialize(t,{format:"html"})};e.on("keydown",(s=>{const a=s.keyCode;if(!f(s)&&(a===o||a===n)&&e.selection.isEditable()){const n=e.selection.isCollapsed(),o=e.getBody();if(n&&!Ar(e.schema,o))return;if(!n&&!(n=>{const o=t(n),s=r.createRng();return s.selectNode(e.getBody()),o===t(s)})(e.selection.getRng()))return;s.preventDefault(),e.setContent(""),o.firstChild&&r.isBlock(o.firstChild)?e.selection.setCursorLocation(o.firstChild,0):e.selection.setCursorLocation(o,0),e.nodeChanged()}}))})(),Tt.windowsPhone||e.on("keyup focusin mouseup",(t=>{Qf.modifierPressed(t)||(e=>{const t=e.getBody(),n=e.selection.getRng();return n.startContainer===n.endContainer&&n.startContainer===t&&0===n.startOffset&&n.endOffset===t.childNodes.length})(e)||s.normalize()}),!0),d&&(p(),b(),e.on("init",(()=>{m("DefaultParagraphSeparator",ed(e))})),k(),v(),a.addNodeFilter("br",(e=>{let t=e.length;for(;t--;)"Apple-interchange-newline"===e[t].attr("class")&&e[t].remove()})),c?(w(),E(),S()):g()),l&&(e.on("keydown",(t=>{if(!f(t)&&t.keyCode===n){if(!e.getBody().getElementsByTagName("hr").length)return;if(s.isCollapsed()&&0===s.getRng().startOffset){const e=s.getNode(),n=e.previousSibling;if("HR"===e.nodeName)return r.remove(e),void t.preventDefault();n&&n.nodeName&&"hr"===n.nodeName.toLowerCase()&&(r.remove(n),t.preventDefault())}}})),h(),(()=>{const n=()=>{const n=r.getAttribs(s.getStart().cloneNode(!1));return()=>{const o=s.getStart();o!==e.getBody()&&(r.setAttrib(o,"style",null),t(n,(e=>{o.setAttributeNode(e.cloneNode(!0))})))}},o=()=>!s.isCollapsed()&&r.getParent(s.getStart(),r.isBlock)!==r.getParent(s.getEnd(),r.isBlock);e.on("keypress",(t=>{let r;return!(!(f(t)||8!==t.keyCode&&46!==t.keyCode)&&o()&&(r=n(),e.getDoc().execCommand("delete",!1),r(),t.preventDefault(),1))})),r.bind(e.getDoc(),"cut",(t=>{if(!f(t)&&o()){const t=n();Uf.setEditorTimeout(e,(()=>{t()}))}}))})(),y(),e.on("SetContent ExecCommand",(e=>{"setcontent"!==e.type&&"mceInsertLink"!==e.command||t(r.select("a:not([data-mce-block])"),(e=>{var t;let n=e.parentNode;const o=r.getRoot();if((null==n?void 0:n.lastChild)===e){for(;n&&!r.isBlock(n);){if((null===(t=n.parentNode)||void 0===t?void 0:t.lastChild)!==n||n===o)return;n=n.parentNode}r.add(n,"br",{"data-mce-bogus":1})}}))})),C(),x(),v())),{refreshContentEditable:N,isHidden:()=>{if(!l||e.removed)return!1;const t=e.selection.getSel();return!t||!t.rangeCount||0===t.rangeCount}}},_O=ma.DOM,SO=e=>e.inline?e.getElement().nodeName.toLowerCase():void 0,kO=e=>Ce(e,(e=>!1===v(e))),NO=e=>{const t=e.options.get,n=e.editorUpload.blobCache;return kO({allow_conditional_comments:t("allow_conditional_comments"),allow_html_data_urls:t("allow_html_data_urls"),allow_svg_data_urls:t("allow_svg_data_urls"),allow_html_in_named_anchor:t("allow_html_in_named_anchor"),allow_script_urls:t("allow_script_urls"),allow_mathml_annotation_encodings:t("allow_mathml_annotation_encodings"),allow_unsafe_link_target:t("allow_unsafe_link_target"),convert_unsafe_embeds:t("convert_unsafe_embeds"),convert_fonts_to_spans:t("convert_fonts_to_spans"),extended_mathml_attributes:t("extended_mathml_attributes"),extended_mathml_elements:t("extended_mathml_elements"),fix_list_elements:t("fix_list_elements"),font_size_legacy_values:t("font_size_legacy_values"),forced_root_block:t("forced_root_block"),forced_root_block_attrs:t("forced_root_block_attrs"),preserve_cdata:t("preserve_cdata"),inline_styles:t("inline_styles"),root_name:SO(e),sandbox_iframes:t("sandbox_iframes"),sandbox_iframes_exclusions:Ic(e),sanitize:t("xss_sanitization"),validate:!0,blob_cache:n,document:e.getDoc()})},RO=e=>{const t=e.options.get;return kO({custom_elements:t("custom_elements"),extended_valid_elements:t("extended_valid_elements"),invalid_elements:t("invalid_elements"),invalid_styles:t("invalid_styles"),schema:t("schema"),valid_children:t("valid_children"),valid_classes:t("valid_classes"),valid_elements:t("valid_elements"),valid_styles:t("valid_styles"),verify_html:t("verify_html"),padd_empty_block_inline_children:t("format_empty_lines")})},AO=e=>e.inline?e.ui.styleSheetLoader:e.dom.styleSheetLoader,TO=e=>{const t=AO(e),n=_d(e),o=e.contentCSS,r=()=>{t.unloadAll(o),e.inline||e.ui.styleSheetLoader.unloadAll(n)},s=()=>{e.removed?r():e.on("remove",r)};if(e.contentStyles.length>0){let t="";Dt.each(e.contentStyles,(e=>{t+=e+"\r\n"})),e.dom.addStyle(t)}const a=Promise.all(((e,t,n)=>{const{pass:o,fail:r}=K(t,(e=>tinymce.Resource.has(fE(e)))),s=o.map((t=>{const n=tinymce.Resource.get(fE(t));return m(n)?Promise.resolve(AO(e).loadRawCss(t,n)):Promise.resolve()})),a=[...s,AO(e).loadAll(r)];return e.inline?a:a.concat([e.ui.styleSheetLoader.loadAll(n)])})(e,o,n)).then(s).catch(s),i=xd(e);return i&&((e,t)=>{const n=Cn(e.getBody()),o=Vn($n(n)),r=vn("style");Jt(r,"type","text/css"),vo(r,yn(t)),vo(o,r),e.on("remove",(()=>{Eo(r)}))})(e,i),a},OO=e=>{!0!==e.removed&&((e=>{pw(e)||e.load({initial:!0,format:"html"}),e.startContent=e.getContent({format:"raw"})})(e),(e=>{e.bindPendingEventDelegates(),e.initialized=!0,(e=>{e.dispatch("Init")})(e),e.focus(!0),(e=>{const t=e.dom.getRoot();e.inline||km(e)&&e.selection.getStart(!0)!==t||Zu(t).each((t=>{const n=t.getNode(),o=sr(n)?Zu(n).getOr(t):t;e.selection.setRng(o.toRange())}))})(e),e.nodeChanged({initial:!0});const t=ac(e);w(t)&&t.call(e,e),(e=>{const t=lc(e);t&&Uf.setEditorTimeout(e,(()=>{let n;n=!0===t?e:e.editorManager.get(t),n&&!n.destroyed&&(n.focus(),n.selection.scrollIntoView())}),100)})(e),lE(e)&&cE(e,!0)})(e))},BO=e=>{const t=e.getElement();let n=e.getDoc();e.inline&&(_O.addClass(t,"mce-content-body"),e.contentDocument=n=document,e.contentWindow=window,e.bodyElement=t,e.contentAreaContainer=t);const o=e.getBody();o.disabled=!0,e.readonly=Gd(e),e._editableRoot=Xd(e),!jc(e)&&e.hasEditableRoot()&&(e.inline&&"static"===_O.getStyle(o,"position",!0)&&(o.style.position="relative"),o.contentEditable="true"),o.disabled=!1,e.editorUpload=wE(e),e.schema=Fs(RO(e)),e.dom=ma(n,{keep_values:!0,url_converter:e.convertURL,url_converter_scope:e,update_styles:!0,root_element:e.inline?e.getBody():null,collect:e.inline,schema:e.schema,contentCssCors:hd(e),referrerPolicy:bd(e),onSetAttrib:t=>{e.dispatch("SetAttrib",t)}}),e.parser=(e=>{const t=LC(NO(e),e.schema);return t.addAttributeFilter("src,href,style,tabindex",((t,n)=>{const o=e.dom,r="data-mce-"+n;let s=t.length;for(;s--;){const a=t[s];let i=a.attr(n);if(i&&!a.attr(r)){if(0===i.indexOf("data:")||0===i.indexOf("blob:"))continue;"style"===n?(i=o.serializeStyle(o.parseStyle(i),a.name),i.length||(i=null),a.attr(r,i),a.attr(n,i)):"tabindex"===n?(a.attr(r,i),a.attr(n,null)):a.attr(r,e.convertURL(i,n,a.name))}}})),t.addNodeFilter("script",(e=>{let t=e.length;for(;t--;){const n=e[t],o=n.attr("type")||"no/type";0!==o.indexOf("mce-")&&n.attr("type","mce-"+o)}})),Rc(e)&&t.addNodeFilter("#cdata",(t=>{var n;let o=t.length;for(;o--;){const r=t[o];r.type=8,r.name="#comment",r.value="[CDATA["+e.dom.encode(null!==(n=r.value)&&void 0!==n?n:"")+"]]"}})),t.addNodeFilter("p,h1,h2,h3,h4,h5,h6,div",(t=>{let n=t.length;const o=e.schema.getNonEmptyElements();for(;n--;){const e=t[n];e.isEmpty(o)&&0===e.getAll("br").length&&e.append(new tp("br",1))}})),t})(e),e.serializer=kw((e=>{const t=e.options.get;return{...NO(e),...RO(e),...kO({remove_trailing_brs:t("remove_trailing_brs"),pad_empty_with_br:t("pad_empty_with_br"),url_converter:t("url_converter"),url_converter_scope:t("url_converter_scope"),element_format:t("element_format"),entities:t("entities"),entity_encoding:t("entity_encoding"),indent:t("indent"),indent_after:t("indent_after"),indent_before:t("indent_before")})}})(e),e),e.selection=xw(e.dom,e.getWin(),e.serializer,e),e.annotator=vf(e),e.formatter=BE(e),e.undoManager=DE(e),e._nodeChangeDispatcher=new aA(e),e._selectionOverrides=$T(e),(e=>{const t=Dr(),n=Br(!1),o=wa((t=>{e.dispatch("longpress",{...t,type:"longpress"}),n.set(!0)}),400);e.on("touchstart",(e=>{cS(e).each((r=>{o.cancel();const s={x:r.clientX,y:r.clientY,target:e.target};o.throttle(e),n.set(!1),t.set(s)}))}),!0),e.on("touchmove",(r=>{o.cancel(),cS(r).each((o=>{t.on((r=>{((e,t)=>{const n=Math.abs(e.clientX-t.x),o=Math.abs(e.clientY-t.y);return n>5||o>5})(o,r)&&(t.clear(),n.set(!1),e.dispatch("longpresscancel"))}))}))}),!0),e.on("touchend touchcancel",(r=>{o.cancel(),"touchcancel"!==r.type&&t.get().filter((e=>e.target.isEqualNode(r.target))).each((()=>{n.get()?r.preventDefault():e.dispatch("tap",{...r,type:"tap"})}))}),!0)})(e),(e=>{(e=>{e.on("click",(t=>{e.dom.getParent(t.target,"details")&&t.preventDefault()}))})(e),(e=>{e.parser.addNodeFilter("details",(t=>{const n=Dc(e);q(t,(e=>{"expanded"===n?e.attr("open","open"):"collapsed"===n&&e.attr("open",null)}))})),e.serializer.addNodeFilter("details",(t=>{const n=Lc(e);q(t,(e=>{"expanded"===n?e.attr("open","open"):"collapsed"===n&&e.attr("open",null)}))}))})(e)})(e),(e=>{const t="contenteditable",n=" "+Dt.trim(kc(e))+" ",o=" "+Dt.trim(Sc(e))+" ",r=hS(n),s=hS(o),a=Nc(e);a.length>0&&e.on("BeforeSetContent",(t=>{((e,t,n)=>{let o=t.length,r=n.content;if("raw"!==n.format){for(;o--;)r=r.replace(t[o],bS(e,r,Sc(e)));n.content=r}})(e,a,t)})),e.parser.addAttributeFilter("class",(e=>{let n=e.length;for(;n--;){const o=e[n];r(o)?o.attr(t,"true"):s(o)&&o.attr(t,"false")}})),e.serializer.addAttributeFilter(t,(e=>{let n=e.length;for(;n--;){const o=e[n];if(!r(o)&&!s(o))continue;const i=o.attr("data-mce-content");a.length>0&&i?vS(a,i)?(o.name="#text",o.type=3,o.raw=!0,o.value=i):o.remove():o.attr(t,null)}}))})(e),pw(e)||((e=>{e.on("mousedown",(t=>{t.detail>=3&&(t.preventDefault(),hT(e))}))})(e),(e=>{EO(e)})(e));const r=sA(e);((e,t)=>{e.addCommand("delete",(()=>{((e,t)=>{dS(e,t,!1).fold((()=>{e.selection.isEditable()&&(Ih(e),jh(e))}),D)})(e,t)})),e.addCommand("forwardDelete",(()=>{((e,t)=>{dS(e,t,!0).fold((()=>{e.selection.isEditable()&&Fh(e)}),D)})(e,t)}))})(e,r),(e=>{e.on("NodeChange",(()=>(e=>{const t=e.dom,n=e.selection,o=e.schema,r=o.getBlockElements(),s=n.getStart(),a=e.getBody();let i,l,d=null;const c=ed(e);if(!s||!Jo(s))return;const u=a.nodeName.toLowerCase();if(!o.isValidChild(u,c.toLowerCase())||((e,t,n)=>$(jp(Cn(n),Cn(t)),(t=>uS(e,t.dom))))(r,a,s))return;if(a.firstChild===a.lastChild&&gr(a.firstChild))return i=gS(e),i.appendChild(Qa().dom),a.replaceChild(i,a.firstChild),e.selection.setCursorLocation(i,0),void e.nodeChanged();let m=a.firstChild;for(;m;)if(Jo(m)&&Kr(o,m),mS(o,m)){if(fS(r,m)){l=m,m=m.nextSibling,t.remove(l);continue}if(!i){if(!d&&e.hasFocus()&&(d=hx(e.selection.getRng(),(()=>document.createElement("span")))),!m.parentNode){m=null;break}i=gS(e),a.insertBefore(i,m)}l=m,m=m.nextSibling,i.appendChild(l)}else i=null,m=m.nextSibling;d&&(e.selection.setRng(bx(d)),e.nodeChanged())})(e)))})(e),(e=>{var t;const n=e.dom,o=ed(e),r=null!==(t=Ad(e))&&void 0!==t?t:"",s=(t,a)=>{if((e=>{if(IE(e)){const t=e.keyCode;return!FE(e)&&(Qf.metaKeyPressed(e)||e.altKey||t>=112&&t<=123||H(LE,t))}return!1})(t))return;const i=e.getBody(),l=!(e=>IE(e)&&!(FE(e)||"keyup"===e.type&&229===e.keyCode))(t)&&((e,t,n)=>{if(e.isEmpty(t,void 0,{skipBogus:!1,includeZwsp:!0})){const o=t.firstElementChild;return!o||!e.getStyle(t.firstElementChild,"padding-left")&&!e.getStyle(t.firstElementChild,"padding-right")&&n===o.nodeName.toLowerCase()}return!1})(n,i,o);(""!==n.getAttrib(i,ME)!==l||a)&&(n.setAttrib(i,ME,l?r:null),((e,t)=>{e.dispatch("PlaceholderToggle",{state:t})})(e,l),e.on(l?"keydown":"keyup",s),e.off(l?"keyup":"keydown",s))};Ge(r)&&e.on("init",(t=>{s(t,!0),e.on("change SetContent ExecCommand",s),e.on("paste",(t=>Uf.setEditorTimeout(e,(()=>s(t)))))}))})(e),dT(e);const s=(e=>{const t=e;return(e=>xe(e.plugins,"rtc").bind((e=>I.from(e.setup))))(e).fold((()=>(t.rtcInstance=gw(e),I.none())),(e=>(t.rtcInstance=(()=>{const e=N(null),t=N("");return{init:{bindEvents:_},undoManager:{beforeChange:_,add:e,undo:e,redo:e,clear:_,reset:_,hasUndo:L,hasRedo:L,transact:e,ignore:_,extra:_},formatter:{match:L,matchAll:N([]),matchNode:N(void 0),canApply:L,closest:t,apply:_,remove:_,toggle:_,formatChanged:N({unbind:_})},editor:{getContent:t,setContent:N({content:"",html:""}),insertContent:N(""),addVisual:_},selection:{getContent:t},autocompleter:{addDecoration:_,removeDecoration:_},raw:{getModel:N(I.none())}}})(),I.some((()=>e().then((e=>(t.rtcInstance=(e=>{const t=e=>f(e)?e:{},{init:n,undoManager:o,formatter:r,editor:s,selection:a,autocompleter:i,raw:l}=e;return{init:{bindEvents:n.bindEvents},undoManager:{beforeChange:o.beforeChange,add:o.add,undo:o.undo,redo:o.redo,clear:o.clear,reset:o.reset,hasUndo:o.hasUndo,hasRedo:o.hasRedo,transact:(e,t,n)=>o.transact(n),ignore:(e,t)=>o.ignore(t),extra:(e,t,n,r)=>o.extra(n,r)},formatter:{match:(e,n,o,s)=>r.match(e,t(n),s),matchAll:r.matchAll,matchNode:r.matchNode,canApply:e=>r.canApply(e),closest:e=>r.closest(e),apply:(e,n,o)=>r.apply(e,t(n)),remove:(e,n,o,s)=>r.remove(e,t(n)),toggle:(e,n,o)=>r.toggle(e,t(n)),formatChanged:(e,t,n,o,s)=>r.formatChanged(t,n,o,s)},editor:{getContent:e=>s.getContent(e),setContent:(e,t)=>({content:s.setContent(e,t),html:""}),insertContent:(e,t)=>(s.insertContent(e),""),addVisual:s.addVisual},selection:{getContent:(e,t)=>a.getContent(t)},autocompleter:{addDecoration:i.addDecoration,removeDecoration:i.removeDecoration},raw:{getModel:()=>I.some(l.getRawModel())}}})(e),e.rtc.isRemote))))))))})(e);(e=>{const t=e.getDoc(),n=e.getBody();(e=>{e.dispatch("PreInit")})(e),dc(e)||(t.body.spellcheck=!1,_O.setAttrib(n,"spellcheck","false")),e.quirks=xO(e),(e=>{e.dispatch("PostRender")})(e);const o=Sd(e);void 0!==o&&(n.dir=o);const r=cc(e);r&&e.on("BeforeSetContent",(e=>{Dt.each(r,(t=>{e.content=e.content.replace(t,(e=>"\x3c!--mce:protected "+escape(e)+"--\x3e"))}))})),e.on("SetContent",(()=>{e.addVisual(e.getBody())})),e.on("compositionstart compositionend",(t=>{e.composing="compositionstart"===t.type}))})(e),(e=>{const t=Uc(e);m(zc(e))||!v(t)&&"INVALID"!==(e=>(e=>"gpl"===e.toLowerCase())(e)||(e=>e.length>=64&&e.length<=255)(e)?"VALID":"INVALID")(t)||console.warn("TinyMCE is running in evaluation mode. Provide a valid license key or add license_key: 'gpl' to the init config to agree to the open source license terms. Read more at https://www.tiny.cloud/license-key/")})(e),s.fold((()=>{const t=(e=>{let t=!1;const n=setTimeout((()=>{t||e.setProgressState(!0)}),500);return()=>{clearTimeout(n),t=!0,e.setProgressState(!1)}})(e);TO(e).then((()=>{OO(e),t()}))}),(t=>{e.setProgressState(!0),TO(e).then((()=>{t().then((t=>{e.setProgressState(!1),OO(e),vw(e)}),(t=>{e.notificationManager.open({type:"error",text:String(t)}),OO(e),vw(e)}))}))}))},PO=M,DO=ma.DOM,LO=ma.DOM,MO=(e,t)=>({editorContainer:e,iframeContainer:t,api:{}}),IO=e=>{const t=e.getElement();return e.inline?MO(null):(e=>{const t=LO.create("div");return LO.insertAfter(t,e),MO(t,t)})(t)},FO=async e=>{e.dispatch("ScriptsLoaded"),(e=>{const t=Dt.trim(cd(e)),n=e.ui.registry.getAll().icons,o={...jw.get("default").icons,...jw.get(t).icons};pe(o,((t,o)=>{_e(n,o)||e.ui.registry.addIcon(o,t)}))})(e),(e=>{const t=Bd(e);if(m(t)){const n=Zw.get(t);e.theme=n(e,Zw.urls[t])||{},w(e.theme.init)&&e.theme.init(e,Zw.urls[t]||e.documentBaseUrl.replace(/\/$/,""))}else e.theme={}})(e),(e=>{const t=Dd(e),n=Hw.get(t);e.model=n(e,Hw.urls[t])})(e),(e=>{const t=[];q(Qd(e),(n=>{((e,t,n)=>{const o=Xw.get(n),r=Xw.urls[n]||e.documentBaseUrl.replace(/\/$/,"");if(n=Dt.trim(n),o&&-1===Dt.inArray(t,n)){if(e.plugins[n])return;try{const s=o(e,r)||{};e.plugins[n]=s,w(s.init)&&(s.init(e,r),t.push(n))}catch(t){((e,t,n)=>{const o=va.translate(["Failed to initialize plugin: {0}",t]);Pl(e,"PluginLoadError",{message:o}),oE(o,n),eE(e,o)})(e,n,t)}}})(e,t,(e=>e.replace(/^\-/,""))(n))}))})(e);const t=await(e=>{const t=e.getElement();return e.orgDisplay=t.style.display,m(Bd(e))?(e=>{const t=e.theme.renderUI;return t?t():IO(e)})(e):w(Bd(e))?(e=>{const t=e.getElement(),n=Bd(e)(e,t);return n.editorContainer.nodeType&&(n.editorContainer.id=n.editorContainer.id||e.id+"_parent"),n.iframeContainer&&n.iframeContainer.nodeType&&(n.iframeContainer.id=n.iframeContainer.id||e.id+"_iframecontainer"),n.height=n.iframeHeight?n.iframeHeight:t.offsetHeight,n})(e):IO(e)})(e);((e,t)=>{const n={show:I.from(t.show).getOr(_),hide:I.from(t.hide).getOr(_),isEnabled:I.from(t.isEnabled).getOr(M),setEnabled:n=>{n&&("readonly"===e.mode.get()||lE(e))||I.from(t.setEnabled).each((e=>e(n)))}};e.ui={...e.ui,...n}})(e,I.from(t.api).getOr({})),e.editorContainer=t.editorContainer,(e=>{e.contentCSS=e.contentCSS.concat((e=>gE(e,Ed(e)))(e),(e=>gE(e,_d(e)))(e))})(e),e.inline?BO(e):((e,t)=>{((e,t)=>{const n=Tt.browser.isFirefox()?rc(e):"Rich Text Area",o=e.translate(n),r=nn(Cn(e.getElement()),"tabindex").bind(Ze),s=((e,t,n,o)=>{const r=vn("iframe");return o.each((e=>Jt(r,"tabindex",e))),en(r,n),en(r,{id:e+"_ifr",frameBorder:"0",allowTransparency:"true",title:t}),mn(r,"tox-edit-area__iframe"),r})(e.id,o,Kl(e),r).dom;s.onload=()=>{s.onload=null,e.dispatch("load")},e.contentAreaContainer=t.iframeContainer,e.iframeElement=s,e.iframeHTML=(e=>{let t=Yl(e)+"<html><head>";Gl(e)!==e.documentBaseUrl&&(t+='<base href="'+e.documentBaseURI.getURI()+'" />'),t+='<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />';const n=Xl(e),o=Zl(e),r=e.translate(rc(e));return Ql(e)&&(t+='<meta http-equiv="Content-Security-Policy" content="'+Ql(e)+'" />'),t+=`</head><body id="${n}" class="mce-content-body ${o}" data-id="${e.id}" aria-label="${r}"><br></body></html>`,t})(e),DO.add(t.iframeContainer,s)})(e,t),t.editorContainer&&(t.editorContainer.style.display=e.orgDisplay,e.hidden=DO.isHidden(t.editorContainer)),e.getElement().style.display="none",DO.setAttrib(e.id,"aria-hidden","true"),e.getElement().style.visibility=e.orgVisibility,(e=>{const t=e.iframeElement,n=()=>{e.contentDocument=t.contentDocument,BO(e)};if(Oc(e)||Tt.browser.isFirefox()){const t=e.getDoc();t.open(),t.write(e.iframeHTML),t.close(),n()}else{const r=(o=Cn(t),No(o,"load",PO,(()=>{r.unbind(),n()})));t.srcdoc=e.iframeHTML}var o})(e)})(e,{editorContainer:t.editorContainer,iframeContainer:t.iframeContainer})},UO=ma.DOM,zO=e=>"-"===e.charAt(0),jO=(e,t,n)=>I.from(t).filter((e=>Ge(e)&&!jw.has(e))).map((t=>({url:`${e.editorManager.baseURL}/icons/${t}/icons${n}.js`,name:I.some(t)}))),HO=(e,t)=>{const n=ga.ScriptLoader,o=()=>{!e.removed&&(e=>{const t=Bd(e);return!m(t)||C(Zw.get(t))})(e)&&(e=>{const t=Dd(e);return C(Hw.get(t))})(e)&&FO(e)};((e,t)=>{const n=Bd(e);if(m(n)&&!zO(n)&&!_e(Zw.urls,n)){const o=Pd(e),r=o?e.documentBaseURI.toAbsolute(o):`themes/${n}/theme${t}.js`;Zw.load(n,r).catch((()=>{((e,t,n)=>{tE(e,"ThemeLoadError",nE("theme",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=Dd(e);if("plugin"!==n&&!_e(Hw.urls,n)){const o=Ld(e),r=m(o)?e.documentBaseURI.toAbsolute(o):`models/${n}/model${t}.js`;Hw.load(n,r).catch((()=>{((e,t,n)=>{tE(e,"ModelLoadError",nE("model",t,n))})(e,r,n)}))}})(e,t),((e,t)=>{const n=vd(t),o=yd(t);if(!va.hasCode(n)&&"en"!==n){const r=Ge(o)?o:`${t.editorManager.baseURL}/langs/${n}.js`;e.add(r).catch((()=>{((e,t,n)=>{tE(e,"LanguageLoadError",nE("language",t,n))})(t,r,n)}))}})(n,e),((e,t,n)=>{const o=jO(t,"default",n),r=(e=>I.from(ud(e)).filter(Ge).map((e=>({url:e,name:I.none()}))))(t).orThunk((()=>jO(t,cd(t),"")));q((e=>{const t=[],n=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(n);return t})([o,r]),(n=>{e.add(n.url).catch((()=>{((e,t,n)=>{tE(e,"IconsLoadError",nE("icons",t,n))})(t,n.url,n.name.getOrUndefined())}))}))})(n,e,t),((e,t)=>{const n=(t,n)=>{Xw.load(t,n).catch((()=>{((e,t,n)=>{tE(e,"PluginLoadError",nE("plugin",t,n))})(e,n,t)}))};pe(Jd(e),((t,o)=>{n(o,t),e.options.set("plugins",Qd(e).concat(o))})),q(Qd(e),(e=>{!(e=Dt.trim(e))||Xw.urls[e]||zO(e)||n(e,`plugins/${e}/plugin${t}.js`)}))})(e,t),n.loadQueue().then(o,o)},$O=xt().deviceType,VO=$O.isPhone(),qO=$O.isTablet(),WO=e=>{if(y(e))return[];{const t=p(e)?e:e.split(/[ ,]/),n=V(t,We);return Y(n,Ge)}},KO=(e,t)=>{const n=(t=>{const n={},o={};return ye(t,((t,n)=>H(e,n)),ve(n),ve(o)),{t:n,f:o}})(t);return o=n.t,r=n.f,{sections:N(o),options:N(r)};var o,r},YO=(e,t)=>_e(e.sections(),t),GO=(e,t)=>({table_grid:!1,object_resizing:!1,resize:!1,toolbar_mode:xe(e,"toolbar_mode").getOr("scrolling"),toolbar_sticky:!1,...t?{menubar:!1}:{}}),XO=(e,t)=>{var n;const o=null!==(n=t.external_plugins)&&void 0!==n?n:{};return e&&e.external_plugins?Dt.extend({},e.external_plugins,o):o},ZO=(e,t,n,o,r)=>{var s;const a=e?{mobile:GO(null!==(s=r.mobile)&&void 0!==s?s:{},t)}:{},i=KO(["mobile"],Pk(a,r)),l=Dt.extend(n,o,i.options(),((e,t)=>e&&YO(t,"mobile"))(e,i)?((e,t,n={})=>{const o=e.sections(),r=xe(o,t).getOr({});return Dt.extend({},n,r)})(i,"mobile"):{},{external_plugins:XO(o,i.options())});return((e,t,n,o)=>{const r=WO(n.forced_plugins),s=WO(o.plugins),a=((e,t)=>YO(e,t)?e.sections()[t]:{})(t,"mobile"),i=((e,t,n,o)=>e&&YO(t,"mobile")?o:n)(e,t,s,a.plugins?WO(a.plugins):s),l=((e,t)=>[...WO(e),...WO(t)])(r,i);return Dt.extend(o,{forced_plugins:r,plugins:l})})(e,i,o,l)},QO=e=>{(e=>{const t=t=>()=>{q("left,center,right,justify".split(","),(n=>{t!==n&&e.formatter.remove("align"+n)})),"none"!==t&&(t=>{e.formatter.toggle(t,void 0),e.nodeChanged()})("align"+t)};e.editorCommands.addCommands({JustifyLeft:t("left"),JustifyCenter:t("center"),JustifyRight:t("right"),JustifyFull:t("justify"),JustifyNone:t("none")})})(e),(e=>{const t=t=>()=>{const n=e.selection,o=n.isCollapsed()?[e.dom.getParent(n.getNode(),e.dom.isBlock)]:n.getSelectedBlocks();return $(o,(n=>C(e.formatter.matchNode(n,t))))};e.editorCommands.addCommands({JustifyLeft:t("alignleft"),JustifyCenter:t("aligncenter"),JustifyRight:t("alignright"),JustifyFull:t("alignjustify")},"state")})(e)},JO=(e,t)=>{const n=e.selection,o=e.dom;return/^ | $/.test(t)?((e,t,n,o)=>{const r=Cn(e.getRoot());return n=ch(r,el.fromRangeStart(t),o)?n.replace(/^ /,"&nbsp;"):n.replace(/^&nbsp;/," "),uh(r,el.fromRangeEnd(t),o)?n.replace(/(&nbsp;| )(<br( \/)>)?$/,"&nbsp;"):n.replace(/&nbsp;(<br( \/)?>)?$/," ")})(o,n.getRng(),t,e.schema):t},eB=(e,t)=>{if(e.selection.isEditable()){const{content:n,details:o}=(e=>{if("string"!=typeof e){const t=Dt.extend({paste:e.paste,data:{paste:e.paste}},e);return{content:e.content,details:t}}return{content:e,details:{}}})(t);zC(e,{...o,content:JO(e,n),format:"html",set:!1,selection:!0}).each((t=>{const n=((e,t,n)=>hw(e).editor.insertContent(t,n))(e,t.content,o);jC(e,n,t),e.addVisual()}))}},tB={"font-size":"size","font-family":"face"},nB=Zt("font"),oB=e=>(t,n)=>I.from(n).map(Cn).filter(Kt).bind((n=>((e,t,n)=>Vb(Cn(n),(t=>(t=>mo(t,e).orThunk((()=>nB(t)?xe(tB,e).bind((e=>nn(t,e))):I.none())))(t)),(e=>Sn(Cn(t),e))))(e,t,n.dom).or(((e,t)=>I.from(ma.DOM.getStyle(t,e,!0)))(e,n.dom)))).getOr(""),rB=oB("font-size"),sB=S((e=>e.replace(/[\'\"\\]/g,"").replace(/,\s+/g,",")),oB("font-family")),aB=e=>Zu(e.getBody()).bind((e=>{const t=e.container();return I.from(lr(t)?t.parentNode:t)})),iB=(e,t)=>((e,t)=>(e=>I.from(e.selection.getRng()).bind((t=>{const n=e.getBody();return t.startContainer===n&&0===t.startOffset?I.none():I.from(e.selection.getStart(!0))})))(e).orThunk(T(aB,e)).map(Cn).filter(Kt).bind(t))(e,k(I.some,t)),lB=(e,t)=>{if(/^[0-9.]+$/.test(t)){const n=parseInt(t,10);if(n>=1&&n<=7){const o=(e=>Dt.explode(e.options.get("font_size_style_values")))(e),r=(e=>Dt.explode(e.options.get("font_size_classes")))(e);return r.length>0?r[n-1]||t:o[n-1]||t}return t}return t},dB=e=>{const t=e.split(/\s*,\s*/);return V(t,(e=>-1===e.indexOf(" ")||$e(e,'"')||$e(e,"'")?e:`'${e}'`)).join(",")},cB=(e,t)=>{if(e.mode.isReadOnly())return;const n=e.dom,o=e.selection.getRng(),r=t?e.selection.getStart():e.selection.getEnd(),s=t?o.startContainer:o.endContainer,a=sR(n,s);if(!a||!a.isContentEditable)return;const i=t?po:ho,l=ed(e);((e,t,n,o)=>{const r=e.dom,s=e=>r.isBlock(e)&&e.parentElement===n,a=s(t)?t:r.getParent(o,s,n);return I.from(a).map(Cn)})(e,r,a,s).each((t=>{const n=dR(e,s,t.dom,a,!1,l);i(t,Cn(n)),e.selection.setCursorLocation(n,0),e.dispatch("NewBlock",{newBlock:n}),QN(e,"insertParagraph")}))},uB=e=>{QO(e),(e=>{e.editorCommands.addCommands({"Cut,Copy,Paste":t=>{const n=e.getDoc();let o;try{n.execCommand(t)}catch(e){o=!0}if("paste"!==t||n.queryCommandEnabled(t)||(o=!0),o||!n.queryCommandSupported(t)){let t=e.translate("Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.");(Tt.os.isMacOS()||Tt.os.isiOS())&&(t=t.replace(/Ctrl\+/g,"\u2318+")),e.notificationManager.open({text:t,type:"error"})}}})})(e),(e=>{e.editorCommands.addCommands({mceAddUndoLevel:()=>{e.undoManager.add()},mceEndUndoLevel:()=>{e.undoManager.add()},Undo:()=>{e.undoManager.undo()},Redo:()=>{e.undoManager.redo()}})})(e),(e=>{e.editorCommands.addCommands({mceSelectNodeDepth:(t,n,o)=>{let r=0;e.dom.getParent(e.selection.getNode(),(t=>!Jo(t)||r++!==o||(e.selection.select(t),!1)),e.getBody())},mceSelectNode:(t,n,o)=>{e.selection.select(o)},selectAll:()=>{const t=e.dom.getParent(e.selection.getStart(),hr);if(t){const n=e.dom.createRng();n.selectNodeContents(t),e.selection.setRng(n)}}})})(e),(e=>{e.editorCommands.addCommands({mceCleanup:()=>{const t=e.selection.getBookmark();e.setContent(e.getContent()),e.selection.moveToBookmark(t)},insertImage:(t,n,o)=>{eB(e,e.dom.createHTML("img",{src:o}))},insertHorizontalRule:()=>{e.execCommand("mceInsertContent",!1,"<hr>")},insertText:(t,n,o)=>{eB(e,e.dom.encode(o))},insertHTML:(t,n,o)=>{eB(e,o)},mceInsertContent:(t,n,o)=>{eB(e,o)},mceSetContent:(t,n,o)=>{e.setContent(o)},mceReplaceContent:(t,n,o)=>{e.execCommand("mceInsertContent",!1,o.replace(/\{\$selection\}/g,e.selection.getContent({format:"text"})))},mceNewDocument:()=>{e.setContent(pc(e))}})})(e),(e=>{const t=(t,n,o)=>{if(e.mode.isReadOnly())return;const r=m(o)?{href:o}:o,s=e.dom.getParent(e.selection.getNode(),"a");f(r)&&m(r.href)&&(r.href=r.href.replace(/ /g,"%20"),s&&r.href||e.formatter.remove("link"),r.href&&e.formatter.apply("link",r,s))};e.editorCommands.addCommands({unlink:()=>{if(e.selection.isEditable()){if(e.selection.isCollapsed()){const t=e.dom.getParent(e.selection.getStart(),"a");return void(t&&e.dom.remove(t,!0))}e.formatter.remove("link")}},mceInsertLink:t,createLink:t})})(e),(e=>{e.editorCommands.addCommands({Indent:()=>{(e=>{aS(e,"indent")})(e)},Outdent:()=>{iS(e)}}),e.editorCommands.addCommands({Outdent:()=>oS(e)},"state")})(e),(e=>{e.editorCommands.addCommands({InsertNewBlockBefore:()=>{(e=>{cB(e,!0)})(e)},InsertNewBlockAfter:()=>{(e=>{cB(e,!1)})(e)}})})(e),(e=>{e.editorCommands.addCommands({insertParagraph:()=>{HR(CR,e)},mceInsertNewLine:(t,n,o)=>{$R(e,o)},InsertLineBreak:(t,n,o)=>{HR(RR,e)}})})(e),(e=>{(e=>{const t=(t,n)=>{e.formatter.toggle(t,n),e.nodeChanged()};e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>{t(e)},"ForeColor,HiliteColor":(e,n,o)=>{t(e,{value:o})},BackColor:(e,n,o)=>{t("hilitecolor",{value:o})},FontName:(t,n,o)=>{((e,t)=>{const n=lB(e,t);e.formatter.toggle("fontname",{value:dB(n)}),e.nodeChanged()})(e,o)},FontSize:(t,n,o)=>{((e,t)=>{e.formatter.toggle("fontsize",{value:lB(e,t)}),e.nodeChanged()})(e,o)},LineHeight:(t,n,o)=>{((e,t)=>{e.formatter.toggle("lineheight",{value:String(t)}),e.nodeChanged()})(e,o)},Lang:(e,n,o)=>{var r;t(e,{value:o.code,customValue:null!==(r=o.customCode)&&void 0!==r?r:null})},RemoveFormat:t=>{e.formatter.remove(t)},mceBlockQuote:()=>{t("blockquote")},FormatBlock:(e,n,o)=>{t(m(o)?o:"p")},mceToggleFormat:(e,n,o)=>{t(o)}})})(e),(e=>{const t=t=>e.formatter.match(t);e.editorCommands.addCommands({"Bold,Italic,Underline,Strikethrough,Superscript,Subscript":e=>t(e),mceBlockQuote:()=>t("blockquote")},"state"),e.editorCommands.addQueryValueHandler("FontName",(()=>(e=>iB(e,(t=>sB(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("FontSize",(()=>(e=>iB(e,(t=>rB(e.getBody(),t.dom))).getOr(""))(e))),e.editorCommands.addQueryValueHandler("LineHeight",(()=>(e=>iB(e,(t=>{const n=Cn(e.getBody()),o=Vb(t,(e=>mo(e,"line-height")),T(Sn,n));return o.getOrThunk((()=>{const e=parseFloat(co(t,"line-height")),n=parseFloat(co(t,"font-size"));return String(e/n)}))})).getOr(""))(e)))})(e)})(e),(e=>{e.editorCommands.addCommands({mceRemoveNode:(t,n,o)=>{const r=null!=o?o:e.selection.getNode();if(r!==e.getBody()){const t=e.selection.getBookmark();e.dom.remove(r,!0),e.selection.moveToBookmark(t)}},mcePrint:()=>{e.getWin().print()},mceFocus:(t,n,o)=>{((e,t)=>{e.removed||(t?Zf(e):(e=>{const t=e.selection,n=e.getBody();let o=t.getRng();e.quirks.refreshContentEditable();const r=e=>{If(e).each((t=>{e.selection.setRng(t),o=t}))};!Gf(e)&&e.hasEditableRoot()&&r(e);const s=((e,t)=>e.dom.getParent(t,(t=>"true"===e.dom.getContentEditable(t))))(e,t.getNode());if(s&&e.dom.isChildOf(s,n))return((e,t)=>null!==e.dom.getParent(t,(t=>"false"===e.dom.getContentEditable(t))))(e,s)||Yf(n),Yf(s),e.hasEditableRoot()||r(e),Kf(e,o),void Zf(e);e.inline||(Tt.browser.isOpera()||Yf(n),e.getWin().focus()),(Tt.browser.isFirefox()||e.inline)&&(Yf(n),Kf(e,o)),Zf(e)})(e))})(e,!0===o)},mceToggleVisualAid:()=>{e.hasVisual=!e.hasVisual,e.addVisual()}})})(e)},mB=["toggleview"],fB=e=>H(mB,e.toLowerCase());class gB{constructor(e){this.commands={state:{},exec:{},value:{}},this.editor=e}execCommand(e,t=!1,n,o){const r=this.editor,s=e.toLowerCase(),a=null==o?void 0:o.skip_focus;if(r.removed)return!1;if("mcefocus"!==s&&(/^(mceAddUndoLevel|mceEndUndoLevel)$/i.test(s)||a?(e=>{If(e).each((t=>e.selection.setRng(t)))})(r):r.focus()),r.dispatch("BeforeExecCommand",{command:e,ui:t,value:n}).isDefaultPrevented())return!1;const i=this.commands.exec[s];return!!w(i)&&(i(s,t,n),r.dispatch("ExecCommand",{command:e,ui:t,value:n}),!0)}queryCommandState(e){if(!fB(e)&&this.editor.quirks.isHidden()||this.editor.removed)return!1;const t=e.toLowerCase(),n=this.commands.state[t];return!!w(n)&&n(t)}queryCommandValue(e){if(!fB(e)&&this.editor.quirks.isHidden()||this.editor.removed)return"";const t=e.toLowerCase(),n=this.commands.value[t];return w(n)?n(t):""}addCommands(e,t="exec"){const n=this.commands;pe(e,((e,o)=>{q(o.toLowerCase().split(","),(o=>{n[t][o]=e}))}))}addCommand(e,t,n){const o=e.toLowerCase();this.commands.exec[o]=(e,o,r)=>t.call(null!=n?n:this.editor,o,r)}queryCommandSupported(e){const t=e.toLowerCase();return!!this.commands.exec[t]}addQueryStateHandler(e,t,n){this.commands.state[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}addQueryValueHandler(e,t,n){this.commands.value[e.toLowerCase()]=()=>t.call(null!=n?n:this.editor)}}const pB=Dt.makeMap("focus blur focusin focusout click dblclick mousedown mouseup mousemove mouseover beforepaste paste cut copy selectionchange mouseout mouseenter mouseleave wheel keydown keypress keyup input beforeinput contextmenu dragstart dragend dragover draggesture dragdrop drop drag submit compositionstart compositionend compositionupdate touchstart touchmove touchend touchcancel"," ");class hB{static isNative(e){return!!pB[e.toLowerCase()]}constructor(e){this.bindings={},this.settings=e||{},this.scope=this.settings.scope||this,this.toggleEvent=this.settings.toggleEvent||L}fire(e,t){return this.dispatch(e,t)}dispatch(e,t){const n=e.toLowerCase(),o=Xs(n,null!=t?t:{},this.scope);this.settings.beforeFire&&this.settings.beforeFire(o);const r=this.bindings[n];if(r)for(let e=0,t=r.length;e<t;e++){const t=r[e];if(!t.removed){if(t.once&&this.off(n,t.func),o.isImmediatePropagationStopped())return o;if(!1===t.func.call(this.scope,o))return o.preventDefault(),o}}return o}on(e,t,n,o){if(!1===t&&(t=L),t){const r={func:t,removed:!1};o&&Dt.extend(r,o);const s=e.toLowerCase().split(" ");let a=s.length;for(;a--;){const e=s[a];let t=this.bindings[e];t||(t=[],this.toggleEvent(e,!0)),t=n?[r,...t]:[...t,r],this.bindings[e]=t}}return this}off(e,t){if(e){const n=e.toLowerCase().split(" ");let o=n.length;for(;o--;){const r=n[o];let s=this.bindings[r];if(!r)return pe(this.bindings,((e,t)=>{this.toggleEvent(t,!1),delete this.bindings[t]})),this;if(s){if(t){const e=K(s,(e=>e.func===t));s=e.fail,this.bindings[r]=s,q(e.pass,(e=>{e.removed=!0}))}else s.length=0;s.length||(this.toggleEvent(e,!1),delete this.bindings[r])}}}else pe(this.bindings,((e,t)=>{this.toggleEvent(t,!1)})),this.bindings={};return this}once(e,t,n){return this.on(e,t,n,{once:!0})}has(e){e=e.toLowerCase();const t=this.bindings[e];return!(!t||0===t.length)}}const bB=e=>(e._eventDispatcher||(e._eventDispatcher=new hB({scope:e,toggleEvent:(t,n)=>{hB.isNative(t)&&e.toggleNativeEvent&&e.toggleNativeEvent(t,n)}})),e._eventDispatcher),vB={fire(e,t,n){return this.dispatch(e,t,n)},dispatch(e,t,n){const o=this;if(o.removed&&"remove"!==e&&"detach"!==e)return Xs(e.toLowerCase(),null!=t?t:{},o);const r=bB(o).dispatch(e,t);if(!1!==n&&o.parent){let t=o.parent();for(;t&&!r.isPropagationStopped();)t.dispatch(e,r,!1),t=t.parent?t.parent():void 0}return r},on(e,t,n){return bB(this).on(e,t,n)},off(e,t){return bB(this).off(e,t)},once(e,t){return bB(this).once(e,t)},hasEventListeners(e){return bB(this).has(e)}},yB=ma.DOM;let CB;const wB=(e,t)=>{if("selectionchange"===t)return e.getDoc();if(!e.inline&&/^(?:mouse|touch|click|contextmenu|drop|dragover|dragend)/.test(t))return e.getDoc().documentElement;const n=Td(e);return n?(e.eventRoot||(e.eventRoot=yB.select(n)[0]),e.eventRoot):e.getBody()},EB=(e,t,n)=>{(e=>!e.hidden&&!lE(e))(e)?e.dispatch(t,n):lE(e)&&((e,t)=>{if((e=>"click"===e.type)(t)&&!Qf.metaKeyPressed(t)){const n=Cn(t.target);((e,t)=>to(t,"a",(t=>Sn(t,Cn(e.getBody())))).bind((e=>nn(e,"href"))))(e,n).each((n=>{if(t.preventDefault(),/^#/.test(n)){const t=e.dom.select(`${n},[name="${je(n,"#")}"]`);t.length&&e.selection.scrollIntoView(t[0],!0)}else window.open(n,"_blank","rel=noopener noreferrer,menubar=yes,toolbar=yes,location=yes,status=yes,resizable=yes,scrollbars=yes")}))}else(e=>H(mE,e.type))(t)&&e.dispatch(t.type,t)})(e,n)},xB=(e,t)=>{if(e.delegates||(e.delegates={}),e.delegates[t]||e.removed)return;const n=wB(e,t);if(Td(e)){if(CB||(CB={},e.editorManager.on("removeEditor",(()=>{e.editorManager.activeEditor||CB&&(pe(CB,((t,n)=>{e.dom.unbind(wB(e,n))})),CB=null)}))),CB[t])return;const o=n=>{const o=n.target,r=e.editorManager.get();let s=r.length;for(;s--;){const e=r[s].getBody();(e===o||yB.isChildOf(o,e))&&EB(r[s],t,n)}};CB[t]=o,yB.bind(n,t,o)}else{const o=n=>{EB(e,t,n)};yB.bind(n,t,o),e.delegates[t]=o}},_B={...vB,bindPendingEventDelegates(){const e=this;Dt.each(e._pendingNativeEvents,(t=>{xB(e,t)}))},toggleNativeEvent(e,t){const n=this;"focus"!==e&&"blur"!==e&&(n.removed||(t?n.initialized?xB(n,e):n._pendingNativeEvents?n._pendingNativeEvents.push(e):n._pendingNativeEvents=[e]:n.initialized&&n.delegates&&(n.dom.unbind(wB(n,e),e,n.delegates[e]),delete n.delegates[e])))},unbindAllNativeEvents(){const e=this,t=e.getBody(),n=e.dom;e.delegates&&(pe(e.delegates,((t,n)=>{e.dom.unbind(wB(e,n),n,t)})),delete e.delegates),!e.inline&&t&&n&&(t.onload=null,n.unbind(e.getWin()),n.unbind(e.getDoc())),n&&(n.unbind(t),n.unbind(e.getContainer()))}},SB=e=>m(e)?{value:e.split(/[ ,]/),valid:!0}:x(e,m)?{value:e,valid:!0}:{valid:!1,message:"The value must be a string[] or a comma/space separated string."},kB=(e,t)=>e+(Xe(t.message)?"":`. ${t.message}`),NB=e=>e.valid,RB=(e,t,n="")=>{const o=t(e);return b(o)?o?{value:e,valid:!0}:{valid:!1,message:n}:o},AB=e=>e.readonly,TB=["design","readonly"],OB=(e,t,n,o)=>{const r=n[t.get()],s=n[o];try{s.activate()}catch(e){return void console.error(`problem while activating editor mode ${o}:`,e)}r.deactivate(),r.editorReadOnly!==s.editorReadOnly&&((e,t)=>{const n=Cn(e.getBody());t?(e.readonly=!0,e.hasEditableRoot()&&(n.dom.contentEditable="true"),aE(e)):(e.readonly=!1,iE(e))})(e,s.editorReadOnly),t.set(o),((e,t)=>{e.dispatch("SwitchMode",{mode:t})})(e,o)},BB=e=>{const t=Br("design"),n=Br({design:{activate:_,deactivate:_,editorReadOnly:!1},readonly:{activate:_,deactivate:_,editorReadOnly:!0}});return(e=>{e.on("beforeinput paste cut dragend dragover draggesture dragdrop drop drag",(t=>{AB(e)&&t.preventDefault()})),e.on("BeforeExecCommand",(t=>{"Undo"!==t.command&&"Redo"!==t.command||!AB(e)||t.preventDefault()})),e.on("input",(t=>{if(!t.isComposing&&AB(e)){const t=e.undoManager.add();C(t)&&e.undoManager.undo()}})),e.on("compositionend",(()=>{if(AB(e)){const t=e.undoManager.add();C(t)&&e.undoManager.undo()}}))})(e),(e=>{(e=>{e.serializer?uE(e):e.on("PreInit",(()=>{uE(e)}))})(e),(e=>{e.on("ShowCaret ObjectSelected",(t=>{lE(e)&&t.preventDefault()})),e.on("DisabledStateChange",(t=>{t.isDefaultPrevented()||cE(e,t.state)}))})(e)})(e),{isReadOnly:()=>AB(e),set:o=>((e,t,n,o)=>{if(!(o===n.get()||e.initialized&&lE(e))){if(!_e(t,o))throw new Error(`Editor mode '${o}' is invalid`);e.initialized?OB(e,n,t,o):e.on("init",(()=>OB(e,n,t,o)))}})(e,n.get(),t,o),get:()=>t.get(),register:(e,t)=>{n.set(((e,t,n)=>{if(H(TB,t))throw new Error(`Cannot override default mode ${t}`);return{...e,[t]:{...n,deactivate:()=>{try{n.deactivate()}catch(e){console.error(`problem while deactivating editor mode ${t}:`,e)}}}}})(n.get(),e,t))}}},PB=Dt.each,DB=Dt.explode,LB={f1:112,f2:113,f3:114,f4:115,f5:116,f6:117,f7:118,f8:119,f9:120,f10:121,f11:122,f12:123},MB=Dt.makeMap("alt,ctrl,shift,meta,access"),IB=e=>{const t={},n=Tt.os.isMacOS()||Tt.os.isiOS();PB(DB(e.toLowerCase(),"+"),(e=>{(e=>e in MB)(e)?t[e]=!0:/^[0-9]{2,}$/.test(e)?t.keyCode=parseInt(e,10):(t.charCode=e.charCodeAt(0),t.keyCode=LB[e]||e.toUpperCase().charCodeAt(0))}));const o=[t.keyCode];let r;for(r in MB)t[r]?o.push(r):t[r]=!1;return t.id=o.join(","),t.access&&(t.alt=!0,n?t.ctrl=!0:t.shift=!0),t.meta&&(n?t.meta=!0:(t.ctrl=!0,t.meta=!1)),t};class FB{constructor(e){this.shortcuts={},this.pendingPatterns=[],this.editor=e;const t=this;e.on("keyup keypress keydown",(e=>{!t.hasModifier(e)&&!t.isFunctionKey(e)||e.isDefaultPrevented()||(PB(t.shortcuts,(n=>{t.matchShortcut(e,n)&&(t.pendingPatterns=n.subpatterns.slice(0),"keydown"===e.type&&t.executeShortcutAction(n))})),t.matchShortcut(e,t.pendingPatterns[0])&&(1===t.pendingPatterns.length&&"keydown"===e.type&&t.executeShortcutAction(t.pendingPatterns[0]),t.pendingPatterns.shift()))}))}add(e,t,n,o){const r=this,s=r.normalizeCommandFunc(n);return PB(DB(Dt.trim(e)),(e=>{const n=r.createShortcut(e,t,s,o);r.shortcuts[n.id]=n})),!0}remove(e){const t=this.createShortcut(e);return!!this.shortcuts[t.id]&&(delete this.shortcuts[t.id],!0)}normalizeCommandFunc(e){const t=this,n=e;return"string"==typeof n?()=>{t.editor.execCommand(n,!1,null)}:Dt.isArray(n)?()=>{t.editor.execCommand(n[0],n[1],n[2])}:n}createShortcut(e,t,n,o){const r=Dt.map(DB(e,">"),IB);return r[r.length-1]=Dt.extend(r[r.length-1],{func:n,scope:o||this.editor}),Dt.extend(r[0],{desc:this.editor.translate(t),subpatterns:r.slice(1)})}hasModifier(e){return e.altKey||e.ctrlKey||e.metaKey}isFunctionKey(e){return"keydown"===e.type&&e.keyCode>=112&&e.keyCode<=123}matchShortcut(e,t){return!!t&&t.ctrl===e.ctrlKey&&t.meta===e.metaKey&&t.alt===e.altKey&&t.shift===e.shiftKey&&!!(e.keyCode===t.keyCode||e.charCode&&e.charCode===t.charCode)&&(e.preventDefault(),!0)}executeShortcutAction(e){return e.func?e.func.call(e.scope):null}}const UB=()=>{const e=(()=>{const e={},t={},n={},o={},r={},s={},a={},i={},l={},d=(e,t)=>(n,o)=>{e[n.toLowerCase()]={...o,type:t}};return{addButton:d(e,"button"),addGroupToolbarButton:d(e,"grouptoolbarbutton"),addToggleButton:d(e,"togglebutton"),addMenuButton:d(e,"menubutton"),addSplitButton:d(e,"splitbutton"),addMenuItem:d(t,"menuitem"),addNestedMenuItem:d(t,"nestedmenuitem"),addToggleMenuItem:d(t,"togglemenuitem"),addAutocompleter:d(n,"autocompleter"),addContextMenu:d(r,"contextmenu"),addContextToolbar:d(s,"contexttoolbar"),addContextForm:(c=s,(e,t)=>{c[e.toLowerCase()]={type:"contextform",...t}}),addSidebar:d(i,"sidebar"),addView:d(l,"views"),addIcon:(e,t)=>o[e.toLowerCase()]=t,addContext:(e,t)=>a[e.toLowerCase()]=t,getAll:()=>({buttons:e,menuItems:t,icons:o,popups:n,contextMenus:r,contextToolbars:s,sidebars:i,views:l,contexts:a})};var c})();return{addAutocompleter:e.addAutocompleter,addButton:e.addButton,addContextForm:e.addContextForm,addContextMenu:e.addContextMenu,addContextToolbar:e.addContextToolbar,addIcon:e.addIcon,addMenuButton:e.addMenuButton,addMenuItem:e.addMenuItem,addNestedMenuItem:e.addNestedMenuItem,addSidebar:e.addSidebar,addSplitButton:e.addSplitButton,addToggleButton:e.addToggleButton,addGroupToolbarButton:e.addGroupToolbarButton,addToggleMenuItem:e.addToggleMenuItem,addView:e.addView,addContext:e.addContext,getAll:e.getAll}},zB=ma.DOM,jB=Dt.extend,HB=Dt.each;class $B{constructor(e,t,n){this.plugins={},this.contentCSS=[],this.contentStyles=[],this.loadedCSS={},this.isNotDirty=!1,this.composing=!1,this.destroyed=!1,this.hasHiddenInput=!1,this.iframeElement=null,this.initialized=!1,this.readonly=!1,this.removed=!1,this.startContent="",this._pendingNativeEvents=[],this._skinLoaded=!1,this._editableRoot=!0,this.editorManager=n,this.documentBaseUrl=n.documentBaseURL,jB(this,_B);const o=this;this.id=e,this.hidden=!1;const r=((e,t)=>{const n=Dk(t);return ZO(VO||qO,VO,n,e,n)})(n.defaultOptions,t);this.options=((e,t,n=t)=>{const o={},r={},s=(e,t,n)=>{const o=RB(t,n);return NB(o)?(r[e]=o.value,!0):(console.warn(kB(`Invalid value passed for the ${e} option`,o)),!1)},a=e=>_e(o,e);return{register:(e,n)=>{const a=(e=>m(e.processor))(n)?(e=>{const t=(()=>{switch(e){case"array":return p;case"boolean":return b;case"function":return w;case"number":return E;case"object":return f;case"string":return m;case"string[]":return SB;case"object[]":return e=>x(e,f);case"regexp":return e=>u(e,RegExp);default:return M}})();return n=>RB(n,t,`The value must be a ${e}.`)})(n.processor):n.processor,i=((e,t,n)=>{if(!v(t)){const o=RB(t,n);if(NB(o))return o.value;console.error(kB(`Invalid default value passed for the "${e}" option`,o))}})(e,n.default,a);o[e]={...n,default:i,processor:a},xe(r,e).orThunk((()=>xe(t,e))).each((t=>s(e,t,a)))},isRegistered:a,get:e=>xe(r,e).orThunk((()=>xe(o,e).map((e=>e.default)))).getOrUndefined(),set:(e,t)=>{if(a(e)){const n=o[e];return n.immutable?(console.error(`"${e}" is an immutable option and cannot be updated`),!1):s(e,t,n.processor)}return console.warn(`"${e}" is not a registered option. Ensure the option has been registered before setting a value.`),!1},unset:e=>{const t=a(e);return t&&delete r[e],t},isSet:e=>_e(r,e),debug:()=>{try{console.log(JSON.parse(JSON.stringify(n,((e,t)=>b(t)||E(t)||m(t)||h(t)||p(t)||g(t)?t:Object.prototype.toString.call(t)))))}catch(e){console.error(e)}}}})(0,r,t),(e=>{const t=e.options.register;t("id",{processor:"string",default:e.id}),t("selector",{processor:"string"}),t("target",{processor:"object"}),t("suffix",{processor:"string"}),t("cache_suffix",{processor:"string"}),t("base_url",{processor:"string"}),t("referrer_policy",{processor:"string",default:""}),t("language_load",{processor:"boolean",default:!0}),t("inline",{processor:"boolean",default:!1}),t("iframe_attrs",{processor:"object",default:{}}),t("doctype",{processor:"string",default:"<!DOCTYPE html>"}),t("document_base_url",{processor:"string",default:e.documentBaseUrl}),t("body_id",{processor:Wl(e,"tinymce"),default:"tinymce"}),t("body_class",{processor:Wl(e),default:""}),t("content_security_policy",{processor:"string",default:""}),t("br_in_pre",{processor:"boolean",default:!0}),t("forced_root_block",{processor:e=>{const t=m(e)&&Ge(e);return t?{value:e,valid:t}:{valid:!1,message:"Must be a non-empty string."}},default:"p"}),t("forced_root_block_attrs",{processor:"object",default:{}}),t("newline_behavior",{processor:e=>{const t=H(["block","linebreak","invert","default"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: block, linebreak, invert or default."}},default:"default"}),t("br_newline_selector",{processor:"string",default:".mce-toc h2,figcaption,caption"}),t("no_newline_selector",{processor:"string",default:""}),t("keep_styles",{processor:"boolean",default:!0}),t("end_container_on_empty_block",{processor:e=>b(e)||m(e)?{valid:!0,value:e}:{valid:!1,message:"Must be boolean or a string"},default:"blockquote"}),t("font_size_style_values",{processor:"string",default:"xx-small,x-small,small,medium,large,x-large,xx-large"}),t("font_size_legacy_values",{processor:"string",default:"xx-small,small,medium,large,x-large,xx-large,300%"}),t("font_size_classes",{processor:"string",default:""}),t("automatic_uploads",{processor:"boolean",default:!0}),t("images_reuse_filename",{processor:"boolean",default:!1}),t("images_replace_blob_uris",{processor:"boolean",default:!0}),t("icons",{processor:"string",default:""}),t("icons_url",{processor:"string",default:""}),t("images_upload_url",{processor:"string",default:""}),t("images_upload_base_path",{processor:"string",default:""}),t("images_upload_credentials",{processor:"boolean",default:!1}),t("images_upload_handler",{processor:"function"}),t("language",{processor:"string",default:"en"}),t("language_url",{processor:"string",default:""}),t("entity_encoding",{processor:"string",default:"named"}),t("indent",{processor:"boolean",default:!0}),t("indent_before",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_after",{processor:"string",default:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,th,ul,ol,li,dl,dt,dd,area,table,thead,tfoot,tbody,tr,section,details,summary,article,hgroup,aside,figure,figcaption,option,optgroup,datalist"}),t("indent_use_margin",{processor:"boolean",default:!1}),t("indentation",{processor:"string",default:"40px"}),t("content_css",{processor:e=>{const t=!1===e||m(e)||x(e,m);return t?m(e)?{value:V(e.split(","),We),valid:t}:p(e)?{value:e,valid:t}:!1===e?{value:[],valid:t}:{value:e,valid:t}:{valid:!1,message:"Must be false, a string or an array of strings."}},default:Hd(e)?[]:["default"]}),t("content_style",{processor:"string"}),t("content_css_cors",{processor:"boolean",default:!1}),t("font_css",{processor:e=>{const t=m(e)||x(e,m);return t?{value:p(e)?e:V(e.split(","),We),valid:t}:{valid:!1,message:"Must be a string or an array of strings."}},default:[]}),t("extended_mathml_attributes",{processor:"string[]"}),t("extended_mathml_elements",{processor:"string[]"}),t("inline_boundaries",{processor:"boolean",default:!0}),t("inline_boundaries_selector",{processor:"string",default:"a[href],code,span.mce-annotation"}),t("object_resizing",{processor:e=>{const t=b(e)||m(e);return t?!1===e||zl.isiPhone()||zl.isiPad()?{value:"",valid:t}:{value:!0===e?"table,img,figure.image,div,video,iframe":e,valid:t}:{valid:!1,message:"Must be boolean or a string"}},default:!jl}),t("resize_img_proportional",{processor:"boolean",default:!0}),t("event_root",{processor:"string"}),t("service_message",{processor:"string"}),t("onboarding",{processor:"boolean",default:!0}),t("tiny_cloud_entry_url",{processor:"string"}),t("theme",{processor:e=>!1===e||m(e)||w(e),default:"silver"}),t("theme_url",{processor:"string"}),t("formats",{processor:"object"}),t("format_empty_lines",{processor:"boolean",default:!1}),t("format_noneditable_selector",{processor:"string",default:""}),t("preview_styles",{processor:e=>{const t=!1===e||m(e);return t?{value:!1===e?"":e,valid:t}:{valid:!1,message:"Must be false or a string"}},default:"font-family font-size font-weight font-style text-decoration text-transform color background-color border border-radius outline text-shadow"}),t("custom_ui_selector",{processor:"string",default:""}),t("hidden_input",{processor:"boolean",default:!0}),t("submit_patch",{processor:"boolean",default:!0}),t("encoding",{processor:"string"}),t("add_form_submit_trigger",{processor:"boolean",default:!0}),t("add_unload_trigger",{processor:"boolean",default:!0}),t("custom_undo_redo_levels",{processor:"number",default:0}),t("disable_nodechange",{processor:"boolean",default:!1}),t("disabled",{processor:t=>b(t)?(e.initialized&&jc(e)!==t&&Promise.resolve().then((()=>{((e,t)=>{e.dispatch("DisabledStateChange",{state:t})})(e,t)})),{valid:!0,value:t}):{valid:!1,message:"The value must be a boolean."},default:!1}),t("readonly",{processor:"boolean",default:!1}),t("editable_root",{processor:"boolean",default:!0}),t("plugins",{processor:"string[]",default:[]}),t("external_plugins",{processor:"object"}),t("forced_plugins",{processor:"string[]"}),t("model",{processor:"string",default:e.hasPlugin("rtc")?"plugin":"dom"}),t("model_url",{processor:"string"}),t("block_unsupported_drop",{processor:"boolean",default:!0}),t("visual",{processor:"boolean",default:!0}),t("visual_table_class",{processor:"string",default:"mce-item-table"}),t("visual_anchor_class",{processor:"string",default:"mce-item-anchor"}),t("iframe_aria_text",{processor:"string",default:"Rich Text Area".concat(e.hasPlugin("help")?". Press ALT-0 for help.":"")}),t("setup",{processor:"function"}),t("init_instance_callback",{processor:"function"}),t("url_converter",{processor:"function",default:e.convertURL}),t("url_converter_scope",{processor:"object",default:e}),t("urlconverter_callback",{processor:"function"}),t("allow_conditional_comments",{processor:"boolean",default:!1}),t("allow_html_data_urls",{processor:"boolean",default:!1}),t("allow_svg_data_urls",{processor:"boolean"}),t("allow_html_in_named_anchor",{processor:"boolean",default:!1}),t("allow_script_urls",{processor:"boolean",default:!1}),t("allow_unsafe_link_target",{processor:"boolean",default:!1}),t("allow_mathml_annotation_encodings",{processor:e=>{const t=x(e,m);return t?{value:e,valid:t}:{valid:!1,message:"Must be an array of strings."}},default:[]}),t("convert_fonts_to_spans",{processor:"boolean",default:!0,deprecated:!0}),t("fix_list_elements",{processor:"boolean",default:!1}),t("preserve_cdata",{processor:"boolean",default:!1}),t("remove_trailing_brs",{processor:"boolean",default:!0}),t("pad_empty_with_br",{processor:"boolean",default:!1}),t("inline_styles",{processor:"boolean",default:!0,deprecated:!0}),t("element_format",{processor:"string",default:"html"}),t("entities",{processor:"string"}),t("schema",{processor:"string",default:"html5"}),t("convert_urls",{processor:"boolean",default:!0}),t("relative_urls",{processor:"boolean",default:!0}),t("remove_script_host",{processor:"boolean",default:!0}),t("custom_elements",{processor:ql}),t("extended_valid_elements",{processor:"string"}),t("invalid_elements",{processor:"string"}),t("invalid_styles",{processor:ql}),t("valid_children",{processor:"string"}),t("valid_classes",{processor:ql}),t("valid_elements",{processor:"string"}),t("valid_styles",{processor:ql}),t("verify_html",{processor:"boolean",default:!0}),t("auto_focus",{processor:e=>m(e)||!0===e}),t("browser_spellcheck",{processor:"boolean",default:!1}),t("protect",{processor:"array"}),t("images_file_types",{processor:"string",default:"jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp"}),t("deprecation_warnings",{processor:"boolean",default:!0}),t("a11y_advanced_options",{processor:"boolean",default:!1}),t("api_key",{processor:"string"}),t("license_key",{processor:"string"}),t("paste_block_drop",{processor:"boolean",default:!1}),t("paste_data_images",{processor:"boolean",default:!0}),t("paste_preprocess",{processor:"function"}),t("paste_postprocess",{processor:"function"}),t("paste_webkit_styles",{processor:"string",default:"none"}),t("paste_remove_styles_if_webkit",{processor:"boolean",default:!0}),t("paste_merge_formats",{processor:"boolean",default:!0}),t("smart_paste",{processor:"boolean",default:!0}),t("paste_as_text",{processor:"boolean",default:!1}),t("paste_tab_spaces",{processor:"number",default:4}),t("text_patterns",{processor:e=>x(e,f)||!1===e?{value:Bl(!1===e?[]:e),valid:!0}:{valid:!1,message:"Must be an array of objects or false."},default:[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"#",format:"h1",trigger:"space"},{start:"##",format:"h2",trigger:"space"},{start:"###",format:"h3",trigger:"space"},{start:"####",format:"h4",trigger:"space"},{start:"#####",format:"h5",trigger:"space"},{start:"######",format:"h6",trigger:"space"},{start:"1.",cmd:"InsertOrderedList",trigger:"space"},{start:"*",cmd:"InsertUnorderedList",trigger:"space"},{start:"-",cmd:"InsertUnorderedList",trigger:"space"},{start:">",cmd:"mceBlockQuote",trigger:"space"},{start:"---",cmd:"InsertHorizontalRule",trigger:"space"}]}),t("text_patterns_lookup",{processor:e=>{return w(e)?{value:(t=e,e=>{const n=t(e);return Bl(n)}),valid:!0}:{valid:!1,message:"Must be a single function"};var t},default:e=>[]}),t("noneditable_class",{processor:"string",default:"mceNonEditable"}),t("editable_class",{processor:"string",default:"mceEditable"}),t("noneditable_regexp",{processor:e=>x(e,$l)?{value:e,valid:!0}:$l(e)?{value:[e],valid:!0}:{valid:!1,message:"Must be a RegExp or an array of RegExp."},default:[]}),t("table_tab_navigation",{processor:"boolean",default:!0}),t("highlight_on_focus",{processor:"boolean",default:!0}),t("xss_sanitization",{processor:"boolean",default:!0}),t("details_initial_state",{processor:e=>{const t=H(["inherited","collapsed","expanded"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),t("details_serialized_state",{processor:e=>{const t=H(["inherited","collapsed","expanded"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: inherited, collapsed, or expanded."}},default:"inherited"}),t("init_content_sync",{processor:"boolean",default:!1}),t("newdocument_content",{processor:"string",default:""}),t("sandbox_iframes",{processor:"boolean",default:!0}),t("sandbox_iframes_exclusions",{processor:"string[]",default:["youtube.com","youtu.be","vimeo.com","player.vimeo.com","dailymotion.com","embed.music.apple.com","open.spotify.com","giphy.com","dai.ly","codepen.io"]}),t("convert_unsafe_embeds",{processor:"boolean",default:!0}),e.on("ScriptsLoaded",(()=>{t("directionality",{processor:"string",default:va.isRtl()?"rtl":void 0}),t("placeholder",{processor:"string",default:Hl.getAttrib(e.getElement(),"placeholder")})}))})(o);const s=this.options.get;s("deprecation_warnings")&&((e,t)=>{((e,t)=>{const n=Pw(e),o=Mw(t),r=o.length>0,s=n.length>0,a="mobile"===t.theme;if(r||s||a){const e="\n- ",t=a?`\n\nThemes:${e}mobile`:"",i=r?`\n\nPlugins:${e}${o.join(e)}`:"",l=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled and have been removed in TinyMCE 7.0. These features will no longer work and should be removed from the TinyMCE configuration. See https://www.tiny.cloud/docs/tinymce/7/migration-from-6x/ for more information."+t+i+l)}})(e,t),((e,t)=>{const n=Dw(e),o=Iw(t),r=o.length>0,s=n.length>0;if(r||s){const e="\n- ",t=r?`\n\nPlugins:${e}${o.map(Fw).join(e)}`:"",a=s?`\n\nOptions:${e}${n.join(e)}`:"";console.warn("The following deprecated features are currently enabled but will be removed soon."+t+a)}})(e,t)})(t,r);const a=s("suffix");a&&(n.suffix=a),this.suffix=n.suffix;const i=s("base_url");i&&n._setBaseUrl(i),this.baseUri=n.baseURI;const l=bd(o);l&&(ga.ScriptLoader._setReferrerPolicy(l),ma.DOM.styleSheetLoader._setReferrerPolicy(l));const d=Zd(o);C(d)&&ma.DOM.styleSheetLoader._setContentCssCors(d),ya.languageLoad=s("language_load"),ya.baseURL=n.baseURL,this.setDirty(!1),this.documentBaseURI=new CC(Gl(o),{base_uri:this.baseUri}),this.baseURI=this.baseUri,this.inline=Hd(o),this.hasVisual=tc(o),this.shortcuts=new FB(this),this.editorCommands=new gB(this),uB(this);const c=s("cache_suffix");c&&(Tt.cacheSuffix=c.replace(/^[\?\&]+/,"")),this.ui={registry:UB(),styleSheetLoader:void 0,show:_,hide:_,setEnabled:_,isEnabled:M},this.mode=BB(o),n.dispatch("SetupEditor",{editor:this});const y=sc(o);w(y)&&y.call(o,o)}render(){(e=>{const t=e.id;va.setCode(vd(e));const n=()=>{UO.unbind(window,"ready",n),e.render()};if(!na.Event.domLoaded)return void UO.bind(window,"ready",n);if(!e.getElement())return;const o=Cn(e.getElement()),r=sn(o);e.on("remove",(()=>{W(o.dom.attributes,(e=>rn(o,e.name))),en(o,r)})),e.ui.styleSheetLoader=((e,t)=>rs.forElement(e,{contentCssCors:Zd(t),referrerPolicy:bd(t)}))(o,e),Hd(e)?e.inline=!0:(e.orgVisibility=e.getElement().style.visibility,e.getElement().style.visibility="hidden");const s=e.getElement().form||UO.getParent(t,"form");s&&(e.formElement=s,$d(e)&&!ir(e.getElement())&&(UO.insertAfter(UO.create("input",{type:"hidden",name:t}),t),e.hasHiddenInput=!0),e.formEventDelegate=t=>{e.dispatch(t.type,t)},UO.bind(s,"submit reset",e.formEventDelegate),e.on("reset",(()=>{e.resetContent()})),!Vd(e)||s.submit.nodeType||s.submit.length||s._mceOldSubmit||(s._mceOldSubmit=s.submit,s.submit=()=>(e.editorManager.triggerSave(),e.setDirty(!1),s._mceOldSubmit(s)))),e.windowManager=Qw(e),e.notificationManager=Gw(e),(e=>"xml"===e.options.get("encoding"))(e)&&e.on("GetContent",(e=>{e.save&&(e.content=UO.encode(e.content))})),qd(e)&&e.on("submit",(()=>{e.initialized&&e.save()})),Wd(e)&&(e._beforeUnload=()=>{!e.initialized||e.destroyed||e.isHidden()||e.save({format:"raw",no_events:!0,set_dirty:!1})},e.editorManager.on("BeforeUnload",e._beforeUnload)),e.editorManager.add(e),HO(e,e.suffix)})(this)}focus(e){this.execCommand("mceFocus",!1,e)}hasFocus(){return Gf(this)}translate(e){return va.translate(e)}getParam(e,t,n){const o=this.options;return o.isRegistered(e)||(C(n)?o.register(e,{processor:n,default:t}):o.register(e,{processor:M,default:t})),o.isSet(e)||v(t)?o.get(e):t}hasPlugin(e,t){return!(!H(Qd(this),e)||t&&void 0===Xw.get(e))}nodeChanged(e){this._nodeChangeDispatcher.nodeChanged(e)}addCommand(e,t,n){this.editorCommands.addCommand(e,t,n)}addQueryStateHandler(e,t,n){this.editorCommands.addQueryStateHandler(e,t,n)}addQueryValueHandler(e,t,n){this.editorCommands.addQueryValueHandler(e,t,n)}addShortcut(e,t,n,o){this.shortcuts.add(e,t,n,o)}execCommand(e,t,n,o){return this.editorCommands.execCommand(e,t,n,o)}queryCommandState(e){return this.editorCommands.queryCommandState(e)}queryCommandValue(e){return this.editorCommands.queryCommandValue(e)}queryCommandSupported(e){return this.editorCommands.queryCommandSupported(e)}show(){const e=this;e.hidden&&(e.hidden=!1,e.inline?e.getBody().contentEditable="true":(zB.show(e.getContainer()),zB.hide(e.id)),e.load(),e.dispatch("show"))}hide(){const e=this;e.hidden||(e.save(),e.inline?(e.getBody().contentEditable="false",e===e.editorManager.focusedEditor&&(e.editorManager.focusedEditor=null)):(zB.hide(e.getContainer()),zB.setStyle(e.id,"display",e.orgDisplay)),e.hidden=!0,e.dispatch("hide"))}isHidden(){return this.hidden}setProgressState(e,t){this.dispatch("ProgressState",{state:e,time:t})}load(e={}){const t=this,n=t.getElement();if(t.removed)return"";if(n){const o={...e,load:!0},r=ir(n)?n.value:n.innerHTML,s=t.setContent(r,o);return o.no_events||t.dispatch("LoadContent",{...o,element:n}),s}return""}save(e={}){const t=this;let n=t.getElement();if(!n||!t.initialized||t.removed)return"";const o={...e,save:!0,element:n};let r=t.getContent(o);const s={...o,content:r};if(s.no_events||t.dispatch("SaveContent",s),"raw"===s.format&&t.dispatch("RawSaveContent",s),r=s.content,ir(n))n.value=r;else{!e.is_removing&&t.inline||(n.innerHTML=r);const o=zB.getParent(t.id,"form");o&&HB(o.elements,(e=>e.name!==t.id||(e.value=r,!1)))}return s.element=o.element=n=null,!1!==s.set_dirty&&t.setDirty(!1),r}setContent(e,t){return Nw(this,e,t)}getContent(e){return((e,t={})=>{const n=((e,t)=>({...e,format:t,get:!0,getInner:!0}))(t,t.format?t.format:"html");return FC(e,n).fold(R,(t=>{const n=((e,t)=>hw(e).editor.getContent(t))(e,t);return UC(e,n,t)}))})(this,e)}insertContent(e,t){t&&(e=jB({content:e},t)),this.execCommand("mceInsertContent",!1,e)}resetContent(e){void 0===e?Nw(this,this.startContent,{format:"raw"}):Nw(this,e),this.undoManager.reset(),this.setDirty(!1),this.nodeChanged()}isDirty(){return!this.isNotDirty}setDirty(e){const t=!this.isNotDirty;this.isNotDirty=!e,e&&e!==t&&this.dispatch("dirty")}getContainer(){const e=this;return e.container||(e.container=e.editorContainer||zB.get(e.id+"_parent")),e.container}getContentAreaContainer(){return this.contentAreaContainer}getElement(){return this.targetElm||(this.targetElm=zB.get(this.id)),this.targetElm}getWin(){const e=this;if(!e.contentWindow){const t=e.iframeElement;t&&(e.contentWindow=t.contentWindow)}return e.contentWindow}getDoc(){const e=this;if(!e.contentDocument){const t=e.getWin();t&&(e.contentDocument=t.document)}return e.contentDocument}getBody(){var e,t;const n=this.getDoc();return null!==(t=null!==(e=this.bodyElement)&&void 0!==e?e:null==n?void 0:n.body)&&void 0!==t?t:null}convertURL(e,t,n){const o=this,r=o.options.get,s=ic(o);if(w(s))return s.call(o,e,n,!0,t);if(!r("convert_urls")||"link"===n||f(n)&&"LINK"===n.nodeName||0===e.indexOf("file:")||0===e.length)return e;const a=new CC(e);return"http"!==a.protocol&&"https"!==a.protocol&&""!==a.protocol?e:r("relative_urls")?o.documentBaseURI.toRelative(e):e=o.documentBaseURI.toAbsolute(e,r("remove_script_host"))}addVisual(e){((e,t)=>{((e,t)=>{bw(e).editor.addVisual(t)})(e,t)})(this,e)}setEditableRoot(e){((e,t)=>{e._editableRoot!==t&&(e._editableRoot=t,lE(e)||(e.getBody().contentEditable=String(e.hasEditableRoot()),e.nodeChanged()),((e,t)=>{e.dispatch("EditableRootStateChange",{state:t})})(e,t))})(this,e)}hasEditableRoot(){return this._editableRoot}remove(){(e=>{if(!e.removed){const{_selectionOverrides:t,editorUpload:n}=e,o=e.getBody(),r=e.getElement();o&&e.save({is_removing:!0}),e.removed=!0,e.unbindAllNativeEvents(),e.hasHiddenInput&&C(null==r?void 0:r.nextSibling)&&Uw.remove(r.nextSibling),(e=>{e.dispatch("remove")})(e),e.editorManager.remove(e),!e.inline&&o&&(e=>{Uw.setStyle(e.id,"display",e.orgDisplay)})(e),(e=>{e.dispatch("detach")})(e),Uw.remove(e.getContainer()),zw(t),zw(n),e.destroy()}})(this)}destroy(e){((e,t)=>{const{selection:n,dom:o}=e;e.destroyed||(t||e.removed?(t||(e.editorManager.off("beforeunload",e._beforeUnload),e.theme&&e.theme.destroy&&e.theme.destroy(),zw(n),zw(o)),(e=>{const t=e.formElement;t&&(t._mceOldSubmit&&(t.submit=t._mceOldSubmit,delete t._mceOldSubmit),Uw.unbind(t,"submit reset",e.formEventDelegate))})(e),(e=>{const t=e;t.contentAreaContainer=t.formElement=t.container=t.editorContainer=null,t.bodyElement=t.contentDocument=t.contentWindow=null,t.iframeElement=t.targetElm=null;const n=e.selection;if(n){const e=n.dom;t.selection=n.win=n.dom=e.doc=null}})(e),e.destroyed=!0):e.remove())})(this,e)}uploadImages(){return this.editorUpload.uploadImages()}_scanForImages(){return this.editorUpload.scanForImages()}}const VB=ma.DOM,qB=Dt.each;let WB,KB=!1,YB=[];const GB=e=>{const t=e.type;qB(JB.get(),(n=>{switch(t){case"scroll":n.dispatch("ScrollWindow",e);break;case"resize":n.dispatch("ResizeWindow",e)}}))},XB=e=>{if(e!==KB){const t=ma.DOM;e?(t.bind(window,"resize",GB),t.bind(window,"scroll",GB)):(t.unbind(window,"resize",GB),t.unbind(window,"scroll",GB)),KB=e}},ZB=e=>{const t=YB;return YB=Y(YB,(t=>e!==t)),JB.activeEditor===e&&(JB.activeEditor=YB.length>0?YB[0]:null),JB.focusedEditor===e&&(JB.focusedEditor=null),t.length!==YB.length},QB="CSS1Compat"!==document.compatMode,JB={...vB,baseURI:null,baseURL:null,defaultOptions:{},documentBaseURL:null,suffix:null,majorVersion:"7",minorVersion:"8.0",releaseDate:"TBD",i18n:va,activeEditor:null,focusedEditor:null,setup(){const e=this;let t="",n="",o=CC.getDocumentBaseUrl(document.location);/^[^:]+:\/\/\/?[^\/]+\//.test(o)&&(o=o.replace(/[\?#].*$/,"").replace(/[\/\\][^\/]+$/,""),/[\/\\]$/.test(o)||(o+="/"));const r=window.tinymce||window.tinyMCEPreInit;if(r)t=r.base||r.baseURL,n=r.suffix;else{const e=document.getElementsByTagName("script");for(let o=0;o<e.length;o++){const r=e[o].src||"";if(""===r)continue;const s=r.substring(r.lastIndexOf("/"));if(/tinymce(\.full|\.jquery|)(\.min|\.dev|)\.js/.test(r)){-1!==s.indexOf(".min")&&(n=".min"),t=r.substring(0,r.lastIndexOf("/"));break}}if(!t&&document.currentScript){const e=document.currentScript.src;-1!==e.indexOf(".min")&&(n=".min"),t=e.substring(0,e.lastIndexOf("/"))}}var s;e.baseURL=new CC(o).toAbsolute(t),e.documentBaseURL=o,e.baseURI=new CC(e.baseURL),e.suffix=n,(s=e).on("AddEditor",T(qf,s)),s.on("RemoveEditor",T(Wf,s))},overrideDefaults(e){const t=e.base_url;t&&this._setBaseUrl(t);const n=e.suffix;n&&(this.suffix=n),this.defaultOptions=e;const o=e.plugin_base_urls;void 0!==o&&pe(o,((e,t)=>{ya.PluginManager.urls[t]=e}))},init(e){const t=this;let n;const o=Dt.makeMap("area base basefont br col frame hr img input isindex link meta param embed source wbr track colgroup option table tbody tfoot thead tr th td script noscript style textarea video audio iframe object menu"," ");let r=e=>{n=e};const s=()=>{let n=0;const a=[];let i;VB.unbind(window,"ready",s),(()=>{const n=e.onpageload;n&&n.apply(t,[])})(),i=me((e=>Tt.browser.isIE()||Tt.browser.isEdge()?(oE("TinyMCE does not support the browser you are using. For a list of supported browsers please see: https://www.tiny.cloud/docs/tinymce/7/support/#supportedwebbrowsers"),[]):QB?(oE("Failed to initialize the editor as the document is not in standards mode. TinyMCE requires standards mode."),[]):m(e.selector)?VB.select(e.selector):C(e.target)?[e.target]:[])(e)),Dt.each(i,(e=>{var n;(n=t.get(e.id))&&n.initialized&&!(n.getContainer()||n.getBody()).parentNode&&(ZB(n),n.unbindAllNativeEvents(),n.destroy(!0),n.removed=!0)})),i=Dt.grep(i,(e=>!t.get(e.id))),0===i.length?r([]):qB(i,(s=>{((e,t)=>e.inline&&t.tagName.toLowerCase()in o)(e,s)?oE("Could not initialize inline editor on invalid inline target element",s):((e,o,s)=>{const l=new $B(e,o,t);a.push(l),l.on("init",(()=>{++n===i.length&&r(a)})),l.targetElm=l.targetElm||s,l.render()})((e=>{let t=e.id;return t||(t=xe(e,"name").filter((e=>!VB.get(e))).getOrThunk(VB.uniqueId),e.setAttribute("id",t)),t})(s),e,s)}))};return VB.bind(window,"ready",s),new Promise((e=>{n?e(n):r=t=>{e(t)}}))},get(e){return 0===arguments.length?YB.slice(0):m(e)?Q(YB,(t=>t.id===e)).getOr(null):E(e)&&YB[e]?YB[e]:null},add(e){const t=this,n=t.get(e.id);return n===e||(null===n&&YB.push(e),XB(!0),t.activeEditor=e,t.dispatch("AddEditor",{editor:e}),WB||(WB=e=>{const n=t.dispatch("BeforeUnload");if(n.returnValue)return e.preventDefault(),e.returnValue=n.returnValue,n.returnValue},window.addEventListener("beforeunload",WB))),e},createEditor(e,t){return this.add(new $B(e,t,this))},remove(e){const t=this;let n;if(e){if(!m(e))return n=e,h(t.get(n.id))?null:(ZB(n)&&t.dispatch("RemoveEditor",{editor:n}),0===YB.length&&window.removeEventListener("beforeunload",WB),n.remove(),XB(YB.length>0),n);qB(VB.select(e),(e=>{n=t.get(e.id),n&&t.remove(n)}))}else for(let e=YB.length-1;e>=0;e--)t.remove(YB[e])},execCommand(e,t,n){var o;const r=this,s=f(n)?null!==(o=n.id)&&void 0!==o?o:n.index:n;switch(e){case"mceAddEditor":if(!r.get(s)){const e=n.options;new $B(s,e,r).render()}return!0;case"mceRemoveEditor":{const e=r.get(s);return e&&e.remove(),!0}case"mceToggleEditor":{const e=r.get(s);return e?(e.isHidden()?e.show():e.hide(),!0):(r.execCommand("mceAddEditor",!1,n),!0)}}return!!r.activeEditor&&r.activeEditor.execCommand(e,t,n)},triggerSave:()=>{qB(YB,(e=>{e.save()}))},addI18n:(e,t)=>{va.add(e,t)},translate:e=>va.translate(e),setActive(e){const t=this.activeEditor;this.activeEditor!==e&&(t&&t.dispatch("deactivate",{relatedTarget:e}),e.dispatch("activate",{relatedTarget:t})),this.activeEditor=e},_setBaseUrl(e){this.baseURL=new CC(this.documentBaseURL).toAbsolute(e.replace(/\/+$/,"")),this.baseURI=new CC(this.baseURL)}};JB.setup();const eP=(()=>{const e=Dr();return{FakeClipboardItem:e=>({items:e,types:fe(e),getType:t=>xe(e,t).getOrUndefined()}),write:t=>{e.set(t)},read:()=>e.get().getOrUndefined(),clear:e.clear}})(),tP=Math.min,nP=Math.max,oP=Math.round,rP=(e,t,n)=>{let o=t.x,r=t.y;const s=e.w,a=e.h,i=t.w,l=t.h,d=(n||"").split("");return"b"===d[0]&&(r+=l),"r"===d[1]&&(o+=i),"c"===d[0]&&(r+=oP(l/2)),"c"===d[1]&&(o+=oP(i/2)),"b"===d[3]&&(r-=a),"r"===d[4]&&(o-=s),"c"===d[3]&&(r-=oP(a/2)),"c"===d[4]&&(o-=oP(s/2)),sP(o,r,s,a)},sP=(e,t,n,o)=>({x:e,y:t,w:n,h:o}),aP={inflate:(e,t,n)=>sP(e.x-t,e.y-n,e.w+2*t,e.h+2*n),relativePosition:rP,findBestRelativePosition:(e,t,n,o)=>{for(let r=0;r<o.length;r++){const s=rP(e,t,o[r]);if(s.x>=n.x&&s.x+s.w<=n.w+n.x&&s.y>=n.y&&s.y+s.h<=n.h+n.y)return o[r]}return null},intersect:(e,t)=>{const n=nP(e.x,t.x),o=nP(e.y,t.y),r=tP(e.x+e.w,t.x+t.w),s=tP(e.y+e.h,t.y+t.h);return r-n<0||s-o<0?null:sP(n,o,r-n,s-o)},clamp:(e,t,n)=>{let o=e.x,r=e.y,s=e.x+e.w,a=e.y+e.h;const i=t.x+t.w,l=t.y+t.h,d=nP(0,t.x-o),c=nP(0,t.y-r),u=nP(0,s-i),m=nP(0,a-l);return o+=d,r+=c,n&&(s+=d,a+=c,o-=u,r-=m),s-=u,a-=m,sP(o,r,s-o,a-r)},create:sP,fromClientRect:e=>sP(e.left,e.top,e.width,e.height)},iP=(()=>{const e={},t={},n={};return{load:(n,o)=>{const r=`Script at URL "${o}" failed to load`,s=`Script at URL "${o}" did not call \`tinymce.Resource.add('${n}', data)\` within 1 second`;if(void 0!==e[n])return e[n];{const a=new Promise(((e,a)=>{const i=((e,t,n=1e3)=>{let o=!1,r=null;const s=e=>(...t)=>{o||(o=!0,null!==r&&(window.clearTimeout(r),r=null),e.apply(null,t))},a=s(e),i=s(t);return{start:(...e)=>{o||null!==r||(r=window.setTimeout((()=>i.apply(null,e)),n))},resolve:a,reject:i}})(e,a);t[n]=i.resolve,ga.ScriptLoader.loadScript(o).then((()=>i.start(s)),(()=>i.reject(r)))}));return e[n]=a,a}},add:(o,r)=>{void 0!==t[o]&&(t[o](r),delete t[o]),e[o]=Promise.resolve(r),n[o]=r},has:e=>e in n,get:e=>n[e],unload:t=>{delete e[t],delete n[t]}}})();let lP;try{const e="__storage_test__";lP=window.localStorage,lP.setItem(e,e),lP.removeItem(e)}catch(e){lP=(()=>{let e={},t=[];const n={getItem:t=>e[t]||null,setItem:(n,o)=>{t.push(n),e[n]=String(o)},key:e=>t[e],removeItem:n=>{t=t.filter((e=>e===n)),delete e[n]},clear:()=>{t=[],e={}},length:0};return Object.defineProperty(n,"length",{get:()=>t.length,configurable:!1,enumerable:!1}),n})()}const dP={geom:{Rect:aP},util:{Delay:Uf,Tools:Dt,VK:Qf,URI:CC,EventDispatcher:hB,Observable:vB,I18n:va,LocalStorage:lP,ImageUploader:e=>{const t=hE(),n=CE(e,t);return{upload:(t,o=!0)=>n.upload(t,o?yE(e):void 0)}}},dom:{EventUtils:na,TreeWalker:$o,TextSeeker:za,DOMUtils:ma,ScriptLoader:ga,RangeUtils:Sg,Serializer:kw,StyleSheetLoader:os,ControlSelection:og,BookmarkManager:yf,Selection:xw,Event:na.Event},html:{Styles:Ks,Entities:ws,Node:tp,Schema:Fs,DomParser:LC,Writer:yp,Serializer:Cp},Env:Tt,AddOnManager:ya,Annotator:vf,Formatter:BE,UndoManager:DE,EditorCommands:gB,WindowManager:Qw,NotificationManager:Gw,EditorObservable:_B,Shortcuts:FB,Editor:$B,FocusManager:Ff,EditorManager:JB,DOM:ma.DOM,ScriptLoader:ga.ScriptLoader,PluginManager:Xw,ThemeManager:Zw,ModelManager:Hw,IconManager:jw,Resource:iP,FakeClipboard:eP,trim:Dt.trim,isArray:Dt.isArray,is:Dt.is,toArray:Dt.toArray,makeMap:Dt.makeMap,each:Dt.each,map:Dt.map,grep:Dt.grep,inArray:Dt.inArray,extend:Dt.extend,walk:Dt.walk,resolve:Dt.resolve,explode:Dt.explode,_addCacheSuffix:Dt._addCacheSuffix},cP=Dt.extend(JB,dP);(e=>{window.tinymce=e,window.tinyMCE=e})(cP),(e=>{if("object"==typeof module)try{module.exports=e}catch(e){}})(cP)}();