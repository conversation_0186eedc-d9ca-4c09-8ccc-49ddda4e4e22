<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Client extends Model
{
    //
    protected $table = 'clients';
    protected $fillable = [
        'name',
        'job_code',
        'user_id',

    ];

    public function brands()
    {
        return $this->belongsToMany(Brand::class, 'client_brand', 'client_id', 'brand_id');
    }

    public function socialDetails()
    {
        return $this->belongsToMany(SocialDetail::class, 'social_details_pivot');
    }
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_client', 'client_id', 'user_id');
    }

    public function linkedUsers()
    {
        return $this->belongsToMany(User::class, 'client_user_links', 'client_id', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function projects()
    {
        return $this->hasMany(Project::class);
    }
}
