<?php

use App\Models\AccessLevel;
use Illuminate\Support\Facades\Storage;


function set_user_image(?string $path, string $default = 'images/default-user.jpg'): string
{
    if (!$path) {
        return asset($default);
    }
    // Check if the image exists in public path
    if (file_exists(public_path($path))) {
        return asset($path);
    }
    // Check if the image exists in storage
    $publicImagePath = public_path('images/uploads/' . $path);
    if (file_exists($publicImagePath)) {
        return asset('images/uploads/' . $path);
    }
    // Optional: Check if it exists in storage/public/images/uploads
    if (Storage::disk('public')->exists('images/uploads/' . $path)) {
        return Storage::url('images/uploads/' . $path);
    }
    if (Storage::disk('public')->exists('images/' . $path)) {
        return Storage::url('images/' . $path);
    }
    return asset($default);
}



function admin_superadmin_permissions()
{
    $admin_access_level = \App\Models\AccessLevel::where('name', '=', 'Admin')->first();
    $admin_access_level_id = $admin_access_level ? $admin_access_level->id : null;

    return auth()->check() && (
        auth()->user()->hasRole(['SuperAdmin', 'Admin']) ||
        (isset(auth()->user()->access_level_id) && auth()->user()->access_level_id === $admin_access_level_id)
    );
}
