@extends('layout.app')
@section('title', 'loginorg')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">
                    <h4>
                        @if(request()->route()->named('profile'))
                            Profile Information
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('profile'))
                        <div class="">
                            <div class="m-3">
                                <label class="label" for="name">Name:</label>
                                <span>{{ $user->name }}</span>
                            </div>
                            <div class="m-3">
                                <label class="label">Email:</label>
                                <span>{{ $user->email }}</span>
                            </div>
                        </div>
                        <div class="">
                            <div class="m-3">
                                <label class="label">Role:</label>
                                <span>{{ $user->role }}</span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }

        .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;
            overflow:scroll;

            left: 0;
            z-index: 0;
            height: calc(100vh - 50px);
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            padding:0px 50px;
        }
        .label{
            width:10%;
        }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){
            $('.section-div').click(function(){
                if($('.navbar-menu-button').hasClass('closed')){
                    $('.navbar-menu-button').css({"margin":"auto"})
                    $('.navbar-menu-button').removeClass('closed').addClass('opened');
                    $('.navbar-menu-icon').removeClass('fa-chevron-circle-right').addClass('fa-chevron-circle-left');
                    $('.section-div').css({"width":"15%","overflow":"scroll"});
                    $('.content-div').css({"width":"85%"});
                    $('.sidebar-link').css({"width":"100%","padding":"0 5px"});
                    $('.sidebar-link-text').css({"display":"block"});
                    $('.sidebar-link-icon').css({"text-align":"left","width":"auto"});
                }
                else{
                    $('.navbar-menu-button').removeClass('opened').addClass('closed');
                    $('.navbar-menu-icon').removeClass('fa-chevron-circle-left').addClass('fa-chevron-circle-right');
                    $('.section-div').css({"width":"5%","overflow":"none"});
                    $('.content-div').css({"width":"95%"});
                    $('.sidebar-link-text').css({"display":"none"});
                    $('.sidebar-link-icon').css({"text-align":"center","width":"100%"});
                }
            });
        });
    </script>
@endpush