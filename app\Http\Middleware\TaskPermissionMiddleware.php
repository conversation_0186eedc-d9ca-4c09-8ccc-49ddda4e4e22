<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\User;
use App\Models\Company;
use App\Models\Permission;
use App\Models\Project;
use App\Models\Role;
use App\Models\Task;
use App\Models\AccessLevel;
use Illuminate\Support\Facades\Auth;


class TaskPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission)
    {
        // $task = Task::withWhereHas('projects.companies.user', function($query) use ($request){
        //     $query
        //       ->where('id', '=', Auth::user()->id)
        //       ->orWhere('role_id', '=', '1');
        // })->get();
        // return response($task);


    	// $role = Role::with("permissions")->where('id', '=', Auth::user()->role_id)->first();
    	// if ($role->permissions->isNotEmpty()) {
    	// 	if($role->permissions->contains('name', $permission)){
    	// 		// return response($role->permissions);
    	// 		return $next($request);
    	// 	}
		// }
    	// abort(403, 'OPERATION/ACCESS NOT ALLOWED');

        $user = Auth::user();

        // Ensure user and their role relationship exist
        if (!$user || !$user->role) {
            abort(403, 'Unauthorized. User or Role not found.');
        }

        

        $fetch_permissions_according_to_role_name = $user->role->name;

        if ($user->role->name === 'Team Member') { //if the role is Team Member, check if access level is Admin or Team Member then assign permissions
            // Fetch the access_level id for the 'Admin' name
            $admin_access_level_record = AccessLevel::where('name', 'Admin')->first();
            $admin_access_level_id = $admin_access_level_record ? $admin_access_level_record->id : null;

            // Fetch the access_level id for the 'Team Member' name
            $team_member_access_level_record = AccessLevel::where('name', 'Team Member')->first();
            $team_member_access_level_id = $team_member_access_level_record ? $team_member_access_level_record->id : null;

            //if the team member has admin access level, fetch corresponding permissions according to admin role
            if ($admin_access_level_id !== null && $user->access_level_id == $admin_access_level_id) {
                $fetch_permissions_according_to_role_name = 'Admin';
            } 
            //if the team member has admin access level, fetch corresponding permissions according to team member role
            elseif ($team_member_access_level_id !== null && $user->access_level_id == $team_member_access_level_id) {
                $fetch_permissions_according_to_role_name = 'Team Member';
            }
            // If access_level_id doesn't match the fetched IDs, or if the
            // 'Admin' or 'Team Member' AccessLevel records aren't found,
            // it will default to 'Team Member' (the initial value of $fetch_permissions_according_to_role_name).
        }

        // Now, fetch the permissions for the determined role
        if ($fetch_permissions_according_to_role_name) {
            $role = Role::with('permissions')->where('name', $fetch_permissions_according_to_role_name)->first();

            if ($role && $role->permissions->isNotEmpty()) {
                if ($role->permissions->contains('name', $permission)) {
                    return $next($request); // Permission granted, proceed
                }
            }
        }

        // If no permission is granted after all checks
        abort(403, 'Unauthorized.');
    }
}
