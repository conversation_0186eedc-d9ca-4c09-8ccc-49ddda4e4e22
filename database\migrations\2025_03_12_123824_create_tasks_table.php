<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('project_id')->nullable()->references('id')->on('projects');
            $table->foreignId('status_id')->nullable()->references('id')->on('status');
            $table->string('due_date')->nullable();
            $table->foreignId('admin_id')->nullable()->references('id')->on('users');
            $table->string('attachemenet')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tasks', function (Blueprint $table) {
            $table->dropForeign(['project_id']);  // Dropping the foreign key constraint
            $table->dropForeign(['status_id']);  // Dropping the foreign key constraint
        });
        Schema::dropIfExists('tasks');
    }
};
