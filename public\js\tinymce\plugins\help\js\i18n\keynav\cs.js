tinymce.Resource.add('tinymce.html-i18n.help-keynav.cs',
'<h1>Začínáme navigovat pomocí klávesnice</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>P<PERSON>ej<PERSON>t na řádek nabídek</dt>\n' +
  '  <dd>Windows nebo Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Přejít na panel nástrojů</dt>\n' +
  '  <dd>Windows nebo Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Přejít na zápatí</dt>\n' +
  '  <dd>Windows nebo Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Přejít na oznámení</dt>\n' +
  '  <dd>Windows nebo Linux: Alt+F12</dd>\n' +
  '  <dd>macOS: &#x2325;F12</dd>\n' +
  '  <dt>Přejít na kontextový panel nástrojů</dt>\n' +
  '  <dd>Windows, Linux nebo macOS: Ctrl+F9</dd>\n' +
  '</dl>\n' +
  '\n' +
  '<p>Navigace začne u první položky uživatelského rozhraní, která bude zvýrazněna nebo v případě první položky\n' +
  '  cesty k prvku zápatí podtržena.</p>\n' +
  '\n' +
  '<h1>Navigace mezi oddíly uživatelského rozhraní</h1>\n' +
  '\n' +
  '<p>Stisknutím klávesy <strong>Tab</strong> se posunete z jednoho oddílu uživatelského rozhraní na další.</p>\n' +
  '\n' +
  '<p>Stisknutím kláves <strong>Shift+Tab</strong> se posunete z jednoho oddílu uživatelského rozhraní na předchozí.</p>\n' +
  '\n' +
  '<p>Pořadí přepínání mezi oddíly uživatelského rozhraní pomocí klávesy <strong>Tab</strong>:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Řádek nabídek</li>\n' +
  '  <li>Každá skupina panelu nástrojů</li>\n' +
  '  <li>Boční panel</li>\n' +
  '  <li>Cesta k prvku v zápatí.</li>\n' +
  '  <li>Tlačítko přepínače počtu slov v zápatí</li>\n' +
  '  <li>Odkaz na informace o značce v zápatí</li>\n' +
  '  <li>Úchyt pro změnu velikosti editoru v zápatí</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Pokud nějaký oddíl uživatelského rozhraní není přítomen, je přeskočen.</p>\n' +
  '\n' +
  '<p>Pokud je zápatí vybrané pro navigaci pomocí klávesnice a není zobrazen žádný boční panel, stisknutím kláves <strong>Shift+Tab</strong>\n' +
  '  přejdete na první skupinu panelu nástrojů, nikoli na poslední.</p>\n' +
  '\n' +
  '<h1>Navigace v rámci oddílů uživatelského rozhraní</h1>\n' +
  '\n' +
  '<p>Chcete-li se přesunout z jednoho prvku uživatelského rozhraní na další, stiskněte příslušnou klávesu s <strong>šipkou</strong>.</p>\n' +
  '\n' +
  '<p>Klávesy s šipkou <strong>vlevo</strong> a <strong>vpravo</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>umožňují přesun mezi nabídkami na řádku nabídek;</li>\n' +
  '  <li>otevírají podnabídku nabídky;</li>\n' +
  '  <li>umožňují přesun mezi tlačítky ve skupině panelu nástrojů;</li>\n' +
  '  <li>umožňují přesun mezi položkami cesty prvku v zápatí.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Klávesy se šipkou <strong>dolů</strong> a <strong>nahoru</strong></p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>umožňují přesun mezi položkami nabídky;</li>\n' +
  '  <li>umožňují přesun mezi položkami místní nabídky panelu nástrojů.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Šipky</strong> provádí přepínání v rámci vybraného oddílu uživatelského rozhraní.</p>\n' +
  '\n' +
  '<p>Chcete-li zavřít otevřenou nabídku, podnabídku nebo místní nabídku, stiskněte klávesu <strong>Esc</strong>.</p>\n' +
  '\n' +
  '<p>Pokud je aktuálně vybrána horní část oddílu uživatelského rozhraní, stisknutím klávesy <strong>Esc</strong> zcela ukončíte také\n' +
  '  navigaci pomocí klávesnice.</p>\n' +
  '\n' +
  '<h1>Provedení příkazu položky nabídky nebo tlačítka panelu nástrojů</h1>\n' +
  '\n' +
  '<p>Pokud je zvýrazněna požadovaná položka nabídky nebo tlačítko panelu nástrojů, stisknutím klávesy <strong>Return</strong>, <strong>Enter</strong>\n' +
  '  nebo <strong>mezerníku</strong> provedete příslušný příkaz.</p>\n' +
  '\n' +
  '<h1>Navigace v dialogových oknech bez záložek</h1>\n' +
  '\n' +
  '<p>Při otevření dialogových oken bez záložek přejdete na první interaktivní komponentu.</p>\n' +
  '\n' +
  '<p>Přecházet mezi interaktivními komponentami dialogového okna můžete stisknutím klávesy <strong>Tab</strong> nebo kombinace <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<h1>Navigace v dialogových oknech se záložkami</h1>\n' +
  '\n' +
  '<p>Při otevření dialogových oken se záložkami přejdete na první tlačítko v nabídce záložek.</p>\n' +
  '\n' +
  '<p>Přecházet mezi interaktivními komponentami této záložky dialogového okna můžete stisknutím klávesy <strong>Tab</strong> nebo\n' +
  '  kombinace <strong>Shift+Tab</strong>.</p>\n' +
  '\n' +
  '<p>Chcete-li přepnout na další záložku dialogového okna, přejděte na nabídku záložek a poté můžete stisknutím požadované <strong>šipky</strong>\n' +
  '  přepínat mezi dostupnými záložkami.</p>\n');