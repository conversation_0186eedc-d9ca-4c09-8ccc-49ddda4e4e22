<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add the column
            $table->foreignId('access_level_id')
                  ->nullable() // Allows users to initially not have an access level
                  ->after('role_id') // You can adjust where it's placed, e.g., after 'email'
                  ->constrained('access_level') // This creates the foreign key constraint
                  ->onDelete('set null'); // When an access level is deleted, set user's access_level_id to null
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['access_level_id']);
            // Then drop the column
            $table->dropColumn('access_level_id');
        });
    }
};