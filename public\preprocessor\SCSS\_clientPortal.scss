@import 'colorMixin';

.client-header {
	.back-page,
	.meta {
		margin-block: 2rem;

		a {
			color: #fff;
			font: 500 16px/1 $inter;
		}

		.cta {
			font-size: 14px;
		}
	}

	h1 {
		font: 700 28px $futura;

		i {
			font-family: $play;
		}

		&.heavy {
			font-size: 32px;
		}
	}

	.page-links {
		a {
			color: rgba($white, 0.5);
			font: 500 16px $inter;
			text-decoration: none;
			transition: color 0.2s ease;

			&:hover {
				color: $white;
			}

			&.active {
				color: $orange;
				text-decoration: underline;
			}

			+ a {
				margin-left: 1.5rem;
			}
		}
	}

	.cta {
		&:hover {
			.img {
				svg {
					path {
						stroke: #fff;
					}
				}
			}
		}
	}

	&.main-header {
		h1 {
			font-size: 32px;
		}
	}
}

.client-project {
	padding-block: 4rem 2rem;

	.go-back {
		a {
			color: #111;
			font: 500 16px/1 $inter;
		}
	}

	.heading {
		h2 {
			font-size: 28px;
		}

		.links {
			a {
				color: rgba($black, 0.5);
				cursor: pointer;
				font: 500 16px $inter;
				text-decoration: none;

				&:hover {
					color: rgba($black, 1);
				}

				&.active {
					color: $orange;
					text-decoration: underline;
				}

				+ a {
					margin-left: 1.25rem;
				}
			}
		}
	}

	.status {
		padding-top: 2rem;

		h2 {
			color: $orange;
			font-size: 16px;
		}

		h3 {
			font-size: 28px;
		}
	}
}

.project-timeline {
	overflow: hidden;
	padding-block: 5rem;

	.head {
		h2 {
			font-size: 46px;
		}
	}

	.card-row {
		display: grid;
		grid-column-gap: 16px;
		grid-row-gap: 0px;
		grid-template-columns: repeat(5, 1fr);
		grid-template-rows: 1fr;

		.card-timeline {
			.flag {
				border-radius: 10px;
				overflow: hidden;
				position: relative;
				z-index: 1;

				.head {
					padding: 1rem;

					h2 {
						font-size: 80px;
					}

					h3 {
						font-size: 20px;
					}
				}

				.text {
					background: #fff;
					padding: 1.25rem 1rem 1.5rem;

					p {
						line-height: normal;
						margin: 0;
					}

					.h3 {
						font-size: 18px;
					}
				}
			}

			.icon-wrap {
				isolation: isolate;
				padding-bottom: 20px;
				position: relative;
				width: 52px;

				.line {
					height: 100%;
					left: 50%;
					position: absolute;
					transform: translateX(-50%);
					width: 1px;
					z-index: -1;
				}

				.icon {
					background: #fff;
					border-radius: 50%;
					height: 52px;
					width: 52px;
				}
			}

			@each $name, $color in $colors {
				&.#{ $name } {
					@include project-card($color);
				}
			}

			&:first-child {
				.icon-wrap {
					height: 100px;
				}
			}

			&:nth-child(2) {
				.icon-wrap {
					height: 120px;
				}
			}

			&:nth-child(3) {
				.icon-wrap {
					height: 150px;
				}
			}

			&:nth-child(4) {
				.icon-wrap {
					height: 170px;
				}
			}

			&:last-child {
				.icon-wrap {
					height: 200px;
				}
			}
		}
	}

	.timeline-bar {
		.bar {
			&:before {
				background: #fff;
				border: 1px solid transparent;
				border-bottom: 0 none;
				border-radius: 3px 3px 0 0;
				bottom: calc(100% + 10px);
				content: '';
				height: 10px;
				position: absolute;
				width: 100%;
			}

			@each $name, $color in $colors {
				&.#{ $name } {
					@include timeline-bar-after($color);
				}
			}
		}
	}
}

.timeline-status {
	overflow: hidden;
	padding-block: 5rem;

	.timeline-bar {
		.bar {
			.icon-bar {
				transition: width 1s ease-in-out;
				width: 0;

				.icon-circle {
					border: 3px solid $white;
					border-radius: 50%;
					height: 1rem;
					left: calc(100% - 0.5rem);
					position: absolute;
					top: 50%;
					transform: translateY(-50%);
					width: 1rem;

					&:after {
						bottom: 1rem;
						content: '';
						height: 50px;
						position: absolute;
						right: 50%;
						width: 1px;
					}
				}

				.icon {
					bottom: calc(100% + 1.5rem);
					height: 52px;
					left: 100%;
					position: absolute;
					transform: translateX(-50%);
					user-select: none;
					width: 52px;
				}
			}
		}
	}

	.status-meta {
		margin-top: 2.5rem;

		.head {
			border: 1px solid $white;
			border-radius: 10px 10px 0 0;
			padding: 1.25rem 1rem;

			h2 {
				font-size: 16px;
			}

			h3 {
				font-size: 24px;
			}

			h2,
			h3 {
				color: $white;
			}
		}

		.content-meta {
			border: 1px solid $white;
			border-radius: 0 0 10px 10px;
			padding: 1.25rem 1rem;
		}

		.actions {
			.circle-icon {
				flex: 0 0 2rem;
				font-size: 20px;
				height: 2rem;
				width: 2rem;
			}

			h2 {
				font-size: 20px;
			}
		}

		&.green {
			.head {
				background: $green;
			}

			.actions {
				h2 {
					color: $green;
				}
			}

			.head,
			.content-meta {
				border-color: $green;
			}
		}
	}
}

.timeline-bar {
	background: #eee;
	position: relative;

	.bar {
		flex: 1;
		height: 1.55rem;
		position: relative;

		.icon-bar {
			height: 100%;
			position: relative;
			width: 100%;
		}

		&.first,
		&.last {
			flex: 0 0 52px;
		}

		&.second {
			flex: 0.6;
		}

		@each $name, $color in $colors {
			&.#{ $name } {
				@include timeline-color($color);
			}
		}

		+ .bar {
			margin-left: 1.25rem;
		}
	}

	&:before,
	&:after {
		background: #eee;
		content: '';
		height: 100%;
		position: absolute;
		top: 0;
		width: 100vw;
	}

	&:after {
		right: 100%;
	}

	&:before {
		left: 100%;
	}
}

.actions-need {
	padding-bottom: 5rem;

	.heading {
		h2 {
			font-size: 16px;
		}

		h3 {
			font-size: 28px;
		}
	}

	.check-actions {
		margin-top: 2rem;

		.check-icon {
			color: $orange;
			font-size: 20px;
			margin-right: 1.25rem;
		}

		p {
			line-height: 1.35;
		}

		.cta {
			border-width: 1px;
			color: $orange;
			font-size: 12px;
			min-height: 36px;

			i {
				font-size: 18px;
			}

			&:hover {
				color: $white;
			}

			+ .cta {
				margin-left: 1.25rem;
			}
		}
	}

	.message-box {
		.message {
			@media (min-width: 1200px) {
				max-width: 1100px;
			}
		}
	}
}

.message-box {
	.pic {
		background: #d9d9d9;
		border-radius: 50%;
		flex: 0 0 110px;
		height: 110px;
		margin-block: 1rem;
		margin-right: -1.25rem;
		overflow: hidden;
		position: relative;
		width: 110px;
		z-index: 1;

		img {
			height: 100%;
			mix-blend-mode: multiply;
			object-fit: cover;
			width: 100%;
		}
	}

	.message {
		border: 1px solid $orange;
		border-radius: 10px;
		padding: 1.5rem 2.5rem;

		h2 {
			color: #000;
			font: 700 24px $futura;

			.quote {
				color: $orange;
				font: 700 40px $futura;
			}
		}

		.meta {
			color: $orange;
			font: 700 16px $inter;

			a {
				text-decoration: none;
			}
		}

		a {
			color: $orange;
		}
	}

	.cta {
		border: 1px solid $orange;
		color: $orange;
		font-size: 12px;

		i {
			color: $orange;
			font-size: 24px;
		}

		&:hover {
			background: $orange;
			color: #fff;

			i {
				color: #fff;
			}
		}
	}

	&.upload {
		padding-left: 90px;

		.upload-btn-wrapper {
			.btn-upload {
				border-color: #ff5811;
				color: #ff5811;
			}
		}

		.cta {
			background: #ff5811;
			color: #fff;
		}
	}

	.send-along {
		.form-check-label {
			color: #111;
			font: 16px $inter;
		}

		a {
			color: $orange;
			text-decoration: none;
		}
	}

	+ .message-box {
		margin-top: 2.25rem;
	}
}

.message-ctas {
	.cta {
		color: $orange;

		i {
			font-size: 24px;
		}

		&:hover {
			color: #fff;
		}

		+ .cta {
			margin-left: 1.5rem;
		}
	}
}

.messages-wrap {
	margin-top: 2rem;

	.head {
		color: #000;
		font: 700 16px $inter;

		h2 {
			color: #ff5811;
			font: inherit;
		}
	}

	.message-row {
		.icons {
			min-width: 84px;

			.icon {
				border-radius: 50%;
				color: #fff;
				font-size: 26px;
				height: 44px;
				overflow: hidden;
				width: 44px;

				&.pic {
					background: #d9d9d9;
					position: relative;
					z-index: 1;

					img {
						height: 100%;
						mix-blend-mode: multiply;
						object-fit: cover;
						width: 100%;
					}
				}

				+ .icon {
					margin-left: -20px;
				}
			}
		}

		p {
			font-size: 14px;
			margin-bottom: 0;
			white-space: nowrap;
		}

		a {
			color: inherit;
			text-decoration: underline;
		}

		.name,
		.date {
			p {
				font-weight: 700;
			}
		}

		.message {
			overflow: hidden;

			p {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			a {
				color: $orange;
				font: 14px $inter;
				text-decoration: none;
				white-space: nowrap;
			}
		}

		.cta {
			border-color: $orange;
			border-width: 1px;
			color: $orange;
		}

		@each $name, $color in $colors {
			&.#{ $name } {
				@include message-color($color);
			}
		}

		&.has-attachment {
			.notification {
				position: relative;

				.attachment {
					left: 0;
					position: absolute;
					top: 100%;
					width: 100%;
				}
			}
		}
	}
}

.circle-icon {
	border: 1px solid $orange;
	color: $orange;
	flex: 0 0 24px;
	font: 14px $inter;
	height: 24px;
	width: 24px;
}

.current-timeline {
	.heading {
		h2 {
			font-size: 28px;
		}

		.tab-timeline {
			.tab {
				color: rgba($black, 0.5);
				cursor: pointer;
				font: 500 16px $inter;

				&:hover {
					color: rgba($black, 1);
				}

				&.active {
					color: $orange;
					text-decoration: underline;
				}

				+ .tab {
					margin-left: 1.25rem;
				}
			}
		}
	}

	.meta-row {
		.logo {
			border-style: solid;
			border-width: 1px;
			flex: 0 0 52px;
			height: 52px;
			width: 52px;
		}

		.wrap-meta {
			border-style: solid;
			border-width: 1px;
			font: 16px $futura;
			overflow: hidden;

			.title {
				color: $white;
				flex: 0 0 8.25rem;
				font: 700 20px $futura;
			}

			.col-meta {
				flex: 0 0 calc(calc(100% - 8.25rem) / 3);
				font: 16px $inter;

				@media (min-width: 540px) {
					padding-inline: 1.5rem;
				}

				strong {
					font-weight: 700;
				}

				.text {
					position: relative;

					.check {
						color: $orange;
						position: absolute;
						right: calc(100% + 0.5rem);
					}
				}
			}

			&.payment {
				.col-meta {
					flex: 0 0 calc(calc(100% - 540px - 8.25rem) / 3);
				}

				.payment-type {
					flex: 0 0 540px;
				}
			}
		}

		@each $name, $color in $colors {
			&.#{ $name } {
				@include timeline-current($color);
			}
		}
	}

	.payment-list {
		@media (min-width: 540px) {
			padding-left: 68px;
		}

		@media (min-width: 768px) {
			padding-left: 200px;
		}

		.payment-row {
			.col-meta {
				flex: 0 0 25%;
				padding: 0.5rem;

				@media (min-width: 768px) {
					flex: 0 0 calc((100% - 540px) / 3);
					padding: 0.5rem 1.5rem;
				}
			}

			.payment-type {
				@media (max-width: 767px) {
					padding-block: 0;
				}

				@media (min-width: 768px) {
					flex: 0 0 540px;
				}
			}

			@media (max-width: 767px) {
				border: 1px solid $orange;
				border-radius: 0.5rem;
				margin-top: 1.5rem;
			}
		}
	}
}

.project-contact {
	padding-block: 5rem;

	.cta {
		color: $orange;
		min-width: 225px;

		&:hover {
			color: $white;
		}

		+ .cta {
			margin-left: 2rem;
		}
	}
}

.analytics-meta {
	.head {
		.title {
			background: $orange;
			color: #fff;
			padding: 0.8rem 1rem;

			h2 {
				font-size: 16px;
			}
		}

		.select-month {
			position: relative;
			width: 160px;

			select {
				appearance: none;
				font: 500 16px $inter;
				height: 100%;
				width: 100%;

				&:focus {
					border: 0 none;
					outline: 0 none;
				}
			}

			.arrow {
				pointer-events: none;
				position: absolute;
				right: 0;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}

	.copy {
		p,
		li {
			font-size: 18px;
		}

		ol {
			li {
				+ li {
					margin-top: 1rem;
				}
			}
		}
	}
}

.analytics-row {
	.box {
		h2 {
			font-size: 52px;
			line-height: 0.8;
		}

		.p {
			line-height: 1.25;
		}

		h4 {
			color: rgba(#000, 0.5);
		}

		&.graph {
			h2 {
				font-size: 24px;
			}
		}
	}

	.border-left {
		border-left: 1px solid #111;
	}
}

@mixin project-hub-card($color) {
	.icon-wrap {
		.progress-bar {
			--fill: #{$color};
		}

		.over {
			h2 {
				color: $color;
			}
		}
	}

	.copy {
		h2 {
			color: $color;
		}
	}
}

.project-row {
	gap: 1.25rem;

	.project-column {
		flex: 0 0 20rem;
		max-width: 20rem;

		h2 {
			color: #ff5811;
			font-size: 16px;
		}
	}
}

.projects-row {
	.project-column {
		h2,
		.h2 {
			color: #ff5811;
			font-size: 16px;
		}

		.link {
			color: #ff5811;
			font-size: 14px;
			font-weight: 400;

			&:hover {
				background: transparent;
				color: #fff;
			}
		}

		.project-list {
			max-height: 450px;
			overflow-y: auto;

			.meta-project {
				p {
					text-wrap: balance;
				}
			}
		}
	}
}

@keyframes growProgressBar {
	0%,
	33% {
		--pgPercentage: 0;
	}

	100% {
		--pgPercentage: var(--value);
	}
}

@property --pgPercentage {
	inherits: false;
	initial-value: 0;
	syntax: '<number>';
}

.meta-project {
	margin-top: 2rem;

	.icon-wrap {
		flex: 0 0 56px;
		height: 56px;
		isolation: isolate;
		position: relative;

		.progress-bar {
			--fill: var($color);
			animation: growProgressBar 1.5s 1 forwards;
			background: radial-gradient(closest-side, #111 90%, transparent 0 99.9%, rgba(#fff, 0.05)), conic-gradient(var(--fill) calc(var(--pgPercentage) * 1%), rgba(#fff, 0.1) 0);
		}

		.border-bar {
			border: 3px solid rgba(#fff, 0.1);
		}

		.progress-bar,
		.border-bar {
			border-radius: 50%;
			height: 100%;
			left: 0;
			pointer-events: none;
			position: absolute;
			top: 0;
			user-select: none;
			width: 100%;
			z-index: -1;
		}

		.icon {
			height: 30px;
			width: 30px;

			img {
				max-height: 100%;
				max-width: 100%;
			}
		}

		.over {
			left: 50%;
			opacity: 0;
			position: absolute;
			top: 50%;
			transform: translate(-50%, -50%);

			h2 {
				font-size: 14px;
			}
		}
	}

	.checked {
		border: 2px solid $orange;
		border-radius: 50%;
		height: 38px;
		margin-right: 1.25rem;
		width: 38px;
	}

	h2 {
		font-size: 16px;
	}

	.cta-row {
		a {
			color: #fff;
			font: 14px $inter;
			text-decoration: underline;

			&:hover {
				color: $orange;
			}

			+ a {
				margin-left: 1.25rem;
			}
		}
	}

	@each $name, $color in $colors {
		&.#{ $name } {
			@include project-hub-card($color);
		}
	}

	a {
		color: inherit;
		text-decoration: none;
	}

	&.hover {
		&:hover {
			.icon {
				opacity: 0;
			}

			.over {
				opacity: 1;
			}
		}
	}
}

.projects-list-table {
	.pages,
	.years {
		color: rgba(#fff, 0.5);
		font: 500 16px $inter;

		a {
			color: #fff;
			text-decoration: none;

			&.active {
				color: $orange;
				text-decoration: underline;
			}
		}
	}

	.years {
		a {
			+ a {
				margin-left: 1.25rem;
			}
		}
	}

	.pages {
		a {
			margin-left: 0.4rem;
		}
	}
}

.ask-question {
	h2 {
		color: #fff;
		font-size: 32px;
	}

	a {
		+ a {
			margin-left: 2rem;
		}
	}
}

.upload-cta-row {
	padding-left: 90px;

	.cta-file {
		border: 1px solid $orange;
		border-radius: 10px;
		color: #111;
		font: 700 12px $inter;
		padding: 0.8rem 1rem;

		.icon {
			background: $orange;
			border-radius: 50%;
			flex: 0 0 32px;
			height: 32px;
			width: 32px;
		}
	}

	.more-file {
		color: $orange;
		font: 700 12px $inter;
	}
}
