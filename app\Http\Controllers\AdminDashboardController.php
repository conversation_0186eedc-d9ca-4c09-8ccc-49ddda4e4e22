<?php

namespace App\Http\Controllers;

use App\Models\Comments;
use Illuminate\Http\Request;
use App\Models\Project;
use App\Models\Client;
use App\Models\Brand;
use App\Models\Holiday;
use App\Models\Permission;
use App\Models\Role;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AdminDashboardController extends Controller
{
    //

    public function dashboard()
    {
        $user = User::findOrFail(Auth::user()->id);

        $projects = $user->projects()
            ->with(['tasks' => function ($query) use ($user) {
                $query->whereHas('users', function ($q) use ($user) {
                    $q->where('users.id', $user->id);
                })->with('status');
            }])
            ->get();

        // Initialize grouped tasks instead of grouped projects
        $groupedTasks = [
            'urgent' => [],
            'regular' => [],
        ];

        // Group tasks by their status - only show urgent and new tasks
        foreach ($projects as $project) {
            foreach ($project->tasks as $task) {
                $status = strtolower($task->status->name ?? '');

                if ($status === 'urgent') {
                    $groupedTasks['urgent'][] = $task;
                } else if ($status === 'new') {
                    $groupedTasks['regular'][] = $task;
                }
            }
        }

        // $userTasks = $user->tasks()->pluck('tasks.id');


        // $latestMessages = Comments::with(['task', 'user'])
        //     ->whereIn('task_id', $userTasks)
        //     ->orderBy('created_at', 'desc')
        //     ->take(3)
        //     ->get();






        $tasks = Task::all();

        return view('admin.dashboard.index', compact('groupedTasks', 'projects', 'tasks'));
    }


    public function projects(Request $request)
    {
        $year = $request->input('year');
        $query = Project::query();
        if ($year && $year !== 'all') {
            $query->whereYear('created_at', $year);
        }
        $years = Project::selectRaw('YEAR(created_at) as year') //IMP
            ->distinct()
            ->orderByDesc('year')
            ->pluck('year')
            ->toArray();

        $projects = $query->paginate(10)->withQueryString();
        return view('admin.projects.index', compact('projects', 'year', 'years'));
    }




    public function save_project_messages(Request $request)
    {
        // return $request->all();

        // 1. Define and perform validation
        $validator = Validator::make($request->all(), [
            'project_id' => ['required', 'integer', 'exists:projects,id'],
            'project_message_title' => ['required', 'string', 'max:255'],
            'project_message_due_date' => [''],
            'project_message_reason' => [''],
            'project_message_description' => ['required', 'string'],
            'task_attachments' => ['nullable', 'array'],
            // Apply rules to each individual file in the array
            // Note: The 'file' rule will validate that it's an uploaded file.
            // 'mimes' and 'max' are still useful for frontend checks and backend re-validation
            'task_attachments.*' => ['file', 'mimes:jpeg,png,jpg,gif,pdf,doc,docx,xls,xlsx', 'max:2048'], // 2MB limit            
        ]);

        // If validation fails, return a JSON response with the errors.
        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed. Please correct the errors and try again.',
                'errors' => $validator->errors()
            ], 422);
        }

        $validatedData = $validator->validated();

        // 2. Process and store data within a database transaction
        try {
            DB::beginTransaction();

            $project = Project::findOrFail($validatedData['project_id']);

            $message = $project->messages()->create([
                'created_by_user_id' => Auth::user()->id,
                'title' => $validatedData['project_message_title'],
                'due_date' => $validatedData['project_message_due_date'],
                'reason' => $validatedData['project_message_reason'],
                'description' => $validatedData['project_message_description'],
            ]);

            // Handle file uploads if attachments were provided.
            if ($request->hasFile('task_attachments')) {
                foreach ($request->file('task_attachments') as $file) {
                    if ($file && $file->isValid()) {
                        // Generate a unique file name for storage.
                        // It's crucial to store files with unique names to prevent overwrites,
                        // even if the database only keeps the original name.
                        $storedFileName = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                        // Store the file in a 'project_message_attachments' subdirectory within the 'public' disk.
                        // The 'file_path' returned here (e.g., 'project_message_attachments/unique_name.ext')
                        // is not saved to the DB in your current schema, but it's where the file resides.
                        $file->storeAs('project_message_attachments', $storedFileName, 'public');

                        // Create an attachment record linked to the new message.
                        // Only 'file_name' is stored in your database table.
                        $message->attachments()->create([
                            'file_name' => $storedFileName, // Save the original client-provided name
                        ]);
                    }
                }
            }

            DB::commit();

            return response()->json([
                'message' => 'Project message and attachments saved successfully!',
                'data' => [
                    'message_id' => $message->id,
                    'project_id' => $project->id,
                ]
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            // \Log::error('Failed to save project message: ' . $e->getMessage(), [
            //     'exception' => $e,
            //     'request_data' => $validatedData
            // ]);

            return response()->json([
                'message' => 'An unexpected server error occurred while saving the message. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }




    public function projectsListView(Request $request)
    {
        return view('admin.projects.projects_list_view');
    }


    public function calender()
    {

        $projects = Project::with(['phases', 'client'])->get();


        $user = Auth::user();

        $isAdmin = $user->hasRole(['Admin', 'SuperAdmin']);

        if ($isAdmin) {

            $holidays = Holiday::with('user')->get();
        } else {

            $holidays = Holiday::with('user')
                ->where('is_global', true)
                ->orWhere('user_id', $user->id)
                ->get();
        }


        return view('admin.calender.calender_view', compact('projects', 'holidays', 'isAdmin'));
    }




    // public function resource_allocation(Request $request)
    // {

    //     $projects = Project::with(['phases', 'client'])->get();


    //     $teamUsers = User::with('teams', 'holidays', 'resource')->get();

    //     $holidays = Holiday::all();

    //     $latestDate = null;
    //     foreach ($projects as $project) {
    //         foreach ($project->phases as $phase) {
    //             if (isset($phase->pivot) && $phase->pivot->project_target) {
    //                 $phaseDate = Carbon::parse($phase->pivot->project_target);
    //                 if ($latestDate === null || $phaseDate->gt($latestDate)) {
    //                     $latestDate = $phaseDate;
    //                 }
    //             }
    //         }
    //     }

    //     // If no projects with phases, use current date + 3 months
    //     if ($latestDate === null) {
    //         $latestDate = Carbon::now()->addMonths(3);
    //     }

    //     // Generate weekly dates from now until the latest project phase completion date
    //     $startDate = Carbon::now()->startOfWeek();
    //     $endDate = $latestDate->copy()->addWeeks(1)->endOfWeek(); // Add buffer week

    //     $weekDates = [];
    //     $currentDate = $startDate->copy();

    //     while ($currentDate->lte($endDate)) {
    //         $weekStart = $currentDate->copy()->startOfWeek();
    //         $weekEnd = $currentDate->copy()->endOfWeek();

    //         // Skip to next week if already processed
    //         if (isset($weekDates[$weekStart->format('Y-m-d')])) {
    //             $currentDate->addWeek();
    //             continue;
    //         }

    //         // Initialize week data
    //         $weekDates[$weekStart->format('Y-m-d')] = [
    //             'date' => $weekStart,
    //             'week_end' => $weekEnd,
    //             'projects' => [],
    //             'designer_allocation' => [
    //                 'percentage' => 0,
    //                 'allocated_hours' => 0,
    //                 'available_hours' => 0,
    //                 'completed_hours' => 0,
    //                 'remaining_hours' => 0
    //             ],
    //             'developer_allocation' => [
    //                 'percentage' => 0,
    //                 'allocated_hours' => 0,
    //                 'available_hours' => 0,
    //                 'completed_hours' => 0,
    //                 'remaining_hours' => 0
    //             ],
    //             'pm_allocation' => [
    //                 'percentage' => 0,
    //                 'allocated_hours' => 0,
    //                 'available_hours' => 0,
    //                 'completed_hours' => 0,
    //                 'remaining_hours' => 0
    //             ],
    //             'is_past_week' => $weekEnd->lt(Carbon::now())
    //         ];

    //         $currentDate->addWeek();
    //     }

    //     // Calculate holidays and leaves for each week
    //     foreach ($weekDates as $dateKey => &$dateData) {
    //         $weekStart = $dateData['date'];
    //         $weekEnd = $dateData['week_end'];


    //         $holidaysInWeek = $holidays->filter(function ($holiday) use ($weekStart, $weekEnd) {
    //             $holidayStart = Carbon::parse($holiday->start_date);
    //             $holidayEnd = $holiday->end_date ? Carbon::parse($holiday->end_date) : $holidayStart;

    //             // Check if holiday overlaps with this week
    //             return $holidayStart->lte($weekEnd) && $holidayEnd->gte($weekStart);
    //         });


    //         $holidayDays = 0;
    //         $currentDay = $weekStart->copy();
    //         while ($currentDay->lte($weekEnd)) {
    //             if (!$currentDay->isWeekend()) {
    //                 foreach ($holidaysInWeek as $holiday) {
    //                     $holidayStart = Carbon::parse($holiday->start_date);
    //                     $holidayEnd = $holiday->end_date ? Carbon::parse($holiday->end_date) : $holidayStart;

    //                     if ($currentDay->gte($holidayStart) && $currentDay->lte($holidayEnd)) {
    //                         $holidayDays++;
    //                         break;
    //                     }
    //                 }
    //             }
    //             $currentDay->addDay();
    //         }


    //         $workDays = 5 - $holidayDays;
    //         if ($workDays < 0) $workDays = 0;

    //         $standardWorkWeek = 32;


    //         $designerAvailableHours = 0;
    //         $developerAvailableHours = 0;
    //         $pmAvailableHours = 0;

    //         foreach ($teamUsers as $user) {
    //             if ($user->resource) {
    //                 // Calculate based on the proportion of work days available
    //                 $designerAvailableHours += ($workDays / 5) * $user->resource->designer_hours;
    //                 $developerAvailableHours += ($workDays / 5) * $user->resource->developer_hours;
    //                 $pmAvailableHours += ($workDays / 5) * $user->resource->pm_hours;
    //             }
    //         }

    //         // Store available hours
    //         $dateData['designer_allocation']['available_hours'] = $designerAvailableHours;
    //         $dateData['developer_allocation']['available_hours'] = $developerAvailableHours;
    //         $dateData['pm_allocation']['available_hours'] = $pmAvailableHours;
    //     }

    //     // Initialize project phase tracking
    //     $projectPhaseTracking = [];

    //     // First pass: Calculate project allocations for each week and track completed hours
    //     foreach ($projects as $project) {
    //         // Skip projects without phases
    //         if ($project->phases->isEmpty()) {
    //             continue;
    //         }

    //         // Get phases sorted by order
    //         $sortedPhases = $project->phases->sortBy('order');

    //         // Get hours for each role from the project
    //         $pmHours = $project->pm_hours ?? 0;
    //         $designerHours = $project->designer_hours ?? 0;
    //         $developerHours = $project->developer_hours ?? 0;

    //         // Initialize project tracking
    //         $projectPhaseTracking[$project->id] = [
    //             'name' => $project->name,
    //             'total_pm_hours' => $pmHours,
    //             'total_designer_hours' => $designerHours,
    //             'total_developer_hours' => $developerHours,
    //             'completed_pm_hours' => 0,
    //             'completed_designer_hours' => 0,
    //             'completed_developer_hours' => 0,
    //             'phases' => []
    //         ];

    //         // Create an array of phases with their details
    //         $projectPhases = [];
    //         $previousPhaseEndDate = null;

    //         foreach ($sortedPhases as $phase) {
    //             if (!isset($phase->pivot) || !$phase->pivot->project_target) {
    //                 continue;
    //             }

    //             $phaseEndDate = Carbon::parse($phase->pivot->project_target);
    //             $phaseStartDate = $previousPhaseEndDate ? $previousPhaseEndDate->copy()->addDay() : $phaseEndDate->copy()->subWeeks(4);

    //             // Calculate weeks duration
    //             $weeksDuration = ceil($phaseStartDate->diffInDays($phaseEndDate) / 7);
    //             if ($weeksDuration < 1) $weeksDuration = 1;

    //             $projectPhases[] = [
    //                 'id' => $phase->id,
    //                 'name' => $phase->name,
    //                 'start' => $phaseStartDate,
    //                 'end' => $phaseEndDate,
    //                 'weeks' => $weeksDuration
    //             ];

    //             // Initialize phase tracking
    //             $projectPhaseTracking[$project->id]['phases'][$phase->id] = [
    //                 'name' => $phase->name,
    //                 'start' => $phaseStartDate,
    //                 'end' => $phaseEndDate,
    //                 'weeks' => $weeksDuration,
    //                 'pm_hours' => 0,
    //                 'designer_hours' => 0,
    //                 'developer_hours' => 0,
    //                 'completed_pm_hours' => 0,
    //                 'completed_designer_hours' => 0,
    //                 'completed_developer_hours' => 0
    //             ];

    //             $previousPhaseEndDate = $phaseEndDate;
    //         }

    //         // For each phase, calculate the allocation during its duration
    //         foreach ($projectPhases as $projectPhase) {
    //             $phaseId = $projectPhase['id'];
    //             $phaseName = strtolower($projectPhase['name']);
    //             $phaseStartDate = $projectPhase['start'];
    //             $phaseEndDate = $projectPhase['end'];
    //             $weeksDuration = $projectPhase['weeks'];

    //             // Calculate phase-specific hours based on phase type
    //             // Initialize hours for this phase
    //             $phaseDesignerHours = 0;
    //             $phaseDeveloperHours = 0;
    //             $phasePmHours = 0;


    //             if (strpos($phaseName, 'code') !== false || strpos($phaseName, 'deploy') !== false || strpos($phaseName, 'execution') !== false) {
    //                 // Developer phase
    //                 $phaseDeveloperHours = $developerHours;
    //             } elseif (strpos($phaseName, 'design') !== false) {
    //                 // Designer phase
    //                 $phaseDesignerHours = $designerHours;
    //             } elseif (strpos($phaseName, 'strategy') !== false || strpos($phaseName, 'content') !== false || strpos($phaseName, 'wireframes') !== false) {
    //                 // PM phase
    //                 $phasePmHours = $pmHours;
    //             } elseif (strpos($phaseName, 'manage') !== false) {
    //                 // PM management phase
    //                 $phasePmHours = $pmHours;
    //             }

    //             // Store total phase hours
    //             $projectPhaseTracking[$project->id]['phases'][$phaseId]['pm_hours'] = $phasePmHours;
    //             $projectPhaseTracking[$project->id]['phases'][$phaseId]['designer_hours'] = $phaseDesignerHours;
    //             $projectPhaseTracking[$project->id]['phases'][$phaseId]['developer_hours'] = $phaseDeveloperHours;

    //             // Calculate weekly allocation during this phase
    //             foreach ($weekDates as $dateKey => &$dateData) {
    //                 $weekStart = $dateData['date'];
    //                 $weekEnd = $dateData['week_end'];

    //                 // Check if this week is within the phase duration
    //                 if ($weekStart->lte($phaseEndDate) && $weekEnd->gte($phaseStartDate)) {
    //                     // Add project to this week's projects if not already added
    //                     if (!in_array($project->name, $dateData['projects'])) {
    //                         $dateData['projects'][] = $project->name;
    //                     }

    //                     // Calculate weekly allocation - divide total phase hours by the number of weeks
    //                     // This ensures we're allocating hours evenly across the phase duration
    //                     $weeklyDesignerHours = $phaseDesignerHours / $weeksDuration;
    //                     $weeklyDeveloperHours = $phaseDeveloperHours / $weeksDuration;
    //                     $weeklyPmHours = $phasePmHours / $weeksDuration;

    //                     // Add to allocated hours
    //                     $dateData['designer_allocation']['allocated_hours'] += $weeklyDesignerHours;
    //                     $dateData['developer_allocation']['allocated_hours'] += $weeklyDeveloperHours;
    //                     $dateData['pm_allocation']['allocated_hours'] += $weeklyPmHours;

    //                     // Track completed hours for past weeks
    //                     if ($dateData['is_past_week']) {
    //                         // Add to completed hours for the week
    //                         $dateData['designer_allocation']['completed_hours'] += $weeklyDesignerHours;
    //                         $dateData['developer_allocation']['completed_hours'] += $weeklyDeveloperHours;
    //                         $dateData['pm_allocation']['completed_hours'] += $weeklyPmHours;

    //                         // Add to completed hours for the phase
    //                         $projectPhaseTracking[$project->id]['phases'][$phaseId]['completed_designer_hours'] += $weeklyDesignerHours;
    //                         $projectPhaseTracking[$project->id]['phases'][$phaseId]['completed_developer_hours'] += $weeklyDeveloperHours;
    //                         $projectPhaseTracking[$project->id]['phases'][$phaseId]['completed_pm_hours'] += $weeklyPmHours;

    //                         // Add to completed hours for the project
    //                         $projectPhaseTracking[$project->id]['completed_designer_hours'] += $weeklyDesignerHours;
    //                         $projectPhaseTracking[$project->id]['completed_developer_hours'] += $weeklyDeveloperHours;
    //                         $projectPhaseTracking[$project->id]['completed_pm_hours'] += $weeklyPmHours;
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     // Second pass: Calculate remaining hours and adjust future allocations
    //     foreach ($projectPhaseTracking as $projectId => $projectData) {
    //         // Calculate remaining hours for the project
    //         $remainingDesignerHours = $projectData['total_designer_hours'] - $projectData['completed_designer_hours'];
    //         $remainingDeveloperHours = $projectData['total_developer_hours'] - $projectData['completed_developer_hours'];
    //         $remainingPmHours = $projectData['total_pm_hours'] - $projectData['completed_pm_hours'];

    //         // Find current and future phases
    //         $currentAndFuturePhases = [];
    //         foreach ($projectData['phases'] as $phaseId => $phaseData) {
    //             if (Carbon::parse($phaseData['end'])->gte(Carbon::now())) {
    //                 $currentAndFuturePhases[$phaseId] = $phaseData;
    //             }
    //         }

    //         // Count future weeks for allocation
    //         $futureWeeks = 0;
    //         foreach ($weekDates as $dateKey => $dateData) {
    //             if (!$dateData['is_past_week']) {
    //                 $futureWeeks++;
    //             }
    //         }

    //         // Avoid division by zero
    //         if ($futureWeeks == 0) $futureWeeks = 1;

    //         // Calculate weekly allocation for remaining hours
    //         $weeklyDesignerAllocation = $remainingDesignerHours / $futureWeeks;
    //         $weeklyDeveloperAllocation = $remainingDeveloperHours / $futureWeeks;
    //         $weeklyPmAllocation = $remainingPmHours / $futureWeeks;

    //         // Add project completion data to each week
    //         foreach ($weekDates as $dateKey => &$dateData) {
    //             if (!$dateData['is_past_week']) {
    //                 // Add remaining hours data
    //                 $dateData['designer_allocation']['remaining_hours'] += $remainingDesignerHours;
    //                 $dateData['developer_allocation']['remaining_hours'] += $remainingDeveloperHours;
    //                 $dateData['pm_allocation']['remaining_hours'] += $remainingPmHours;
    //             }
    //         }
    //     }

    //     // Calculate allocation percentages and check for overallocation
    //     foreach ($weekDates as $dateKey => &$dateData) {
    //         // Calculate percentages (can exceed 100%)
    //         $dateData['developer_allocation']['percentage'] = $dateData['developer_allocation']['available_hours'] > 0
    //             ? round(($dateData['developer_allocation']['allocated_hours'] / $dateData['developer_allocation']['available_hours']) * 100)
    //             : 0;

    //         $dateData['designer_allocation']['percentage'] = $dateData['designer_allocation']['available_hours'] > 0
    //             ? round(($dateData['designer_allocation']['allocated_hours'] / $dateData['designer_allocation']['available_hours']) * 100)
    //             : 0;

    //         $dateData['pm_allocation']['percentage'] = $dateData['pm_allocation']['available_hours'] > 0
    //             ? round(($dateData['pm_allocation']['allocated_hours'] / $dateData['pm_allocation']['available_hours']) * 100)
    //             : 0;

    //         // Check if team needs to work beyond capacity
    //         $dateData['designer_allocation']['overallocated'] = $dateData['designer_allocation']['percentage'] > 100;
    //         $dateData['developer_allocation']['overallocated'] = $dateData['developer_allocation']['percentage'] > 100;
    //         $dateData['pm_allocation']['overallocated'] = $dateData['pm_allocation']['percentage'] > 100;
    //     }

    //     $user = Auth::user();

    //     $isAdmin = $user->role && in_array($user->role->name, ['Admin', 'SuperAdmin']);


    //     $holidays = $isAdmin
    //         ? Holiday::with('user')->get()
    //         : Holiday::with('user')
    //         ->where('is_global', true)
    //         ->orWhere('user_id', $user->id)
    //         ->get();

    //     return view('admin.calender.resource_allocation', compact(
    //         'weekDates',
    //         'projects',
    //         'teamUsers',
    //         'holidays',
    //         'isAdmin',
    //         'projectPhaseTracking'
    //     ));
    // }



    public function resource_allocation(Request $request)
    {
        $projects = Project::with(['phases', 'client'])->get();
        $teamUsers = User::with('teams', 'holidays', 'resource')->get();
        $holidays = Holiday::all();

        $latestDate = $projects->flatMap->phases
            ->filter(fn($phase) => isset($phase->pivot->project_target))
            ->map(fn($phase) => Carbon::parse($phase->pivot->project_target))
            ->max() ?? Carbon::now()->addMonths(3);

        // dd($latestDate);

        $startDate = Carbon::now()->startOfWeek();
        $endDate = $latestDate->copy()->addWeek()->endOfWeek();
        $weekDates = [];

        for ($current = $startDate->copy(); $current->lte($endDate); $current->addWeek()) {
            $weekStart = $current->copy()->startOfWeek();
            $weekEnd = $current->copy()->endOfWeek();


            $weekDates[$weekStart->format('Y-m-d')] = [
                'date' => $weekStart,
                'week_end' => $weekEnd,
                'projects' => [],
                'designer_allocation' => $this->initAllocation(),
                'developer_allocation' => $this->initAllocation(),
                'pm_allocation' => $this->initAllocation(),
                'is_past_week' => $weekEnd->lt(Carbon::now()),
            ];
        }

        foreach ($weekDates as &$week) {
            $this->calculateAvailableHours($teamUsers, $week);
        }

        $projectPhaseTracking = [];
        foreach ($projects as $project) {
            if ($project->phases->isEmpty()) continue;

            $projectPhaseTracking[$project->id] = $this->initProjectTracking($project);
            $phases = $this->preparePhases($project);

            foreach ($phases as $phase) {
                $this->allocatePhaseHours($weekDates, $project, $phase, $projectPhaseTracking[$project->id]);
            }
        }

        foreach ($weekDates as &$week) {
            $this->finalizeWeekAllocations($week);
        }

        $user = Auth::user();

        $isAdmin = $user->role && in_array($user->role->name, ['Admin', 'SuperAdmin']);


        return view('admin.calender.resource_allocation', [
            'projects' => $projects,
            'holidays' => $holidays,
            'teamUsers' => $teamUsers,
            'weekDates' => $weekDates,
            'projectPhaseTracking' => $projectPhaseTracking,
            'isAdmin' => $isAdmin,
        ]);
    }

    private function initAllocation()
    {
        return [
            'percentage' => 0,
            'allocated_hours' => 0,
            'available_hours' => 0,
            'completed_hours' => 0,
            'remaining_hours' => 0,
        ];
    }

    private function calculateAvailableHours($teamUsers, &$week)
    {
        $weekStart = $week['date'];
        $weekEnd = $week['week_end'];

        $totalDesignerHours = 0;
        $totalDeveloperHours = 0;
        $totalPmHours = 0;

        foreach ($teamUsers as $user) {
            if ($user->resource) {
                // Calculate workdays for this specific user in this week
                $userWorkDays = $this->calculateUserWorkdays($user, $weekStart, $weekEnd);

                // Calculate available hours for this user based on their workdays
                $userDesignerHours = ($userWorkDays / 5) * $user->resource->designer_hours;
                $userDeveloperHours = ($userWorkDays / 5) * $user->resource->developer_hours;
                $userPmHours = ($userWorkDays / 5) * $user->resource->pm_hours;

                $totalDesignerHours += $userDesignerHours;
                $totalDeveloperHours += $userDeveloperHours;
                $totalPmHours += $userPmHours;
            }
        }

        $week['designer_allocation']['available_hours'] = $totalDesignerHours;
        $week['developer_allocation']['available_hours'] = $totalDeveloperHours;
        $week['pm_allocation']['available_hours'] = $totalPmHours;
    }

    private function calculateUserWorkdays($user, $weekStart, $weekEnd)
    {
        // Start with 5 workdays (excluding weekends)
        $workDays = 5;

        // Get user-specific holidays and global holidays
        $userHolidays = Holiday::where(function ($query) use ($user) {
            $query->where('user_id', $user->id)
                ->orWhere('is_global', true);
        })->get();

        // Calculate holiday days that fall within this week
        foreach ($userHolidays as $holiday) {
            $holidayStart = Carbon::parse($holiday->start_date);
            $holidayEnd = $holiday->end_date ? Carbon::parse($holiday->end_date) : $holidayStart;

            // Check if holiday overlaps with this week
            if ($holidayStart->lte($weekEnd) && $holidayEnd->gte($weekStart)) {
                // Calculate overlap days
                $overlapStart = $holidayStart->max($weekStart);
                $overlapEnd = $holidayEnd->min($weekEnd);

                // Count weekdays in the overlap (excluding weekends)
                $holidayDays = 0;
                $currentDay = $overlapStart->copy();
                while ($currentDay->lte($overlapEnd)) {
                    if (!$currentDay->isWeekend()) {
                        $holidayDays++;
                    }
                    $currentDay->addDay();
                }

                $workDays -= $holidayDays;
            }
        }

        return max($workDays, 0);
    }

    private function initProjectTracking($project)
    {
        return [
            'name' => $project->name,
            'total_pm_hours' => $project->pm_hours ?? 0,
            'total_designer_hours' => $project->designer_hours ?? 0,
            'total_developer_hours' => $project->developer_hours ?? 0,
            'completed_pm_hours' => 0,
            'completed_designer_hours' => 0,
            'completed_developer_hours' => 0,
            'phases' => [],
        ];
    }

    private function preparePhases($project)
    {
        $sortedPhases = $project->phases->sortBy('order');
        $phases = [];
        $prevEnd = null;

        foreach ($sortedPhases as $phase) {
            if (isset($phase->pivot->project_target)) {
                $end = Carbon::parse($phase->pivot->project_target);
                $start = $prevEnd ? $prevEnd->copy()->addDay() : $end->copy()->subWeeks(4);
                $weeks = max(ceil($start->diffInDays($end) / 7), 1);

                $phases[] = [
                    'id' => $phase->id,
                    'name' => strtolower($phase->name),
                    'start' => $start,
                    'end' => $end,
                    'weeks' => $weeks,
                ];

                $prevEnd = $end;
            }
        }

        return $phases;
    }

    private function allocatePhaseHours(&$weekDates, $project, $phase, &$tracking)
    {
        $roleHours = [
            'pm' => 0,
            'designer' => 0,
            'developer' => 0,
        ];

        if (Str::contains($phase['name'], ['code', 'deploy', 'execution'])) {
            $roleHours['developer'] = $project->developer_hours;
        } elseif (Str::contains($phase['name'], 'design')) {
            $roleHours['designer'] = $project->designer_hours;
        } elseif (Str::contains($phase['name'], ['strategy', 'content', 'wireframes', 'manage'])) {
            $roleHours['pm'] = $project->pm_hours;
        }

        $tracking['phases'][$phase['id']] = array_merge($phase, [
            'pm_hours' => $roleHours['pm'],
            'designer_hours' => $roleHours['designer'],
            'developer_hours' => $roleHours['developer'],
            'completed_pm_hours' => 0,
            'completed_designer_hours' => 0,
            'completed_developer_hours' => 0,
        ]);

        foreach ($weekDates as &$week) {
            if ($week['date']->lte($phase['end']) && $week['week_end']->gte($phase['start'])) {
                if (!in_array($project->name, $week['projects'])) {
                    $week['projects'][] = $project->name;
                }

                $weeklyHours = [
                    'designer' => $roleHours['designer'] / $phase['weeks'],
                    'developer' => $roleHours['developer'] / $phase['weeks'],
                    'pm' => $roleHours['pm'] / $phase['weeks'],
                ];

                foreach ($weeklyHours as $role => $hours) {
                    $week["{$role}_allocation"]['allocated_hours'] += $hours;

                    if ($week['is_past_week']) {
                        $week["{$role}_allocation"]['completed_hours'] += $hours;
                        $tracking["completed_{$role}_hours"] += $hours;
                        $tracking['phases'][$phase['id']]["completed_{$role}_hours"] += $hours;
                    }
                }
            }
        }
    }

    private function finalizeWeekAllocations(&$week)
    {
        foreach (['designer', 'developer', 'pm'] as $role) {
            $alloc = &$week["{$role}_allocation"];
            $alloc['remaining_hours'] = max($alloc['available_hours'] - $alloc['allocated_hours'], 0);
            $alloc['percentage'] = $alloc['available_hours'] > 0
                ? round(($alloc['allocated_hours'] / $alloc['available_hours']) * 100, 2)
                : 0;
        }
    }










    public function clients()
    {
        return view('admin.clients.index');
    }






    public function add_client()
    {
        $brands = Brand::all();
        if ($brands->isEmpty()) {
            return view('admin.clients.add_client');
        } else {
            return view('admin.clients.add_client', compact('brands'));
        }
    }

    public function brands(Request $request)
    {
        $year = $request->input('year');
        $query = Brand::query();
        if ($year && $year !== 'all') {
            $query->whereYear('created_at', $year);
        }

        $years = Brand::selectRaw('YEAR(created_at) as year') //IMP
            ->distinct()
            ->orderByDesc('year')
            ->pluck('year')
            ->toArray();

        $brands = $query->paginate(10)->withQueryString();

        return view('admin.brands.index', compact('brands', 'year', 'years'));
    }




    public function show_permissions()
    {

        $permissions = Permission::all();
        return view('admin.permissions.index', ['permissions' => $permissions]);
    }

    public function add_permission()
    {
        return view('admin.permissions.add_permissions');
    }

    public function save_permission(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'permission_name' => ['required', 'unique:permissions,name'],
        ]);
        if ($validate->fails()) {
            return redirect()->back()->withErrors($validate)->withInput();
        }

        $permission = new Permission;
        $permission->name = $request->input("permission_name");
        $permission->save();
        if ($permission) {
            return back()->with("permission_saved_success_message", "Permission Saved Successfully");
        }
    }

    public function edit_permission(Request $request)
    {
        $permission = Permission::where('id', '=', $request->route("id"))->get();
        return view('admin.permissions.edit_permissions', ['permission' => $permission]);
    }

    public function update_permission(Request $request)
    {
        $permission = Permission::where('id', '=', $request->route("id"))->first();
        $permission->name = $request->input("permission_name");
        $permission->save();
        if ($permission) {
            return back()->with("permission_updated_success_message", "Permission Updated Successfuly");
        }
    }

    public function delete_permission(Request $request)
    {
        $permission = Permission::where('id', '=', $request->route("id"))->first();
        $permission_name = $permission->name;
        if ($permission) {
            $permission->delete();
            if ($permission) {
                return back()->with("permission_deleted_success_message", "Permission: '" . $permission_name . "' deleted successfully");
            } else {
            }
        }
    }



    public function show_roles()
    {
        $roles = Role::all();
        return view("admin.roles.index", ['roles' => $roles]);
    }

    public function add_role()
    {
        return view('admin.roles.add_roles');
    }

    public function save_role(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'role_name' => ['required', 'unique:roles,name,' . $request->route("role_id")],
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }


        $role = new Role;
        $role->name = $request->input("role_name");
        $role->save();
        if ($role) {
            return back()->with("role_saved_success_message", "Role Saved Successfully");
        } else {
        }
    }

    public function edit_role(Request $request)
    {
        $role = Role::with(['permissions'])->where("id", '=', $request->route("role_id"))->first();
        $permissions = Permission::all();
        if ($role) {
            return view('admin.roles.edit_roles', ['role' => $role, 'permissions' => $permissions]);
        } else {
            return view('admin.roles.edit_roles', ['no_roles_found_error' => 'Role Not Found']);
        }
    }

    public function update_role(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'role_name' => 'required|unique:roles,name,' . $request->route("role_id"),
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $role = Role::findOrFail($request->route("role_id"));

        $role->name = $request->input("role_name");
        $role->save();

        //IMP
        $permissions = json_decode($request->input('role_permissions', '[]'), true);
        $permissions = array_unique(array_map('intval', $permissions)); // sanitize & remove duplicates

        $role->permissions()->sync($permissions); // use sync, not syncWithoutDetaching for update

        return back()->with("role_updated_success_message", "Role updated successfully.");
    }


    public function delete_role(Request $request)
    {
        try {
            $role = Role::where('id', $request->route("role_id"))->firstOrFail();
            $role_name = $role->name;

            $default_role = Role::where('name', 'Team Member')->first();

            if (!$default_role) {
                return back()->with("role_deleted_error_message", "Default Role Not Found");
            }

            $role->users()->update(['role_id' => $default_role->id]);

            $role->permissions()->detach();

            $role->delete();

            return redirect()->route('roles')->with([
                "success" => true,
                "message" => "Role: '" . $role_name . "' deleted successfully"
            ]);
        } catch (\Exception $e) {
            return back()->with([
                "success" => false,
                "message" => "An error occurred while deleting the role: " . $e->getMessage()
            ]);
        }
    }
}
