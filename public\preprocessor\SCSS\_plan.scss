.plan-col {
	border: 1px solid transparent;
	border-radius: 10px;
	margin-top: 21px;
	width: 160px;

	.icon-head {
		border-radius: 10px 10px 0 0;
		padding: 2rem 1rem 1rem;
		position: relative;

		.icon {
			background: #fff;
			border: 1px solid transparent;
			border-radius: 50%;
			height: 52px;
			left: 50%;
			position: absolute;
			top: 0;
			transform: translate(-50%, -50%);
			width: 52px;
		}

		h2 {
			font-size: 18px;
		}
	}

	.bar {
		background: rgba(#111, 0.5);
		height: 0.5px;
		width: 68px;
	}

	.check-list {
		.icon-check {
			border-radius: 4px;
			height: 15px;
			width: 15px;
		}

		.check {
			color: #000;
			font: 500 14px/1 $inter;

			+ .check {
				border-top: 0.5px solid rgba(#111, 0.5);
			}
		}
	}

	@each $name, $color in $colors {
		&.#{ $name } {
			@include plan-col-color($color);
		}
	}
}

.plan-content {
	h2 {
		color: #ff5811;
		font-size: 16px;
	}

	h3 {
		font-size: 28px;
	}
}

.plan-meta-content {
	padding-block: 4rem;

	.icons-row {
		.icon {
			+ .icon {
				margin-left: 1.25rem;
			}
		}
	}

	.head {
		h2 {
			color: #ff5811;
			font-size: 16px;
		}

		h3 {
			font-size: 28px;
		}
	}

	.number-list {
		counter-reset: counter;
		list-style: none;

		li {
			counter-increment: counter;
			position: relative;

			&:before {
				align-items: center;
				border: 1px solid #ff5811;
				border-radius: 50%;
				color: #ff5811;
				content: counter(counter);
				display: inline-flex;
				font: 14px/1 $inter;
				height: 24px;
				justify-content: center;
				left: -2rem;
				position: absolute;
				top: 0;
				width: 24px;
			}

			+ li {
				margin-top: 1rem;
			}
		}
	}
}

.plan-chart {
	h2 {
		color: #ff5811;
		font-size: 16px;
	}

	h3 {
		font-size: 28px;
	}
}
