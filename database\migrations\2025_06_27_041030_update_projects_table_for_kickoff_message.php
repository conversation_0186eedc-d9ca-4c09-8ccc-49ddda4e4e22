<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, drop old kickoff columns
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn([
                'kickoff_type',
                'kickoff_title',
                'kickoff_description',
                'kickoff_file',
            ]);
        });

        // Then, add kickoff_message_id after invoice_schedule
        Schema::table('projects', function (Blueprint $table) {
            $table->foreignId('kickoff_message_id')
                ->nullable()
                ->constrained('project_message_threads')
                ->nullOnDelete();
        });

        // Move it to correct position manually
        DB::statement("ALTER TABLE projects MODIFY COLUMN kickoff_message_id BIGINT UNSIGNED NULL AFTER invoice_schedule");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // First, drop kickoff_message_id
        Schema::table('projects', function (Blueprint $table) {
            $table->dropConstrainedForeignId('kickoff_message_id');
        });

        // Then, re-add kickoff columns after invoice_schedule
        Schema::table('projects', function (Blueprint $table) {
            $table->enum('kickoff_type', ['yes', 'no'])->nullable()->default('no')->after('invoice_schedule');
            $table->string('kickoff_title')->nullable()->after('kickoff_type');
            $table->text('kickoff_description')->nullable()->after('kickoff_title');
            $table->string('kickoff_file')->nullable()->after('kickoff_description');
        });
    }
};
