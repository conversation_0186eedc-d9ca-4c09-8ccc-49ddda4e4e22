<div>
    <style>

        .loading-blur {
            opacity: 0.6;
            filter: blur(1px);
            pointer-events: none;
        }

        .project-list-container {
            position: relative;
            min-height: 200px;
        }

        .project-list-container.loading::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(3px);
            z-index: 10;
        }

        /* Table styling */
        .projects-table {
            width: 100%;
            color: #fff;
            border-collapse: separate;
            border-spacing: 0;
        }

        .projects-table th {
            padding: 15px 10px;
            font-family: "Futura Std", serif;
            font-weight: 700;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: left;
            cursor: pointer;
        }

        .projects-table th:hover {
            color: #ff6600;
        }

        .projects-table td {
            padding: 15px 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .projects-table tr:hover td {
            background-color: rgba(255, 255, 255, 0.05);
        }



        .filter-section label {
            color: #fff;
            font-weight: 500;
            margin-bottom: 8px;
        }

        /* View options styling */
        .view-options a {
            color: #fff;
            text-decoration: none;
            padding: 8px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .view-options a:hover, .view-options a.active {
            background-color: #ff4c00;
            border-color: #ff4c00;
        }

        /* Pagination styling */
        .pages {
            display: flex;
            align-items: center;
        }

        .pages span {
            color: rgba(255, 255, 255, 0.7);
            margin-right: 10px;
        }

        .pages a {
            color: #fff;
            text-decoration: none;
            margin: 0 5px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
        }

        .pages a.active {
            background-color: #ff4c00;
        }

        .pages a:hover:not(.active) {
            background-color: rgba(255, 255, 255, 0.1);
        }
    </style>

    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal
                </a>
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project Regroup- <i>Project Overview</i></h1>
                </div>

            </div>
        </div>
    </section>

    <section class="client-project pt-0">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />

            <div class="view-options d-flex mb-4">
                <a href="{{ route('admin-projects-list-view') }}" class="active me-3">List View</a>
                <a href="{{ route('admin-calender') }}" class="me-3">Calendar View</a>
              
                <a href="{{ route('resource-allocation') }}">Resource Allocation</a>
            </div>

            <div class="filter-section mb-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="timelineFilter" class="form-label">Filter by Type</label>
                            <select wire:model.live="timelineFilter" id="timelineFilter" class="form-select">
                                <option value="">All Types</option>
                                @foreach ($timelines as $timeline)
                                    <option value="{{ $timeline->id }}">{{ $timeline->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="categoryFilter" class="form-label">Filter by Phase</label>
                            <select wire:model.live="categoryFilter" id="categoryFilter" class="form-select">
                                <option value="">All Phases</option>
                                @foreach ($categories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="project-list-container" wire:loading.class="loading">
                <div class="table-responsive">
                    <table class="projects-table">
                        <thead>
                            <tr>
                                <th wire:click="sortBy('client_name')">
                                    Client
                                    @if($sortColumn === 'client_name')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('name')">
                                    Project Name
                                    @if($sortColumn === 'name')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('job_code')">
                                    Project Code
                                    @if($sortColumn === 'job_code')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('timeline_name')">
                                    Type
                                    @if($sortColumn === 'timeline_name')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('category_id')">
                                    Phase
                                    @if($sortColumn === 'category_id')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>




                                <th wire:click="sortBy('phase_completion')">
                                   Phase Completion
                                    @if($sortColumn === 'phase_completion')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('project_duration')">
                                  Project Completion Date
                                    @if($sortColumn === 'project_duration')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('pm_hours')">
                                    PM HOURS
                                    @if($sortColumn === 'pm_hours')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('designer_hours')">
                                    DESIGNER HOURS
                                    @if($sortColumn === 'designer_hours')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                                <th wire:click="sortBy('developer_hours')">
                                    DEVELOPER HOURS
                                    @if($sortColumn === 'developer_hours')
                                        <span class="ms-1">
                                            @if($sortDirection === 'asc') &#9650; @else &#9660; @endif
                                        </span>
                                    @endif
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($projects as $project)
                            <tr>
                                <td>{{ $project->client ? $project->client->name : 'N/A' }}</td>
                                <td>{{ $project->name }}</td>
                                <td>{{ $project->job_code }}</td>
                                <td>{{ $project->timeline ? $project->timeline->name : 'N/A' }}</td>
                                <td>
                                    @php
                                        // Get the category based on category_id in the project table
                                        $category = null;
                                        if($project->category_id) {
                                            $category = $project->category;
                                        }
                                    @endphp
                                    {{ $category ? $category->name : 'N/A' }}
                                </td>
                                <td>
                                    @php
                                        // Get the current phase based on phase_id in the project table
                                        $currentPhase = null;
                                        if($project->phase_id) {
                                            $currentPhase = $project->phases->firstWhere('id', $project->phase_id);
                                        }

                                        // Phase completion date is the project_target of the current phase
                                        $phaseCompletionDate = $currentPhase && isset($currentPhase->pivot) ? $currentPhase->pivot->project_target : null;
                                    @endphp
                                    {{ $phaseCompletionDate ?: 'N/A' }}
                                </td>
                                <td>
                                    @php
                                        // Project completion date is the project_target of the phase with the highest order (last phase)
                                        $completionDate = null;
                                        if($project->phases && $project->phases->isNotEmpty()) {
                                            // Get the phase with the highest order (last phase in the project)
                                            $lastPhase = $project->phases->sortByDesc('order')->first();
                                            $completionDate = $lastPhase->pivot->project_target;
                                        }
                                    @endphp
                                    {{ $completionDate ?: 'N/A' }}
                                </td>
                                <td>{{ $project->pm_hours }}</td>
                                <td>{{ $project->designer_hours }}</td>
                                <td>{{ $project->developer_hours }}</td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center">No projects found</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <div class="pages-top d-flex justify-content-end mt-3 mb-4">
                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $projects->lastPage(); $i++)
                        <a wire:click="gotoPage({{ $i }})" href="javascript:void(0)" class="{{ $projects->currentPage() == $i ? 'active' : '' }}">
                            {{ $i }}
                      </a>
                        @endfor
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="ask-question">
        <div class="container-xxl">
            <div class="d-flex align-items-center justify-content-between pb-4">
                <div class="head">
                    <h2 class="mb-0">
                        Have a question?<br />
                        <i>We're here to help.</i>
                    </h2>
                </div>
                <div class="cta-row">
                    <a class="cta link text-decoration-none text-white" href="#">GET EMAIL UPDATES</a>
                    <a class="cta link text-decoration-none text-white" href="#">NEED HELP? CONTACT</a>
                </div>
            </div>
            <hr class="mt-0 mt-4 border-white" />
        </div>
    </section>
</div>