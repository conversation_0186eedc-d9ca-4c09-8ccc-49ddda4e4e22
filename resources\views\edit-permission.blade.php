@extends('layout.app')
@section('title', 'loginorg')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">
                    <h4>
                        @if(request()->route()->named('edit-permission'))
                            Edit Permission
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('edit-permission'))
                        @if(isset($permission_not_found_error) && $permission_not_found_error!='')
                            <div class="permission_not_found_error">{{ $permission_not_found_error }}</div>
                        @else
                            @if(Session::has('permission_updated_success_message'))
                                <div class="bg-success text-light text-center mx-auto my-1 p-2 w-50 rounded">
                                    {{ Session::get('permission_updated_success_message') }}
                                </div>
                            @endif
                            @foreach($permission as $permission)
                                <div class="permission-details-div mx-auto rounded">
                                    <form action="{{ route('update-permission', ['id'=>request()->route("id")]) }}" method="post" name="edit-permission-form">
                                        @csrf
                                        @method("put")
                                        <div class="permission-details-div-row1 d-flex mb-1">
                                            <label class="permission-details-div-label permission_name_label">Name: </label>
                                            <input type="text" name="permission_name" value="{{ $permission->name }}" class="permission-details-div-span permission_name_span">
                                        </div>
                                        <button type="submit">Update</button>
                                    </form>
                                </div>
                            @endforeach
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }

        .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;
            overflow:scroll;

            left: 0;
            z-index: 0;
            height: calc(100vh - 50px);
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            /* padding:0px 50px;
            display:flex;
            justify-content:center; */
        }
        .add-project-btn-div{
            display: flex;
            justify-content: end;
        }
        form[name="edit-permission-form"]{
            width:100%;
            text-align:center;
        }
        .permission-details-div{
            padding:10px;
            width:50%;
            border:1px solid grey;
            display: flex;
            justify-content: center;
        }
        .permission-details-div-label{
            width:25%;
            text-align:left;
        }
        .permission-details-div-span{
            width:75%;
            text-align:left;
        }
        .permission_not_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
    </style>
@endpush

@push('script')
    <script>
        
    </script>
@endpush