'use strict';

$(function () {
  cursorDot();
  domManipulate();
  tabTimeline();
  observeElements();
  setColorCode();
});

$(window).on('load', function () {
  setTimeout(function () {
    loading();
  }, 200);
});

function loading() {
  $('body.loading').removeClass('loading');
  $('#loader').remove();
}

function domManipulate() {
  if ($('.circle-icon')) $('.circle-icon').addClass('align-items-center d-inline-flex justify-content-center lh-1 rounded-circle text-decoration-none');
  if ($('.cta-action')) $('.cta-action').addClass('d-inline-flex align-items-center justify-content-center text-uppercase text-decoration-none py-2 px-3 mt-0');
}

function cursorDot() {
  $(document).mousemove(function (e) {
    $('.cursor').eq(0).css({ left: e.pageX, top: e.pageY });
    setTimeout(function () {
      $('.cursor').eq(1).css({ left: e.pageX, top: e.pageY });
    }, 20);
  });

  $('a, #itemsSlider .arrow').hover(
    function () {
      $('.cursor').addClass('animation-link');
    },
    function () {
      $('.cursor').removeClass('animation-link');
    }
  );
  $('button').hover(
    function () {
      $('.cursor').addClass('animation-link-button');
    },
    function () {
      $('.cursor').removeClass('animation-link-button');
    }
  );
}

function tabTimeline() {
  $('#tabTimeline .tab').on('click', function () {
    let data = $(this).data('timeline-tab');
    $('#tabTimeline .tab').removeClass('active');
    $(this).addClass('active');

    $('.tab-timeline-content').addClass('d-none');
    $(`.tab-timeline-content[data-timeline-tab="${data}"]`).removeClass('d-none');
  });
}

function numberCounter() {
  $('.counter').each(function () {
    let $this = $(this);
    let countTo = $this.attr('data-countTo');
    let countDuration = parseInt($this.attr('data-duration'));

    let hasDecimal = countTo.includes('.');

    $({ counter: parseFloat($this.text().replace(/,/g, '')) }).animate(
      {
        counter: parseFloat(countTo),
      },
      {
        duration: countDuration,
        easing: 'linear',
        step: function () {
          $this.text(formatNumber(this.counter, hasDecimal));
        },
        complete: function () {
          $this.text(formatNumber(this.counter, hasDecimal));
        },
      }
    );
  });

  function formatNumber(num, hasDecimal) {
    return hasDecimal ? num.toLocaleString(undefined, { minimumFractionDigits: 1, maximumFractionDigits: 1 }) : Math.floor(num).toLocaleString();
  }
}

function barAnimate() {
  let activeBar = $('#timelineBar .bar.current .icon-bar');

  if (activeBar.length) {
    activeBar.css('width', activeBar.data('bar-width') + '%');
  }
}

function observeElements() {
  let elementsToObserve = document.querySelectorAll('.timeline-status, .counter');

  let observer = new IntersectionObserver(
    function (entries, observer) {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          if (entry.target.classList.contains('timeline-status')) {
            barAnimate();
          } else if (entry.target.classList.contains('counter')) {
            numberCounter();
          }
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.6 }
  );

  elementsToObserve.forEach((element) => {
    observer.observe(element);
  });
}

function openDatePicker(event) {
  const dateInput = event.currentTarget.querySelector('.date-picker');
  dateInput.showPicker();
}

function updateLabel(event) {
  const selectedDate = event.target.value;
  const labelText = event.target.previousElementSibling.querySelector('.label-text');

  if (selectedDate) {
    labelText.textContent = new Date(selectedDate).toLocaleDateString();
  } else {
    labelText.textContent = 'ADD DUE DATE'; // Reset if no date is selected
  }
}


//IMP
function setColorCode() {
  const colorPickerFields = document.querySelectorAll('.input_color');

  colorPickerFields.forEach((field) => {
    const text = field.querySelector('.value');
    const picker = field.querySelector('.picker');

    if (!text || !picker) return;

    const syncColor = (source, target) => {
      target.value = source.value;
    };

    if (text.value) {
      syncColor(text, picker);
    }

    text.addEventListener('change', () => {
      syncColor(text, picker);
    });

    picker.addEventListener('input', () => {
      syncColor(picker, text);
    });
  });
}


// Simple toast notification system 

function createToast(type, message, options = {}) {

  if (options.unique) {
    const existingToasts = document.querySelectorAll(".custom-toast");
    existingToasts.forEach(toast => dismissToast(toast));
  }


  let toastContainer = document.getElementById("toast-container");
  if (!toastContainer) {
    toastContainer = document.createElement("div");
    toastContainer.id = "toast-container";
    toastContainer.className = "position-fixed bottom-0 end-0 p-3";
    toastContainer.style.zIndex = "1080";
    document.body.appendChild(toastContainer);
  }


  const toast = document.createElement("div");
  toast.className = "custom-toast";
  toast.style.margin = "0.75rem 0";
  toast.style.transformOrigin = "right bottom";
  toast.style.borderRadius = "12px";
  toast.style.overflow = "hidden";
  toast.style.boxShadow = "0 15px 30px rgba(0, 0, 0, 0.15), 0 6px 12px rgba(0, 0, 0, 0.1)";
  toast.style.width = "350px";
  toast.style.maxWidth = "95vw";
  toast.style.opacity = "0";
  toast.style.transform = "translateX(100%)";
  toast.style.transition = "all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)";
  toast.style.backdropFilter = "blur(10px)";
  toast.style.WebkitBackdropFilter = "blur(10px)";
  toast.style.borderLeft = "6px solid #fff";
  toast.style.fontFamily = "'Futura Std', sans-serif";


  const typeConfig = {
    success: {
      gradient: "linear-gradient(145deg, #155831, #1e7e44)",
      borderColor: "#34d399",
      icon: "fa-circle-check",
      iconColor: "#34d399"
    },
    error: {
      gradient: "linear-gradient(145deg, #7f1d1d, #b91c1c)",
      borderColor: "#f87171",
      icon: "fa-circle-xmark",
      iconColor: "#f87171"
    },
    warning: {
      gradient: "linear-gradient(145deg, #854d0e, #a16207)",
      borderColor: "#fbbf24",
      icon: "fa-triangle-exclamation",
      iconColor: "#fbbf24"
    },
    info: {
      gradient: "linear-gradient(145deg, #1e40af, #1d4ed8)",
      borderColor: "#60a5fa",
      icon: "fa-circle-info",
      iconColor: "#60a5fa"
    },
    brand: {
      gradient: "linear-gradient(145deg, #9a2c00, #ff4c00)",
      borderColor: "#ff6b2c",
      icon: "fa-fire-flame-curved",
      iconColor: "#ff6b2c"
    },
    dark: {
      gradient: "linear-gradient(145deg, #1f2937, #111827)",
      borderColor: "#6b7280",
      icon: "fa-bell",
      iconColor: "#d1d5db"
    }
  };


  const config = typeConfig[type] || typeConfig.dark;
  toast.style.background = config.gradient;
  toast.style.borderLeftColor = config.borderColor;


  const inner = document.createElement("div");
  inner.className = "d-flex position-relative";
  inner.style.padding = "16px";


  const contentWrapper = document.createElement("div");
  contentWrapper.className = "toast-content d-flex";


  const iconContainer = document.createElement("div");
  iconContainer.className = "me-3 d-flex align-items-center justify-content-center";
  iconContainer.style.width = "32px";
  iconContainer.style.height = "32px";
  iconContainer.style.borderRadius = "50%";
  iconContainer.style.backgroundColor = "rgba(255, 255, 255, 0.15)";
  iconContainer.style.flexShrink = "0";


  const useFontAwesome = typeof window !== 'undefined' &&
    window.document.querySelector('link[href*="fontawesome"], script[src*="fontawesome"]');

  if (useFontAwesome) {
    const icon = document.createElement("i");
    icon.className = `fa-light ${config.icon}`;
    icon.style.color = config.iconColor;
    icon.style.fontSize = "1.2rem";
    iconContainer.appendChild(icon);
  } else {

    const svgIcons = {
      'fa-circle-check': `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="${config.iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>`,
      'fa-circle-xmark': `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="${config.iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>`,
      'fa-triangle-exclamation': `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="${config.iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>`,
      'fa-circle-info': `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="${config.iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>`,
      'fa-bell': `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="${config.iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path><path d="M13.73 21a2 2 0 0 1-3.46 0"></path></svg>`,
      'fa-fire-flame-curved': `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="${config.iconColor}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"></path></svg>`
    };

    iconContainer.innerHTML = svgIcons[config.icon] || svgIcons['fa-bell'];
  }


  const textContainer = document.createElement("div");
  textContainer.className = "d-flex flex-column justify-content-center";


  if (options.title) {
    const title = document.createElement("h6");
    title.textContent = options.title;
    title.className = "mb-1";
    title.style.color = "#ffffff";
    title.style.fontWeight = "600";
    title.style.fontSize = "1rem";
    title.style.marginTop = "0";
    title.style.marginBottom = "4px";
    textContainer.appendChild(title);
  }


  const messageEl = document.createElement("div");
  messageEl.textContent = message;
  messageEl.style.color = "rgba(255, 255, 255, 0.9)";
  messageEl.style.fontSize = "0.875rem";
  messageEl.style.lineHeight = "1.4";
  textContainer.appendChild(messageEl);


  const closeBtn = document.createElement("button");
  closeBtn.className = "position-absolute top-0 end-0 mt-2 me-2";
  closeBtn.style.backgroundColor = "transparent";
  closeBtn.style.border = "none";
  closeBtn.style.padding = "8px";
  closeBtn.style.display = "flex";
  closeBtn.style.alignItems = "center";
  closeBtn.style.justifyContent = "center";
  closeBtn.style.cursor = "pointer";
  closeBtn.style.color = "rgba(255, 255, 255, 0.7)";
  closeBtn.style.transition = "all 0.2s";
  closeBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>';
  closeBtn.ariaLabel = "Close";


  closeBtn.addEventListener("mouseover", () => {
    closeBtn.style.color = "rgba(255, 255, 255, 1)";
    closeBtn.style.transform = "scale(1.1)";
  });

  closeBtn.addEventListener("mouseout", () => {
    closeBtn.style.color = "rgba(255, 255, 255, 0.7)";
    closeBtn.style.transform = "scale(1)";
  });


  const progressContainer = document.createElement("div");
  progressContainer.className = "position-absolute bottom-0 start-0";
  progressContainer.style.height = "4px";
  progressContainer.style.width = "100%";
  progressContainer.style.backgroundColor = "rgba(255, 255, 255, 0.1)";

  const progressBar = document.createElement("div");
  progressBar.className = "progress-bar";
  progressBar.style.height = "100%";
  progressBar.style.width = "100%";
  progressBar.style.backgroundColor = config.borderColor;
  progressBar.style.transition = `width ${options.duration || 5000}ms linear`;
  progressContainer.appendChild(progressBar);


  contentWrapper.appendChild(iconContainer);
  contentWrapper.appendChild(textContainer);
  inner.appendChild(contentWrapper);
  inner.appendChild(closeBtn);
  toast.appendChild(inner);
  toast.appendChild(progressContainer);


  toastContainer.appendChild(toast);


  const animateIn = () => {
    requestAnimationFrame(() => {
      toast.style.transform = "translateX(0)";
      toast.style.opacity = "1";
    });
  };


  function dismissToast(toastElement) {
    toastElement.style.transform = "translateX(100%)";
    toastElement.style.opacity = "0";

    setTimeout(() => {
      if (toastElement.parentNode) {
        toastElement.parentNode.removeChild(toastElement);
      }

      if (toastContainer.children.length === 0 && toastContainer.parentNode) {
        document.body.removeChild(toastContainer);
      }
    }, 500);
  }


  closeBtn.addEventListener("click", () => {
    clearTimeout(toast.autoCloseTimeout);
    dismissToast(toast);
  });

  toast.addEventListener("mouseenter", () => {
    progressBar.style.transition = "none";
    clearTimeout(toast.autoCloseTimeout);
  });

  toast.addEventListener("mouseleave", () => {
    const width = parseFloat(getComputedStyle(progressBar).width);
    const totalWidth = parseFloat(getComputedStyle(progressContainer).width);
    const remainingTime = (width / totalWidth) * (options.duration || 5000);

    progressBar.style.transition = `width ${remainingTime}ms linear`;
    progressBar.style.width = "0";

    toast.autoCloseTimeout = setTimeout(() => {
      dismissToast(toast);
    }, remainingTime);
  });


  toast.addEventListener("click", (e) => {
    if (e.target !== closeBtn && !closeBtn.contains(e.target)) {
      toast.style.transform = "scale(0.98)";
      setTimeout(() => {
        toast.style.transform = "scale(1)";
      }, 100);
    }
  });


  setTimeout(animateIn, 10);


  setTimeout(() => {
    progressBar.style.width = "0";
  }, 100);


  toast.autoCloseTimeout = setTimeout(() => {
    dismissToast(toast);
  }, options.duration || 5000);


  toast.addEventListener("mousedown", (e) => {
    if (e.target !== closeBtn && !closeBtn.contains(e.target)) {
      const ripple = document.createElement("div");
      ripple.className = "ripple";
      ripple.style.position = "absolute";
      ripple.style.width = "10px";
      ripple.style.height = "10px";
      ripple.style.backgroundColor = "rgba(255, 255, 255, 0.4)";
      ripple.style.borderRadius = "50%";
      ripple.style.transform = "scale(0)";
      ripple.style.animation = "ripple-effect 0.6s linear";
      ripple.style.pointerEvents = "none";

      const rect = toast.getBoundingClientRect();
      ripple.style.left = `${e.clientX - rect.left}px`;
      ripple.style.top = `${e.clientY - rect.top}px`;

      toast.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    }
  });


  if (!document.getElementById("toast-styles")) {
    const style = document.createElement("style");
    style.id = "toast-styles";
    style.innerHTML = `
            @keyframes ripple-effect {
                0% {
                    transform: scale(0);
                    opacity: 1;
                }
                100% {
                    transform: scale(40);
                    opacity: 0;
                }
            }

            .custom-toast {
                position: relative;
                overflow: hidden;
            }

            .custom-toast:after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 100%;
                background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0.05) 100%);
                pointer-events: none;
            }
        `;
    document.head.appendChild(style);
  }

  return toast;
}


function successToast(message, options = {}) {
  return createToast('success', message, options);
}

function errorToast(message, options = {}) {
  return createToast('error', message, options);
}

function warningToast(message, options = {}) {
  return createToast('warning', message, options);
}

function infoToast(message, options = {}) {
  return createToast('info', message, options);
}

function brandToast(message, options = {}) {
  return createToast('brand', message, options);
}

function darkToast(message, options = {}) {
  return createToast('dark', message, options);
}




/**
 * GlobalFormValidator - A reusable form validation library
 * 
 * Usage:
 * const validator = new GlobalFormValidator(formId, {
 *   rules: { '#selector': value => validation_logic },
 *   messages: { '#selector': 'Error message' }
 * });
 * 
 * validator.initialize();
 */
class GlobalFormValidator {
  constructor(formId, config = {}) {
    this.formId = formId;
    this.form = document.getElementById(formId);
    this.rules = config.rules || {};
    this.messages = config.messages || {};
    this.submitSelector = config.submitSelector || `#${formId} button[type="submit"]`;
    this.toastConfig = config.toastConfig || { title: "Error" };
    this.onSuccess = config.onSuccess || (form => form.submit());

    if (!this.form) {
      console.warn(`Form with ID "${formId}" not found`);
    }
  }

  initialize() {
    const submitButton = document.querySelector(this.submitSelector);

    if (submitButton) {
      submitButton.addEventListener('click', (e) => {
        e.preventDefault();

        if (this.validateForm()) {
          this.onSuccess(this.form);
        } else {
          console.log('Validation failed');
        }
      });
    } else {
      console.warn('Submit button not found using selector:', this.submitSelector);
    }

    return this;
  }

  validateForm() {
    let isValid = true;

    for (let selector of Object.keys(this.rules)) {
      const fieldValid = this.validateField(selector);
      if (!fieldValid) {
        isValid = false;
        break;
      }
    }

    return isValid;
  }

  validateField(selector) {
    const elements = this.form.querySelectorAll(selector);
    if (!elements.length) return true;
  
    let isValid = false;
    const firstElement = elements[0];
  
    if (firstElement.type === 'radio') {
      const checked = [...elements].find(el => el.checked);
      const value = checked ? checked.value : '';
      isValid = this.rules[selector](value);
    } else if (firstElement.type === 'checkbox') {
      const checkboxElements = elements.length === 1 ? elements[0] : [...elements];
      isValid = this.rules[selector](checkboxElements);
    } else {
      const value = firstElement.value;
      isValid = this.rules[selector](value);
    }
  
    if (!isValid) {
      this.showValidationError(selector);
    } else {
      this.clearValidationError(selector);
    }
  
    return isValid;
  }
  

  showValidationError(selector) {
    const elements = this.form.querySelectorAll(selector);
    const firstElement = elements[0];
    const errorMessage = this.messages[selector] || 'This field is required';

    if (!elements.length) return;

    // Scroll to and focus first field
    firstElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    firstElement.focus();

    // Add visual feedback if not radio group
    if (firstElement.type !== 'radio' && firstElement.type !== 'checkbox') {
      firstElement.classList.add('is-invalid');
    }

    this.showToast(errorMessage);
  }

  clearValidationError(selector) {
    const elements = this.form.querySelectorAll(selector);
    const firstElement = elements[0];

    if (!firstElement) return;

    if (firstElement.type !== 'radio' && firstElement.type !== 'checkbox') {
      firstElement.classList.remove('is-invalid');
    }
  }

  showToast(message) {
    if (typeof errorToast === 'function') {
      errorToast(message, this.toastConfig);
    } else {
      console.error('Error:', message);
    }
  }

  // Static reusable patterns and validators
  static patterns = {
    email: /\S+@\S+\.\S+/,
    phone: /^\+?[0-9]{10,15}$/,
    url: /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
  }

  static normalizeElements(elements) {
    if (Array.isArray(elements) || elements instanceof NodeList || elements instanceof HTMLCollection) {
      return Array.from(elements);
    }
    if (elements instanceof HTMLInputElement) {
      return [elements];
    }
    return [];
  }
  
  static validators = {
    required: value => typeof value === 'string' ? value.trim() !== '' : !!value,
  
    email: value => GlobalFormValidator.patterns.email.test(value),
    phone: value => GlobalFormValidator.patterns.phone.test(value),
    url: value => GlobalFormValidator.patterns.url.test(value),
  
    minLength: (value, length) => value.length >= length,
    maxLength: (value, length) => value.length <= length,
    numeric: value => /^[0-9]+$/.test(value),
  
    anyChecked: elements => {
      const checkboxes = GlobalFormValidator.normalizeElements(elements);
  
      return checkboxes.some(el => el.checked);
    },
  
    allChecked: elements => {
      const checkboxes = GlobalFormValidator.normalizeElements(elements);
      return checkboxes.every(el => el.checked);
    }
  }
  
  
}



