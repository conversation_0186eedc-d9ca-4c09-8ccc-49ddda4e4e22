<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TimelineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $timelines = [
            [
                'name' => 'Web Project',
                'type' => 'web',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Voyager Project',
                'type' => 'voyager',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Print Project',
                'type' => 'print',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Publication Project',
                'type' => 'publication',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Video Project',
                'type' => 'video',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Social Media Project',
                'type' => 'social_media',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Other Project',
                'type' => 'other',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Maintenance Project',
                'type' => 'maintenance',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('timeline')->insert($timelines);
    }
}
