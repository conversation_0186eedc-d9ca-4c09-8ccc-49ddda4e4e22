- let pageName = 'Client Portal';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/projectBoxMeta.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Welcome to your project hub,<br> <i>Sample Project Client</i>', true, true)

        section.client-project.pt-0
            .container-xxl
                hr.mt-0.mb-4.border-white
                .row.projects-row
                    .col-md-4.project-column.mb-5
                        h2.text-uppercase ACTIVE Projects
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('green', 'content', '[SP-003-25] - PHASE 2/5', 'Sample Project 3 Lorem Ipsum', ['Project Details'], 2)
                            +projectBoxMeta('purple', 'code', '[SP-004-25] - PHASE 4/5', 'Sample Project 2 Lorem Ipsum', ['Project Details'], 4)
                    .col-md-4.project-column.mb-5
                        h2.text-uppercase VOYAGER
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('orange', 'deploy', '[SP-006-25]', 'Sample Project Website 2025 Voyager Lorem Ipsum', ['Site Analytics', 'Billing History'])
                    .col-md-4.project-column.mb-5
                        .align-items-center.d-flex.justify-content-between
                            h2.text-uppercase ARCHIVED Projects
                            a.cta.link.border-0.p-0.mt-0.mb-2(href="client-archived-projects.html") See All
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('orange', 'checked', '[SP-001-25]', 'Sample Project Website Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])
                            +projectBoxMeta('orange', 'checked', '[SP-002-25]', 'Sample Project Poster Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])
                            +projectBoxMeta('orange', 'checked', '[SP-003-25]', 'Sample Project Poster Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])
                            +projectBoxMeta('orange', 'checked', '[SP-004-25]', 'Sample Project Poster Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])
                            +projectBoxMeta('orange', 'checked', '[SP-005-25]', 'Sample Project Poster Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])
                            +projectBoxMeta('orange', 'checked', '[SP-006-25]', 'Sample Project Poster Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])
                            +projectBoxMeta('orange', 'checked', '[SP-007-25]', 'Sample Project Poster Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])
                            +projectBoxMeta('orange', 'checked', '[SP-008-25]', 'Sample Project Poster Lorem Ipsum Set Dolor', ['Project History', 'Billing History'])

        include ../partials/footer.pug
        +footer()