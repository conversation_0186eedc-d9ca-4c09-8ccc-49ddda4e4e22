<?php

namespace App\Livewire\Admin;

use App\Models\Project;
use App\Models\Timeline;
use App\Models\Category;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class ProjectListView extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $page = 1;
    public $timelineFilter = '';
    public $categoryFilter = '';
    public $sortColumn = 'name';
    public $sortDirection = 'asc';

    protected $queryString = [
        'page' => ['except' => 1],
        'timelineFilter' => ['except' => ''],
        'categoryFilter' => ['except' => ''],
        'sortColumn' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
    ];

    protected function paginateCollection($collection, $perPage = 10)
    {
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;
        $items = $collection->forPage($page, $perPage)->values();
        
        return new LengthAwarePaginator(
            $items,
            $collection->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }

    public function updatingTimelineFilter()
    {
        $this->resetPage();
    }

    public function updatingCategoryFilter()
    {
        $this->resetPage();
    }

    public function updatingSortColumn()
    {
        $this->resetPage();
    }

    public function updatingSortDirection()
    {
        $this->resetPage();
    }

    public function sortBy($column)
    {
        if ($this->sortColumn === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortColumn = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function render()
    {
        // Create a base query with filters
        $baseQuery = Project::query();

        if ($this->timelineFilter && $this->timelineFilter !== '') {
            $baseQuery->where('projects.timeline_id', $this->timelineFilter);
        }

        if ($this->categoryFilter && $this->categoryFilter !== '') {
            $baseQuery->where('projects.category_id', $this->categoryFilter);
        }

        // Get the IDs of the filtered projects
        $projectIds = $baseQuery->pluck('projects.id')->toArray();

        // Create a new query to fetch projects with sorting
        $query = Project::with(['timeline', 'phases', 'client', 'category']);

        // Only apply the whereIn if we have project IDs and the filter is active
        if (!empty($projectIds) && ($this->timelineFilter || $this->categoryFilter)) {
            $query->whereIn('projects.id', $projectIds);
        } else if ($this->timelineFilter || $this->categoryFilter) {
            // If filters are active but no projects found, return empty result
            return view('livewire.admin.project-list-view', [
                'projects' => $this->paginateCollection(collect()),
                'timelines' => Timeline::all(),
                'categories' => Category::all()
            ]);
        }

        // Handle different sorting options
        if ($this->sortColumn === 'client_name') {
            $defaultValue = $this->sortDirection === 'asc' ? 'ZZZZZZ' : '';
            $query->select('projects.*', 'clients.name as client_sort_name')
                ->leftJoin('clients', 'projects.client_id', '=', 'clients.id')
                ->orderByRaw("IFNULL(clients.name, '$defaultValue') {$this->sortDirection}");
        } elseif ($this->sortColumn === 'timeline_name') {
            $defaultValue = $this->sortDirection === 'asc' ? 'ZZZZZZ' : '';
            $query->select('projects.*', 'timeline.name as timeline_sort_name')
                ->leftJoin('timeline', 'projects.timeline_id', '=', 'timeline.id')
                ->orderByRaw("IFNULL(timeline.name, '$defaultValue') {$this->sortDirection}");
        } elseif ($this->sortColumn === 'project_target' || $this->sortColumn === 'project_duration') {
            // For project completion, we need to sort by the project_target of the last phase (highest order)
            $defaultDate = $this->sortDirection === 'asc' ? '9999-12-31' : '0000-01-01';
            $query->select('projects.*')
                ->addSelect(DB::raw("(
                    SELECT IFNULL(pp.project_target, '$defaultDate')
                    FROM project_phase pp
                    LEFT JOIN phases p ON pp.phase_id = p.id
                    WHERE pp.project_id = projects.id
                    ORDER BY p.order DESC
                    LIMIT 1
                ) as completion_date"))
                ->orderBy('completion_date', $this->sortDirection);
        } else if ($this->sortColumn === 'phase_completion') {
            // Sort by the project_target date of the current phase (phase_id in projects table)
            $defaultDate = $this->sortDirection === 'asc' ? '9999-12-31' : '0000-01-01';
            $query->select('projects.*')
                ->leftJoin('project_phase', function ($join) {
                    $join->on('projects.id', '=', 'project_phase.project_id')
                        ->on('projects.phase_id', '=', 'project_phase.phase_id');
                })
                ->addSelect(DB::raw("IFNULL(project_phase.project_target, '$defaultDate') as phase_completion_date"))
                ->orderBy('phase_completion_date', $this->sortDirection);
        } else if ($this->sortColumn === 'category_id') {
            // Sort by category name by joining with the categories table
            $defaultValue = $this->sortDirection === 'asc' ? 'ZZZZZZ' : '';
            $query->select('projects.*', 'categories.name as category_sort_name')
                ->leftJoin('categories', 'projects.category_id', '=', 'categories.id')
                ->orderByRaw("IFNULL(categories.name, '$defaultValue') {$this->sortDirection}");
        } else {
            // Default sorting by project columns
            $query->select('projects.*')
                ->orderBy("projects.{$this->sortColumn}", $this->sortDirection);
        }

        $projects = $query->paginate(10);
        $timelines = Timeline::all();
        $categories = Category::all();

        return view('livewire.admin.project-list-view', compact('projects', 'timelines', 'categories'));
    }
}
