@extends('layout.app')
@section('title', 'Dashboard')
@section('content')

<section class="client-header main-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="meta d-flex justify-content-between align-items-center">
            <div class="copy">
                <h1 class="text-white mb-0">
                    Welcome to your brand hub,<br />
                    <i>{{$user->name}}</i>
                </h1>
            </div>
            <div class="page-links">
                <span class="p me-2">Show Projects by:</span>
                <a href="{{ route('brand.projects.by.client') }}" class="btn btn-link {{ request()->routeIs('brand.projects.by.client') ? 'active' : '' }}">Client</a>
                <a href="{{ route('brand.dashboard') }}" class="btn btn-link {{ request()->routeIs('brand.dashboard') ? 'active' : '' }}">Phase</a>
            </div>
        </div>
    </div>
</section>

<section class="client-project pt-0">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        
        <div class="project-row overflow-x-auto d-flex">
            @foreach ($clientsWithProjects as $client)
                <div class="project-column mb-5">
                    <h2 class="text-uppercase">{{$client->name}}</h2>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="project-list">

                        {{-- Define Phase Projects --}}
                        @foreach($client->projectInDefinePhase as $project)
                            <div class="meta-project d-flex orange hover">
                                <div class="icon-wrap d-flex align-items-center justify-content-center me-3">
                                    <div class="progress-bar" data-steps="{{ $project->progress_percentage }}" style="--value: {{ $project->progress_percentage }}"></div>
                                    <div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/define-project-icon.svg')}}" alt="" /></div>
                                    <div class="over">
                                        <h2 class="mb-0">{{ $project->progress_percentage }}%</h2>
                                    </div>
                                </div>
                                <div class="copy">
                                    <h2 class="mb-1"><a href="track-project.html">[{{ $project->job_code ?? 'N/A' }}] - PHASE 1/5</a></h2>
                                    <p class="text-white">{{ $project->name }}</p>
                                </div>
                            </div>
                        @endforeach

                        {{-- Content Phase Projects --}}
                        @foreach($client->projectInContentPhase as $project)
                            <div class="meta-project d-flex green hover">
                                <div class="icon-wrap d-flex align-items-center justify-content-center me-3">
                                    <div class="progress-bar" data-steps="{{ $project->progress_percentage }}" style="--value: {{ $project->progress_percentage }}"></div>
                                    <div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/content-project-icon.svg')}}" alt="" /></div>
                                    <div class="over">
                                        <h2 class="mb-0">{{ $project->progress_percentage }}%</h2>
                                    </div>
                                </div>
                                <div class="copy">
                                    <h2 class="mb-1"><a href="track-project.html">[{{ $project->job_code ?? 'N/A' }}] - PHASE 2/5</a></h2>
                                    <p class="text-white">{{ $project->name }}</p>
                                </div>
                            </div>
                        @endforeach

                        {{-- Design Phase Projects --}}
                        @foreach($client->projectInDesignPhase as $project)
                            <div class="meta-project d-flex pink hover">
                                <div class="icon-wrap d-flex align-items-center justify-content-center me-3">
                                    <div class="progress-bar" data-steps="{{ $project->progress_percentage }}" style="--value: {{ $project->progress_percentage }}"></div>
                                    <div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/design-project-icon.svg')}}" alt="" /></div>
                                    <div class="over">
                                        <h2 class="mb-0">{{ $project->progress_percentage }}%</h2>
                                    </div>
                                </div>
                                <div class="copy">
                                    <h2 class="mb-1"><a href="track-project.html">[{{ $project->job_code ?? 'N/A' }}] - PHASE 3/5</a></h2>
                                    <p class="text-white">{{ $project->name }}</p>
                                </div>
                            </div>
                        @endforeach

                        {{-- Code Phase Projects --}}
                        @foreach($client->projectInCodePhase as $project)
                            <div class="meta-project d-flex purple hover">
                                <div class="icon-wrap d-flex align-items-center justify-content-center me-3">
                                    <div class="progress-bar" data-steps="{{ $project->progress_percentage }}" style="--value: {{ $project->progress_percentage }}"></div>
                                    <div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/code-project-icon.svg')}}" alt="" /></div>
                                    <div class="over">
                                        <h2 class="mb-0">{{ $project->progress_percentage }}%</h2>
                                    </div>
                                </div>
                                <div class="copy">
                                    <h2 class="mb-1"><a href="track-project.html">[{{ $project->job_code ?? 'N/A' }}] - PHASE 4/5</a></h2>
                                    <p class="text-white">{{ $project->name }}</p>
                                </div>
                            </div>
                        @endforeach

                        {{-- Deploy Phase Projects --}}
                        @foreach($client->projectInDeployPhase as $project)
                            <div class="meta-project d-flex orange hover">
                                <div class="icon-wrap d-flex align-items-center justify-content-center me-3">
                                    <div class="progress-bar" data-steps="{{ $project->progress_percentage }}" style="--value: {{ $project->progress_percentage }}"></div>
                                    <div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/deploy-project-icon.svg')}}" alt="" /></div>
                                    <div class="over">
                                        <h2 class="mb-0">{{ $project->progress_percentage }}%</h2>
                                    </div>
                                </div>
                                <div class="copy">
                                    <h2 class="mb-1"><a href="track-project.html">[{{ $project->job_code ?? 'N/A' }}] - PHASE 5/5</a></h2>
                                    <p class="text-white">{{ $project->name }}</p>
                                </div>
                            </div>
                        @endforeach

                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>

@endsection