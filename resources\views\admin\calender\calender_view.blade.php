@extends('admin.layouts.app')
@section('title', 'Calendar')
@section('content')


<div>
    <!-- Notification container for AJAX responses -->
    <div id="notification-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999; width: 350px;"></div>

    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">

                @if($isAdmin)
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal
                </a>
                @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('dashboard') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Dashboard
                </a>
                @endif

            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project Regroup- <i>Calendar Overview</i></h1>
                </div>

            </div>
        </div>
    </section>

    <section class="client-project pt-0">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
             @if($isAdmin)
            <div class="view-options d-flex mb-4">
                <a href="{{ route('admin-projects-list-view') }}" class="me-3">List View</a>
                <a href="{{ route('admin-calender') }}" class="active me-3">Calendar View</a>

                <a href="{{ route('resource-allocation') }}">Resource Allocation</a>
            </div>
            @endif
        </section>



            <div id="calendar"></div>

            <!-- Calendar Legend -->
            <div class="calendar-legend mt-4 mb-3">
                <h5>Legend</h5>
                <div class="d-flex flex-wrap">
                    <div class="legend-item me-4 mb-2">
                        <span class="color-box" style="background-color: #0066cc;"></span>
                        <span>Project Completion</span>
                    </div>
                    <div class="legend-item me-4 mb-2">
                        <span class="color-box" style="background-color: #ff4c00;"></span>
                        <span>Phase 1 Completion</span>
                    </div>
                    <div class="legend-item me-4 mb-2">
                        <span class="color-box" style="background-color: #65ccb0;"></span>
                        <span>Phase 2 Completion</span>
                    </div>
                    <div class="legend-item me-4 mb-2">
                        <span class="color-box" style="background-color: #f45689;"></span>
                        <span>Phase 3 Completion</span>
                    </div>
                    <div class="legend-item me-4 mb-2">
                        <span class="color-box" style="background-color: #a15cd4;"></span>
                        <span>Phase 4 Completion</span>
                    </div>
                    <div class="legend-item me-4 mb-2">
                        <span class="color-box" style="background-color: #ffc107;"></span>
                        <span>Company Holiday</span>
                    </div>
                    <div class="legend-item me-4 mb-2">
                        <span class="color-box" style="background-color: #28a745;"></span>
                        <span>Personal Leave</span>
                    </div>
                </div>
            </div>

            <!-- Add Holiday Button (visible to all users) -->
            {{-- <div class="text-end mt-3 mb-4">
                <button type="button" class="btn btn-orange" data-bs-toggle="modal" data-bs-target="#addHolidayModal">
                    @if($isAdmin)
                        Add Holiday
                    @else
                        Add Personal Leave
                    @endif
                </button>

            </div> --}}
        </div>

    <!-- Add Holiday Modal -->
    <div class="modal fade" id="addHolidayModal" tabindex="-1" aria-labelledby="addHolidayModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="background: linear-gradient(135deg, rgb(250, 250, 250) 0%, rgb(240, 240, 240) 100%); border: 1px solid rgba(255, 76, 0, 0.3); border-radius: 16px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="border-bottom: 1px solid rgba(255, 76, 0, 0.2);">
                    <h5 class="modal-title" id="addHolidayModalLabel" style="color: #111;">
                        @if($isAdmin)
                            Add Holiday
                        @else
                            Add Personal Leave
                        @endif
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addHolidayForm" action="{{ route('holidays.store.ajax') }}" method="POST">
                    @csrf
                    <div class="modal-body">
                        @if(!$isAdmin)
                        <div class="alert alert-info">
                            Use this form to mark your personal leave dates. These will be visible only to you and administrators.
                        </div>
                        @endif
                        <div id="holidayFormErrors" class="alert alert-danger" style="display: none;"></div>
                        <div class="mb-3">
                            <label for="holiday_title" class="form-label" style="color: #111; font-weight: 600;">Title</label>
                            <input type="text" class="form-control" id="holiday_title" name="title" required
                                placeholder="{{ $isAdmin ? 'Holiday name' : 'Reason for leave' }}" style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;">
                        </div>
                        <div class="mb-3">
                            <label for="holiday_description" class="form-label" style="color: #111; font-weight: 600;">Description</label>
                            <textarea class="form-control" id="holiday_description" name="description" rows="3"
                                placeholder="{{ $isAdmin ? 'Holiday details' : 'Additional details about your leave' }}" style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="holiday_start_date" class="form-label" style="color: #111; font-weight: 600;">Start Date</label>
                            <input type="date" class="form-control" id="holiday_start_date" name="start_date" required style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;">
                        </div>
                        <div class="mb-3">
                            <label for="holiday_end_date" class="form-label" style="color: #111; font-weight: 600;">End Date (Optional)</label>
                            <input type="date" class="form-control" id="holiday_end_date" name="end_date" style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;">
                        </div>
                        @if($isAdmin)
                        <div class="mb-3">
                            <label class="form-label" style="color: #111; font-weight: 600;">Holiday Type</label>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="holiday_is_global" name="is_global" value="1">
                                <label class="form-check-label" for="holiday_is_global" style="color: #111;">Company-wide Holiday</label>
                            </div>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="holiday_is_personal" name="is_global" value="0" checked>
                                <label class="form-check-label" for="holiday_is_personal" style="color: #111;">Personal Leave</label>
                            </div>
                        </div>

                        <!-- Region selection (only visible when company-wide holiday is selected) -->
                        <div class="mb-3" id="region_selection" style="display: none;">
                            <label class="form-label" style="color: #111; font-weight: 600;">Holiday Region</label>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="holiday_region_indian" name="region" value="indian">
                                <label class="form-check-label" for="holiday_region_indian" style="color: #111;">Indian Holiday</label>
                            </div>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="holiday_region_us" name="region" value="us">
                                <label class="form-check-label" for="holiday_region_us" style="color: #111;">US Holiday</label>
                            </div>
                        </div>
                        @endif
                    </div>
                    <div class="modal-footer" style="border-top: 1px solid rgba(255, 76, 0, 0.2);">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="background-color: #6c757d; border: none;">Close</button>
                        <button type="submit" class="btn" id="saveHolidayBtn" style="background-color: #ff4c00; color: white; border: none;">Save Holiday</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Holiday Modal -->
    <div class="modal fade" id="editHolidayModal" tabindex="-1" aria-labelledby="editHolidayModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="background: linear-gradient(135deg, rgb(250, 250, 250) 0%, rgb(240, 240, 240) 100%); border: 1px solid rgba(255, 76, 0, 0.3); border-radius: 16px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="border-bottom: 1px solid rgba(255, 76, 0, 0.2);">
                    <h5 class="modal-title" id="editHolidayModalLabel" style="color: #111;">
                        @if($isAdmin)
                            Edit Holiday
                        @else
                            Edit Personal Leave
                        @endif
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="{{ route('holidays.update') }}" method="POST">
                    @csrf
                    @method('PUT')
                    <input type="hidden" id="edit_holiday_id" name="id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_holiday_title" class="form-label" style="color: #111; font-weight: 600;">Title</label>
                            <input type="text" class="form-control" id="edit_holiday_title" name="title" required style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;">
                        </div>
                        <div class="mb-3">
                            <label for="edit_holiday_description" class="form-label" style="color: #111; font-weight: 600;">Description</label>
                            <textarea class="form-control" id="edit_holiday_description" name="description" rows="3" style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit_holiday_start_date" class="form-label" style="color: #111; font-weight: 600;">Start Date</label>
                            <input type="date" class="form-control" id="edit_holiday_start_date" name="start_date" required style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;">
                        </div>
                        <div class="mb-3">
                            <label for="edit_holiday_end_date" class="form-label" style="color: #111; font-weight: 600;">End Date (Optional)</label>
                            <input type="date" class="form-control" id="edit_holiday_end_date" name="end_date" style="border: 1px solid rgba(255, 76, 0, 0.3); background-color: #fff; color: #111;">
                        </div>
                        @if($isAdmin)
                        <div class="mb-3">
                            <label class="form-label" style="color: #111; font-weight: 600;">Holiday Type</label>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="edit_holiday_is_global" name="is_global" value="1">
                                <label class="form-check-label" for="edit_holiday_is_global" style="color: #111;">Company-wide Holiday</label>
                            </div>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="edit_holiday_is_personal" name="is_global" value="0" checked>
                                <label class="form-check-label" for="edit_holiday_is_personal" style="color: #111;">Personal Leave</label>
                            </div>
                        </div>

                        <!-- Region selection (only visible when company-wide holiday is selected) -->
                        <div class="mb-3" id="edit_region_selection" style="display: none;">
                            <label class="form-label" style="color: #111; font-weight: 600;">Holiday Region</label>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="edit_holiday_region_indian" name="region" value="indian">
                                <label class="form-check-label" for="edit_holiday_region_indian" style="color: #111;">Indian Holiday</label>
                            </div>
                            <div class="form-check">
                                <input type="radio" class="form-check-input" id="edit_holiday_region_us" name="region" value="us">
                                <label class="form-check-label" for="edit_holiday_region_us" style="color: #111;">US Holiday</label>
                            </div>
                        </div>
                        @endif
                    </div>
                    <div class="modal-footer" style="border-top: 1px solid rgba(255, 76, 0, 0.2);">
                        <button type="button" class="btn" id="deleteHolidayBtn" style="background-color: #dc3545; color: white; border: none;">Delete</button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="background-color: #6c757d; border: none;">Close</button>
                        <button type="submit" class="btn" style="background-color: #ff4c00; color: white; border: none;">Update Holiday</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Holiday Form (hidden) -->
    <form id="deleteHolidayForm" action="{{ route('holidays.destroy') }}" method="POST" style="display: none;">
        @csrf
        @method('DELETE')
        <input type="hidden" id="delete_holiday_id" name="id">
    </form>

    <!-- Project/Phase Event Details Modal -->
    <div class="modal fade" id="eventDetailsModal" tabindex="-1" aria-labelledby="eventDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="background: linear-gradient(135deg, rgb(250, 250, 250) 0%, rgb(240, 240, 240) 100%); border: 1px solid rgba(255, 76, 0, 0.3); border-radius: 16px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="border-bottom: 1px solid rgba(255, 76, 0, 0.2);">
                    <h5 class="modal-title" id="eventDetailsModalLabel" style="color: #111;">Event Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <h5 id="event_title" style="color: #111; font-weight: 600;"></h5>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" style="color: #111; font-weight: 600;">Date</label>
                        <p id="event_date" style="color: #111;"></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" style="color: #111; font-weight: 600;">Description</label>
                        <p id="event_description" style="color: #111;"></p>
                    </div>
                    <div class="mb-3" id="event_client_container">
                        <label class="form-label" style="color: #111; font-weight: 600;">Client</label>
                        <p id="event_client" style="color: #111;"></p>
                    </div>
                </div>
                <div class="modal-footer" style="border-top: 1px solid rgba(255, 76, 0, 0.2);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="background-color: #6c757d; border: none;">Close</button>
                </div>
            </div>
        </div>
    </div>

    <section class="ask-question">
        <div class="container-xxl">
            <div class="d-flex align-items-center justify-content-between pb-4">
                <div class="head">
                    <h2 class="mb-0">
                        Have a question?<br />
                        <i>We're here to help.</i>
                    </h2>
                </div>
                <div class="cta-row">
                    <a class="cta link text-decoration-none text-white" href="#">GET EMAIL UPDATES</a>
                    <a class="cta link text-decoration-none text-white" href="#">NEED HELP? CONTACT</a>
                </div>
            </div>
            <hr class="mt-0 mt-4 border-white" />
        </div>
    </section>
</div>





@endsection

@push('styles')

<style>
.btn-orange {
    background-color: white;
    color: black;
    border: 2px solid #ff4c00;
    width: 200px;
    border-radius: 30px;
    padding: 10px 20px;
    text-align: center;
    font-weight: 600;
}

.btn-orange:hover {
    background-color: #ff4c00;
    border-color: #ff4c00;
}

    .calendar-legend {
        background-color: rgba(255, 255, 255, 0.1);
        padding: 15px;
        border-radius: 5px;
    }

    .calendar-legend h5 {
        color: #fff;
        margin-bottom: 10px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        color: #fff;
    }

    .color-box {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 5px;
        border-radius: 3px;
    }

    .loading-blur {
        opacity: 0.6;
        filter: blur(1px);
        pointer-events: none;
    }

    .project-list-container {
        position: relative;
        min-height: 200px;
    }

    .project-list-container.loading::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(3px);
        z-index: 10;
    }

    /* Table styling */
    .projects-table {
        width: 100%;
        color: #fff;
        border-collapse: separate;
        border-spacing: 0;
    }

    .projects-table th {
        padding: 15px 10px;
        font-family: "Futura Std", serif;
        font-weight: 700;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        text-align: left;
        cursor: pointer;
    }

    .projects-table th:hover {
        color: #ff6600;
    }

    .projects-table td {
        padding: 15px 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .projects-table tr:hover td {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .filter-section label {
        color: #fff;
        font-weight: 500;
        margin-bottom: 8px;
    }

    /* View options styling */
    .view-options a {
        color: #fff;
        text-decoration: none;
        padding: 8px 15px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .view-options a:hover, .view-options a.active {
        background-color: #ff4c00;
        border-color: #ff4c00;
    }

    /* Pagination styling */
    .pages {
        display: flex;
        align-items: center;
    }

    .pages span {
        color: rgba(255, 255, 255, 0.7);
        margin-right: 10px;
    }

    .pages a {
        color: #fff;
        text-decoration: none;
        margin: 0 5px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
    }

    .pages a.active {
        background-color: #ff6600;
    }

    .pages a:hover:not(.active) {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Custom FullCalendar Styling */
    #calendar {
        background-color: #111;
        border-radius: 5px;
        padding: 15px;
        font-family: "Inter", sans-serif;
    }

    /* Calendar Header Styling */
    .fc-toolbar {
        margin-bottom: 20px !important;
    }

    .fc-toolbar h2 {
        color: #fff !important;
        font-family: "Futura Std", serif !important;
        font-size: 24px !important;
    }

    /* Calendar Button Styling */
    .fc-button {
        background-color: #282828 !important;
        color: black !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        box-shadow: none !important;
        text-shadow: none !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
    }

    .fc-button:hover {
        background-color: #ff4c00 !important;
        border-color: #ff4c00 !important;
    }

    .fc-state-active,
    .fc-state-down {
        background-color: #ff4c00 !important;
        border-color: #ff4c00 !important;
    }

    /* Calendar Table Styling */
    .fc-day-header {
        background-color: #282828 !important;
        color: #fff !important;
        font-family: "Futura Std", serif !important;
        font-weight: 700 !important;
        padding: 10px 0 !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .fc-day {
        background-color: #1a1a1a !important;
        border-color: rgba(255, 255, 255, 0.05) !important;
    }

    .fc-day-number {
        color: #fff !important;
        padding: 8px !important;
    }

    .fc-day-top {
        background-color: #1a1a1a !important;
    }

    .fc-today {
        background-color: rgba(241, 165, 51, 0.1) !important;
    }

    .fc-today-button{
        background-color: #ff4c00 !important;
        border-color: #ff4c00 !important;
    }

    /* Event Styling */
    .fc-event {
        border-radius: 3px !important;
        border: none !important;
        padding: 3px 5px !important;
        font-size: 12px !important;
    }

    .fc-title {
        font-weight: 600 !important;
    }

    .fc-title small {
        font-weight: 400 !important;
        opacity: 0.8 !important;
    }

    /* Other Month Days */
    .fc-other-month {
        background-color: #151515 !important;
        opacity: 0.7 !important;
    }

    /* Week/Day View */
    .fc-time-grid-container {
        background-color: #1a1a1a !important;
    }

    .fc-axis {
        background-color: #282828 !important;
        color: #fff !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
    }

    .fc-time-grid .fc-slats td {
        border-color: rgba(255, 255, 255, 0.05) !important;
    }

    /* More Link */
    .fc-more {
        color: #ff4c00 !important;
    }

    .fc-more-cell {
        background-color: #1a1a1a !important;
    }

    /* Popover */
    .fc-popover {
        background-color: #282828 !important;
        border-color: rgba(255, 255, 255, 0.1) !important;
        border-radius: 5px !important;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5) !important;
    }

    .fc-popover .fc-header {
        background-color: #1a1a1a !important;
        color: #fff !important;
        border-top-left-radius: 5px !important;
        border-top-right-radius: 5px !important;
        padding: 8px !important;
    }

    .fc-popover .fc-header .fc-close {
        color: #fff !important;
        opacity: 0.7 !important;
        transition: opacity 0.2s ease !important;
    }

    .fc-popover .fc-header .fc-close:hover {
        opacity: 1 !important;
    }

    /* Today highlight */
    .fc-today-button {
        font-weight: 600 !important;
    }

    /* Empty cells hover effect */
    .fc-day:hover {
        background-color: rgba(255, 76, 0, 0.05) !important;
        cursor: pointer;
    }

    /* Week numbers */
    .fc-week-number {
        color: rgba(255, 255, 255, 0.5) !important;
        font-size: 0.9em !important;
    }

    /* List view styling */
    .fc-list-heading {
        background-color: #282828 !important;
        color: #fff !important;
    }

    .fc-list-item:hover td {
        background-color: rgba(255, 76, 0, 0.1) !important;
    }

    .fc-list-item-time {
        color: rgba(255, 255, 255, 0.7) !important;
    }

    /* Loading indicator */
    .fc-loading {
        background-color: rgba(17, 17, 17, 0.7) !important;
        color: #fff !important;
    }
</style>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.4.0/fullcalendar.css" />

@endpush


@push('script')

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fullcalendar/3.4.0/fullcalendar.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
    $(document).ready(function() {
        const startDateInput = document.getElementById('holiday_start_date');
    const endDateInput = document.getElementById('holiday_end_date');

    function setEndDateDefaultMonthAndPreserveValue() {
        // If the end date input already has a value, do NOT mess with it.
        // The native date picker will automatically open to the month of the current value.
        if (endDateInput.value) {
            return; // Exit the function, preserve existing date.
        }

        let targetDate;

        if (startDateInput.value) {
            // If start date is selected, use its month
            targetDate = new Date(startDateInput.value);
        } else {
            // If start date is not selected, use current month
            targetDate = new Date();
        }

        const year = targetDate.getFullYear();
        const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');

        // Temporarily set the end date input's value to the first day of the target month
        // This will make the calendar open to that month
        endDateInput.value = `${year}-${month}-01`;

        // Immediately clear the value after setting, so it doesn't appear in the input
        // unless the user selects a date. This is the trick for native date pickers.
        setTimeout(() => {
            endDateInput.value = '';
        }, 0);
    }

    // When the end date input is clicked
    endDateInput.addEventListener('click', setEndDateDefaultMonthAndPreserveValue);

    // Optional: Also update the end date calendar if the start date changes
    // (This will only affect the *next* time endDateInput is clicked if it's empty)
    startDateInput.addEventListener('change', setEndDateDefaultMonthAndPreserveValue);

    // --- Additional consideration for the native date picker ---
    // If you want the end date to default to the start date if the start date is picked
    // and the end date is still empty, you might want to consider this:
    startDateInput.addEventListener('change', function() {
        if (startDateInput.value && !endDateInput.value) {
            endDateInput.value = startDateInput.value;
        }
    });
    
        const phaseColors = {
            1: '#ff4c00',
            2: '#65ccb0',
            3: '#f45689',
            4: '#a15cd4',
            5: '#ff4c00'
        };


        const projectCompletionColor = '#0066cc';


        const holidayColors = {
            global: '#ffc107',
            personal: '#28a745'
        };


        let events = [];


        @foreach($projects as $project)
            @if($project->phases && $project->phases->isNotEmpty())
                @php

                    $lastPhase = $project->phases->sortByDesc('order')->first();
                    $completionDate = $lastPhase->pivot->project_target;
                @endphp

                @if($completionDate)
                    events.push({
                        title: '{{ $project->name }} (Completion)',
                        start: '{{ $completionDate }}',
                        allDay: true,
                        color: projectCompletionColor,
                        description: 'Project completion date for {{ str_replace("'", "\\'", $project->name) }}',
                        client: '{{ $project->client ? $project->client->name : "N/A" }}'
                    });
                @endif
            @endif
        @endforeach


        @foreach($projects as $project)
            @foreach($project->phases as $phase)
                @if(isset($phase->pivot) && $phase->pivot->project_target)
                    events.push({
                        title: '{{ $project->name }} (Phase: {{ $phase->name }})',
                        start: '{{ $phase->pivot->project_target }}',
                        allDay: true,
                        color: phaseColors[{{ $phase->order ?: 1 }}],
                        description: 'Phase {{ str_replace("'", "\\'", $phase->name) }} completion for {{ str_replace("'", "\\'", $project->name) }}',
                        client: '{{ $project->client ? $project->client->name : "N/A" }}'
                    });
                @endif
            @endforeach
        @endforeach


        @foreach($holidays as $holiday)
            events.push({
                title: '{{ !$holiday->is_global && $holiday->user ? $holiday->user->name . " is out" : $holiday->title }}',
                start: '{{ $holiday->start_date }}',
                @if($holiday->end_date)
                end: '{{ \Carbon\Carbon::parse($holiday->end_date)->addDay()->toDateString() }}'/* calendar styling shows start-date to end-date - 1 so increase one day to display start-date to end-date in calendar */,
                @endif
                allDay: true,
                color: {{ $holiday->is_global ? 'holidayColors.global' : 'holidayColors.personal' }},
                description: '{{ !$holiday->is_global && $holiday->user ? str_replace("'", "\\'", $holiday->user->name) . " is on personal leave" . ($holiday->title ? " - " . str_replace("'", "\\'", $holiday->title) : "") : str_replace("'", "\\'", $holiday->description ?: "") }}',
                holiday: true,
                holidayId: {{ $holiday->id }},
                isGlobal: {{ $holiday->is_global ? 'true' : 'false' }},
                userId: {{ $holiday->user_id ?: 'null' }},
                userName: '{{ $holiday->user ? $holiday->user->name : "" }}'
            });
        @endforeach


        $('#calendar').fullCalendar({
            header: {
                left: 'prev,next today',
                center: 'title',
                right: 'month,agendaWeek,agendaDay'
            },
            defaultDate: '{{ date("Y-m-d") }}',
            navLinks: true,
            editable: {{ $isAdmin ? 'true' : 'false' }},
            eventLimit: true,
            events: events,
            themeSystem: 'standard',
            timeFormat: 'h:mm A',
            displayEventTime: false,
            displayEventEnd: false,
            eventRender: function(event, element) {

                if (event.description) {
                    element.attr('title', event.description);
                }


                if (event.client) {
                    element.find('.fc-title').append('<br/><small>' + event.client + '</small>');
                }


                if (event.holiday || event.allDay) {
                    element.find('.fc-time').remove();
                }


                element.css('transition', 'all 0.2s ease');

                element.hover(
                    function() {
                        $(this).css('transform', 'scale(1.05)');
                        $(this).css('z-index', '10');
                        $(this).css('box-shadow', '0 4px 8px rgba(0, 0, 0, 0.3)');
                    },
                    function() {
                        $(this).css('transform', 'scale(1)');
                        $(this).css('z-index', '1');
                        $(this).css('box-shadow', 'none');
                    }
                );
            },
            dayClick: function(date, jsEvent, view) {
                if ({{ $isAdmin ? 'true' : 'false' }} || view.name === 'month') {
                    $('#addHolidayModal').modal('show');
                    $('#holiday_start_date').val(date.format('YYYY-MM-DD'));
                    $('#holiday_end_date').val('');
                }
            },
            eventClick: function(calEvent, jsEvent, view) {
                if (calEvent.holiday) {
                    // Handle holiday events
                    const currentUserId = {{ Auth::id() ?: 'null' }};
                    if ({{ $isAdmin ? 'true' : 'false' }} || calEvent.userId === currentUserId || calEvent.userId === null) {
                        $('#editHolidayModal').modal('show');
                        $('#edit_holiday_id').val(calEvent.holidayId);
                        // For personal leaves, we need to extract the original title
                        if (!calEvent.isGlobal && calEvent.userName) {
                            // If it's a personal leave, the title might be in the description
                            const descParts = calEvent.description.split(' - ');
                            if (descParts.length > 1) {
                                $('#edit_holiday_title').val(descParts[1]);
                            } else {
                                $('#edit_holiday_title').val('');
                            }
                        } else {
                            $('#edit_holiday_title').val(calEvent.title);
                        }
                        $('#edit_holiday_description').val(calEvent.description);
                        $('#edit_holiday_start_date').val(moment(calEvent.start).format('YYYY-MM-DD'));
                        if (calEvent.end) {
                            /* 
                                Calendar excludes one day from end-date and shows start-date to end-date-1 so I have added one day to end-date in this file as follows to show correct in the calendar styling:
                                {{-- 
                                    @if($holiday->end_date)
                                    end: '{{ \Carbon\Carbon::parse($holiday->end_date)->addDay()->toDateString() }}',
                                    @endif 
                                --}}
                                But it will also add more day to the end-date input in calendar modal.
                                To display the correct end-date in the modal end-date input, I have also reduced 1 day here.
                            */
                            $('#edit_holiday_end_date').val(moment(calEvent.end).subtract(1, 'days').format('YYYY-MM-DD'));
                        } else {
                            $('#edit_holiday_end_date').val('');
                        }

                        // Set the correct radio button based on the holiday type
                        if (calEvent.isGlobal) {
                            $('#edit_holiday_is_global').prop('checked', true);
                            $('#edit_holiday_is_personal').prop('checked', false);

                            // Show region selection for company-wide holidays
                            $('#edit_region_selection').show();

                            // Set the correct region radio button
                            if (calEvent.region === 'indian') {
                                $('#edit_holiday_region_indian').prop('checked', true);
                                $('#edit_holiday_region_us').prop('checked', false);
                            } else if (calEvent.region === 'us') {
                                $('#edit_holiday_region_indian').prop('checked', false);
                                $('#edit_holiday_region_us').prop('checked', true);
                            } else {
                                // If no region is set, default to none selected
                                $('#edit_holiday_region_indian').prop('checked', false);
                                $('#edit_holiday_region_us').prop('checked', false);
                            }
                        } else {
                            $('#edit_holiday_is_global').prop('checked', false);
                            $('#edit_holiday_is_personal').prop('checked', true);
                            $('#edit_region_selection').hide();
                        }

                        // Show delete button only for admins or the holiday owner
                        if ({{ $isAdmin ? 'true' : 'false' }} || calEvent.userId === currentUserId) {
                            $('#deleteHolidayBtn').show();
                        } else {
                            $('#deleteHolidayBtn').hide();
                        }
                    }
                } else {
                    // Handle project and phase events
                    $('#eventDetailsModal').modal('show');
                    $('#event_title').text(calEvent.title);

                    // Format the date
                    let dateText = moment(calEvent.start).format('MMMM D, YYYY');
                    if (calEvent.end) {
                        dateText += ' - ' + moment(calEvent.end).format('MMMM D, YYYY');
                    }
                    $('#event_date').text(dateText);

                    // Set description
                    $('#event_description').text(calEvent.description || 'No description available');

                    // Set client info if available
                    if (calEvent.client) {
                        $('#event_client_container').show();
                        $('#event_client').text(calEvent.client);
                    } else {
                        $('#event_client_container').hide();
                    }
                }
            }
        });
    });

    // Handle delete holiday button click
    $('#deleteHolidayBtn').on('click', function() {
        if (confirm('Are you sure you want to delete this holiday?')) {
            const holidayId = $('#edit_holiday_id').val();
            $('#delete_holiday_id').val(holidayId);
            $('#deleteHolidayForm').submit();
        }
    });

    // Toggle region selection based on holiday type
    $('#holiday_is_global, #holiday_is_personal').on('change', function() {
        if ($('#holiday_is_global').is(':checked')) {
            $('#region_selection').show();
        } else {
            $('#region_selection').hide();
        }
    });

    // Toggle region selection in edit modal based on holiday type
    $('#edit_holiday_is_global, #edit_holiday_is_personal').on('change', function() {
        if ($('#edit_holiday_is_global').is(':checked')) {
            $('#edit_region_selection').show();
        } else {
            $('#edit_region_selection').hide();
        }
    });


    function adjustCalendarForScreenSize() {
        const windowWidth = $(window).width();

        if (windowWidth < 768) {

            $('#calendar').fullCalendar('changeView', 'listMonth');
            $('#calendar').fullCalendar('option', 'header', {
                left: 'prev,next',
                center: 'title',
                right: 'today'
            });
        } else if (windowWidth < 992) {

            $('#calendar').fullCalendar('option', 'header', {
                left: 'prev,next today',
                center: 'title',
                right: 'month,agendaWeek'
            });
        } else {

            $('#calendar').fullCalendar('option', 'header', {
                left: 'prev,next today',
                center: 'title',
                right: 'month,agendaWeek,agendaDay'
            });
        }
    }


    adjustCalendarForScreenSize();


    $(window).resize(function() {
        adjustCalendarForScreenSize();
    });


    $(document).on('click', '.fc-button', function() {
        $('#calendar').css('opacity', '0.6');
        setTimeout(function() {
            $('#calendar').css('opacity', '1');
        }, 300);
    });

    // Handle AJAX form submission for adding holidays
    $('#addHolidayForm').on('submit', function(e) {
        e.preventDefault();


        const saveBtn = $('#saveHolidayBtn');
        const originalBtnText = saveBtn.text();
        saveBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...');


        $('#holidayFormErrors').hide().empty();

        $.ajax({
            url: $(this).attr('action'),
            method: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {

                    $('#calendar').fullCalendar('renderEvent', response.eventData, true);


                    $('#addHolidayForm')[0].reset();
                    $('#addHolidayModal').modal('hide');


                    const notification = $('<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                        response.message +
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                        '</div>');

                    $('#notification-container').append(notification);
                    setTimeout(function() {
                        notification.alert('close');
                        // location.reload();
                    }, 5000);
                    
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorHtml = '<ul>';

                    $.each(errors, function(key, value) {
                        errorHtml += '<li>' + value + '</li>';
                    });

                    errorHtml += '</ul>';
                    $('#holidayFormErrors').html(errorHtml).show();
                } else {
                    $('#holidayFormErrors').html('An error occurred while saving the holiday. Please try again.').show();
                }
            },
            complete: function() {

                saveBtn.prop('disabled', false).text(originalBtnText);
            }
        });
    });
</script>


@endpush