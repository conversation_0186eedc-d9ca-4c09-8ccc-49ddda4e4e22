<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PermissionsTableSeeder extends Seeder
{
    public function run()
    {
        // Define the permission names for each resource
        $permissions = [
            //
            'permissions-view',
            'permission-create',
            'permission-edit',
            'permission-update',
            'permission-delete',

            // User permissions
            'users-view',
            'user-create',
            'user-edit',
            'user-update',
            'user-delete',

            // Company permissions
            'companies-view',
            'company-create',
            'company-edit',
            'company-update',
            'company-delete',

            // Project permissions
            'projects-view',
            'project-create',
            'project-edit',
            'project-update',
            'project-delete',

            // Task permissions
            'tasks-view',
            'task-create',
            'task-edit',
            'task-update',
            'task-delete',

            // Status permissions
            'status-view',
            'status-create',

            // Role permissions
            'roles-view',
            'role-create',
            'role-edit',
            'role-update',
            'role-delete',

            // clients Permissions
            'clients-view',
            'clients-create',
            'clients-edit',
            'clients-update',
            'clients-delete',

            // Brand Permissions
            'brands-view',
            'brands-create',
            'brands-edit',
            'brands-update',
            'brands-delete',

            // Category Permissions
            'categories-view',
            'categories-create',
            'categories-edit',
            'categories-update',
            'categories-delete',

            // Phase View
            'phases-view',
            'phases-create',
            'phases-edit',
            'phases-update',
            'phases-delete',

            // Dashboard Permissions
            'dashboard-view',

            //Team Permissions
            'team-view',
            'team-create',
            'team-update',
            'team-delete',

            // Statushub Permissions
            'statushub-view',
            'statushub-update',
            
        ];

        // Iterate through the permissions and insert them into the table
        foreach ($permissions as $permission) {
            DB::table('permissions')->insert([
                'name' => $permission,  // Insert the permission name
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }
}
