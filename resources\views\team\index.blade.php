@extends('layout.app')
@section('title', 'Team Members')
@section('content')

<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @endif
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Edit <i>Teams</i></h1>
            <div class="add-task ms-auto d-flex">
                <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0" href="{{ route('add-team-member') }}"
                    >NEW TEAM MEMBER<span class="img ms-2">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                            <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5"></path></svg></span
                ></a>
            </div>
        </div>
    </div>
</section>
<section class="team-dashboard pb-5">
    <div class="container-xxl">
        <div class="row gx-lg-5">
            @foreach ($teams as $team)
            <div class="col-md-4 team-col">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">{{ $team->name }}</h2>
                </div>
                <hr class="mt-0 mb-4 border-white" />
                <div class="team-list">
                    @foreach( $team->users as $user)
                    <div class="team-box mb-3 d-flex align-items-center">
                        <a class="d-flex align-items-center text-decoration-none me-4" href="{{ route('team-member-profile', ['team_member_id' => $user->id]) }}">
                            <div class="img"><img src="{{ set_user_image($user->profile_image) }}" alt="" /></div>
                            <div class="name">{{ $user->name }}</div></a
                        ><a class="cta-edit text-decoration-none ms-auto" href="{{ route('edit-team-member', ['team_member_id' => $user->id]) }}">Edit</a>
                    </div>
                    @endforeach
                </div>
            </div>
            @endforeach
        </div>
        <hr class="my-4" style="border-color: rgba(136, 136, 136, .4); opacity: 1;">
        <div class="row gx-lg-5">
            <div class="col-md-4 team-col">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">Unassigned Users</h2>
                </div>
                <hr class="mt-0 mb-4 border-white" />
                <div class="team-list">
                    @foreach( $usersWithoutTeam as $user)
                    <div class="team-box mb-3 d-flex align-items-center">
                        <a class="d-flex align-items-center text-decoration-none me-4" href="{{ route('team-member-profile', ['team_member_id' => $user->id]) }}">
                            <div class="img"><img src="{{ set_user_image($user->profile_image) }}" alt="" /></div>
                            <div class="name">{{ $user->name }}</div></a
                        ><a class="cta-edit text-decoration-none ms-auto" href="{{ route('edit-team-member', ['team_member_id' => $user->id]) }}">Edit</a>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>


@endsection