.header {
	isolation: isolate;
	position: relative;
	width: 100%;
	z-index: 1000;

	.header_float {
		padding-bottom: 15px;
		padding-top: 15px;
		z-index: 500;

		@media (min-width: 992px) {
			height: 120px;
			padding-bottom: 30px;
			padding-top: 30px;
		}
	}

	@media (max-width: 991px) {
		.brand {
			img {
				height: 45px;
				width: 115px;
			}
		}
	}

	.site-nav {
		.user-pic {
			background: #d9d9d9;
			border-radius: 50%;
			height: 40px;
			overflow: hidden;
			position: relative;
			width: 40px;
			z-index: 1;

			img {
				height: 100%;
				mix-blend-mode: multiply;
				object-fit: cover;
				width: 100%;
			}
		}

		.cta {
			border: 2px solid rgba($white, 0.3);
			border-radius: 26px;
			color: $white;
			font: 600 14px/1.45 $inter;
			padding: 10px 25px;
			text-align: center;

			&:hover {
				background: $orange;
				border-color: $orange;
			}
		}

		.nav_call {
			border: 2px solid rgba($white, 0.3);
			border-radius: 50%;
			cursor: pointer;
			height: 40px;
			overflow: hidden;
			position: relative;
			width: 40px;

			span {
				background: $white;
				display: block;
				height: 2px;
				width: 22px;

				+ span {
					margin-top: 6px;
				}
			}

			@media (min-width: 992px) {
				&:hover {
					background: $orange;
					border-color: $orange;
				}
			}

			&.clicked {
				background: $orange;
				border-color: $orange;

				span {
					&:first-child {
						position: relative;
						top: 4px;
						transform: rotate(45deg);
					}

					&:last-child {
						position: relative;
						top: -3px;
						transform: rotate(-45deg);
					}
				}
			}
		}

		.drop-nav {
			background: #282828;
			border: 1px solid #ff5811;
			border-radius: 8px;
			position: absolute;
			right: 50%;
			top: calc(100% + 10px);
			transform: translateX(50%);

			.link {
				border-bottom: 1px solid #ff5811;
				color: #fff;
				font: 16px 'Inter', sans-serif;
			}
		}
	}

	h1 {
		color: $white;
		font: 500 16px $futura;
		left: 50%;
		opacity: 0.5;
		position: absolute;
		top: 50%;
		transform: translate(-50%, -50%);

		&.hub {
			font: 700 20px $futura;
			opacity: 1;

			i {
				font-family: $play;
			}
		}
	}

	&.overlap {
		@media (max-width: 991px) {
			.header_float {
				background: $siteBlack;
			}
		}

		.navmain {
			&.open {
				background: $siteBlack;
				padding: 30px 0;

				@media (min-width: 992px) {
					opacity: 1;
					padding: 0;
				}
			}
		}
	}

	&.bg-white {
		h1 {
			color: $siteBlack;
		}

		.site-nav {
			.cta {
				border-color: rgba($siteBlack, 0.3);
				color: $siteBlack;

				&:hover {
					border-color: $orange;
					color: $white;
				}
			}

			.nav_call {
				border-color: rgba($siteBlack, 0.3);

				.bar {
					span {
						background: $siteBlack;
					}
				}

				@media (min-width: 992px) {
					&:hover {
						border-color: $orange;

						.bar {
							span {
								background: $white;
							}
						}
					}
				}
			}
		}
	}
}
