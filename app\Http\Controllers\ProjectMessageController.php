<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Project;
use App\Models\ProjectMessageThread;
use App\Models\ProjectMessageFile;
use App\Notifications\NewProjectMessage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ProjectMessageController extends Controller
{
    public function store(Request $request, Project $project)
    {



        try {
            $request->validate([
                'subject' => 'required|string|max:255',
                'message' => 'required|string',
                'files.*' => 'nullable|file|max:20480', // 20MB max
                'sendMessageAlong' => 'required|in:all,selected',
                'email_recipients' => 'required_if:sendMessageAlong,selected|array',
                'email_recipients.*' => 'exists:users,id'
            ]);

            DB::beginTransaction();

            // Create message
            $message = ProjectMessageThread::create([
                'project_id' => $project->getKey(),
                'subject' => $request->subject,
                'message' => $request->message,
                'posted_by' => Auth::id()
            ]);

            // Handle file uploads
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $path = $file->store('project-messages/' . $project->getKey(), 'public');

                    ProjectMessageFile::create([
                        'message_id' => $message->getKey(),
                        'file_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_type' => $file->getMimeType(),
                        'file_size' => $file->getSize()
                    ]);
                }
            }

            // Handle email notifications
            if ($request->sendMessageAlong === 'all') {
                $recipients = $project->users;

                $recipients = $recipients->filter(fn($recipient) => $recipient->resource->team == "US");
            } else {
                $recipients = User::whereIn('id', $request->email_recipients)->get();
            }

            foreach ($recipients as $recipient) {
                if ($recipient->getKey() !== Auth::id()) {
                    $recipient->notify(new NewProjectMessage($message));
                }
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Message posted successfully',
                    'redirect' => route('message-centre', $project->getKey())
                ]);
            }

            return redirect()->route('message-centre', $project->getKey())
                ->with('success', 'Message posted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to post message: ' . $e->getMessage());

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to post message: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Failed to post message. Please try again.');
        }
    }

    public function show($project_id, $message_id)
    {
        $project = Project::findOrFail($project_id);
        $client = $project->client;

        $clientUser = Client::whereHas('linkedUsers', function ($query) use ($client) {
            $query->where('clients.id', $client->id);
        })->first();

        // Get the main message
        $message = ProjectMessageThread::with(['postedBy', 'files'])
            ->where('id', $message_id)
            ->where('project_id', $project_id)
            ->firstOrFail();

        // Get the root message (either the current message or its parent)
        $rootMessage = $message->parent_id ? $message->parent : $message;        // Get message IDs in thread
        $messageIds = ProjectMessageThread::where('project_id', $project_id)
            ->where(function ($query) use ($rootMessage) {
                $query->where('id', $rootMessage->id)
                    ->orWhere('parent_id', $rootMessage->id);
            })
            ->pluck('id')
            ->toArray();

        // Get unique messages with their relationships
        $threadMessages = ProjectMessageThread::with(['postedBy', 'files'])
            ->whereIn('id', array_unique($messageIds))
            ->orderBy('created_at', 'asc')
            ->get();






        // Get project users for recipient selection
        $projectUsers = $project->users()->get();

        // Get default recipients
        $defaultRecipients = User::where('name', 'like', '%drew%')
            ->orWhere('name', 'like', '%sally%')
            ->orWhere('name', 'like', '%robert%')
            ->get();




        return view('message-thread', compact(
            'project',
            'client',
            'clientUser',
            'message',
            'threadMessages',
            'rootMessage',
            'projectUsers',
            'defaultRecipients'
        ));
    }
    public function reply(Request $request, $project_id, $message_id)
    {



        try {
            $request->validate([
                'subject' => 'required|string|max:255',
                'message' => 'required|string',
                'files.*' => 'nullable|file|max:20480', // 20MB max
                'sendMessageAlong' => 'required|in:all,selected',
                'email_recipients' => 'required_if:sendMessageAlong,selected|array',
                'email_recipients.*' => 'exists:users,id'
            ]);

            DB::beginTransaction();

            $project = Project::findOrFail($project_id);
            $parentMessage = ProjectMessageThread::findOrFail($message_id);

            // Create the reply message
            $message = new ProjectMessageThread();
            $message->project_id = $project_id;
            $message->parent_id = $message_id;
            $message->subject = $request->subject;
            $message->message = $request->message;
            $message->posted_by = Auth::id();
            $message->save();        // Handle file uploads
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $path = $file->store('project-messages/' . $project->getKey());

                    $messageFile = new ProjectMessageFile();
                    $messageFile->message_id = $message->id;
                    $messageFile->file_name = $file->getClientOriginalName();
                    $messageFile->file_path = $path;
                    $messageFile->file_type = $file->getClientMimeType();
                    $messageFile->file_size = $file->getSize();
                    $messageFile->save();
                }
            }

            // Handle email notifications
            if ($request->sendMessageAlong === 'all') {
                $recipients = $project->users;
            } else {
                $recipients = User::whereIn('id', $request->email_recipients)->get();
            }

            foreach ($recipients as $recipient) {
                if ($recipient->getKey() !== Auth::id()) {
                    $recipient->notify(new NewProjectMessage($message));
                }
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Reply posted successfully',
                    'redirect' => route('view-message', ['project_id' => $project_id, 'message_id' => $message_id])
                ]);
            }

            return redirect()->route('view-message', ['project_id' => $project_id, 'message_id' => $message_id])
                ->with('success', 'Reply posted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to post reply: ' . $e->getMessage());

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to post reply: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Failed to post reply. Please try again.');
        }
    }
}
