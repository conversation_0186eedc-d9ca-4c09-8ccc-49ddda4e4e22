<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddUniqueConstraintToClientsNameColumn extends Migration
{
    public function up(): void
    {
        // Check if a unique index on `name` already exists
        $indexExists = DB::select("
            SELECT COUNT(1) as count
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
              AND table_name = 'clients' 
              AND column_name = 'name' 
              AND non_unique = 0
        ");

        if (empty($indexExists) || $indexExists[0]->count == 0) {
            Schema::table('clients', function (Blueprint $table) {
                $table->unique('name');
            });
        }
    }

    public function down(): void
    {
        // Check if the unique index exists before dropping it
        $indexExists = DB::select("
            SELECT INDEX_NAME
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
              AND table_name = 'clients' 
              AND column_name = 'name' 
              AND non_unique = 0
            LIMIT 1
        ");

        if (!empty($indexExists)) {
            $indexName = $indexExists[0]->INDEX_NAME;

            Schema::table('clients', function (Blueprint $table) use ($indexName) {
                $table->dropUnique($indexName);
            });
        }
    }
}
