<?php

namespace App\Models;
use Carbon\Carbon;
use Illuminate\Support\Str;

use Illuminate\Database\Eloquent\Model;

class EssentialMonth extends Model
{
    //
    protected $table = 'essential_months';
    protected $fillable = [
        'name',
        'slug',
    ];

    public static function initialize($year = null)
    {
        $year = $year ?? now()->year;

        $exists = self::where('slug', 'like', '%' . $year)->exists();

        if (! $exists) {
            self::createMonthsForYear($year);
        }
    }

    public static function createMonthsForYear($year)
    {
        for ($month = 1; $month <= 12; $month++) {
            $monthName = Carbon::create()->month($month)->format('F') . ' ' . $year;
            $slug = Str::slug($monthName);

            $exists = self::where('slug', $slug)->exists();

            if (! $exists) {
                self::create([
                    'name' => $monthName,
                    'slug' => $slug,
                ]);
            }
        }
    }


    public function essentialMetrics()
    {
        return $this->hasMany(EssentialMetrics::class);
    }
    public function getRouteKeyName()
    {
        return 'slug';
    }
    public function getNameAttribute($value)
    {
        return ucwords($value);
    }

    public function month($month)
    {
        return $this->whereMonth('slug', $month);
    }
}
