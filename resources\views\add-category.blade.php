
@extends('admin.layouts.app')
@section('title', 'Add Category')
@section('content')


<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('categories') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Add <i>Category</i></h1>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl projects-row">
        <div class="project-column task-overview">
            <div class="col-head align-items-center d-flex justify-content-between">
                <h2 class="text-uppercase">Hey {{ auth()->user()->name }}</h2>
            </div>
            <hr class="mt-0 mb-4 border-white" />
            <form class="row form-wrap" action="{{ route('save-category') }}" method="post" enctype="multipart/form-data">
                @csrf
                @method('post')
                
                <div class="col-md-12 col-xl-9 mb-4">
                    <input name="category_name" class="form-control border-0" type="text" id="category_name" value="" placeholder="Add a Category name..." />
                </div>
               
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">Assign To Project</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4">
                    <div class="row">
                        <div class="col-md-4">
                            <h2 class="text-white">Add Project</h2>
                        </div>
                        <div class="col-md-8">
                            <input type="hidden" name="category_projects" class="d-none" value="[]">
                            <div class="brand-wrap">
                            @if(isset($projects) && count($projects) > 0)
                                @foreach($projects as $project)
                                    
                                    <div class="brand-select d-flex align-items-center justify-content-between ">
                                        <div class="name text-uppercase">{{ $project->name }}</div>
                                        <button class="cta text-uppercase mt-0 " data-project-id="{{ $project->id }}" type="button">
                                            Add
                                        </button>
                                    </div>
                                @endforeach
                            @endif

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-5 text-center">
                    <button class="cta text-uppercase mt-0" type="submit">Add Category</button>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
   
    document.addEventListener("DOMContentLoaded", function () {
        const clientsInput = document.querySelector('[name="category_projects"]');
        let selectedClients = JSON.parse(clientsInput.value || '[]').map(id => parseInt(id)); // 🧠 Normalize to integers

        document.querySelectorAll('.brand-select').forEach(function (item) {
            const button = item.querySelector('button');
            const permissionId = parseInt(button.getAttribute('data-project-id')); // 🧠 Parse ID to integer

            // Pre-fill Added button text
            if (item.classList.contains('added')) {
                button.innerText = 'Added';
                if (!selectedClients.includes(permissionId)) {
                    selectedClients.push(permissionId);
                }
            }

            button.addEventListener('click', function () {
                item.classList.toggle('added');
                button.classList.toggle('added');

                if (item.classList.contains('added')) {
                    if (!selectedClients.includes(permissionId)) {
                        selectedClients.push(permissionId);
                        button.innerText = 'Added';
                    }
                } else {
                    selectedClients = selectedClients.filter(id => id !== permissionId);
                    button.innerText = 'Add';
                }

                // Update hidden input value
                clientsInput.value = JSON.stringify(selectedClients);
            });
        });

        clientsInput.value = JSON.stringify(selectedClients); // Clean value set once
    });

</script>

@endsection