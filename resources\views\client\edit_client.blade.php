@extends('layout.app')
@section('title', 'Edit Client')
@section('content')


@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
@endif
@if (session('error'))
    <script>
        errorToast(@json(session('error')));
    </script>
@endif
@if ($errors->any())
    @foreach ($errors->all() as $error)
        <script>
            errorToast(@json($error));
        </script>
    @endforeach
@endif

<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-clients') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />Back to Clients List</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('clients') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />Back to Clients List</a>
            @endif
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Edit <i>Client</i></h1>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl projects-row">
        <div class="project-column task-overview">
            <div class="col-head align-items-center d-flex justify-content-between">
                <h2 class="text-uppercase">Modify Client</h2>
            </div>
            <hr class="mt-0 mb-4 border-white" />
            <form class="row form-wrap" action="{{ route('update-client', ['client_id' => $client->id]) }}" method="post" enctype="multipart/form-data" id="update_client_form" >
                @csrf
                @method('put')
                <div class="col-md-4 col-xl-3 mb-4">
                    <input name="job_code" class="form-control border-0" type="text" id="job_code" value="{{ $client->job_code }}" placeholder="JOB CODE PREFIX" />
                    <div class="form-text fst-italic opacity-50">Suggested Length: 2-4 Characters</div>
                </div>
                <div class="col-md-8 col-xl-9 mb-4">
                    <input name="name" class="form-control border-0" type="text" id="name" value="{{ $client->name }}" placeholder="Add a client name..." />
                </div>
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">CONTACTS</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4 d-flex align-items-center justify-content-between">
                    <h2 class="text-white mb-0">Add Contacts</h2>
                    <button id="addMoreContactsButton" type="button" class="cta ms-3 mt-0">Add More Contact</button>
                </div>
                <!-- V.IMP - to retain contact input values after form submission -->
                <div id="contactsWrapper">
                    @foreach ($social_details as $index => $detail)
                        <div class="col-12 mt-4 contacts-input">
                            <div class="input-wrap d-flex">
                                <div class="icon d-flex align-items-center justify-content-center">
                                    <img src="{{ asset('images/email-icon.svg') }}" alt="" />
                                </div>
                                <div class="input flex-grow-1">
                                    <input class="form-control border-0" type="email" id="email_{{ $index }}" name="social_detail[]" value="{{ old("social_detail.$index") !== null ? old("social_detail.$index") : $detail->value }}" />
                                </div>
                                <div class="remove-contact ms-2">
                                    <button type="button" class="btn btn-sm btn-danger">Remove</button>
                                </div>
                            </div>
                        </div>
                    @endforeach
                    @if(old('social_detail') && count(old('social_detail')) > $social_details->count())
                        @for($i = $social_details->count(); $i < count(old('social_detail')); $i++)
                            <div class="col-12 mt-3 contacts-input">
                                <div class="input-wrap d-flex">
                                    <div class="icon d-flex align-items-center justify-content-center"><img src="{{ asset('images/email-icon.svg') }}" alt="" /></div>
                                    <div class="input flex-grow-1">
                                        <input class="form-control border-0" type="email" id="email_{{ $i }}" name="social_detail[]" value="{{ old('social_detail.'.$i) }}" />
                                    </div>
                                    <div class="remove-contact ms-2">
                                        <button type="button" class="btn btn-sm btn-danger">Remove</button>
                                    </div>
                                </div>
                            </div>
                        @endfor
                    @endif
                    @if (! $social_details->count() && old('social_detail'))
                        @foreach (old('social_detail') as $index => $oldValue)
                            <div class="col-12 mt-4 contacts-input">
                                <div class="input-wrap d-flex">
                                    <div class="icon d-flex align-items-center justify-content-center"><img src="{{ asset('images/email-icon.svg') }}" alt="" /></div>
                                    <div class="input flex-grow-1">
                                        <input class="form-control border-0" type="email" id="email_{{ $index }}" name="social_detail[]" value="{{ $oldValue }}" />
                                    </div>
                                    <div class="remove-contact ms-2">
                                        <button type="button" class="btn btn-sm btn-danger">Remove</button>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">BRAND</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h2 class="text-white">Link to Larger Brand Hub?</h2>
                        </div>
                        <div class="col-md-6">
                            <div class="select-wrap">
                            <select name="brands" class="form-select border-0">
                                @if(isset($brands) && count($brands) > 0)
                                    <option selected value="null">SELECT BRAND</option>
                                    @foreach($brands as $brand)
                                        <option value="{{ $brand->id }}" {{ $client->brands }}
                                            {{ $client->brands->contains($brand->id) ? 'selected' : '' }}>
                                            {{ $brand->name }}
                                        </option>
                                    @endforeach
                                    @else
                                    <option selected value="null">SELECT BRAND</option>
                                @endif
                            </select>
                                <div class="select-icon"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" width="18" height="10" /></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Users -->
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">Users</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4">
                    <div class="row">
                        <div class="col-md-4">
                            <h2 class="text-white">Add Team Members to Client</h2>
                        </div>
                        <div class="col-md-8">
                            <input type="hidden" name="users" class="d-none" value="[]">
                            <div class="brand-wrap">
                            @if(isset($users) && count($users) > 0)
                                @foreach($users as $user)
                                    @php
                                        $isAdded = $client->users->contains($user->id);
                                    @endphp

                                    <div class="user-select brand-select d-flex align-items-center justify-content-between {{ $isAdded ? 'added' : '' }}">
                                        <div class="name text-uppercase">{{ $user->name }}</div>
                                        <button
                                            class="cta text-uppercase mt-0 {{ $isAdded ? 'added' : '' }}"
                                            data-user-id="{{ $user->id }}"
                                            type="button">
                                            {{ $isAdded ? 'Added' : 'Add' }}
                                        </button>
                                    </div>
                                @endforeach
                            @endif

                            </div>
                        </div>
                    </div>
                </div>
                <!-- USers -->
                <div class="col-12 mt-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h2 class="text-white">Upload Logo</h2>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="upload-btn-wrapper">
                                <input type="file" name="logo" id="logo" class="d-none" />
                                <button type="button" id="uploadTrigger" class="btn-upload text-uppercase text-white d-flex align-items-center">
                                    <i class="bi bi-upload me-2"></i> Upload a file
                                </button>
                                <div class="upload-files d-flex align-items-center justify-content-between mt-2">
                                    <div class="form-text fst-italic opacity-50" id="selectedFileName">{{ set_user_image($client->logo) }}</div>
                                    <span class="remove-selected" style="cursor: pointer;"> <i class="fa-solid fa-xmark fa-lg" style="color: #ffffff;"></i> </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-text fst-italic opacity-50">Suggested Format: One-color white SVG or PNG</div>
                </div>
                <div class="col-12 mt-5 text-center">
                    <button class="cta text-uppercase mt-0 save-client" type="submit">UPDATE CLIENT</button>
                </div>
            </form>
        </div>
    </div>
</section>

<script>

document.addEventListener('DOMContentLoaded', function () {
   

   initializeApp();
   });


function initializeApp() {
    initializeContactManagement();
}

function initializeContactManagement() {
    const addMoreContactsButton = document.getElementById('addMoreContactsButton');
    const contactsWrapper = document.getElementById('contactsWrapper');

    if (addMoreContactsButton && contactsWrapper) {
        let contactCounter = contactsWrapper.querySelectorAll('.contacts-input').length;

        addMoreContactsButton.addEventListener('click', function() {
            contactCounter++;
            addNewContactField(contactsWrapper, contactCounter);
        });

        // Event delegation for dynamically added remove buttons
        contactsWrapper.addEventListener('click', function(event) {
            if (event.target.classList.contains('btn-danger') && event.target.closest('.remove-contact')) {
                const removeButton = event.target;
                const contactDiv = removeButton.closest('.contacts-input');
                if (contactDiv && contactsWrapper) {
                    const inputElement = contactDiv.querySelector('input[type="email"]');
                    if (inputElement && typeof validation !== 'undefined') {
                        validation.removeField(`#${inputElement.id}`);
                    }
                    contactsWrapper.removeChild(contactDiv);
                }
            }
        });

        // Initialize validation for existing fields on page load (if any)
        contactsWrapper.querySelectorAll('.contacts-input input[type="email"]').forEach(input => {
            if (typeof validation !== 'undefined') {
                validation.addField(`#${input.id}`, [
                    {
                        rule: 'email',
                        errorMessage: 'Please provide a valid email address',
                    }
                ]);
            }
        });
    }
}


function addNewContactField(contactsWrapper, contactCounter) {
    const newContactId = `email_${contactCounter}`;

    const newContactDiv = document.createElement('div');
    newContactDiv.className = 'col-12 mt-3 contacts-input';

    const iconSrc = document.querySelector('.contacts-input .icon img')?.src || '';

    newContactDiv.innerHTML = `
        <div class="input-wrap d-flex">
            <div class="icon d-flex align-items-center justify-content-center"><img src="${iconSrc}" alt="" /></div>
            <div class="input flex-grow-1">
                <input class="form-control border-0" type="email" id="${newContactId}" name="social_detail[]" />
            </div>
            <div class="remove-contact ms-2">
                <button type="button" class="btn btn-sm btn-danger">Remove</button>
            </div>
        </div>
    `;
    contactsWrapper.appendChild(newContactDiv);

    if (typeof validation !== 'undefined') {
        validation.addField(`#${newContactId}`, [
            {
                rule: 'email',
                errorMessage: 'Please provide a valid email address',
            }
        ]);
    }
}





    document.getElementById("uploadTrigger").addEventListener("click", function () {
        document.getElementById("logo").click();
    });

    document.getElementById("logo").addEventListener("change", function () {
        const file = this.files[0];
        if (file) {
            console.log("selected file");
            document.getElementById("selectedFileName").append(file.name);
            document.querySelector(".upload-files").classList.remove("d-none");
        }
    });
    document.querySelector(".remove-selected").addEventListener("click", function () {
        document.getElementById("logo").value = "";
        document.getElementById("selectedFileName").innerText = "";
        document.querySelector(".upload-files").classList.add("d-none");
    });

    const usersInput = document.querySelector('[name="users"]');
    // Initialize with existing users
    let selectedUsers = JSON.parse(usersInput.value || '[]');

    // Function to update the hidden input
    function updateSelectedUsers() {
        usersInput.value = JSON.stringify(selectedUsers);
        console.log('Updated selected users:', selectedUsers); // Debug log
    }

    document.querySelectorAll('.user-select').forEach(function (item) {
        const button = item.querySelector('button');
        const userId = button.dataset.userId;
        
        // Set initial state
        if (selectedUsers.includes(userId)) {
            item.classList.add('added');
            button.classList.add('added');
            button.innerText = 'Added';
        }

        button.addEventListener('click', function () {
            // Toggle button and container state
            item.classList.toggle('added');
            this.classList.toggle('added');
            this.innerText = item.classList.contains('added') ? 'Added' : 'Add';

            // Update selected users array
            if (item.classList.contains('added')) {
                if (!selectedUsers.includes(userId)) {
                    selectedUsers.push(userId);
                }
            } else {
                const index = selectedUsers.indexOf(userId);
                if (index > -1) {
                    selectedUsers.splice(index, 1);
                }
            }

            // Update hidden input value
            updateSelectedUsers();
        });
    });

    document.querySelector(".save-client").addEventListener("click", function (event) {
        // Ensure we have the latest selected users before submitting
        const finalSelectedUsers = [];
        document.querySelectorAll('.user-select.added').forEach(element => {
            const id = element.querySelector('button').dataset.userId;
            if (id) {
                finalSelectedUsers.push(id);
            }
        });
        
        // Update the input with the final selection
        usersInput.value = JSON.stringify(finalSelectedUsers);
        console.log('Final selected users before submit:', finalSelectedUsers); // Debug log
        
        document.querySelector('form').submit();
    });

    const form_id = 'update_client_form';
    let args = {
            rules : {
                '#name': GlobalFormValidator.validators.required,
                '#job_code': GlobalFormValidator.validators.required,
                '#social_detail': GlobalFormValidator.validators.required,
            },
            messages: {
                '#name': "Please select a client",
                '#job_code': "Please add a job code",
                '#social_detail': "Please add a project title",
            },
            onSuccess: (form) => {
                form.submit();
            }
        }
    let Validator = new GlobalFormValidator(form_id, args);
    Validator.initialize();

</script>

@endsection