<?php

namespace App\Livewire\Client;

use App\Models\Client;
use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Project;
use Illuminate\Support\Facades\Auth;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class Projects extends Component
{
    use WithPagination;

    public $filter = 'active';
    public $year = 'all';
    public $search = '';
    protected $queryString = ['filter', 'year', 'search'];

    public function mount()
    {
        $this->filter = request()->query('filter', 'active');
        $this->year = request()->query('year', 'all');
        $this->search = request()->query('search', '');
    }

    public function updatedFilter()
    {
        $this->resetPage();
    }

    public function updatedYear()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    protected function paginateCollection($collection, $perPage = 10)
    {
        $page = request()->get('page', 1);
        $offset = ($page - 1) * $perPage;
        $items = $collection->forPage($page, $perPage)->values();
        
        return new LengthAwarePaginator(
            $items,
            $collection->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }

    public function getProjects()
    {
        $user = Auth::user();
        $client = Client::whereHas('linkedUsers', function ($query) use ($user) {
            $query->where('users.id', $user->id);
        })->first();

        if (!$client) {
            return collect()->paginate(10);
        }

        $clientProjects = $client->projects()->with(['phases', 'timeline'])->get();

        $filteredProjects = match($this->filter) {
            'active' => $clientProjects->filter(function ($project) {
                return optional($project->timeline)->type !== 'voyager' && !$project->archived;
            }),
            'voyager' => $clientProjects->filter(function ($project) {
                return optional($project->timeline)->type === 'voyager';
            }),
            'archived' => $clientProjects->filter(function ($project) {
                return $project->archived;
            }),
            default => $clientProjects
        };

        if ($this->search) {
            $searchTerm = strtolower($this->search);
            $filteredProjects = $filteredProjects->filter(function ($project) use ($searchTerm) {
                return str_contains(strtolower($project->name), $searchTerm) || 
                       str_contains(strtolower($project->job_code), $searchTerm);
            });
        }

        if ($this->year !== 'all') {
            $filteredProjects = $filteredProjects->filter(function ($project) {
                return $project->created_at->year == $this->year;
            });
        }

        $filteredProjects = $filteredProjects->sortByDesc('created_at');

        return $this->paginateCollection($filteredProjects);
    }

    public function getProjectCounts()
    {
        $user = Auth::user();
        $client = Client::whereHas('linkedUsers', function ($query) use ($user) {
            $query->where('users.id', $user->id);
        })->first();

        if (!$client) {
            return [
                'active' => 0,
                'voyager' => 0,
                'archived' => 0,
            ];
        }

        $clientProjects = $client->projects()->with(['phases', 'timeline'])->get();

        return [
            'active' => $clientProjects->filter(function ($project) {
                return optional($project->timeline)->type !== 'voyager' && !$project->archived;
            })->count(),
            'voyager' => $clientProjects->filter(function ($project) {
                return optional($project->timeline)->type === 'voyager';
            })->count(),
            'archived' => $clientProjects->filter(function ($project) {
                return $project->archived;
            })->count(),
        ];
    }

    public function getYears()
    {
        $user = Auth::user();
        $client = Client::whereHas('linkedUsers', function ($query) use ($user) {
            $query->where('users.id', $user->id);
        })->first();

        if (!$client) {
            return collect();
        }

        return $client->projects()
            ->selectRaw('YEAR(created_at) as year')
            ->distinct()
            ->orderByDesc('year')
            ->pluck('year');
    }

    public function render()
    {
        return view('livewire.client.projects', [
            'projects' => $this->getProjects(),
            'counts' => $this->getProjectCounts(),
            'years' => $this->getYears(),
        ]);
    }
}
