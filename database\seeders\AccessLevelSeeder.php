<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AccessLevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $accessLevels = [
            [
                'name' => 'Admin',
                'description' => 'Administrator with Admin access',
            ],
            [
                'name' => 'Team Member',
                'description' => 'Guest user with minimal access',
            ],
        ];

        DB::table('access_level')->insert($accessLevels);
    }
}
