<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProjectPhaseCompletion extends Model
{
    protected $fillable = [
        'project_id',
        'phase_id',
        'is_completed',
        'completed_at'
    ];

    protected $casts = [
        'completed_at' => 'datetime'
    ];

    protected $table = 'project_phase_completion';

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function phase()
    {
        return $this->belongsTo(Phase::class);
    }
}
