<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EssentialMetrics extends Model
{
    //
    protected $table = 'essential_metrics';
    protected $fillable = [
        'project_id',
        'essential_month_id',
        'performance',
        'accessibility',
        'best_practice',
        'seo',
        'site_health',
        'site_traffic',
        'site_contacts',
        'site_popular_pages',
        'engaged_traffic',
        'engaged_traffic_rate',
        'site_monthly_insight',
        'focus',
        'report_file',
    ];
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
    public function essentialMonth()
    {
        return $this->belongsTo(EssentialMonth::class);
    }
}
