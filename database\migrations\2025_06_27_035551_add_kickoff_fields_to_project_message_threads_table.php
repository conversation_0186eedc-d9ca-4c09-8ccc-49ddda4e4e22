<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('project_message_threads', function (Blueprint $table) {
            Schema::table('project_message_threads', function (Blueprint $table) {
                $table->boolean('is_kickoff')->default(false)->after('message');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('project_message_threads', function (Blueprint $table) {
            $table->dropColumn('is_kickoff');
        });
    }
};
