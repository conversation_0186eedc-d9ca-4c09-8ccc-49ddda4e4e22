@extends('layout.app')
@section('title', 'Projects')
@section('content')


<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ set_user_image('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
        </div>
        <div class="meta d-flex justify-content-between align-items-center">
            <div class="copy">
                <h1 class="text-white mb-0">Project: <i>{{ $project->name }}</i></h1>
                <div class="page-links mt-3"><a class="active" href="{{ url()->current() }}">Site Analytics</a><a href="#">Plan Details</a></div>
            </div>
            <div class="logo">
                <a href="#"><img src="{{ set_user_image($client->logo) }}" alt="" width="72" height="80" /></a>
            </div>
        </div>
    </div>
</section>
<section class="client-project bg-white">
    <div class="container-xxl">
        <div class="heading d-md-flex align-items-md-baseline justify-content-between">
            <h2 class="mb-0">Site <i>Analytics</i></h2>
            <div class="links"><a class="active" href="{{ url()->current() }}">Essential Metrics</a><a href="#">Audience and Reach</a><a href="#">Monthly Insight &amp; Next Focus</a></div>
        </div>
    </div>
</section>
<section class="analytics-meta bg-white py-5">
    <div class="container-xxl">
        <hr class="my-0" />
        <div class="head d-flex">
            <div class="title">
                <h2 class="mb-0 lh-1 text-uppercase">Essential Metrics</h2>
            </div>
            <div class="select-month">
                <select class="ps-3 border-0" id="month">
                    @foreach ($months as $month)
                        <option value="{{ $month->id }}" {{ $month->slug == $currentMonthSlug ? 'selected' : '' }}>{{ $month->name }}</option>
                    @endforeach
                </select
                ><span class="arrow d-inline-flex"><img src="{{ set_user_image('images/select-down-arrow.svg') }}" alt="" width="14" height="9" /></span>
            </div>
            <a class="circle-icon ms-auto align-self-center" href="#">?</a>
        </div>
        <div class="row analytics-row">
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/performance-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->performance ?? 00 }}" data-duration="2000" data-tag="performance">0</h2>
                        <input type="hidden" name="performance" id="performance" value="{{ $essentialMetrics->performance ?? '' }}" />
                        <h3 class="text-black mb-0 p">Performance</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/accessibility-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->accessibility ?? 00 }}" data-duration="2000" data-tag="accessibility">0</h2>
                        <input type="hidden" name="accessibility" id="accessibility" value="{{ $essentialMetrics->accessibility ?? '' }}" />
                        <h3 class="text-black mb-0 p">Accessibility</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/best-practices-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->best_practice ?? 00 }}" data-duration="2000" data-tag="best_practices" >0</h2>
                        <input type="hidden" name="best_practices" id="best_practices" value="{{ $essentialMetrics->best_practice ?? '' }}" />
                        <h3 class="text-black mb-0 p">Best Practices</h3>
                        <h4 class="p">Perfect!</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/seo-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->seo ?? 00 }}" data-duration="2000" data-tag="seo">0</h2>
                        <input type="hidden" name="seo" id="seo" value="{{ $essentialMetrics->seo ?? '' }}" />
                        <h3 class="text-black mb-0 p">SEO</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/site-health-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->site_health ?? 00 }}" data-duration="2000" data-tag="site_health" >0</h2>
                        <input type="hidden" name="site_health_score" id="site_health_score" value="{{ $essentialMetrics->site_health ?? '' }}" />
                        <h3 class="text-black mb-0 p">Site Health Score</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
        </div>
        <hr class="my-0" />
        <div class="row">
            <div class="col-md-3">
            <a href="javascript:void(0)" class="btn cta text-uppercase" id="save-report">Save Report</a>
            </div>
        </div>
    </div>
</section>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('month').addEventListener('change', function() {
        var selectedMonth = this.value;
        var url = "{{ route('get-metrics-by-month', ['project_id' => $project->id, 'month' => ':month']) }}";
        url = url.replace(':month', selectedMonth);
        console.log("selected month", selectedMonth);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                month: selectedMonth,
                project_id: {{ $project->id }}
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log("Date Fetched:-",data);
            if (data.success) {
                let metricsData = data.data[0];

                // Show Toast
                successToast("Metrics fetched successfully!");
                
                // Update visible counters (default to 0 if not present)
                let el;
                el = document.querySelector('.counter[data-tag="performance"]');
                el.setAttribute('data-countTo', metricsData.performance ?? 0);
                el.textContent = metricsData.performance ?? 0;
                console.log("metricsData.performance",metricsData.performance, el);

                el = document.querySelector('.counter[data-tag="accessibility"]');
                el.setAttribute('data-countTo', metricsData.accessibility ?? 0);
                el.textContent = metricsData.accessibility ?? 0;

                el = document.querySelector('.counter[data-tag="best_practices"]');
                el.setAttribute('data-countTo', metricsData.best_practices ?? 0);
                el.textContent = metricsData.best_practices ?? 0;

                el = document.querySelector('.counter[data-tag="seo"]');
                el.setAttribute('data-countTo', metricsData.seo ?? 0);
                el.textContent = metricsData.seo ?? 0;

                el = document.querySelector('.counter[data-tag="site_health"]');
                el.setAttribute('data-countTo', metricsData.site_health ?? 0);
                el.textContent = metricsData.site_health ?? 0;


                // Update hidden inputs (default to 0 if not present)
                document.getElementById('performance').value = metricsData.performance ?? 0;
                document.getElementById('accessibility').value = metricsData.accessibility ?? 0;
                document.getElementById('best_practices').value = metricsData.best_practices ?? 0;
                document.getElementById('seo').value = metricsData.seo ?? 0;
                document.getElementById('site_health_score').value = metricsData.site_health ?? 0;


            } else {
                // Show Toast
                infoToast("No metrics found for the selected month.");

                document.getElementById('month').selectedMonth;
                document.querySelector('.counter[data-tag="performance"]').textContent =  0;
                document.querySelector('.counter[data-tag="accessibility"]').textContent =  0;
                document.querySelector('.counter[data-tag="best_practices"]').textContent =  0;
                document.querySelector('.counter[data-tag="seo"]').textContent =  0;
                document.querySelector('.counter[data-tag="site_health"]').textContent =  0;

                // Update hidden inputs (default to 0 if not present)
                document.getElementById('performance').value =  0;
                document.getElementById('accessibility').value =  0;
                document.getElementById('best_practices').value =  0;
                document.getElementById('seo').value =  0;
                document.getElementById('site_health_score').value =  0;
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
        
        });

        document.querySelectorAll('.text h2.counter').forEach(function(counter) {
            counter.addEventListener('click', function() {
                this.setAttribute('contenteditable', 'true');
                this.focus();
            });

            counter.addEventListener('blur', function() {
                this.removeAttribute('contenteditable'); // Optional: make it non-editable again after editing
                const input = this.closest('.text').querySelector('input[type="hidden"]');
                if (input) {
                input.value = this.textContent.trim();
                }
            });

            counter.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                event.preventDefault();
                this.blur(); 
                }
            });
        });

        document.getElementById('save-report').addEventListener('click', function() {
            var performance = document.getElementById('performance').value;
            var accessibility = document.getElementById('accessibility').value;
            var best_practices = document.getElementById('best_practices').value;
            var seo = document.getElementById('seo').value;
            var site_health_score = document.getElementById('site_health_score').value;
            var month = document.getElementById('month').value

            console.log("Params", performance, accessibility, best_practices, seo, site_health_score, month);


            fetch("{{ route('save-metrics-by-month') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    performance: performance,
                    accessibility: accessibility,
                    best_practices: best_practices,
                    seo: seo,
                    site_health_score: site_health_score,
                    project_id: {{ $project->id }},
                    month_id: month
                })
            })
            .then(async response => {
                const contentType = response.headers.get("content-type");
                if (!response.ok) {
                    const text = await response.text();
                    throw new Error(`Server error: ${text}`);
                }

                if (contentType && contentType.includes("application/json")) {
                    return response.json();
                } else {
                    const text = await response.text();
                    throw new Error(`Expected JSON, got: ${text}`);
                }
            })
            .then(data => {
                if (data.success) {
                    console.log(data);
                    // Show toast
                    successToast("Report saved successfully!");
                    // alert("Report saved successfully!");
                } else {
                    console.error('Error saving report:', data.message);
                }
            })
            .catch(error => {
                document.querySelector('body').innerHTML = error.message;
                console.log("Error:", error);
                console.error('Error:', error.message);
            });

        });
    });
        
</script>


@endsection

