<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Support\Facades\Auth;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $permission)
    {
    	$role = Role::with("permissions")->where('id', '=', Auth::user()->role_id)->first();
    	if ($role->permissions->isNotEmpty()) {
    		if($role->permissions->contains('name', $permission)){
    			// return response($role->permissions);
    			return $next($request);
    		}  
		}
    	abort(403, 'OPERATION/ACCESS NOT ALLOWED');
    }
}
