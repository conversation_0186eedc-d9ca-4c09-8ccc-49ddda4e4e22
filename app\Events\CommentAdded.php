<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CommentAdded implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $comment;
    public $user;
    public $task_id;
    public $user_profile_image;

    /**
     * Create a new event instance.
     */
    public function __construct($comment, $task_id)
    {
        $this->comment = $comment->load('commentAttachments');
        $this->task_id = $task_id;
        $this->user = auth()->user();
        $this->user_profile_image = set_user_image($this->user->profile_image);
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn()
    {
        return [
            new PrivateChannel('task-comments.' . $this->task_id),
        ];
    }

    /**
     * Get the data to broadcast with the event.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $attachments = $this->comment->commentAttachments->map(function ($attachment) {
            return [
                'id' => $attachment->id,
                'comment_attachment' => $attachment->comment_attachment,
                'created_at' => $attachment->created_at->toISOString(), // Use ISO format for consistent timezone handling
                'full_path' => asset('storage/uploads/' . $attachment->comment_attachment)
            ];
        });

        return [
            'comment' => [
                'id' => $this->comment->id,
                'comment_body' => $this->comment->comment_body,
                'created_at' => $this->comment->created_at->toISOString(), // Use ISO format for consistent timezone handling
                'formatted_time' => $this->comment->created_at->format('M d, Y h:i A'),
                'commentAttachments' => $attachments
            ],
            'user' => $this->user->name,
            'user_id' => $this->user->id,
            'user_profile_image' => $this->user_profile_image,
            'task_id' => $this->task_id
        ];
    }
}
