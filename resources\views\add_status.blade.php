@extends('layout.app')
@section('title', 'Add Status')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">

                    <h4>
                        @if(request()->route()->named('add-status'))
                            Add Status
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('add-status'))
                        @if(Auth::user()->role_id=='1')
                            <div class="see-all-statuses-btn-div">
                                <a class="me-2" href={{ route('statuses') }}><button>See All Statuses <i class="fa fa-list"></i></button></a>
                            </div>
                        @endif
                        <div class="add-status-div">
                            <form method="post" action="{{ route('save-status') }}" class="border border-secondary w-50 mx-auto p-3" name="add-status-form">
                                @csrf
                                <div class="d-flex">
                                    <div class="add-status-div-label">
                                        Status Name:
                                    </div>
                                    <div class="add-status-div-input d-flex flex-column">
                                        <input type="text" name="status_name" value="{{ old('status_name') }}">
                                        @error("status_name")
                                            <div class="text-danger">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>
                                <br>
                              
                                <div class="d-flex justify-content-center">
                                    <button>Submit</button>
                                </div>

                            </form>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection


@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }

        .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;
            overflow:scroll;

            left: 0;
            z-index: 0;
            height: calc(100vh - 50px);
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            /* padding:0px 50px; */
        }
        .see-all-statuses-btn-div{
            display: flex;
            justify-content: end;
        }
        .add-status-div{
            width:100%;
            display: block;
        }
        .add-status-div-label{
            text-align:left;
            width:60%;
        }
        .add-status-div-input{
            width:100%;
        }
        .add-status-div-select{
            width: 225px;
        }
        .no_statuss_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
    </style>
@endpush

@push('script')
    <script>
       
    </script>
@endpush