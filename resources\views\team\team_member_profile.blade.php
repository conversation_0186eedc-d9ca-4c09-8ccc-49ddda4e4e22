@extends('layout.app')
@section('title', 'View Team Member')
@section('content')


@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
@endif
@if (session('error'))
    <script>
        errorToast(@json(session('error')));t 4    
    </script>
@endif
@if ($errors->any())
    @foreach ($errors->all() as $error)
        <script>
            errorToast(@json($error));
        </script>
    @endforeach
@endif

<section class="team-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="meta d-flex align-items-end">
            <div class="member-meta d-flex align-items-center">
                <div class="pic">
                    <div class="img upload-profile-icon"><img src="{{ set_user_image($user->profile_image) }}" alt="" /></div>
                    <button class="change d-flex align-items-center justify-content-center p-0 pic-image" id="pic"><img src="{{ asset('images/change-image-icon.svg') }}" alt=""  /></button>
                </div>
                <div class="meta">
                    <h1 class="text-white mb-0">{{ $user->name }}</h1>
                    <a class="text-decoration-none pic-image" href="#">Change Photo</a>
                </div>
            </div>
            <div class="ms-auto d-flex">
                <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0" href="#"
                    >VIEW MY PROJECTS<span class="img ms-2">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                            <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5"></path></svg></span
                ></a>
            </div>
        </div>
    </div>
</section>
<section class="team-dashboard pt-0 pb-5">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <form class="edit-member row" action="{{ route('update-team-member', ['team_member_id' => $user->id] ) }}" method="POST" enctype="multipart/form-data" id="update-team-member-profile" >
            @csrf
            @method('put')
            <input type="hidden" name="page_name" value="team_member_profile">
            <input type="hidden" name="user_id" value="{{ $user->id }}">
            <input type="hidden" name="name" value="{{ $user->name }}">
            <input type="hidden" name="email_input_disabled" value="true">
            <div class="upload-profile-icon">
                <input type="file" name="profile_image" id="profile-image" class="d-none">
            </div>
            <div class="col-md-6 mb-4">
                <label class="form-label" for="email">Email</label>
                <div class="edit-icon" id="email">
                    <span class="icon d-flex"><img class="me-2" src="{{ asset('images/input-edit-icon.svg') }}" alt="" /> Edit</span>
                    <input class="form-control border-0 non-editable" type="email" id="email" name="email" placeholder="" value="{{ old('email', $user->email) }}" readonly/>                        
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <label class="form-label" for="password">Password</label>
                <div class="edit-icon" id="password">
                    <span class="icon d-flex"><img class="me-2" src="{{ asset('images/input-edit-icon.svg') }}" alt="" /> Edit</span>
                    <input class="form-control border-0 non-editable" type="password" id="password" name="password" placeholder="**********"  readonly/>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <label class="form-label" for="setColorPicker">Set User Color</label>
                <div class="row gx-0 input_color overflow-hidden">
                    <div class="col-md-6">
                        <input class="set_color value form-control border-0" id="setColorValue" name="color_code" type="text" value="{{ $user->color_code }}" />
                    </div>
                    <div class="col-md-6 overflow-hidden">
                        <input class="set_color picker form-control border-0 px-0" id="setColorPicker" name="color_code" type="color" value="{{ $user->color_code }}" />
                    </div>
                </div>
            </div>
            <div class="col-12 cta-row text-center">
                <button class="cta orange">SAVE CHANGES</button>
            </div>
        </form>
    </div>
</section>
<style>
    .edit-icon:focus{
        outline:none !important;
        border:none !important;
        user-select: none !important;
    }
    .non-editable, .non-editable:focus{
        cursor: not-allowed;
        user-select: none !important;
    }
    .editable, .editable:focus{
        cursor: text;
    }
   .non-editable, .non-editable:focus, .editable, .editable:focus {
        color: white !important;
        background-color: #282828 !important;
        outline: none !important;
        border: none !important;
        padding-right:100px !important;
    }
</style>
<script>
    $(document).ready(function() {

        //IMP - disable inputs unless edit button is clicked
        $('.edit-icon > span').on('click', function(event){
            // 'this' inside this function refers to the <span> element that was clicked.

            // To get the ID of the parent div.edit-icon:
            var $id = $(this).parent('.edit-icon').attr('id');

            // Alternative (more robust if structure changes slightly):
            // var $id = $(this).closest('.edit-icon').attr('id');


            // Now, target the input using the retrieved ID
            $('input#' + $id).removeClass('non-editable').addClass('editable').removeAttr('readonly');

            // This line seems to be for some form state tracking.
            // Ensure 'email_input_disabled' is the correct name for the input you want to update.
            // If you have multiple sections, you might need a more dynamic way to select this input too.
            $('input[name="email_input_disabled"]').val("false");
        });


        $('.pic-image').on('click', function() {
            console.log('clicked');
            $('#profile-image').click();
        });
        let upload_input = document.querySelector('.upload-profile-icon input[type="file"]');
        let upload_icon = document.querySelector('.upload-profile-icon img');
        upload_input.addEventListener('change', function () {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    upload_icon.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });


         /**
     * validate form
     */
    const form_id = 'update-team-member-profile';
    let args = {
            rules : {
                '#name': GlobalFormValidator.validators.required,
                '#email': GlobalFormValidator.validators.required,
                '#job_code': GlobalFormValidator.validators.required,
                '#setColorValue': GlobalFormValidator.validators.required,
            },
            messages: {
                '#name': "Please select a client",
                '#email': "Please add a email address",
                '#job_code': "Please add a job code",
                '#setColorValue': "Please add a Color Code",
            },
            onSuccess: (form) => {
                form.submit();
            }
        }
    let Validator = new GlobalFormValidator(form_id, args);
    Validator.initialize();
    });
</script>

@endsection