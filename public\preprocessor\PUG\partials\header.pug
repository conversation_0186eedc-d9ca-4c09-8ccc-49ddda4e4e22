mixin header(logged=false)
	header.header#header
		.align-items-center.container-xxl.d-flex.position-relative.header_float
			#brand.brand
				a(href='/')
					img.white(src='images/logo.svg', alt='', height='44', width='120')
			if pageName === 'Status Hub'
				h1.d-none.d-lg-block.mb-0.hub Status #[i Hub]
			else
				h1.d-none.d-lg-block.mb-0.text-uppercase WE LOVE PEOPLE #[sup &TRADE;]

			nav#siteName.site-nav.ms-auto.d-flex
				if logged
					.user-pic.me-3
						img(src="images/drew-pic.jpg", alt="")
				.user-nav.position-relative
					#navCall.nav_call.align-items-center.d-flex.flex-column.justify-content-center
						span
						span
					#dropNav.drop-nav.d-none.flex-column
						a.link.d-flex.align-items-center.text-decoration-none.text-uppercase.text-nowrap.p-3(href='#') #[span.icon.me-2 #[i.bi.bi-telephone]] COnTaCT
						a.link.d-flex.align-items-center.text-decoration-none.text-uppercase.text-nowrap.p-3(href='#') #[span.icon.me-2 #[i.bi.bi-person-circle]] CLIeNT LOGIN
						button.link.d-flex.align-items-center.text-uppercase.text-nowrap.p-3.w-100.bg-transparent.border-0 #[span.icon.me-2 #[i.bi.bi-box-arrow-right]]Log out