<?php

namespace App\Http\Middleware;

use App\Models\AccessLevel;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class SuperAdminPermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();


        if (!$user) {
            abort(403, 'Access denied.');
        }

        $roleName = $user->role?->name;
        $isSuperOrAdmin = in_array($roleName, ['SuperAdmin', 'Admin']);

        $adminAccessLevelId = AccessLevel::where('name', 'Admin')->value('id');

        if ($isSuperOrAdmin || $user->access_level_id === $adminAccessLevelId) {
            return $next($request);
        }

        abort(403, 'Access denied. Only Super Admins or Admins are allowed.');
    }
}
