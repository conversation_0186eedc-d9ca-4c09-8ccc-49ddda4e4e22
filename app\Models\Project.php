<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\ProjectFile;
use App\Models\ProjectMessageFile;
use App\Models\ProjectMessageThread;

class Project extends Model
{
    use SoftDeletes;
    protected $fillable = [
        'name',
        'job_code',
        'status',
        'client_id',
        'timeline_id',
        'harvest_link',
        'invoice_schedule',
        'kickoff_type',
        'kickoff_title',
        'kickoff_description',
        'kickoff_file'
    ];

    /**
     * Extract Phase Associated with the project
     * 
     */
    public function phases()
    {
        return $this->belongsToMany(Phase::class, 'project_phase', 'project_id', 'phase_id')
            ->withPivot('project_duration', 'project_target')
            ->withTimestamps();
    }


    // Belongs to one client
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    // Belongs to one timeline
    public function timeline()
    {
        return $this->belongsTo(Timeline::class);
    }

    // Belongs to one social detail
    public function socialDetails()
    {
        return $this->belongsToMany(SocialDetail::class, 'social_details_pivot');
    }


    public function brands()
    {
        return $this->belongsToMany(Brand::class, 'brand_projects', 'project_id', 'brand_id');
    }

    public function tasks()
    {
        return $this->hasMany(Task::class, 'project_id');
    }

    public function status()
    {
        return $this->belongsTo(Status::class, 'status_id');
    }

    public function projectStatus()
    {
        return $this->belongsTo(Status::class, 'project_status_id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // Many to many with users
    public function users()
    {
        return $this->belongsToMany(User::class, 'project_user', 'project_id', 'user_id');
    }

    public function essentialMetrics()
    {
        return $this->hasMany(EssentialMetrics::class);
    }


    public function taskWithStatus($statusName)
    {
        return $this->tasks()->whereHas('status', function ($query) use ($statusName) {
            $query->where('name', $statusName);
        });
    }

    public function tasksExceptRecentlyFinished()
    {
        return $this->tasks()->whereDoesntHave('status', function ($query) {
            $query->where('name', 'Recently Finished');
        });
    }

    public function messages()
    {
        return $this->hasMany(ProjectMessage::class);
    }

    public function phaseCompletions()
    {
        return $this->hasMany(ProjectPhaseCompletion::class);
    }

    public function currentStep()
    {
        return $this->hasOne(ProjectCurrentStep::class);
    }

    public function files()
    {
        return $this->hasManyThrough(
            ProjectMessageFile::class,
            ProjectMessageThread::class,
            'project_id', // Foreign key on project_message_threads table
            'message_id', // Foreign key on project_message_files table
            'id', // Local key on projects table
            'id' // Local key on project_message_threads table
        );
    }
}
