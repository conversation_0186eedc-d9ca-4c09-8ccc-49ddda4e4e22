@extends('layout.app')
@section('title', "Forgot Password")
@section('content')
    <section>
        <div class="forgot-password-form-container">
            <form method="POST" action="{{ route('forgotPassword') }}" class="forgot-password-form">
                @csrf
                <h2 class="forgot-password-form-heading">Forgot Password</h2>

                <hr>

                <div class="forgot-password-form-body">
                    @if (session('success'))
                        <div class="message-div success-msg">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="email-div">
                        <label class="form-label" for="email">Email: </label>
                        <input class="form-control form-input @error('email') is-invalid @enderror" type="email" name="email" id="email" value="{{ old('email') }}">
                        @if($errors->has('email'))
                            <div class="validation_error">{{ $errors->first('email') }}</div>
                        @endif
                    </div>
                </div>

                <div class="submit-div">
                    <button type="submit">Send Password Reset Link</button>
                </div>

                <hr>

                <div class="message-div">
                    <div>Remember your password? <a href="{{ route('login') }}" class="login-link">Login Here</a></div>
                </div>
            </form>
        </div>
    </section>
@endsection

@push('styles')
    <style>
        .forgot-password-form-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 120px);
            background-color: #111;
            color: #fff;
        }
        .forgot-password-form {
            width: 100%;
            max-width: 450px;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .forgot-password-form-heading {
            font-family: "Futura Std", serif;
            font-weight: 700;
            font-size: 28px;
            text-align: center;
            margin-bottom: 20px;
            color: #fff;
        }
        .forgot-password-form hr {
            border-color: rgba(255, 255, 255, 0.3);
            margin: 20px 0;
        }
        .forgot-password-form-body {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-label {
            font-family: "Inter", sans-serif;
            font-size: 14px;
            margin-bottom: 5px;
            color: #CBCBCB;
        }
        .form-input {
            width: 100%;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            color: #fff;
            font-family: "Inter", sans-serif;
            transition: border-color 0.2s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #FF4C00;
        }
        .form-input.is-invalid {
            border-color: #FF4C00;
        }
        .submit-div {
            margin-top: 30px;
        }
        .submit-div button {
            width: 100%;
            padding: 12px;
            background-color: #FF4C00;
            border: none;
            border-radius: 26px;
            color: #fff;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .submit-div button:hover {
            background-color: #FF6A2D;
        }
        .validation_error {
            color: #FF4C00;
            font-family: "Inter", sans-serif;
            font-size: 12px;
            margin-top: 5px;
        }
        .message-div {
            text-align: center;
            font-family: "Inter", sans-serif;
            font-size: 14px;
            color: #CBCBCB;
            margin-top: 20px;
        }
        .message-div a {
            color: #FF4C00;
            text-decoration: none;
            transition: color 0.2s ease;
        }
        .message-div a:hover {
            color: #FF6A2D;
        }
        .message-div.success-msg {
            color: #4CAF50;
        }
    </style>
@endpush