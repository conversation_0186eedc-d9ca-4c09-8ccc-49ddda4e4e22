<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PhaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $phases = [
            [
                'name' => 'Define',
                'order' => 1,
                'icon' => asset('images/define-project-icon.svg'),
                'duration' => '1 week',
                'description' => 'End-in-mind planning. Starting off the project right by deciding deliverables, parameters and metrics for success',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Content, sitemap, wireframes',
                'order' => 2,
                'icon' => asset('images/content-project-icon.svg'),
                'duration' => '1 week',
                'description' => 'Content organization and creation. Unifying your message across copy and visuals.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Design',
                'order' => 3,
                'icon' => asset('images/design-project-icon.svg'),
                'duration' => '1 week',
                'description' => 'The message, visualized. Making sure your content shines through supporting imagery, visuals and branding.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Code',
                'order' => 4,
                'icon' => asset('images/code-project-icon.svg'),
                'duration' => '1 week',
                'description' => 'HTML, CMS, etc. Taking everything we’ve developed together and implementing it in a live environment.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Deploy and Manage',
                'order' => 5,
                'icon' => asset('images/deploy-project-icon.svg'),
                'duration' => '1 week',
                'description' => 'Launch, SEO, ongoing maintenance. Finishing off the project as strong as we started it.',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
    
        DB::table('phases')->insert($phases);
        
    }
}
