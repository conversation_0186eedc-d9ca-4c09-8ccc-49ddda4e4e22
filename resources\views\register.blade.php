@extends('layout.app')
@section('title', 'loginorg')
@section('content')
    <section>
        <div class="register-form-container">
            <form method="POST" action="{{ route('register-check') }}" class="register-form">
                @csrf
                <h2 class="register-form-heading">Register New Account</h2>

                <hr>

                <div class="register-form-body">
                    <div class="name-div">
                        <label class="form-label" for="name">Name: </label>
                        <input class="form-control form-input @error('name') is-invalid @enderror" type="text" name="name" id="name" value="{{ old('name') }}">
                        @if($errors->has('name'))
                            <div class="validation_error">{{ $errors->first('name') }}</div>
                        @endif
                    </div>

                    <div class="email-div">
                        <label class="form-label" for="email">Email: </label>
                        <input class="form-control form-input @error('email') is-invalid @enderror" type="email" name="email" id="email" value="{{ old('email') }}">
                        @if($errors->has('email'))
                            <div class="validation_error">{{ $errors->first('email') }}</div>
                        @endif
                    </div>

                    <div class="password-div">
                        <label class="form-label" for="password">Password: </label>
                        <div class="input-group">
                            <input class="form-control form-input password-input @error('password') is-invalid @enderror" type="password" name="password" id="password">
                            <button type="button" class="btn border border-secondary show_password_btn"><i class='fa fa-eye show_password_icon'></i></button>
                        </div>
                        @if($errors->has('password'))
                            <div class="validation_error">{{ $errors->first('password') }}</div>
                        @endif
                    </div>

                    <div class="c-password-div">
                        <label class="form-label" for="confirm_password">Confirm Password: </label>
                        <div class="input-group">
                            <input class="form-control form-input confirm_password-input" type="password" name="password_confirmation" id="confirm_password">
                            <button type="button" class="btn border border-secondary show_confirm_password-btn"><i class='fa fa-eye show_confirm_password-icon'></i></button>
                        </div>
                        @if($errors->has('confirm_password'))
                            <div class="validation_error">{{ $errors->first('confirm_password') }}</div>
                        @endif
                    </div>
                </div>

                <div class="submit-div">
                    <button type="submit">Register</button>
                </div>

                {{--

                <hr>

                <div class="message-div @if(Session::has('Success')) success-msg @elseif(Session::has('Error')) error-msg @endif">
                    @if(Session::has('Success'))
                        <div>{{ Session::get('Success') }} Go to <a href="login">Login Page</a></div>
                    @elseif(Session::has('Error'))
                        <div>{{ Session::get('Error') }}</div>
                    @else
                        <div>Already Registered Your Account? <a href="login" class="login-link">Login Here</a></div>
                    @endif
                </div>

                --}}

            </form>
        </div>
    </section>
@endsection
    
@push('styles')
    <style>
    .navbar {
        background-color: #111;
        padding: 15px 0;
        display: flex;
        justify-content: flex-end;
    }
    .navbar .flt-rgt {
        display: flex;
        gap: 20px;
        margin-right: 30px;
    }
    .navbar .flt-rgt a {
        text-decoration: none;
        color: #CBCBCB;
        font-family: "Inter", sans-serif;
        font-weight: 500;
        font-size: 16px;
        transition: color 0.2s ease;
    }
    .navbar .flt-rgt a:hover {
        color: #fff;
    }
    .navbar .flt-rgt a.active-page {
        color: #FF4C00;
    }
    /* Register Form Styles */
    .register-form-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 120px);
        background-color: #111;
        color: #fff;
    }
    .register-form {
        width: 100%;
        max-width: 450px;
        padding: 30px;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .register-form-heading {
        font-family: "Futura Std", serif;
        font-weight: 700;
        font-size: 28px;
        text-align: center;
        margin-bottom: 20px;
        color: #fff;
    }
    .register-form hr {
        border-color: rgba(255, 255, 255, 0.3);
        margin: 20px 0;
    }
    .register-form-body {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }
    .form-label {
        font-family: "Inter", sans-serif;
        font-size: 14px;
        margin-bottom: 5px;
        color: #CBCBCB;
    }
    .form-input {
        width: 100%;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        color: #fff;
        font-family: "Inter", sans-serif;
        transition: border-color 0.2s ease;
    }
    .form-input:focus {
        outline: none;
        border-color: #FF4C00;
    }
    .form-input.is-invalid {
        border-color: #FF4C00;
    }
    .input-group {
        display: flex;
        align-items: center;
    }
    .show_password_btn,
    .show_confirm_password-btn {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        color: #CBCBCB;
        padding: 10px;
        margin-left: -1px;
        transition: background-color 0.2s ease;
    }
    .show_password_btn:hover,
    .show_confirm_password-btn:hover {
        background-color: #FF4C00;
        border-color: #FF4C00 !important;
    }
    .submit-div {
        margin-top:30px;
    }
    .submit-div button {
        width: 100%;
        padding: 12px;
        background-color: #FF4C00;
        border: none;
        border-radius: 26px;
        color: #fff;
        font-family: "Inter", sans-serif;
        font-weight: 600;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }
    .submit-div button:hover {
        background-color: #FF6A2D;
    }
    .validation_error {
        color: #FF4C00;
        font-family: "Inter", sans-serif;
        font-size: 12px;
        margin-top: 5px;
    }
    .message-div {
        text-align: center;
        font-family: "Inter", sans-serif;
        font-size: 14px;
        color: #CBCBCB;
        margin-top: 20px;
    }
    .message-div a {
        color: #FF4C00;
        text-decoration: none;
        transition: color 0.2s ease;
    }
    .message-div a:hover {
        color: #FF6A2D;
    }
    .message-div.success-msg {
        color: #4CAF50;
    }
    .message-div.error-msg {
        color: #FF4C00;
    }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){
            $('.show_password_btn').on('click', function(event){
                event.preventDefault();
                if($('.show_password_icon').hasClass('fa-eye'))
                {
                    $('.show_password_icon').removeClass('fa-eye').addClass('fa-eye-slash');
                    $('.password-input').attr('type','text');
                }
                else
                {
                    $('.show_password_icon').removeClass('fa-eye-slash').addClass('fa-eye');
                    $('.password-input').attr('type','password');
                }
            })

            $('.show_confirm_password-btn').on('click', function(event){
                event.preventDefault();
                if($('.show_confirm_password-icon').hasClass('fa-eye'))
                {
                    $('.show_confirm_password-icon').removeClass('fa-eye').addClass('fa-eye-slash');
                    $('.confirm_password-input').attr('type','text');
                }
                else
                {
                    $('.show_confirm_password-icon').removeClass('fa-eye-slash').addClass('fa-eye');
                    $('.confirm_password-input').attr('type','password');
                }
            })
        });
    </script>
    @endpush


