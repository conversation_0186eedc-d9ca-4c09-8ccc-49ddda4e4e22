<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Contracts\Container\Container;


return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'hasPermission' => \App\Http\Middleware\TaskPermissionMiddleware::class,
            'superAdmin' => \App\Http\Middleware\SuperAdminPermissionMiddleware::class,
            'isBrand' => \App\Http\Middleware\isBrand::class,
        ]);
    })
    ->withBindings([
        Illuminate\Contracts\Console\Kernel::class => App\Console\Kernel::class,
    ])
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
