- let pageName = 'Client Portal';
- let mainPage = 'Site Analytics';
- let pageTitle = 'Audience and Reach';

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(`${mainPage} - ${pageTitle}`)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>Bear Tide Oysters</i>', false, true, false, 'Back to Client Portal', false, false, true)


        include ../layouts/projectPortal.pug
        +projectPortal('Site <i>Analytics</i>', ['Essential Metrics', 'Audience and Reach', 'Monthly Insight & Next Focus'])

        section.analytics-meta.bg-white.py-5
            .container-xxl
                hr.my-0
                .head.d-flex
                    .title
                        h2.mb-0.lh-1.text-uppercase=pageTitle
                    .select-month
                        select#month.ps-3.border-0
                            option(value='') February 2025
                            option(value='') January 2025
                            option(value='') December 2024
                        span.arrow.d-inline-flex
                            img(src='images/select-down-arrow.svg', alt='', width='14', height='9')
                    a.circle-icon.ms-auto.align-self-center(href='#') ?
                include ../layouts/analyticBox.pug
                .row.analytics-row
                    +analyticBox(229, 'Total Users in January 2025')
                    .col-md.my-5.border-left.d-flex.justify-content-center
                        .box.graph
                            p.mb-2 January visits: #[strong 28]
                            h2.mb-0 +818.4% mo/mo
                            .figure.mt-4
                                img(src="images/graph1.svg", alt="")
                    .col-md.my-5.border-left.d-flex.justify-content-center
                        .box.graph
                            p.mb-2 October 2023 visits: #[strong N/A]
                            h2.mb-0 +N/A yr/yr
                            .figure.mt-4
                                img(src="images/graph2.svg", alt="")
                .row.analytics-row
                    +analyticBox(12, 'Site Contacts')
                    .col-md.my-5
                        .box.d-flex
                            .icon.me-3
                                img(src='images/popular-pages-icon.svg', alt="")
                            .text
                                p.mb-2 Most #[strong Popular Pages]
                                ol.ps-3
                                    each li in ['/(home)', '/location', '/careers', '/our-stores', '/about', '/texttohire']
                                        li=li
                    +analyticBox(1165, 'Engaged Users')
                    +analyticBox(48.6, 'Engagement Rate', false, true)
                hr.my-0

        include ../partials/footer.pug
        +footer()