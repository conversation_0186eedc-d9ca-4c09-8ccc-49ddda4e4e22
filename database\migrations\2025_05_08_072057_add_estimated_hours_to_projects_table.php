<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasColumn('projects', 'pm_hours')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->integer('pm_hours')->nullable()->after('phase_id');
            });
        }

        if (!Schema::hasColumn('projects', 'designer_hours')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->integer('designer_hours')->nullable()->after('pm_hours');
            });
        }

        if (!Schema::hasColumn('projects', 'developer_hours')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->integer('developer_hours')->nullable()->after('designer_hours');
            });
        }
    }

    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            if (Schema::hasColumn('projects', 'pm_hours')) {
                $table->dropColumn('pm_hours');
            }

            if (Schema::hasColumn('projects', 'designer_hours')) {
                $table->dropColumn('designer_hours');
            }

            if (Schema::hasColumn('projects', 'developer_hours')) {
                $table->dropColumn('developer_hours');
            }
        });
    }
};
