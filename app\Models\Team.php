<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Team extends Model
{
    //
    protected $table = 'team';

    protected $fillable = ['name', 'description', 'access_level_id'];

    public function accessLevel()
    {
        return $this->belongsTo(AccessLevel::class);
    }

    public function users()
    {
        return $this->belongsToMany(User::class)->withTimestamps(); // assuming pivot table 'team_user'
    }
  
}
