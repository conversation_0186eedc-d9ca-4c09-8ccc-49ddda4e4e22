@extends('admin.layouts.app')
@section('title', 'Brands')
@section('content')


<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @endif
        </div>
        <div class="meta d-flex justify-content-between align-items-center">
            <div class="copy">
                <h1 class="text-white mb-0">All, <i>Enterprises</i></h1>
            </div>
        </div>
    </div>
</section>
<section class="client-project pt-0">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="projects-list-table">
            <div class="head d-flex align-items-center justify-content-between">

                @php
                    $currentYear = now()->year;
                @endphp

                <div class="years d-flex">
                    <a href="{{ route('admin-brands', ['year' => 'all']) }}" class="{{ $year === 'all' || !$year ? 'active' : '' }}">
                        All
                    </a>

                    @foreach ($years as $y)
                        <a href="{{ route('admin-brands', ['year' => $y]) }}" class="{{ $year == $y ? 'active' : '' }}">
                            {{ $y }}
                        </a>
                    @endforeach
                </div>

                <div class="pages d-flex">
                    <span>Page</span>
                    @for ($i = 1; $i <= $brands->lastPage(); $i++)
                        <a href="{{ $brands->url($i) }}" class="{{ $brands->currentPage() == $i ? 'active' : '' }}">
                            {{ $i }}
                        </a>
                    @endfor
                </div>
            </div>
            @foreach( $brands as $brand )
            <div class="meta-project d-flex orange">
                <div class="icon d-flex align-items-center justify-content-center checked"><img src="{{  set_user_image($brand->logo) }}" alt="" style="height: 100%; width: 100%; border-radius: 50%;" /></div>
                <div class="copy flex-grow-1 d-flex align-items-end justify-content-between">
                    <div class="text">
                        <h2 class="mb-1"><a href="{{ route('edit-brand', ['brand_id' => $brand->id]) }}">[{{ $brand->job_code }}]</a></h2>
                        <p class="text-white">{{ $brand->name }}</p>
                    </div>
                    <div class="cta-row"><a href="{{ route('edit-brand', ['brand_id' => $brand->id]) }}">Edit Enterprise</a></div>
                </div>
            </div>
            @endforeach
            <span class="mt-4">&nbsp;</span>
        </div>
        <hr class="mt-0 mb-4 border-white" />
    </div>
</section>
<section class="ask-question">
    <div class="container-xxl">
        <div class="d-flex align-items-center justify-content-between pb-4">
            <div class="head">
                <h2 class="mb-0">
                    Have a question?<br />
                    <i>We’re here to help.</i>
                </h2>
            </div>
            <div class="cta-row"><a class="cta link text-decoration-none text-white" href="#">GET EMAIL UPDATES</a><a class="cta link text-decoration-none text-white" href="#">NEED HELP? CONTACT</a></div>
        </div>
        <hr class="mt-0 mt-4 border-white" />
    </div>
</section>


@endsection