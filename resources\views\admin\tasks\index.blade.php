@extends('admin.layouts.app')
@section('title', 'Task Overview')
@section('content')




<!-- hshd -->
{{-- 
<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Tasks List</a>
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Tasks <i>Overview</i></h1>
            <div class="add-task ms-auto d-flex">
                <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0" href="{{ route('add-new-task') }}"
                    >NEW TASK<span class="img ms-2">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                            <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5"></path></svg></span
                ></a>
            </div>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl">
        <div class="row projects-row">
            <div class="col-md-3 col-xl-2 project-column quick-links">
                <h2 class="text-uppercase">STATUSES</h2>
                <hr class="mt-0 mb-4 border-white" />
                <div class="statuses-list d-flex flex-column">
                <a class="{{ request()->has('status') ? '' : 'active' }}" 
                    href="{{ route('admin-tasks') }}">
                    All ({{ $alltaskcount }})
                </a>
                @foreach($statuses as $status)
                @php 
                    $tasks = $status->tasks()->where('status_id', $status->id)->get();
                @endphp
                        <a class="{{ request('status') == $status->id ? 'active' : '' }}" 
                            href="{{ route('admin-tasks', ['status' => $status->id]) }}">
                            {{ $status->name }} ({{ count($tasks) }})
                        </a>
                @endforeach

                </div>
            </div>
            <div class="col-md-9 col-xl-10 project-column task-overview">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">TASKS OVERVIEW</h2>
                    <div class="sort d-flex align-items-center">
                        <div class="sort-by d-flex align-items-center">
                            <h3>Sort By: Date</h3>
                            <span class="down ms-2"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" /></span>
                        </div>
                        <div class="more-icon ms-3"><img src="{{ asset('images/three-dots-more.svg') }}" alt="" /></div>
                    </div>
                </div>
                <hr class="mt-0 mb-4 border-white" />
                <div class="task-overview-list">
                    <div class="head-sort mb-4">
                                    
                    @php
                        $status_id = request()->route('status'); 
                        $status = \App\Models\Status::find($status_id);
                    @endphp

                 
                    @if($status_id == 1)
                        <h2 class="text-white text-uppercase ur">Urgent</h2>
                    @elseif($status)
                        <h2 class="text-white text-uppercase s->n">{{ $status->name }}</h2>
                    @else
                        <h2 class="text-white text-uppercase sn">{{ $status_name }}</h2>
                    @endif
                    </div>
                    
                    @foreach( $projects as $project )
                        @if( count($project->tasks) > 0 )
                        @foreach(  $project->tasks as $task  )
                            <div class="task-project d-flex purple">
                                <div class="star-check d-flex">
                                    <div class="check-icon me-3"><i class="bi bi-square text-white"></i></div>
                                </div>
                                <div class="copy d-flex flex-column flex-grow-1">
                                    <h2 class="text-uppercase">{{ $project->job_code }} {{ $project->name }} - {{ $project->status->name }}</h2>
                                        @foreach( $project->tasks as $task )
                                        @if( $task->status->name !== 'Urgent' )
                                            <div class="detail d-flex align-items-center w-100">
                                                <div class="date me-3">{{ $task->created_at->format('M j') }}</div>
                                                <div class="task me-3"><a href="{{ route('view-task', ['task_id' => $task->id] ) }}">{{ $task->name }}</a></div>
                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                    <div class="text over-text d-grid">
                                                        <p class="text-truncate">{{ $task->description }}.</p>
                                                    </div>
                                                    <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', ['task_id' => $task->id] ) }}">Read More</a>
                                                </div>
                                            </div>
                                            @else
                                            <div class="detail d-flex align-items-center w-100">
                                                <div class="date-cta py-1 px-2 rounded-1 d-flex align-items-center me-3"><i class="bi bi-clock me-1"></i> {{ $task->created_at->format('M j') }}</div>
                                                <div class="task me-3"><a href="{{ route('view-task', ['task_id' => $task->id] ) }}">{{ $task->name }}</a></div>
                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                    <div class="text over-text d-grid">
                                                        <p class="text-truncate">{{ $task->description }}.</p>
                                                    </div>
                                                    <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', ['task_id' => $task->id] ) }}">Read More</a>
                                                </div>
                                            </div>
                                            @endif
                                        @endforeach
                                </div>
                            </div>
                        @endforeach
                        @endif
                       
                    @endforeach                 
                </div>
            </div>
        </div>
    </div>
</section> --}}


<livewire:task-overview />
@endsection

@push('styles')
<style>

.rotate-180 {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}
.sort-arrow img {
    transition: transform 0.3s ease;
}
.transition-blur {
    transition: filter 0.3s ease;
}

.blur-sm {
    filter: blur(4px);
}

.detail{
    margin-top:10px;
}

/* Search Bar Styles */
.search-container {
    width: 100%;
    max-width: 1250px; /* limits the max width for desktop */
    margin: 0 auto 2rem auto;
    padding: 0 1rem; /* adds padding on small screens */
}

.search-wrapper {
    position: relative;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    width: 100%;
}

.form-control.background-dark {
    background-color: #2a2a2a;
    border: none;
    color: #ffffff;
    padding: 1rem 1rem 1rem 3rem;
    font-size: 1rem;
    height: auto;
    width: 100%; /* ✅ ensures input fills the wrapper */
    box-sizing: border-box;
}

@media (max-width: 768px) {
    .form-control.background-dark {
        font-size: 0.9rem;
        padding: 0.9rem 0.9rem 0.9rem 2.5rem;
    }

    .search-icon,
    .clear-icon {
        font-size: 1rem;
    }
}

.search-wrapper:hover, .search-wrapper:focus-within {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}
.form-control.background-dark {
    background-color: #2a2a2a;
    border: none;
    color: #ffffff;
    padding: 1rem 1rem 1rem 3rem;
    font-size: 1rem;
    height: auto;
}
.form-control.background-dark::placeholder {
    color: #adb5bd;
}
.form-control.background-dark:focus {
    box-shadow: none;
    border: none;
    outline: 2px solid #ff4c00;
}
.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #ff4c00;
    font-size: 1.2rem;
    z-index: 10;
}
.clear-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #adb5bd;
    font-size: 1rem;
    cursor: pointer;
    z-index: 10;
    transition: color 0.2s ease;
}
.clear-icon:hover {
    color: #ff4c00;
}
/* Alphabet filter styling */
.alphabet-letter {
    border-radius: 4px;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #fff;
    font-weight: 500;
    position: relative;
}

.alphabet-letter:hover {
    background-color: rgba(255, 102, 0, 0.3);
    color: #fff;
}

.alphabet-active {
    background-color: #ff4c00;
    color: #fff;
    box-shadow: 0 0 10px rgba(255, 102, 0, 0.5);
}

.loading-blur {
    opacity: 0.6;
    filter: blur(1px);
    pointer-events: none;
}

.disabled-letter {
    opacity: 0.3;
    cursor: default;
    pointer-events: none;
}

.disabled-letter {
    opacity: 0.3;
    cursor: default;
    pointer-events: none;
}

.letter-count {
    font-size: 0.7em;
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: rgba(255, 102, 0, 0.8);
    border-radius: 50%;
    padding: 1px 5px;
    min-width: 18px;
    text-align: center;
}

</style>



@endpush

@push('script')

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.querySelector('.form-control.background-dark');
            const clearButton = document.querySelector('.clear-icon');
            
            if (searchInput && clearButton) {
                clearButton.addEventListener('click', function() {
                    searchInput.value = '';
                    searchInput.focus();
                    
                    
                    if (typeof window.Livewire !== 'undefined') {
                        
                        const event = new Event('input', { bubbles: true });
                        searchInput.dispatchEvent(event);
                    }
                });
            }
        });
    </script>

@endpush