@extends('layout.app')
@section('title', 'Add Phase')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">
                    <h4>
                        @if(request()->route()->named('add-phase'))
                            Add Phase
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('add-phase'))
                        @if(Auth::user()->role_id=='1')
                            <div class="see-all-tasks-btn-div">
                                <a class="me-2" href="{{ route('phases') }}"><button>See All Phase <i class="fa fa-plus"></i></button></a>
                            </div>
                        @endif

                        @if(Session::has('phase_added_success_message'))
                            <div class="bg-success text-light text-center mx-auto my-1 p-2 w-50 rounded">
                                {{ Session::get('phase_added_success_message') }}
                            </div>
                        @endif
                        <div class="task-details-div rounded mx-auto">
                            <form action="{{ route('save-phase') }}" method="post" name="add-phase-form">
                                @csrf
                                @method("post")
                                <div class="task-details-div-row1 d-flex mb-1">
                                    <label class="task-details-div-label task_name_label">Name: </label>
                                    <input type="text" class="task-details-div-span task_name_span" name="phase_name" value="">
                                </div>
                                <div class="task-details-div-row1 d-flex mb-1">
                                    <label class="task-details-div-label task_name_label">Duration: </label>
                                    <input type="text" class="task-details-div-span task_name_span" name="phase_duration" value="">
                                </div>
                                <div class="task-details-div-row1 d-flex mb-1">
                                    <label class="task-details-div-label task_name_label">Order: </label>
                                    <input type="text" class="task-details-div-span task_name_span" name="phase_order" value="">
                                </div>
                                <div class="task-details-div-row1 d-flex mb-1">
                                    <label class="task-details-div-label task_name_label">Description: </label>
                                    <input type="text" class="task-details-div-span task_name_span" name="phase_description" value="">
                                </div>
                                <button type="submit">Submit</button>
                            </form>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }

        .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;

            left: 0;
            z-index: 0;
            height: calc(100vh - 50px);
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:95%;
            padding:10px;
            overflow:scroll;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            /* padding:0px 50px;
            display:flex;
            justify-content:center; */
        }
        .see-all-tasks-btn-div{
            display: flex;
            justify-content: end;
        }
        form[name="edit-task-form"]{
            width:100%;
            text-align:center;
        }
        .task-details-div{
            margin-top:10px;
            padding:10px;
            width:50%;
            border:1px solid grey;
            display: flex;
            justify-content: center;
        }
        .task-details-div-label{
            width:25%;
            text-align:left;
        }
        .task-details-div-span{
            width:75%;
            text-align:left;
        }
        .task_not_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){

        });
    </script>
@endpush