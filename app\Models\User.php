<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    protected $guard = [];

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */    protected $fillable = [
        'name',
        'email',
        'password',
        'role_id',
        'access_level_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    public function admin_brands()
    {
        return $this->hasMany(Brand::class, 'admin_id');
    }

    public function brands()
    {
        return $this->belongsToMany(Brand::class, 'brand_user', 'user_id', 'brand_id');
    }

    // public function company(){
    //     return $this->hasMany(Company::class);
    // }

    public function tasks()
    {
        return $this->belongsToMany(Task::class, 'task_user', 'user_id', 'task_id')->withTimestamps();
    }


    public function projects()
    {
        return $this->belongsToMany(Project::class, 'project_user', 'user_id', 'project_id');
    }

    // public function companies()
    // {
    //     return $this->belongsToMany(Company::class, 'company_user', 'user_id', 'company_id');
    // }

    public function clients()
    {
        return $this->belongsToMany(Client::class, 'user_client', 'user_id', 'client_id');
    }

    public function client()
    {
        return $this->hasOne(Client::class);
    }


    public function teams()
    {
        return $this->belongsToMany(Team::class)->withTimestamps();
    }

    public function getAccessLevelForTeam($teamId): ?string
    {
        $team = $this->teams()->where('teams.id', $teamId)->with('accessLevel')->first();
        return $team?->accessLevel?->name; // e.g., 'team_member', 'manager'
    }

    public function adminTasks()
    {
        return $this->hasMany(Task::class, 'admin_id');
    }

    public function accessLevel()
    {
        return $this->belongsTo(AccessLevel::class);
    }


    public function hasRole($roles)
    {
        if (!$this->role) {
            return false;
        }

        if (is_array($roles)) {
            return in_array($this->role->name, $roles);
        }

        return $this->role->name === $roles;
    }

    /**
     * Get the holidays associated with the user.
     */
    public function holidays()
    {
        return $this->hasMany(Holiday::class);
    }

    /**
     * Get the resource associated with the user.
     */
    public function resource()
    {
        return $this->hasOne(Resource::class);
    }

    public function roles()
    {
        return $this->belongsToMany(Role::class);
    }

    public function hasRoleName($roleName)
    {
        return $this->role === $roleName;
    }
}
