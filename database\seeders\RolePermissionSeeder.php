<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    // public function run(): void
    // {
    //     //
    //     $permissions = DB::table('permissions')->pluck('id', 'name'); // Fetching name => id pairs
    //     $roles = DB::table('roles')->whereIn('name',['Admin','SuperAdmin'])->get();
    //     foreach( $roles as $role ) {
    //         foreach( $permissions as $name => $id ){
    //             DB::table('role_permissions')->insert([
    //                 'role_id' => $role,
    //                 'permission_id' => DB::table('permissions')->where('name', $name)->value('id'),
    //                 'created_at' => Carbon::now(),
    //                 'updated_at' => Carbon::now(),
    //             ]);
    //         }
    //     }
    // }

    public function run(): void
    {
        $permissions = DB::table('permissions')->pluck('id', 'name'); // name => id

        $roles = DB::table('roles')->whereIn('name', ['SuperAdmin'])->get();

        foreach ($roles as $role) {
            foreach ($permissions as $name => $id) {
                // Avoid duplicate inserts if the seeder is re-run
                $exists = DB::table('role_permissions')
                    ->where('role_id', $role->id)
                    ->where('permission_id', DB::table('permissions')->where('name', $name)->value('id'))
                    ->exists();

                if (!$exists) {
                    DB::table('role_permissions')->insert([
                        'role_id' => $role->id,
                        'permission_id' => DB::table('permissions')->where('name', $name)->value('id'),
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                }
            }
        }
    }
}
