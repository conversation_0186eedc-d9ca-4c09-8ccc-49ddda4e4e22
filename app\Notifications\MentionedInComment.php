<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class MentionedInComment extends Notification //implements ShouldQueue we will set it when queues will working
{
    use Queueable;

    protected $comment;
    protected $sender;
    protected $task;

    public function __construct($comment, $sender, $task)
    {
        $this->comment = $comment;
        $this->sender = $sender;
        $this->task = $task;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $url = route('view-task', ['task_id' => $this->task->id]);

        $project = $this->task->project;



        return (new MailMessage)
            ->subject('New comment on ' . $project->name . ' - ' . $this->task->name)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($this->sender->name . ' mentioned you in a comment:')
            ->line($this->comment)

            ->action('View Comment', $url)
            ->line('Thank you for using our application!');
    }
}
