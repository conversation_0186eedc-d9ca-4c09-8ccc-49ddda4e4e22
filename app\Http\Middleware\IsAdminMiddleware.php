<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IsAdminMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // echo optional(Auth::user())->role;
        // Example check for admin (you can replace this with your own logic)
        if (optional(Auth::user())->role != 'Admin') {
            abort(403);
        }

        return $next($request);  // Continue to the next request if admin
    }
}
