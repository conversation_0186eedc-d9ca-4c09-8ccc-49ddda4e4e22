@extends('layout.app')
@section('title', 'Login')
@section('content')
    <section>
        <div class="login-form-container">
            <form method="POST" action="{{ route('login-check') }}" class="login-form">
                @csrf
                <h2 class="login-form-heading">Log-in Your Account</h2>

                <hr>

                <div class="login-form-body">
                    <div class="email-div">
                        <label class="form-label" name="email" for="email">Email: </label>
                        <input class="form-control form-input @error('email') is-invalid @enderror" type="email" name="email" id="email" value="{{ old('email') }}">
                        @if($errors->has('email'))
                            <div class="validation_error">{{ $errors->first('email') }}</div>
                        @endif
                    </div>

                    <div class="password-div">
                        <label class="form-label" name="password" for="password">Password: </label>
                        <div class="input-group">
                            <input class="form-control form-input password-input @error('password') is-invalid @enderror" type="password" name="password" id="password">
                            <button type="button" class="btn border border-secondary show_password_btn" title="Show Password" style="height: 56px;"><i class='fa fa-eye show_password_icon'></i></button>
                        </div>
                        @if($errors->has('password'))
                            <div class="validation_error">{{ $errors->first('password') }}</div>
                        @endif
                    </div>

                    <div class="remember_me-div">
                        <label for="remember">Remember Me</label>
                        <input type="checkbox" id="remember" name="remember">
                    </div>
                </div>

                <div class="submit-div" class="mt-3">
                    <button type="submit">Login</button>
                </div>

                <hr>

                <div class="message-div @if(Session::has('email_not_registered_error') || Session::has('password_incorrect_error')) error-msg @endif">
                    @if(Session::has('email_not_registered_error'))
                        <div>
                            {{ Session::get('email_not_registered_error') }}
                            <br>
                            Register with New Email <a href={{ route('register') }}>here</a>
                        </div>
                    @elseif(Session::has('password_incorrect_error'))
                        <div>
                            {{ Session::get('password_incorrect_error') }}
                            <br>
                            Please enter correct credentials
                        </div>
                    {{-- 
                    @else
                        <div>Not Registered Your Account Yet? <a href="register" class="register-link">Register Here</a></div> #111
                    --}}
                    @endif
                </div>
            </form>
        </div>

        <div class="col-md-12 text-center">
            <p><a class="text-decoration-none" href="{{ route('forgotPasswordPage') }}">Forgot
                    password?</a></p>
        </div>
    </section>
@endsection
@push('styles')
    <style>
        .login-form-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 120px);
            background-color: #111;
            color: #fff;
        }
        .login-form {
            width: 100%;
            max-width: 450px;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .login-form-heading {
            font-family: "Futura Std", serif;
            font-weight: 700;
            font-size: 28px;
            text-align: center;
            margin-bottom: 20px;
            color: #fff;
        }
        .login-form hr {
            border-color: rgba(255, 255, 255, 0.3);
            margin: 20px 0;
        }
        .login-form-body {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-label {
            font-family: "Inter", sans-serif;
            font-size: 14px;
            margin-bottom: 5px;
            color: #CBCBCB;
        }
        .form-input {
            width: 100%;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            color: #fff;
            font-family: "Inter", sans-serif;
            transition: border-color 0.2s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #FF4C00;
        }
        .form-input.is-invalid {
            border-color: #FF4C00;
        }
        .input-group {
            display: flex;
            align-items: center;
        }
        .show_password_btn {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: #CBCBCB;
            padding: 10px;
            margin-left: -1px;
            transition: background-color 0.2s ease;
        }
        .show_password_btn:hover {
            background-color: #FF4C00;
            border-color: #FF4C00 !important;
        }
        .remember_me-div {
            display: flex;
            align-items: center;
            gap: 10px;
            font-family: "Inter", sans-serif;
            font-size: 14px;
            color: #CBCBCB;
        }
        .remember_me-div input[type="checkbox"] {
            accent-color: #FF4C00;
        }
        .submit-div {
            margin-top:10px;
        }
        .submit-div button {
            width: 100%;
            padding: 12px;
            background-color: #FF4C00;
            border: none;
            border-radius: 26px;
            color: #fff;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .submit-div button:hover {
            background-color: #FF6A2D;
        }
        .validation_error {
            color: #FF4C00;
            font-family: "Inter", sans-serif;
            font-size: 12px;
            margin-top: 5px;
        }
        .message-div {
            text-align: center;
            font-family: "Inter", sans-serif;
            font-size: 14px;
            color: #CBCBCB;
            margin-top: 20px;
        }
        .message-div a {
            color: #FF4C00;
            text-decoration: none;
            transition: color 0.2s ease;
        }
        .message-div a:hover {
            color: #FF6A2D;
        }
        .message-div.error-msg {
            color: #FF4C00;
        }
    </style>
@endpush

@push('script')
		<script>
        $(document).ready(function(){
            $('.show_password_btn').on('click', function(event){
                event.preventDefault();
                if($('.show_password_icon').hasClass('fa-eye'))
                {
                    $('.show_password_btn').attr('title','Hide Password');
                    $('.show_password_icon').removeClass('fa-eye').addClass('fa-eye-slash');
                    $('.password-input').attr('type','text');
                }
                else
                {
                    $('.show_password_btn').attr('title','Show Password');
                    $('.show_password_icon').removeClass('fa-eye-slash').addClass('fa-eye');
                    $('.password-input').attr('type','password');
                }
            })
        });
    </script>
@endpush