<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Project;
use Illuminate\Support\Facades\DB;

class ProjectList extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    public $page = 1;
    public $year = 'all';
    public $alphabet = null;
    public $years = [];
    public $sortColumn = 'name';
    public $sortDirection = 'asc';

    public $search = '';

    protected $queryString = [
        'year' => ['except' => 'all'],
        'alphabet' => ['except' => null],
        'page' => ['except' => 1],
        'sortColumn' => ['except' => 'name'],
        'sortDirection' => ['except' => 'asc'],
        'search' => ['except' => ''],
    ];

    public function mount()
    {
        $this->years = Project::selectRaw('YEAR(created_at) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        $this->search = '';
    }

    public function updatingYear()
    {
        $this->resetPage();
    }

    public function updatingAlphabet()
    {
        $this->resetPage();
    }

    public function updatingSortColumn()
    {
        $this->resetPage();
    }

    public function updatingSortDirection()
    {
        $this->resetPage();
    }

    public function sortBy($column)
    {
        if ($this->sortColumn === $column) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortColumn = $column;
            $this->sortDirection = 'asc';
        }
    }

    public function getProjectsByFirstLetter()
    {
        // Get all first letters that exist in the database
        $query = Project::query();

        // Apply year filter to the query if set
        if ($this->year !== 'all') {
            $query->whereYear('created_at', $this->year);
        }

        // Get first letters with counts
        $letterCounts = $query->select(DB::raw('UPPER(LEFT(name, 1)) as first_letter'), DB::raw('COUNT(*) as count'))
            ->groupBy('first_letter')
            ->orderBy('first_letter')
            ->pluck('count', 'first_letter')
            ->toArray();

        return $letterCounts;
    }

    public function render()
    {
        $query = Project::query();


        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('job_code', 'like', '%' . $this->search . '%');
            });
        }

        // Filter by year
        if ($this->year !== 'all') {
            $query->whereYear('created_at', $this->year);
        }

        // Filter by alphabet
        if ($this->alphabet) {
            $query->where('name', 'like', $this->alphabet . '%');
        }

        // Add a small delay to make the loading effect noticeable
        if ($this->alphabet) {
            usleep(300000); // 300ms delay
        }

        // Sort data
        $projects = $query->orderBy($this->sortColumn, $this->sortDirection)
            ->paginate(10);

        // Get letter counts for the alphabet filter
        $letterCounts = $this->getProjectsByFirstLetter();

        return view('livewire.admin.project-list', [
            'projects' => $projects,
            'letterCounts' => $letterCounts
        ]);
    }
}
