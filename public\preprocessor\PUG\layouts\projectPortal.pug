mixin projectPortal(head, linkList)
    section.client-project.bg-white
        .container-xxl
            .heading.d-md-flex.align-items-md-baseline.justify-content-between
                h2.mb-0 !{head}
                .links
                    each link in linkList
                        - let href = link.replace(/[^a-zA-Z0-9]/g, '-').replace('--','').toLowerCase() + '.html';
                        a(href=href, class=(link === pageTitle ? 'active' : ''))= link
