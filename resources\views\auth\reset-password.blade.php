@extends('layout.app')
@section('title', "Reset Password")
@section('content')
    <section>
        <div class="reset-password-form-container">
            <form method="POST" action="{{ route('password.update') }}" class="reset-password-form">
                @csrf
                <h2 class="reset-password-form-heading">Reset Password</h2>

                <hr>

                <div class="reset-password-form-body">
                    @if (session('success'))
                        <div class="message-div success-msg">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="email-div">
                        <label class="form-label" for="email">Email: </label>
                        <input class="form-control form-input @error('email') is-invalid @enderror" type="email" name="email" id="email" value="{{ request('email') }}">
                        @if($errors->has('email'))
                            <div class="validation_error">{{ $errors->first('email') }}</div>
                        @endif
                    </div>

                    <div class="password-div">
                        <label class="form-label" for="password">New Password: </label>
                        <div class="input-group">
                            <input class="form-control form-input password-input @error('password') is-invalid @enderror" type="password" name="password" id="password">
                            <button type="button" class="btn border border-secondary show_password_btn"><i class='fa fa-eye show_password_icon'></i></button>
                        </div>
                        @if($errors->has('password'))
                            <div class="validation_error">{{ $errors->first('password') }}</div>
                        @endif
                    </div>

                    <div class="confirm-password-div">
                        <label class="form-label" for="password_confirmation">Confirm Password: </label>
                        <div class="input-group">
                            <input class="form-control form-input confirm_password-input" type="password" name="password_confirmation" id="password_confirmation">
                            <button type="button" class="btn border border-secondary show_confirm_password-btn"><i class='fa fa-eye show_confirm_password-icon'></i></button>
                        </div>
                        @if($errors->has('password_confirmation'))
                            <div class="validation_error">{{ $errors->first('password_confirmation') }}</div>
                        @endif
                    </div>

                    <input type="hidden" name="token" value="{{ $token }}">
                </div>

                <div class="submit-div">
                    <button type="submit">Reset Password</button>
                </div>

                <hr>

                <div class="message-div">
                    <div>Remember your password? <a href="{{ route('login') }}" class="login-link">Login Here</a></div>
                </div>
            </form>
        </div>
    </section>
@endsection

@push('styles')
    <style>
        .reset-password-form-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: calc(100vh - 120px);
            background-color: #111;
            color: #fff;
        }
        .reset-password-form {
            width: 100%;
            max-width: 450px;
            padding: 30px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .reset-password-form-heading {
            font-family: "Futura Std", serif;
            font-weight: 700;
            font-size: 28px;
            text-align: center;
            margin-bottom: 20px;
            color: #fff;
        }
        .reset-password-form hr {
            border-color: rgba(255, 255, 255, 0.3);
            margin: 20px 0;
        }
        .reset-password-form-body {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .form-label {
            font-family: "Inter", sans-serif;
            font-size: 14px;
            margin-bottom: 5px;
            color: #CBCBCB;
        }
        .form-input {
            width: 100%;
            padding: 10px;
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            color: #fff;
            font-family: "Inter", sans-serif;
            transition: border-color 0.2s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #FF4C00;
        }
        .form-input.is-invalid {
            border-color: #FF4C00;
        }
        .input-group {
            display: flex;
            align-items: center;
        }
        .show_password_btn,
        .show_confirm_password-btn {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
            color: #CBCBCB;
            padding: 10px;
            margin-left: -1px;
            transition: background-color 0.2s ease;
        }
        .show_password_btn:hover,
        .show_confirm_password-btn:hover {
            background-color: #FF4C00;
            border-color: #FF4C00 !important;
        }
        .submit-div {
            margin-top: 30px;
        }
        .submit-div button {
            width: 100%;
            padding: 12px;
            background-color: #FF4C00;
            border: none;
            border-radius: 26px;
            color: #fff;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .submit-div button:hover {
            background-color: #FF6A2D;
        }
        .validation_error {
            color: #FF4C00;
            font-family: "Inter", sans-serif;
            font-size: 12px;
            margin-top: 5px;
        }
        .message-div {
            text-align: center;
            font-family: "Inter", sans-serif;
            font-size: 14px;
            color: #CBCBCB;
            margin-top: 20px;
        }
        .message-div a {
            color: #FF4C00;
            text-decoration: none;
            transition: color 0.2s ease;
        }
        .message-div a:hover {
            color: #FF6A2D;
        }
        .message-div.success-msg {
            color: #4CAF50;
        }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){
            $('.show_password_btn').on('click', function(event){
                event.preventDefault();
                if($('.show_password_icon').hasClass('fa-eye'))
                {
                    $('.show_password_icon').removeClass('fa-eye').addClass('fa-eye-slash');
                    $('.password-input').attr('type','text');
                }
                else
                {
                    $('.show_password_icon').removeClass('fa-eye-slash').addClass('fa-eye');
                    $('.password-input').attr('type','password');
                }
            })

            $('.show_confirm_password-btn').on('click', function(event){
                event.preventDefault();
                if($('.show_confirm_password-icon').hasClass('fa-eye'))
                {
                    $('.show_confirm_password-icon').removeClass('fa-eye').addClass('fa-eye-slash');
                    $('.confirm_password-input').attr('type','text');
                }
                else
                {
                    $('.show_confirm_password-icon').removeClass('fa-eye-slash').addClass('fa-eye');
                    $('.confirm_password-input').attr('type','password');
                }
            })
        });
    </script>
@endpush