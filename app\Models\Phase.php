<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Phase extends Model
{
    //
    protected $table = 'phases';
    protected $fillable = [
        'name',
        'order',
        'icon',
        'description',
        'duration'
    ];
    protected $colorClassMap = [
        '#FF5811' => 'orange',
        '#65CCB0' => 'green',
        '#F45689' => 'pink',
        '#A15CD4' => 'purple',
        '#ffff'   => 'dull',
    ];

    public function getColorClass()
    {
        return $this->colorClassMap[$this->color_code] ?? $this->colorClassMap['#ffff'];
    }

    public function projects()
    {
        return $this->belongsToMany(Project::class, 'project_phase', 'phase_id', 'project_id')
            ->withPivot('project_duration', 'project_target')
            ->withTimestamps();;
    }

    public function categories()
    {
        return $this->hasMany(Category::class, 'phase_id');
    }

    public function completion()
    {
        return $this->hasOne(ProjectPhaseCompletion::class)->where('project_id', request()->project_id);
    }
}
