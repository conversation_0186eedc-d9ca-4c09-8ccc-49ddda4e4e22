<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectMessage extends Model
{
    use HasFactory;

    protected $table = 'project_messages';

    protected $fillable = [
        'project_id',
        'created_by_user_id',
        'title',
        'due_date',
        'reason',
        'description',
    ];

    /**
     * Get the project that owns the message.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the user who created this message.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    /**
     * Get the attachments for the message.
     */
    public function attachments()
    {
        // This is the correct relationship: one message has many attachments
        return $this->hasMany(ProjectMessageAttachment::class, 'message_id');
    }
}