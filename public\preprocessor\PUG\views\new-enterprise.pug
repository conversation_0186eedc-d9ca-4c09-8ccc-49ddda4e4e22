- let pageName = 'New Enterprise';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/fieldForms.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('New <i>Enterprise</i>', 'Back to Team Portal')

        section.client-project.project-dashboard
            .container-xxl.projects-row
                .project-column.task-overview
                    .col-head.align-items-center.d-flex.justify-content-between
                        h2.text-uppercase SET UP NEW Brand
                        p.fst-italic Note: Only clients who have sub-companies should be set up as a brand
                    hr.mt-0.mb-4.border-white

                    form.row.form-wrap(action="")
                        .col-md-4.col-xl-3.mb-4
                            +inputField('jobCode', 'text', 'JOB CODE Prefix')
                            .form-text.fst-italic.opacity-50 Suggested Length: 2-4 Characters
                        .col-md-8.col-xl-9.mb-4
                            +inputField('clientName', 'text', 'Add a client name...')
                        .col-head.col-12.mt-5
                            h2.text-uppercase CONTACTS
                            hr.mt-0.mb-4.border-white
                        .col-12.mt-4
                            h2.text-white Add Contacts
                        .col-12.mt-4
                            +inputFieldIcon('email', 'email', 'email')
                        .col-head.col-12.mt-5
                            h2.text-uppercase BRAND
                            hr.mt-0.mb-4.border-white
                        .col-12.mt-4
                            .row
                                .col-md-4
                                    h2.text-white Add clients to brand
                                .col-md-8
                                    .brand-wrap
                                        mixin addBrand(name, added = false, claimed = false)
                                            - const cta = added ? 'Added' : 'Add'
                                            .brand-select.d-flex.align-items-center.justify-content-between(class={ claimed, added })
                                                .name.text-uppercase= name
                                                if claimed
                                                    button.cta.text-uppercase.mt-0(type="button") Claimed
                                                else
                                                    button.cta.text-uppercase.mt-0(type="button", class=added ? 'added' : '')= cta

                                        +addBrand('ASHLAND (ASH)', true)
                                        +addBrand('ASTEC BIO (ABI)')
                                        +addBrand('BAY STATE PHYSICAL THERAPY (BSPT)')
                                        +addBrand('BOLTON HISTORIAL MUSEUM (BHM)')
                                        +addBrand('CENTER FOR HEALTH INFORMATION AND ANALYSIS (CHIA)')
                                        +addBrand('CLIPPER PETROLEUM (CP)', false, true)
                                        +addBrand('CONSORTIUM FOR ENERGY EFFICIENCY (CEE)')
                                        - for(let i = 0; i < 5; i++)
                                            +addBrand('CYCLYX (CYC)')
                        .col-12.mt-4
                            .row.align-items-center
                                .col-md-6
                                    h2.text-white Upload Logo
                                .col-md-6.text-end
                                    .upload-btn-wrapper
                                        button.btn-upload.text-uppercase.text-white.d-flex.align-items-center #[i.bi.bi-upload.me-2] Upload a file
                            .form-text.fst-italic.opacity-50 Suggested Format: One-color white SVG or PNG
                        .col-12.mt-5.text-center
                            button.cta.text-uppercase.mt-0(type="submit") CREATE Brand

        include ../partials/footer.pug
        +footer(true)