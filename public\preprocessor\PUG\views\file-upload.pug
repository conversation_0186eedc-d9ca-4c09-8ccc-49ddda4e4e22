- let pageName = 'Client Portal';
- let mainPage = 'File Upload';
- let pageTitle = `${pageName}: ${mainPage}`;

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>[SP-003-25] Sample Project 3</i>', false, true, true, 'Back to Client Portal')

        section.client-project.bg-white
            .container-xxl
                .heading.d-flex.align-items-center.justify-content-between
                    h2.mb-0 File #[i Upload]
                    .links
                        each link, index in ['Messages', 'Project Files (23)']
                            a(href="#" class=(index === 0 ? 'active' : ''))=link

        section.actions-need.bg-white.pb-4
            .container-xxl
                .heading
                    h2.text-uppercase.text-orange.mb-1 LATEST Files
                    h3.d-flex.align-items-center.mb-2 Upload on 2/15/2025

        section.bg-white.pb-5
            .container-xxl
                .message-box.d-flex
                    .pic.bg-black
                    .message-wrap.d-flex.align-items-start
                        .message
                            h2 #[span.quote "]Here are all the files I have#[span.quote "]
                            .copy
                                p Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam viverra dolor eget velit suscipit dapibus. Suspendisse posuere elit ut arcu facilisis semper. Nunc accumsan ut justo sed sollicitudin. Fusce... #[a(href='message.html') See More ]
                                .meta.d-flex
                                    a.user.me-4(href="#") Client F.
                                    span.date 12/12/25
                .upload-cta-row
                    .row.align-items-md-center
                        mixin ctaFileBox(icon, text)
                            .col-md-3.mt-3
                                a.cta-file.d-flex.align-items-center.text-decoration-none(href="#") #[span.icon.d-flex.align-items-center.justify-content-center.me-3 #[img(src=`images/${icon}-file-icon.svg`, alt="", width="20", height="20")]] #[span.text.text-truncate=text]
                        +ctaFileBox('tagline', 'Tagline.svg')
                        +ctaFileBox('pic', '1200px_Headshot_John_Johny_yes_pappa')
                        +ctaFileBox('docs', 'Website-Copy.docx')
                        .col-md-3.mt-3
                            a.more-file.text-decoration-none(href="#") 6 MORE, SEE ALL

        section.bg-white.uploaded-files.pb-5
            .container-xxl.pb-5
                .head.d-flex.justify-content-between.pb-3
                    h2.mb-0 All Files (23)
                    .pages.d-flex
                        span Page
                        each i, index in [1,2]
                            a(href="#", class=(index === 0 ? 'active' : ''))=i

                .row.files-wrap.gx-0
                    mixin fileBox(pic, name, iconSelector=false)
                        .col-md-3.box-wrap.p-3
                            .box
                                .img.mb-3(class=(iconSelector ? 'icon d-flex align-items-center justify-content-center' : 'pic'))
                                    img(src=`images/${pic}.jpg`, alt="")
                                .text.text-center
                                    h2.text-truncate=name
                                    h3.mb-0 Added by #[span.name Client F.] on #[span.date 12/12/25] | #[span.size 2MB]
                    +fileBox('fp1', 'Tagline.svg', true)
                    +fileBox('fp2', '1200px_Headshot_John_Smith_johny_johny_yes_pappa')
                    +fileBox('fp3', 'Website-Copy.docx', true)
                    +fileBox('fp4', 'Stockphoto.jpg')
                    +fileBox('fp5', 'Jeremy’s Favorite Cat Food.xlsx')
                    +fileBox('fp6', 'Beans.ppt', true)
                    +fileBox('fp7', 'Markup_Report.pdf')
                    +fileBox('fp8', 'Markup_Report.pdf')
                    +fileBox('fp9', 'Vacation Photos.zip', true)
                    +fileBox('fp10', 'Final_Final_Final.psd', true)
                    +fileBox('fp11', 'Merchandise.txt', true)
                    +fileBox('fp3', 'Radio_Ad.mp3', true)
                    +fileBox('fp9', 'Vacation Photos.zip', true)
                    +fileBox('fp10', 'Final_Final_Final.psd', true)
                    +fileBox('fp11', 'Merchandise.txt', true)
                    +fileBox('fp3', 'Radio_Ad.mp3', true)

                .d-flex.justify-content-end.pt-3
                    .pages.d-flex
                        span Page
                        each i, index in [1,2]
                            a(href="#", class=(index === 0 ? 'active' : ''))=i






        include ../partials/footer.pug
        +footer()