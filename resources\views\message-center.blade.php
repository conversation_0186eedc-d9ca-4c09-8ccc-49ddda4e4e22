@extends('layout.app')
@section('title', 'Message Center')
@section('content')

    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                @if (admin_superadmin_permissions() && !Auth::user()->has<PERSON><PERSON><PERSON><PERSON>('Client Member'))
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('statushub') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to StatusHub
                    </a>
                @else
                    <a class="d-inline-flex align-items-center text-decoration-none"
                        href="{{ route('client.dashboard') }}"><img class="me-2"
                            src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Client Portal</a>
                @endif
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project: <i>[{{ $project->job_code }}] {{ $project->name }}</i></h1>
                    <div class="page-links mt-3">
                        <a class="" href="{{ route('track-project', ['id' => $project->id]) }}">Track Project</a>
                        <a class="active" href="{{ route('message-centre', ['id' => $project->id]) }}">Message Center</a>
                        <a href="{{ route('file-upload', ['id' => $project->id]) }}">File Upload</a>
                        {{-- <a href="{{ route('billing-history', ['id' => $project->id]) }}">Billing History</a> --}}
                    </div>
                </div>
                <div class="logo">
                    <a href="#"><img src="{{ $client->logo ? set_user_image($client->logo) : asset('images/default-user.jpg') }}" alt="Client Logo" width="72" height="80" /></a>
                </div>
            </div>
        </div>
    </section>
    <section class="client-project bg-white">
        <div class="container-xxl">
            <div class="heading d-flex align-items-center justify-content-between">
                <h2 class="mb-0">Message <i>Center</i></h2>
                <div class="links">
                    <a class="{{ request()->routeIs('message-centre*') ? 'active' : '' }}"
                        href="{{ route('message-centre', ['id' => $project->id]) }}">Messages</a>
                    <a class="{{ request()->routeIs('file-upload*') ? 'active' : '' }}"
                        href="{{ route('file-upload', ['id' => $project->id]) }}">Project Files
                        ({{ $project->files ? $project->files->count() : 0 }})</a>
                </div>
            </div>
        </div>
    </section>
    


@if ($latestMessage)
    <section class="actions-need bg-white pb-4">
        <div class="container-xxl">
            <div class="heading">
                <h2 class="text-uppercase text-orange mb-1">LATEST MESSAGE</h2>
                <h3 class="d-flex align-items-center mb-2">Message from {{ $latestMessage->postedBy->name }}<a class="circle-icon align-items-center d-inline-flex justify-content-center lh-1 rounded-circle text-decoration-none ms-2" href="#">!</a></h3>
            </div>
        </div>
    </section>
@endif

<section class="bg-white">
    <div class="container-xxl">
        @if ($latestMessage)
            <div class="message-box d-flex">
                <div class="pic">
                    <img src="{{ set_user_image($latestMessage->postedBy->profile_image) }}" alt="{{ $latestMessage->postedBy->name }}" />
                </div>
                <div class="message-wrap d-flex align-items-start">
                    <div class="message">
                        <h2>{{ $latestMessage->subject }}</h2>
                        <div class="copy">
                            <p>{!! Str::limit(strip_tags(trim($latestMessage->message)), 200) !!}...<a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $latestMessage->id]) }}">See More</a></p>
                            @if($latestMessage->files->isNotEmpty())
                            <div class="attachment">
                                <p>
                                    <i class="bi bi-link-45deg"></i> 
                                    Files attached: 
                                    @foreach($latestMessage->files->take(3) as $file)
                                        <a href="{{ Storage::url($file->file_path) }}" class="text-decoration-none text-orange">
                                            {{ $file->file_name }}
                                        </a>@if(!$loop->last), @endif
                                    @endforeach
                                    @if($latestMessage->files->count() > 3)
                                        <a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $latestMessage->id]) }}" class="text-decoration-none text-orange">... See All</a>
                                    @endif
                                </p>
                            </div>
                            @endif
                            <div class="meta d-flex">
                                <a class="user me-4" href="#">{{ $latestMessage->postedBy->name }}</a>
                                <span class="date">{{ $latestMessage->created_at->format('m/d/y') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <div class="message-ctas text-center d-flex justify-content-center pt-5 pb-0">
            <a class="cta d-inline-flex align-items-center text-decoration-none text-uppercase py-2 mt-4 mt-md-0" href="{{ route('new-message-thread', ['project_id' => $project->id]) }}">
                <i class="bi bi-envelope me-2"></i> POST A NEW MESSAGE
            </a>
            <a class="cta d-inline-flex align-items-center text-decoration-none text-uppercase py-2 mt-4 mt-md-0" href="{{ route('new-message-thread', ['project_id' => $project->id]) }}">
                <i class="bi bi-upload me-2"></i> UPLOAD FILES
            </a>
            <a class="cta d-inline-flex align-items-center text-decoration-none text-uppercase py-2 mt-4 mt-md-0" href="{{ route('file-upload', ['id' => $project->id]) }}">
                SEE ALL FILES
            </a>
        </div>

        @if ($messages->isNotEmpty())
        <div class="messages-wrap">
            @if($messages->count() > 1)
            <div class="head"><span id="moreMessages">More Messages</span></div>
            @endif
            <div class="messages-list">
                @foreach ($messages as $message)
                    <div class="message-row row flex-nowrap align-items-center gx-md-3 mt-4 {{ $message->files->isNotEmpty() ? 'has-attachment' : '' }} pb-4 orange">
                        <div class="col-md-auto icons d-flex">
                            <div class="icon icon-envelop d-flex align-items-center justify-content-center">
                                <i class="bi bi-envelope-fill"></i>
                            </div>
                            <div class="icon pic">
                                <img src="{{ set_user_image($message->postedBy->profile_image) }}"
                                    alt="{{ $message->postedBy->name }}" />
                            </div>
                        </div>
                        <div class="col-md-auto name">
                            <p>{{ $message->postedBy->name }}</p>
                        </div>
                        <div class="col-md-auto date">
                            <p>{{ $message->created_at->format('m/d/y') }}</p>
                        </div>
                        <div class="col-md-auto notification">
                            <p><a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}">{{ $message->subject }}</a></p>
                            @if($message->files->isNotEmpty())
                            <div class="attachment pt-2">
                                <p>
                                    <i class="bi bi-link-45deg"></i> 
                                    Files attached: 
                                    @foreach($message->files->take(3) as $file)
                                        <a href="{{ Storage::url($file->file_path) }}" class="text-decoration-none text-orange">
                                            {{ $file->file_name }}
                                        </a>@if(!$loop->last), @endif
                                    @endforeach
                                    @if($message->files->count() > 3)
                                        <a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}" class="text-decoration-none text-orange">... See All</a>
                                    @endif
                                </p>
                            </div>
                            @endif
                        </div>
                        <div class="col-md message d-flex align-items-center">
                            <div class="over-text d-grid">
                                <p>{!! Str::limit(strip_tags(trim($message->message)), 200) !!}</p>
                            </div>
                            <a class="ms-2" href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}">Read More</a>
                        </div>
                        <div class="col-md-auto reply">
                            <a class="cta d-inline-flex align-items-center text-decoration-none py-2 mt-0" href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}">
                                <i class="bi bi-envelope me-2"></i> REPLY
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        @if ($messages->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $messages->links('pagination::bootstrap-5') }}
            </div>
        @endif
        @endif

        <div class="message-ctas text-center d-flex justify-content-center py-5">
            <a class="cta d-inline-flex align-items-center text-decoration-none text-uppercase mt-4 mt-md-0" href="#">GET EMAIL UPDATES</a>
            <a class="cta d-inline-flex align-items-center text-decoration-none text-uppercase mt-4 mt-md-0" href="#">NEED HELP? CONTACT</a>
        </div>
    </div>
</section>

  

@endsection

@push('styles')
    <style>
        .message-box {
            width: 100%;
        }

        .message-box .message-wrap {
            width: 100%;
        }

        .message-box .message-wrap .message {
            width: 100%;
        }

        .replyButton:hover {
            color: white;
            background-color: #ff4c00;
        }

        .replyButtonText:hover {
            color: white !important;
            background-color: #ff4c00;
        }

        .no-message-div {
            text-align: center;
            padding: 5px;
            border-top: 0.5px solid #cacaca;
            border-right: 0.5px solid #cacaca;
            border-bottom: 0.5px solid #cacaca;
            border-left: 5px solid #ff4c00;
        }

        .pagination {
            gap: 5px;
        }

        .page-item .page-link {
            border: none;
            color: #333;
            padding: 8px 16px;
            font-weight: 500;
        }

        .page-item.active .page-link {
            background-color: #ff4c00;
            border-color: #ff4c00;
            color: white;
        }

        .page-item.disabled .page-link {
            background: none;
            color: #6c757d;
        }

        .page-link:hover:not(.active) {
            background-color: #f8f9fa;
            color: #ff4c00;
        }

        .page-link:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 76, 0, 0.25);
        }
    </style>
@endpush
