@extends('layout.app')
@section('title', 'loginorg')
@section('content')
    <section>
    <div class="navbar">
        <div class="flt-lft">

        </div>
        <div class="flt-lft">
            <div class="all_companies_dropdown">
                <button class="all_companies_dropdown-btn">
                    <span class="all_companies_dropdown_heading">Select Companies</span>
                    <i class="fa fa-caret-down all_companies_dropdown-icon"></i>
                </button>
                <div class="all_companies_dropdown-content">
                    <div class="create-new-company-div">
                        <form method="post" action="" name="add-new-company-form">
                            @csrf
                            <div class="input-group add-company-in-dropdown fs-1">
                                <input type="text" class=" border border-secondary form-control p-1 border" placeholder="Create New Company" name="name">
                                <button type="submit" class=" border border-secondary btn" title="Click to Add Company">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="existing-companies-div">
                    </div>
                    <form id="company-form" action="/company-selection" method="POST" style="display: none;">
                        @csrf
                        <input type="hidden" name="company_id" id="hidden-input">
                    </form>
                </div>
            </div>
        </div>

        <div class="flt-rgt">
            <!-- <div class="dropdown">
                <span class="dropdown-btn">Welcome! {{ Auth::user()->name }}
                    <i class="fa fa-caret-down"></i>
                </span>
                <div class="dropdown-content">
                    <a href="{{ route('profile') }}" class="">Your Profile</a>
                    <a href="{{ route('logout') }}">Logout</a>
                </div>
            </div> -->
            <div class="dropdown">
                <span class="dropdown-toggle" data-bs-toggle="dropdown">
                    Welcome! {{ Auth::user()->name }}
                </span>
                <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ route('profile') }}">Your Profile</a></li>
                <li><a class="dropdown-item" href="{{ route('logout') }}">Logout</a></li>
                </ul>
            </div>
        </div>
    </div>


    <div class="main-div">
        <div class="content-div">
            <div class="content-heading">
                @if(request()->route()->named('company-selection'))
                    <h4>Users, Companies and Projects Information <i class="fa fa-info-circle information-icon"></i></h4>
                @endif
            </div>
            
            <div class="content-body-content">
                @if(Session::has('company'))
                
                    @php
                        $company = Session::get('company');
                    @endphp

                    <div class="d-flex justify-content-end">
                        <a href="{{ route('edit-company', ['company_id' => $company->id]) }}"><button>Edit/Delete Company</button></a>
                    </div>

                    <div>
                        <div>Company Name: {{ $company->name }}</div>
                        <div>User Name: {{ $company->user->name }}</div>
                        <div>User Email: {{ $company->user->email }}</div>

                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exampleModal">Add Project</button>
                        <!--  -->

                        <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                <div class="modal-header">
                                    <h1 class="modal-title fs-5" id="exampleModalLabel">Add Project</h1>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form method="post" action="{{ route('save-project') }}" class="p-3" name="add-project-form">
                                        @csrf
                                        <div class="form-group">
                                            <label for="exampleFormControlInput1">Project Name</label>
                                            <input type="text" class="form-control" id="project_name" name="project_name" placeholder="Add Project Name" required>
                                            @error("project_name")
                                                <div class="text-danger">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                        <input type="hidden" class="form-control" id="company_id" name="company_id" value="{{ $company->id }}" placeholder="Company Name" required>
                                        @error("company_id")
                                            <div class="text-danger">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                       
                                        <button class="btn btn-secondary mt-3">Submit</button>
                                    </form>
                                </div>
                                </div>
                            </div>
                        </div>
                       
                        @if($company->projects->isEmpty())
                            <div class="content-body-empty">No projects associated with this company.</div>
                        @else
                            <div>Projects:</div>
                            <div class="projects-div col-12">
                                <div class="project-left col-4 border border-secondary">
                                    <form method="post" action="" name="show-task-form">
                                        @csrf
                                        
                                        @foreach($company->projects as $project)
                                            <a href="" data-project_id="{{ $project->id }}" class="@if($project->status === 'Started') project-link-status-started  @elseif($project->status === 'Pending') project-link-status-pending @elseif($project->status === 'Completed') project-link-status-completed @endif project-link">
                                                <div class="project-box">Project Name: {{ $project->name }}</div>
                                            </a>
                                        @endforeach
                                    </form>
                                </div>

                                <div class="project-right col-8 m-auto text-center">

                                </div>
                            </div>
                        @endif
                    </div>
                @else
                    <div class="content-body-empty">Please select a company from dropdown to show details</div>
                @endif
            </div>
        </div>
    </div>
    </section>

@endsection

    @push('styles')
        <style>
            body{
                color:black;
                margin:0px;
            }
            .navbar{
                height:50px;
                padding:10px 50px;
                margin:0px;
                align-items:center;
                background-color:lightgrey;
            }

            .flt-rgt{
                float:right;
            }
            .flt-lft{
                float:left;
                margin-right:5px;
                align-items:center;
            }
            .flt-rgt div{
                padding:0 5px;
                margin:0px 3px;
            }
            .flt-rgt a:hover{
                text-decoration:none;
            }
            .active-page{
                font-weight:bold;
            }
            .company-selection-dropdown{
                text-align:center;
            }
            .existing-companies-div{
                padding:5px;
            }
            /* .company_selection-form-container{
                max-width: fit-content;
                margin-left: auto;
                margin-right: auto;
            }
            .company_selection-form{
                margin-top:50px;
                border:1px solid grey;
                border-radius:10px;
                width:500px;
                padding:5px;
            }
            .company_selection-form-heading{
                text-align:center;
            }
            .company_selection-form-body{
                width:300px;
                max-width: fit-content;
                margin-left: auto;
                margin-right: auto;
            } */

            hr{
                background-color: gray;
                height: 1px;
                margin:5px 0px;
            }
            .message-div{
                padding:10px 0px;
                font-weight:bold;
                margin-top:5px;
                text-align:center;
            }
            .success-msg{
                color:darkgreen;
                background-color:lightgreen;
            }
            .error-msg{
                color:red;
                background-color:pink;
            }
            .all_companies_dropdown-content {
                display: none;
                position: absolute;
                background-color: #f9f9f9;
                min-width: 200px;
                box-shadow: 0px 5px 15px 5px hsla(0, 0.00%, 0.00%, 0.92);
                z-index: 1;
                max-height:300px;
                overflow:scroll;
            }
            .all_companies_dropdown-content a {
                float: none;
                color: black;
                padding: 12px 16px;
                text-decoration: none;
                display: block;
                text-align: left;
                z-index:1;
            }
            .all_companies_dropdown-content a:hover {
                background-color: #ddd;
            }
            .dropdown-content {
                display: none;
                position: absolute;
                background-color: #f9f9f9;
                min-width: 150px;
                box-shadow: 0px 5px 15px 5px hsla(0, 0.00%, 0.00%, 0.92);
                z-index: 1;
                max-height:300px;
                overflow:scroll;
            }
            .dropdown-content a {
                float: none;
                color: black;
                padding: 12px 16px;
                text-decoration: none;
                display: block;
                text-align: left;
            }
            .dropdown-content a:hover {
                background-color: #ddd;
            }
            .dropdown:hover .dropdown-content {
                display: block;
            }

            .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
            .section-div{
                width:5%;
                box-sizing: border-box;
                border-right:0.5px solid grey;
                background-color:lightgrey;
                overflow:scroll;

                left: 0;
                z-index: 0;
                height: calc(100vh - 50px);
            }
            .section-div a{
                text-decoration:none;
            }
            .section-div a:hover{
                background-color:lightblue;
            }
            .section-div div{
                color:black;
                padding:10px;
            }
            .navbar-menu-button-div{
                display:flex;
            }
            .navbar-menu-button{
                margin:auto;
            }
            .sidebar-link-icon{
                text-align:center;
                font-size:20px;
                padding:10px 0px;
                width:100%;
            }
            .sidebar-link-text{
                display:none;
            }
            .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
                transition: all 0s ease;
            }
            .content-div{
                width:100%;
                padding:10px;
                overflow:scroll;
            }
            /* .content-heading{
                didplay:flex;
            } */
            .back-btn{
                background-color: black;
                color: white;
                height: 27px;
                width: 25px;
                border-radius: 50%;
                font-size: 15px;
                padding: 5px;
                margin-right: 10px;
            }
            .content-body-empty{
                padding:5px 30px;
                /* height: 30px; */
                text-align: center;
                background-color: pink;
                color: red;
                font-weight: bold;
            }
            .content-body-content{
                overflow:scroll;
                margin-top:5px;
                height: calc(100vh - 120px);
            }
            .projects-div{
                margin-top:5px;
                display:flex;
                flex-direction:row;
                width:100%;
                box-shadow: 0px 0px 11px 0px rgb(0, 0, 0);
                /* margin:10px; */
            }
            .project-link{
                padding:10px 5px;
                display:block;
                text-decoration:none;
                color:black;
            }
            .project-link:hover{
                border:0.5px solid black;
            }
            .project-link-status-started{
                background-color: #ffb9b9;
            }
            .project-link-status-pending{
                background-color: #ffbf6c;
            }
            .project-link-status-completed{
                background-color: #92cc90;
            }
            .task-link{
                padding:10px 5px;
                display:block;
                text-decoration:none;
                color:black;
            }
            .task-link:hover{
                border:0.5px solid black;
            }

        </style>
    @endpush


    @push('script')
    <script>
        $(document).ready (function(){
            $('.information-icon').click(function(){
                alert("Choose companies from dropdown to view users and projects!");
            })

            $('.all_companies_dropdown-btn').click(function(){
                if($('.all_companies_dropdown-content').hasClass('d-block')) {
                    $('.all_companies_dropdown-content').removeClass('d-block').addClass('d-none');
                    $('.all_companies_dropdown-icon').removeClass('fa-caret-up').addClass('fa-caret-down');
                }
                else{
                    $('.all_companies_dropdown-content').removeClass('d-none').addClass('d-block');
                    $('.all_companies_dropdown-icon').removeClass('fa-caret-down').addClass('fa-caret-up');
                }
            })

            function load_companies_in_dropdown(){
                $.ajax({
                    url: "{{ route('companies-list') }}",
                    method: "get",
                    success: function(response){

                        if(response!=""){
                            $('.existing-companies-div').html("");
                            $.each(response, function(index, company) {
                                $('.existing-companies-div').append(
                                    "<a href='#' class='company-link' id='" + company.id + "' data-company-id='" + company.id + "' data-user_id='" + company.user_id + "'>" + company.name + "</a>"
                                );
                            });
                        }
                        else{
                            $('.existing-companies-div').append("<div>Companies Not Found!</div>").css({"text-align":"center"});
                        }
                    }
                })
            }
            load_companies_in_dropdown();


            // Add click event listener to the dynamically created links.
            $(document).on('click', '.company-link', function(event) {
                event.preventDefault(); // Prevent the default link behavior

                $('#hidden-input').val($(this).data('company-id'));
                $('#company-form').submit();
            });


            $('form[name="add-new-company-form"').submit(function(){
                event.preventDefault();

                $.ajax({
                    url: "{{ route('add-company') }}",
                    method: "post",
                    data: $(this).serialize(),
                    success: function(response){
                        $('input[name="name"]').val("").attr("autofocus", "true");
                        load_companies_in_dropdown();
                    }
                })
            })


            $('.project-right').html("<div>Click on Project Names to view their Tasks</div>")

            let clickedProjectId = null; // Variable to store clicked project ID

            // Click handler for anchor links
            $('form[name="show-task-form"] a').click(function(event) {
                event.preventDefault(); // Prevent default link behavior (form submission)

                $('form[name="show-task-form"] a').css('border', 'none');
                $(this).css({"border":"0.5px solid black"});

                clickedProjectId = $(this).data("project_id"); // Store project ID

                // Manually trigger the AJAX request here
                if (clickedProjectId !== null) {
                    $.ajax({
                        url: "projects/" + clickedProjectId + "/tasks",
                        method: "get",
                        data: { "fetch_single_project_tasks_ajax": 1 },
                        success: function(response) {
                            $('.project-right').html("");
                            if (response && Array.isArray(response) && response.length > 0) {
                                // Project has tasks
                                if (response.length === 1) {
                                    // Project has one task
                                    $('.project-right').append("<div class='task-link'><table><tr><td class='col-8'>"+response[0].name+"</td><td class='col-2'><a href='tasks/"+response[0].id+"/edit'>Edit/Comment</a></td><tr></table</div>").css({"overflow":"scroll"});
                                }
                                else {
                                    // Project has multiple tasks
                                    response.forEach(function(task) {
                                        $('.project-right').append("<div class='task-link'><table><tr><td class='col-8'>"+task.name+"</td><td class='col-2'><a href='tasks/"+task.id+"/edit'>Edit/Comment</a></td><tr></table</div>").css({"overflow":"scroll"});
                                    });
                                }
                            } else {
                                // Project has no tasks (response is null or empty)
<<<<<<< HEAD
                                // Assuming clickedProjectId is defined and set elsewhere in your JavaScript
                                $('.project-right').html(`<div>This project has no tasks. Go to <a href='{{ route('show-tasks', ['project_id'=> 'placeholder']) }}'`.replace('placeholder', clickedProjectId) + `>Tasks</a></div>`); //IMP
=======
                                $('.project-right').html(`<div> This project has no tasks. Go to  <a href="project/${clickedProjectId}/tasks/add">Tasks</a> </div>`);
>>>>>>> fc34922da84fe33d519cd970a68b80d179fdbbbf
                            }
                        }
                    });
                }
            });
        });

    </script>
    @endpush
