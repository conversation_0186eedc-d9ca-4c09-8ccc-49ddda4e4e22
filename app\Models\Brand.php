<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Brand extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $guard = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [];
    }


    protected $fillable = [
        'admin_id',
        'job_code',
        'name',
        'description',
    ];

    public function clients()
    {
        return $this->belongsToMany(Client::class, 'client_brand', 'brand_id', 'client_id');
    }

    public function projects()
    {
        return $this->belongsToMany(Project::class, 'brand_projects',  'company_id', 'project_id')->withTimestamps();
    }

    public function admin()
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'brand_category', 'brand_id', 'category_id');
    }

    public function users()
    {
        return $this->belongsToMany(User::class, 'brand_user', 'brand_id', 'user_id');
    }


    public function socialDetails()
    {
        return $this->belongsToMany(SocialDetail::class, 'social_details_pivot');
    }



    public function linkedUsersWithBrand()
    {
        return $this->belongsToMany(User::class, 'brand_user_links', 'brand_id', 'user_id');
    }
}
