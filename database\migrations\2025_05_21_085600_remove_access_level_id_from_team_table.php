<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('team', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['access_level_id']);
            // Then drop the column
            $table->dropColumn('access_level_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('team', function (Blueprint $table) {
            // Re-add the column and foreign key if rolling back
            $table->foreignId('access_level_id')->nullable()->references('id')->on('access_level');
        });
    }
};