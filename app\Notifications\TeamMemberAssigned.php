<?php

namespace App\Notifications;

use App\Models\User;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TeamMemberAssigned extends Notification
{
    use Queueable;


    public $user;

    public $password;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $user, $password)
    {
        //
        $this->user = $user;
        $this->password = $password;
    }


    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('You have been Add to a New Team')
            ->line('You have Added as a New Team Member in SGF-PORTAL. Please use given credientials to login and see your team')
            ->line('**Username:** ' . $this->user->email)
            ->line('**Password:** ' . $this->password)

            // ->action('View Team', url('/teams/' . $this->user->id . '/profile'))
            ->action('View Team', url('/'))
            ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
