- let pageName = 'New Task';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/fieldForms.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('New <i>Task</i>', 'Back to Tasks')

        section.client-project.project-dashboard
            .container-xxl
                form.row.projects-row(action="")
                    .col-md-3.col-xl-2.project-column.quick-links
                        h2.text-uppercase SET STATUS
                        hr.mt-0.mb-4.border-white
                        .statuses-list.d-flex.flex-column
                            each status, index in ['Urgent', 'New', 'In Progress', 'Ready for Approval', 'Feedback', 'Recently Finished']
                                .select-status.d-flex
                                    +formCheck(status, 'radio', 'setStatus')

                    .col-md-9.col-xl-10.project-column.task-overview
                        .col-head.align-items-center.d-flex.justify-content-between
                            h2.text-uppercase COMPOSE YOUR MESSAGE
                        hr.mt-0.mb-4.border-white

                        .row.new-task-form
                            .col-12.mb-4
                                input.input-text.border-0(type="text", placeholder="Add a task subject...")
                            .col-12
                                textarea.textarea.border-0(placeholder="Type your message here...")
                            .col-head.col-12.mt-5
                                h2.text-uppercase Assign to
                                hr.mt-0.mb-4.border-white
                            .col-12
                                .row
                                    each check in ['Design Team', 'Mike M.', 'Robert M.', 'Megan J.', 'Cynde S.','Dev Team', 'Drew M.', 'Sally M.', 'Jeff C.', 'Erin L.', 'Gursewak', 'Aiden M.', 'Jesse R.', 'Ali D.', 'Rob S.']
                                        .col-md-3.col-xl-2.mb-3
                                            +formCheck(check)
                            .col-12.cta-row.d-flex.mt-5
                                .btn-col.d-flex
                                    .upload-btn-wrapper
                                        button.btn-upload.text-uppercase.text-white.d-flex.align-items-center #[i.bi.bi-upload.me-2] Upload a file
                                        input(type="file", name="myfile")
                                    .add-time.ms-4(position="relative", onclick="openDatePicker(event)")
                                        .meta-label.d-flex.align-items-center
                                            i.bi.bi-clock.me-2
                                            span.label-text ADD DUE DATE
                                        input#dueDate.due-date(type="date", name="due-date", class="date-picker", onchange="updateLabel(event)")

                                .btn-col.ms-auto
                                    button.cta.text-uppercase.mt-0(type="submit") ADD TASK

        include ../partials/footer.pug
        +footer(true)