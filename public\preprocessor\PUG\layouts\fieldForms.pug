mixin inputField(label, type=text, placeholder)
    input.form-control.border-0(type=type, id=label, placeholder=placeholder)

mixin inputFieldIcon(icon, label, type=text)
    .input-wrap.d-flex
        .icon.d-flex.align-items-center.justify-content-center
            img(src=`images/${icon}-icon.svg`, alt="")
        .input.flex-grow-1
            input.form-control.border-0(type=type, id=label)

mixin selectField(optionList)
    .select-wrap
        select.form-select.border-0
            each option, index in optionList
                option(selected=index === 0)= option
        .select-icon
            img(src="images/down-arrow-orange.svg", alt="", width="18", height="10")

mixin formCheck(label, radio=false, labelRadio=false)
    - let forLabel = label.replace(/[^a-zA-Z0-9 ]/g, '').split(' ').map((word, index) => index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1)).join('')
    //- - let capitalLabel = label.charAt(0).toUpperCase() + label.slice(1)
    //- - let labelRadio = `radio${capitalLabel}`
    .form-check
        input.form-check-input(type=(radio ? 'radio' : 'checkbox'), value="", id=forLabel, name=(radio ? labelRadio : undefined))
        label.form-check-label(for=forLabel)= label


mixin inputFieldLabel(label, type='text', placeholder='', value=false, editIcon=false)
    - const forId = label.replace(/[^a-zA-Z0-9 ]/g, '').split(' ').map((word, index) => index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1)).join('')
    label.form-label(for=forId)= label
    if editIcon
        .edit-icon
            span.icon.d-flex #[img.me-2(src="images/input-edit-icon.svg", alt="")] Edit
            input.form-control.border-0(type=type, id=forId, name=forId, placeholder=placeholder, value=value)
    else
        input.form-control.border-0(type=type, id=forId, name=forId, placeholder=placeholder, value=value)

mixin inputColor(label, value)
    label.form-label(for='setColorPicker')= label
    .row.gx-0.input_color.overflow-hidden
        .col-md-6
            input#setColorValue.set_color.value.form-control.border-0(type="text", value=value)
        .col-md-6.overflow-hidden
            input#setColorPicker.set_color.picker.form-control.border-0.px-0(type="color", value=value)

mixin selectFieldLabel(label, optionList)
    - const forId = label.replace(/[^a-zA-Z0-9 ]/g, '').split(' ').map((word, index) => index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1)).join('')
    label.form-label.mb-2(for=forId)= label
    .select-wrap
        select.form-select.border-0(id=forId)
            each option, index in optionList
                option(selected=index === 0)= option
        .select-icon
            img(src="images/down-arrow-orange.svg", alt="", width="18", height="10")