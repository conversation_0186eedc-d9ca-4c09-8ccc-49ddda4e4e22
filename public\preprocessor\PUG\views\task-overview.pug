- let pageName = 'Team Portal';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = 'Task Overview';

include ../layouts/svgIcon.pug
include ../layouts/projectBoxBor.pug

mixin taskProjectsCheck(icon, head, color, star=false)
    .task-project.d-flex(class=color)
        .star-check.d-flex
            if star
                .icon-star.d-flex.me-3
                    img(src="images/star-icon.svg", alt="", height="20", width="20")
            .check-icon.me-3
                i.bi.bi-square.text-white
        .copy.d-flex.flex-column.flex-grow-1
            h2.text-uppercase=head
            .detail.d-flex.align-items-center.w-100
                if star
                    .date-cta.py-1.px-2.rounded-1.d-flex.align-items-center.me-3 #[i.bi.bi-clock.me-1] Mar 12
                else
                    .date.me-3 12/12/25

                .task.me-3 #[a(href="task-view.html") Home Page Margins]
                .text.d-flex.flex-grow-1.align-items-center
                    .text.over-text.d-grid
                        p.text-truncate Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla nec purus feugiat, vestibulum mi nec, ultricies metus.
                    a.read-more.text-decoration-none.ms-3(href="#") Read More

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('Tasks <i>Overview</i>', 'Back to Tasks', 'NEW TASK')

        section.client-project.project-dashboard
            .container-xxl
                .row.projects-row
                    .col-md-3.col-xl-2.project-column.quick-links
                        h2.text-uppercase STATUSES
                        hr.mt-0.mb-4.border-white
                        .statuses-list.d-flex.flex-column
                            each status, index in ['All (30)', 'Urgent (2)', 'New (30)', 'In Progress (30)', 'Ready for Approval (12)', 'Feedback (16)', 'Recently Finished (25)']
                                a(href="#", class=(index === 0 ? 'active' : ''))=status

                    .col-md-9.col-xl-10.project-column.task-overview
                        .col-head.align-items-center.d-flex.justify-content-between
                            h2.text-uppercase TASKS OVERVIEW
                            .sort.d-flex.align-items-center
                                .sort-by.d-flex.align-items-center
                                    h3 Sort By: Date
                                    span.down.ms-2
                                        img(src="images/down-arrow-orange.svg", alt="")
                                .more-icon.ms-3
                                    img(src="images/three-dots-more.svg", alt="")
                        hr.mt-0.mb-4.border-white
                        .task-overview-list
                            .head-sort.mb-4
                                h2.text-white.text-uppercase Urgent
                            - for(let i = 0; i < 2; i++)
                                +taskProjectsCheck('star', 'SP-003-25 Sample project 3 - URGENT', 'purple', true)
                            .head-sort.mb-4
                                h2.text-white.text-uppercase All
                            - for(let i = 0; i < 10; i++)
                                +taskProjectsCheck('alert', 'SP-003-25 Sample project 3 - In Progress', 'purple')

        include ../partials/footer.pug
        +footer(true)