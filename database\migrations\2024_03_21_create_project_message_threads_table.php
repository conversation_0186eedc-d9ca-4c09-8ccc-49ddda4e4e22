<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('project_message_threads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->string('subject');
            $table->text('message');
            $table->foreignId('posted_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();

            // Add the foreign key constraint after the table is created
            $table->foreign('parent_id')
                ->references('id')
                ->on('project_message_threads')
                ->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('project_message_threads');
    }
}; 