<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('essential_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->nullable()->constrained('projects')->onDelete('set null');
            $table->foreignId('essential_month_id')->nullable()->constrained('essential_months')->onDelete('set null');
            $table->string('performance')->nullable();
            $table->string('accessibility')->nullable();
            $table->string('best_practice')->nullable();
            $table->string('seo')->nullable();
            $table->string('site_health')->nullable();
            $table->string('site_traffic')->nullable();
            $table->string('site_contacts')->nullable();
            $table->text('site_popular_pages')->nullable();
            $table->string('engaged_traffic')->nullable();
            $table->string('engaged_traffic_rate')->nullable();
            $table->text('site_monthly_insight')->nullable();
            $table->text('focus')->nullable();
            $table->timestamps();
        });
    }

    /** 
     * Reverse the migrations.
     */
    public function down(): void
    {

        Schema::table('essential_metrics', function (Blueprint $table) {
            $table->dropForeign(['project_id']);  
            $table->dropForeign(['essential_month_id']);  
        });
        Schema::dropIfExists('essential_metrics');
    }
};


