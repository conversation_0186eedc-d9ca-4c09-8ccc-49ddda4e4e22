@extends('layout.app')
@section('title', 'Clients')
@section('content')

<section class="client-project project-dashboard">
    <div class="container-xxl">
        <div class="row projects-row">
            <div class="col-md-12 col-xl-12 project-column task-overview">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">Clients Overview</h2>
                    <div class="sort d-flex align-items-center">
                        <div class="sort-by d-flex align-items-center">
                            <h3>Sort By: Date</h3>
                            <span class="down ms-2"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" /></span>
                        </div>
                        <div class="more-icon ms-3"><img src="{{ asset('images/three-dots-more.svg') }}" alt="" /></div>
                    </div>
                </div>
                <hr class="mt-0 mb-4 border-white" />
                <div class="task-overview-list">
                    <div class="head-sort mb-4 d-flex justify-content-between">
                        <h2 class="text-white text-uppercase">All</h2>
                        <a href="{{ route('add-client') }}" class="fw-bold" style="color: #fff; font-family: 'Inter', sans-serif;"  >Add Client</a>
                    </div>
                    @foreach ($clients as $client)
                        <div class="task-project d-flex purple">
                            <div class="star-check d-flex">
                                <div class="check-icon me-3"><i class="bi bi-square text-white"></i></div>
                            </div>
                            <div class="copy d-flex flex-column flex-grow-1">
                                <h2 class="text-uppercase">{{ $client->job_code }} {{ $client->name }} </h2>
                                <div class="detail d-flex align-items-center w-100">
                                    <div class="date me-3">{{ $client->created_at }}</div>
                                    @foreach ($client->brands as $brand)
                                        <div class="task me-3"><a href="task-view.html">{{ $brand->name }}</a></div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

@endsection