.navmain {
	display: none;
	left: 0;
	position: absolute;
	top: 100%;
	width: 100%;
	z-index: 1000;

	@media (min-width: 992px) {
		min-height: calc(100vh - 120px);
		opacity: 0;
		top: 120px;
		transition: opacity 0.2s ease-in-out;
	}

	.links-wrap {
		height: calc(100vh - 75px);
		overflow-x: hidden;
		overflow-y: auto;

		@media (min-width: 992px) {
			height: calc(100vh - 120px);
		}
	}

	.links {
		li {
			color: $lightFont;
			font: 700 24px $futura;
			margin-bottom: 15px;

			@media (min-width: 992px) {
				margin-bottom: 10px;
			}

			&.active {
				a {
					color: $white;
				}
			}

			&.has-sub {
				.arrow {
					font-size: 16px;
					padding: 10px;
				}

				.links {
					display: none;
					padding: 25px 25px 0;

					li {
						font-size: 18px;
					}
				}

				&.clicked {
					.arrow {
						transform: rotate(180deg);
					}

					.links {
						display: block;
					}
				}
			}
		}

		a {
			&:hover {
				color: $white;
			}
		}

		&.main {
			li {
				font-size: 32px;

				@media (min-width: 992px) {
					font-size: 40px;
				}
			}
		}
	}

	.bottom {
		color: $lightFont;
		font: 14px/1.5 $inter;
	}

	[class*='col-'] {
		&:nth-child(2) {
			border-left: 1px solid rgba($white, 0.3);
			border-right: 1px solid rgba($white, 0.3);
		}
	}

	a {
		color: inherit;
		text-decoration: none;
	}

	&.open {
		display: block;
	}
}
