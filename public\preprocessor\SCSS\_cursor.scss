.cursor {
	background: $orange;
	border-radius: 50%;
	height: 10px;
	margin-left: -5px;
	margin-top: -5px;
	pointer-events: none;
	position: absolute;
	transition: transform 350ms, box-shadow 150ms;
	width: 10px;
	z-index: 10000;

	&:nth-child(1) {
		animation: scale 2s infinite;
		z-index: 1;
	}

	&:nth-child(2) {
		animation: pulse 2s infinite;
		box-shadow: 0 0 0 rgba($orange, 1);
		opacity: 0.2;
	}

	&.animation-link {
		box-shadow: 0 0 50px rgba($orange, 0.4) !important;
		transform: scale(5) !important;
	}

	&.animation-link-button {
		background: $black;
		box-shadow: 0 0 50px rgba($black, 0.8) !important;
		transform: scale(5) !important;
	}

	@keyframes pulse {
		0% {
			box-shadow: 0 0 0 0 rgba($orange, 0.8);
		}

		70% {
			box-shadow: 0 0 0 30px rgba($orange, 0);
		}

		100% {
			box-shadow: 0 0 0 0 rgba($orange, 0);
		}
	}

	@keyframes scale {
		0% {
			opacity: 1;
			transform: scale(0.8);
		}

		60% {
			opacity: 0.6;
			transform: scale(0.6);
		}

		100% {
			opacity: 1;
			transform: scale(0.8);
		}
	}
}
