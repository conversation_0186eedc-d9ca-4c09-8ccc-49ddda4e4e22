<?php

namespace App\Http\Controllers;

use App\Models\EssentialMetrics;
use App\Models\EssentialMonth;
use App\Models\Project;
use Illuminate\Support\Str;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class EssentialMetricsController extends Controller
{
    //

    public function index(Request $request)
    {
        // Initialize the months for the current year
        $project_id = $request->route('project_id');
        $project = Project::find($project_id);

        if ($project == null) {
            return redirect()->route('admin-dashboard')->with([
                'success' => false,
                'message' => 'Project not found.',
            ], 404);
        }


        $client = $project->client;

        EssentialMonth::initialize();
        $months = EssentialMonth::all();
        $currentYear = now()->year;
        $currentMonth = now()->month;
        $currentMonth = now()->format('F');
        $currentMonthSlug = Str::slug($currentMonth . ' ' . $currentYear);
        $currentMonthDB = EssentialMonth::where('slug', $currentMonthSlug)->first();
        $essentialMetrics = EssentialMetrics::with('project', 'essentialMonth')
            ->whereHas('project', function ($query) use ($project_id) {
                $query->where('id', $project_id);
            })
            ->whereHas('essentialMonth', function ($query) use ($currentMonthDB) {
                $query->where('id', $currentMonthDB->id);
            })
            ->first();

        return view('metrics.index', compact('essentialMetrics', 'client', 'project', 'months', 'currentMonthSlug'));
    }

    public function get_metrics_by_month(Request $request)
    {
        // $request->validate([
        //     'month' => 'required|exists:essential_months,id',
        //     'project_id' => 'required|exists:projects,id',
        // ]);
        // $month = $request->input('month');
        // $project_id = $request->input('project_id');
        // $essentialMetrics = EssentialMetrics::with('project', 'essentialMonth')
        //     ->whereHas('essentialMonth', function ($query) use ($month) {
        //         $query->where('id', $month);
        //     })
        //     ->whereHas('project', function ($query) use ($project_id) {
        //         $query->where('id', $project_id);
        //     })
        //     ->get();

        // if ($essentialMetrics->isEmpty()) {
        //     return response()->json([
        //         'success' => false,
        //         'message' => 'No metrics found for this month.',
        //     ], 404);
        // }

        // return response()->json([
        //     'success' => true,
        //     'data' => $essentialMetrics,
        // ], 200);



        // $request->validate([
        //     'month' => 'required|exists:essential_months,id',
        //     'project_id' => 'required|exists:projects,id',
        // ]);
        
        // $month = (int) $request->input('month');
        // $project_id = $request->input('project_id');
        // $previousMonth = $month - 1;
        
        // // Current month metrics using whereHas
        // $essentialMetrics = EssentialMetrics::with('project', 'essentialMonth')
        //     ->whereHas('essentialMonth', function ($query) use ($month) {
        //         $query->where('id', $month);
        //     })
        //     ->whereHas('project', function ($query) use ($project_id) {
        //         $query->where('id', $project_id);
        //     })
        //     ->get();
        
        // // Previous month metrics using same whereHas pattern
        // $previousMetrics = EssentialMetrics::with('project', 'essentialMonth')
        //     ->whereHas('essentialMonth', function ($query) use ($previousMonth) {
        //         $query->where('id', $previousMonth);
        //     })
        //     ->whereHas('project', function ($query) use ($project_id) {
        //         $query->where('id', $project_id);
        //     })
        //     ->get();
        
        // // Check if metrics exist
        // $hasCurrent = !$essentialMetrics->isEmpty();
        // $hasPrevious = !$previousMetrics->isEmpty();
        
        // $currentTraffic = $hasCurrent ? $essentialMetrics->sum('site_traffic') : 0;
        // $previousTraffic = $hasPrevious ? $previousMetrics->sum('site_traffic') : 0;
        
        // if (!is_null($currentTraffic) && !is_null($previousTraffic)) {
        //     $difference = $currentTraffic - $previousTraffic;
        //     $percentageChange = $previousTraffic != 0
        //         ? round(($difference / $previousTraffic) * 100, 2)
        //         : null;
        // } else {
        //     $difference = null;
        //     $percentageChange = null;
        // }
        
        // return response()->json([
        //     'success' => !$essentialMetrics->isEmpty(),
        //     'data' => $essentialMetrics,
        //     // 'previous_data' => $previousMetrics,
        //     'site_traffic_analysis' => [
        //         'current_traffic' => $currentTraffic,
        //         'previous_traffic' => $previousTraffic,
        //         'difference' => $difference,
        //         'percentage_change' => $percentageChange,
        //     ],
        //     'message' => ($essentialMetrics->isEmpty()) //DBT
        //         ? 'No metrics found for this month.'
        //         : null,
        // ], (!$essentialMetrics->isEmpty()) ? 200 : 404);


        
        $month = (int) $request->input('month');
        $project_id = $request->input('project_id');

        // Step 1: Get current month model
        $currentMonthModel = EssentialMonth::find($month); // e.g., May 2026 (id 17)

        // Step 2: Extract names and years for current month
        $currentMonth = null;
        $currentMonthYear = null; // String "2026"
        if ($currentMonthModel && $currentMonthModel->name) {
            [$currentMonth, $currentMonthYear] = explode(' ', $currentMonthModel->name); // e.g., ['May', '2026']
        }

        $previousMonthId = null;
        $previousMonth = null;
        $previousMonthYear = null; // String "2026"

        // --- Dynamic Calculation of Previous Sequential Month and Year ---
        if ($currentMonthModel && $currentMonthModel->name) {
            try {
                $currentDate = Carbon::createFromFormat('F Y', $currentMonthModel->name)->startOfMonth();
                $previousDate = $currentDate->subMonth();

                $previousMonth = $previousDate->format('F'); // e.g., April
                $previousMonthYear = $previousDate->format('Y'); // e.g., 2026

                // Try to find the EssentialMonth ID for the dynamically calculated previous month
                $previousMonthModel = EssentialMonth::where('name', $previousMonth . ' ' . $previousMonthYear)->first();
                if ($previousMonthModel) {
                    $previousMonthId = $previousMonthModel->id;
                }
                // If not found, $previousMonthId will remain null,
                // and we'll proceed with fetching metrics for a potentially non-existent month.

            } catch (\Exception $e) {
                // Handle cases where the current month name format is unexpected
                $previousMonth = null;
                $previousMonthYear = null;
            }
        }
        // --- End Dynamic Calculation ---

        // --- Logic for Year-over-Year Current Month Comparison ---
        $currentYearInt = (int) $currentMonthYear; // e.g., 2026 (integer)
        $previousYearInt = $currentYearInt - 1; // e.g., 2025 (integer)

        // Find the essential_month_id for the *same month* in the *previous year*
        $previousYearCurrentMonthModel = EssentialMonth::where('name', $currentMonth . ' ' . $previousYearInt)
                                                        ->first();

        $previousYearCurrentMonthTraffic = 0;
        if ($previousYearCurrentMonthModel) {
            $previousYearCurrentMonthMetrics = EssentialMetrics::with('project', 'essentialMonth')
                ->selectRaw('*,CAST(
                    CASE
                        WHEN site_traffic IS NULL THEN 0
                        WHEN site_traffic = "" THEN 0
                        WHEN site_traffic = "0" THEN 0
                        ELSE site_traffic
                    END AS UNSIGNED
                ) as site_traffic')
                ->whereHas('essentialMonth', function ($query) use ($previousYearCurrentMonthModel) {
                    $query->where('id', $previousYearCurrentMonthModel->id);
                })
                ->whereHas('project', function ($query) use ($project_id) {
                    $query->where('id', $project_id);
                })
                ->first(); // Get only one record for this specific month/project

            if ($previousYearCurrentMonthMetrics) {
                $previousYearCurrentMonthTraffic = $previousYearCurrentMonthMetrics->site_traffic;
            }
        }
        // --- End Year-over-Year Current Month Logic ---

        // Step 3: Get current month metrics
        $essentialMetrics = EssentialMetrics::with('project', 'essentialMonth')
            ->whereHas('essentialMonth', function ($query) use ($month) {
                $query->where('id', $month);
            })
            ->whereHas('project', function ($query) use ($project_id) {
                $query->where('id', $project_id);
            })
            ->get();

        // Step 4: Get previous *sequential* month metrics
        $previousMetrics = collect(); // default to empty
        if ($previousMonth && $previousMonthYear) {
            // We now use the dynamically calculated previous month and year
            $previousMonthModelForMetrics = EssentialMonth::where('name', $previousMonth . ' ' . $previousMonthYear)->first();
            if ($previousMonthModelForMetrics) {
                $previousMetrics = EssentialMetrics::with('project', 'essentialMonth')
                    ->whereHas('essentialMonth', function ($query) use ($previousMonthModelForMetrics) {
                        $query->where('id', $previousMonthModelForMetrics->id);
                    })
                    ->whereHas('project', function ($query) use ($project_id) {
                        $query->where('id', $project_id);
                    })
                    ->get();
            }
            // If $previousMonthModelForMetrics is null (not found), $previousMetrics will remain empty.
        }

        // Step 5: Calculate traffic comparison (current month vs. directly previous month)
        $hasCurrent = !$essentialMetrics->isEmpty();
        $hasPrevious = !$previousMetrics->isEmpty();

        $currentTraffic = $hasCurrent ? $essentialMetrics->sum('site_traffic') : 0;
        $previousTraffic = $hasPrevious ? $previousMetrics->sum('site_traffic') : 0; // Traffic for the dynamically determined previous month

        if (!is_null($currentTraffic) && !is_null($previousTraffic)) {
            $difference = $currentTraffic - $previousTraffic;
            $percentageChange = $previousTraffic != 0
                ? round(($difference / $previousTraffic) * 100, 2)
                : null;
        } else {
            $difference = null;
            $percentageChange = null;
        }

        // --- Calculate Year-over-Year Current Month Traffic Comparison ---
        $yearlyCurrentMonthDifference = null;
        $yearlyCurrentMonthPercentageChange = null;

        // Compare current month's traffic with the same month's traffic from the previous year
        if (!is_null($currentTraffic) && !is_null($previousYearCurrentMonthTraffic)) {
            $yearlyCurrentMonthDifference = $currentTraffic - $previousYearCurrentMonthTraffic;
            $yearlyCurrentMonthPercentageChange = $previousYearCurrentMonthTraffic != 0
                ? round(($yearlyCurrentMonthDifference / $previousYearCurrentMonthTraffic) * 100, 2)
                : null;
        }
        // --- End Year-over-Year Current Month Comparison ---        


        return response()->json([
            'success' => !$essentialMetrics->isEmpty(),
            'data' => $essentialMetrics, // This contains the current month's metrics, potentially useful for charts etc.
            'current_month_with_year' => [
                'month' => $currentMonth,
                'year' => $currentMonthYear, // String "2026"
            ],
            'previous_month_with_year' => [ // The previous month in sequence (e.g., April 2026)
                'month' => $previousMonth,
                'year' => $previousMonthYear, // String "2026"
            ],
            'current_year' => (string) $currentYearInt, // Explicitly string for response consistency
            'previous_year' => (string) $previousYearInt,
            'monthly_site_traffic_analysis' => [ // Comparison of current month vs. sequential previous month
                'current_month_traffic' => $currentTraffic,
                'previous_month_traffic' => $previousTraffic,
                'difference' => $difference,
                'percentage' => $percentageChange,
            ],
            'yearly_site_traffic_analysis' => [ // Comparison of current month vs. same month of previous year
                'current_month_traffic_current_year' => $currentTraffic,
                'current_month_traffic_previous_year' => $previousYearCurrentMonthTraffic,
                'difference' => $yearlyCurrentMonthDifference,
                'percentage' => $yearlyCurrentMonthPercentageChange,
            ],
            'message' => ($essentialMetrics->isEmpty())
                ? 'No metrics found for this month.'
                : null,
        ], (!$essentialMetrics->isEmpty()) ? 200 : 404);

        
    }

    public function save_metrics_by_month(Request $request)
    {




        $request->validate([
            'project_id' => 'required|exists:projects,id',
            'month_id' => 'required|exists:essential_months,id',
            'performance' => 'nullable|numeric',
            'accessibility' => 'nullable|numeric',
            'best_practice' => 'nullable|numeric',
            'seo' => 'nullable|numeric',
            'site_health_score' => 'nullable|numeric',
            'site_traffic' => 'nullable|numeric',
            'site_contacts' => 'nullable|numeric',
            'popular_pages' => 'nullable',
            'engaged_traffic' => 'nullable|numeric',
            'engaged_traffic_rate' => 'nullable|numeric',
            'site_monthly_insight' => 'nullable',
            'focus' => 'nullable',
            'reportFile' => 'nullable',
        ]);

        if ($request->hasFile('reportFile')) {
            $file = $request->file('reportFile');
            $filename = time() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('reports', $filename, 'public');
        }


        $monthId = $request->input('month_id');


        $fields = [
            'performance' => $request->performance,
            'accessibility' => $request->accessibility,
            'best_practice' => $request->best_practice,
            'seo' => $request->seo,
            'site_health' => $request->site_health_score,
            'site_traffic' => $request->site_traffic,
            'site_contacts' => $request->site_contacts,
            'site_popular_pages' => $request->popular_pages,
            'engaged_traffic' => $request->engaged_traffic,
            'engaged_traffic_rate' => $request->engaged_traffic_rate,
            'site_monthly_insight' => $request->site_monthly_insight,
            'focus' => $request->focus,
            'report_file' => $path ?? null,
        ];


        // Remove all null values
        $updateData = array_filter($fields, function ($value) {
            return !is_null($value);
        });

        $metric = EssentialMetrics::updateOrCreate(
            [
                'project_id' => $request->project_id,
                'essential_month_id' => intval($monthId),
            ],
            $updateData
        );


        return response()->json([
            'success' => true,
            'message' => 'Metrics saved successfully.',
            'data' => $metric
        ], 200);
    }


    public function download_metrics_by_month(Request $request)
    {
        $request->validate([
            'project_id' => 'required|exists:projects,id',
            'month' => 'required|exists:essential_months,id',
        ]);

        $project_id = $request->input('project_id');
        
        $project = Project::find($project_id);
        $client = $project->client;

        $month = $request->input('month');

        $metric = EssentialMetrics::with('project', 'essentialMonth')
            ->where('project_id', $project_id)
            ->where('essential_month_id', $month)
            ->first();

        if (!$metric) {
            return response()->json([
                'success' => false,
                'message' => 'No metrics found for this project and month.',
            ], 404);
        }


        if ($metric->report_file && Storage::exists($metric->report_file)) {
            try {


                $fileSize = Storage::size($metric->report_file);

                if ($fileSize > 0) {
                    return Storage::download($metric->report_file, 'metrics_report.pdf');
                } else {

                    Storage::delete($metric->report_file);
                    Log::warning("Zero-byte file found at {$metric->report_file}. Regenerating...");
                }
            } catch (\Exception $e) {

                Log::error("Error accessing report file: " . $e->getMessage());
            }
        }


        try {
            // Create filename and relative path
            $fileName = 'metrics_report_' . $project_id . '_' . $month . '_' . time() . '.pdf';
            $relativePath = 'reports/' . $fileName;
            $fullStoragePath = storage_path('app/public/' . $relativePath);
        
            // Ensure the reports directory exists
            $directory = dirname($fullStoragePath);
            if (!File::isDirectory($directory)) {
                File::makeDirectory($directory, 0755, true);
            }
        
            // Generate the PDF and save it
            $pdf = $this->generatePdf($metric, $request->input("content_to_print"));
            $pdf->save($fullStoragePath);
        
            // Confirm the file was actually created
            if (!File::exists($fullStoragePath) || File::size($fullStoragePath) === 0) {
                Log::error("PDF generation failed or empty file: {$fullStoragePath}");
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to generate PDF report.',
                ], 500);
            }
        
            // Save the relative path in the database
            $metric->report_file = $relativePath;
            $metric->save();
        
            // Return the file as a download
            return Storage::disk('public')->download($relativePath, $fileName);
        
        }  catch (\Exception $e) {
            Log::error("PDF generation error: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error generating PDF report: ' . $e->getMessage(),
            ], 500);
        }
    }

    private function generatePdf(EssentialMetrics $metric, $content_to_print)
    {
        try {
            $cleanedContentToPrint = [];

            if (is_array($content_to_print)) {
                $cleanedContentToPrint = array_map(function ($item) {
                    return preg_replace('/\d+$/', '', $item);
                }, $content_to_print);
            } 
            elseif (is_string($content_to_print)) {
                $cleanedContentToPrint[] = preg_replace('/\d+$/', '', $content_to_print);
            } 
            else {
                $cleanedContentToPrint = []; // Handle other cases (e.g., null)
            }

            $data = [
                'metric' => $metric,
                'project' => $metric->project,
                'month' => $metric->essentialMonth,
                'generated_at' => now()->format('Y-m-d H:i:s'),
                'content_to_print' => $cleanedContentToPrint // Use the cleaned version here
            ];
            
            return Pdf::view('admin.reports.metrics_report', $data)
                ->format('a4')
                ->margins(15, 15, 15, 15);
        } catch (\Exception $e) {
            Log::error("Error in PDF generation: " . $e->getMessage());
            throw $e; // Re-throw to be caught by the calling function
        }
    }
}
