<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>419 Page Expired</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html, body {
      width: 100%;
      height: 100%;
      overflow: hidden;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #0a0a0a;
      color: white;
    }

    canvas#bg-canvas {
      display: block;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .overlay {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      z-index: 10;
      font-family: "Futura Std", sans-serif;
      color: #fff;
      max-width: 90%;
    }

    .overlay h1 {
      font-size: 8rem;
      font-weight: 700;
      animation: glitch 1s infinite;
      color: #ff4c00; 
    }

    .overlay p {
      font-size: 1.5rem;
      margin-bottom: 10px;
      font-weight: 500;
      animation: fadeInUp 0.8s ease-in-out;
      opacity: 0.9;
    }

    .overlay a {
      display: inline-block;
      padding: 12px 30px;
      border-radius: 30px;
      font-size: 1rem;
      font-weight: 600;
      text-decoration: none;
      color: #fff;
      border: 2px solid #ff4c00;
      transition: all 0.3s ease;
    }

    .overlay a:hover {
      background-color: #ff4c00;
      color: #0a0a0a;
      transform: scale(1.05);
    }

    @keyframes glitch {
      0% { transform: translate(0); }
      20% { transform: translate(-2px, 2px); }
      40% { transform: translate(-2px, -2px); }
      60% { transform: translate(2px, 2px); }
      80% { transform: translate(2px, -2px); }
      100% { transform: translate(0); }
    }

    @keyframes fadeInUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
  </style>
</head>
<body>
  <canvas id="bg-canvas"></canvas>
  <div class="overlay">
    <h1>419</h1>
    <p>Agent, your session has expired.</p>
    <p>Link timeout: CONNECTION LOST ⏱️</p>
    <a href="{{ route('login') }}">Re-establish Connection</a>
  </div>
  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.160.0/three.min.js"></script>

  <script>
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);
    camera.position.z = 30;

    const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('bg-canvas'), antialias: true, alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setPixelRatio(window.devicePixelRatio);

    window.addEventListener('resize', () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    });

    const particlesCount = 8000;
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(particlesCount * 3);

    for (let i = 0; i < particlesCount * 3; i++) {
      positions[i] = (Math.random() - 0.5) * 300;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

    const material = new THREE.PointsMaterial({
      color: 0xff4e4e,
      size: 0.5,
      transparent: true,
      blending: THREE.AdditiveBlending
    });

    const particles = new THREE.Points(geometry, material);
    scene.add(particles);

    // Create hexagon instead of ring
    const hexGeometry = new THREE.CircleGeometry(12, 6);
    const hexMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff, side: THREE.DoubleSide, wireframe: true });
    const hexagon = new THREE.Mesh(hexGeometry, hexMaterial);
    scene.add(hexagon);

   
    const innerHexGeometry = new THREE.CircleGeometry(8, 6);
    const innerHexMaterial = new THREE.MeshBasicMaterial({ color: 0xff4c00, side: THREE.DoubleSide, wireframe: true });
    const innerHexagon = new THREE.Mesh(innerHexGeometry, innerHexMaterial);
    scene.add(innerHexagon);
  


    let mouseX = 0, mouseY = 0;
    document.addEventListener('mousemove', (event) => {
      mouseX = (event.clientX / window.innerWidth) * 2 - 1;
      mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
    });

    let clock = new THREE.Clock();

    function animate() {
      requestAnimationFrame(animate);

      const elapsed = clock.getElapsedTime();

      particles.rotation.y += 0.0005;
      particles.rotation.x += 0.0003;

      hexagon.rotation.z = elapsed * 0.2;
      innerHexagon.rotation.z = -elapsed * 0.1;
      
      hexagon.scale.x = 1 + Math.sin(elapsed * 0.5) * 0.1;
      hexagon.scale.y = 1 + Math.sin(elapsed * 0.5) * 0.1;
      
      innerHexagon.scale.x = 1 + Math.cos(elapsed * 0.7) * 0.1;
      innerHexagon.scale.y = 1 + Math.cos(elapsed * 0.7) * 0.1;

      camera.position.x += (mouseX * 5 - camera.position.x) * 0.05;
      camera.position.y += (mouseY * 5 - camera.position.y) * 0.05;
      camera.lookAt(scene.position);

      renderer.render(scene, camera);
    }

    animate();
  </script>

</body>
</html>