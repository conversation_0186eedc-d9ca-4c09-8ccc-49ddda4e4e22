<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Role;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $role = Role::where('name', 'SuperAdmin')->value('id');
        $users = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('_1aM@Oph!0YC'),
                'role_id' => $role,
            ],
            [
                'name' => '<PERSON><PERSON>',
                'email' => '<EMAIL>',
                'password' => bcrypt('Nssh@123'),
                'role_id' => $role,
            ],
        ];

        foreach ($users as $user) {
            DB::table('users')->insert($user);
        }
        
    }
}
