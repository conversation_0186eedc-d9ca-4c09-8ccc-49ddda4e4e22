<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $categoryData = [
            "Define" => [
                [
                    'name' => 'Cold/Stale',
                    'order' => 1,
                ],
                [
                    'name' => 'Lead Conversion',
                    'order' => 2,
                ],
                [
                    'name' => 'On Deck',
                    'order' => 3,                    
                ]
                
            ],
            "Content, sitemap, wireframes" => [
                [
                    'name' => 'Strategy',
                    'order' => 1,
                ],
                [
                    'name' => 'Content',
                    'order' => 2,
                ]
            ],
            "Design" => [
                [
                    'name' => 'Design (P)',
                    'order' => 1,
                ],
                [
                    'name' => 'Design (D)',
                    'order' => 2,
                ],
                [
                    'name' => 'Execution (P)',
                    'order' => 3,
                ]
            ],
            "Code" => [
                [
                    'name' => 'HTML/CMS',
                    'order' => 1,
                ],
                [
                    'name' => 'DEV/PROD',
                    'order' => 2,
                ],
            ],
            "Deploy and Manage" => [
                [
                    'name' => 'DEPLOY/MANAGE',
                    'order' => 1,
                ],
            ],

        ];

        foreach( $categoryData as $phasename => $categories ){
            $phase = \App\Models\Phase::where('name', $phasename)->first();
            if( $phase ) {
                $phase_id = $phase->id;                
            }else{
                $phase_id = null;
            }

            foreach( $categories as $category ){
                \App\Models\Category::create([
                    'name' => $category['name'],
                    'order' => $category['order'],
                    'phase_id' => $phase_id,
                    'description' => null,
                ]);
            }


        }
        
       
    }
}
