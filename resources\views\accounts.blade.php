@extends('layout.app')
@section('title', 'Users')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                    <div class="content-heading d-flex">
                                <h4>
                            @if(request()->route()->named('accounts'))
                                Accounts Information
                            @endif
                        </h4>
                    </div>
                <div class="content-body">
                    @if(request()->route()->named('accounts'))
                        @if(Session::has("user_deleted_success_message"))
                            <div>
                                Session::get("user_deleted_success_message")
                            </div>
                        @endif
                        @if(isset($not_authorized_error_msg) && $not_authorized_error_msg!='')
                            <div class="content-body-empty">
                                <div class="not_authorized_error_msg">{{ $not_authorized_error_msg }}</div>
                            </div>
                        @else
                            <div class="content-body-content">
                                <div class="d-flex justify-content-end my-1">
                                    <a href="{{ route('add-user') }}"><button>Add User Account</button></a>
                                </div>
                                <table class="table table-responsive">
                                    <thead>
                                        <tr>
                                            <td>Id</td>
                                            <td>Name</td>
                                            <td>Email</td>
                                            <td>Actions</td>
                                        </tr>
                                    </thead>
                                    <tbody class="table-body">
                                        @if(isset($users) && !empty($users))
                                            @foreach($users as $users)
                                                <tr>
                                                    <td class="user-id-col">
                                                        {{ $users->id }}
                                                    </td>
                                                    <td class="user-name-col">
                                                        {{ $users->name }}
                                                    </td>
                                                    <td class="user-email-col">
                                                        {{ $users->email }}
                                                    </td>
                                                    <td class="actions-col">
                                                        <form method="post" action="{{ route('edit-user', ['id' => $users->id]) }}" class="edit-project-form d-inline" name="edit-project-form">
                                                            @csrf
                                                            @method("put")
                                                            <a href="" class="p-1" onclick="event.preventDefault(); this.closest('form').submit()"><i class='fa fa-edit'></i></a>
                                                        </form>
                                                        <form method="post" action="{{ route('delete-user', ['id' => $users->id]) }}" class="delete-project-form d-inline" name="delete-project-form">
                                                            @csrf
                                                            @method("delete")
                                                            <a href="" class="p-1" onclick="event.preventDefault(); this.closest('form').submit()"><i class='fa fa-trash'></i></a>
                                                        </form>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>No User Found</tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection


    @push('styles')
        <style>
            body{
                color:black;
                margin:0px;
            }
            .navbar{
                height:50px;
                padding:10px 50px;
                margin:0px;
                align-items:center;
                background-color:lightgrey;
            }
            .flt-lft{
                float:left;
                margin-right:5px;
                align-items:center;
            }
            .flt-rgt{
                float:right;
            }
            .flt-rgt div{
                padding:0 5px;
                margin:0px 3px;
            }
            .flt-rgt a:hover{
                text-decoration:none;
            }
            .active-page{
                font-weight:bold;
            }
            .dropdown-content {
                display: none;
                position: absolute;
                background-color: #f9f9f9;
                min-width: 160px;
                box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
                z-index: 1;
            }
            .dropdown-content a {
                float: none;
                color: black;
                padding: 12px 16px;
                text-decoration: none;
                display: block;
                text-align: left;
            }
            .dropdown-content a:hover {
                background-color: #ddd;
            }
            .dropdown:hover .dropdown-content {
                display: block;
            }
            .loggedInMsg{
                font-size: 13px;
                padding: 2px 0px;
                margin-bottom:3px;
                text-align: center;
                color: white;
                background-color: #1a8234;
            }

            .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
            .section-div{
                width:5%;
                box-sizing: border-box;
                border-right:0.5px solid grey;
                background-color:lightgrey;
                overflow:scroll;

                left: 0;
                z-index: 0;
                height: calc(100vh - 50px);
            }
            .section-div a{
                text-decoration:none;
            }
            .section-div a:hover{
                background-color:lightblue;
            }
            .section-div div{
                color:black;
                padding:10px;
            }
            .navbar-menu-button-div{
                display:flex;
            }
            .navbar-menu-button{
                margin:auto;
            }
            .sidebar-link-icon{
                text-align:center;
                font-size:20px;
                padding:10px 0px;
                width:100%;
            }
            .sidebar-link-text{
                display:none;
            }
            .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
                transition: all 0s ease;
            }
            .content-div{
                width:100%;
                padding:10px;
                overflow:scroll;
            }
            /* .content-heading{
                didplay:flex;
            } */
            .back-btn{
                background-color: black;
                color: white;
                height: 27px;
                width: 25px;
                border-radius: 50%;
                font-size: 15px;
                padding: 5px;
                margin-right: 10px;
            }
            /* .content-body{
            } */
            .add-user-btn-div{
                display: flex;
                justify-content: end;
            }
            .content-body-empty{
                padding:5px 30px;
                /* height: 30px; */
                text-align: center;
                background-color: pink;
                color: red;
                font-weight: bold;
            }
            .not_authorized_error_msg{
                text-align: center;
                padding: 10px;
                background-color: pink;
                color: red;
                font-weight: bold;
            }
            .content-body-content{
                margin-top:5px;
                height: calc(100vh - 120px);
            }
            .table{
                border:1px solid grey;
            }
            thead{
                font-weight:bold;
            }
            .user-id-col{
                width:3%;
            }
            .user-name-col{
                width:10%;
            }
            .user-email-col{
                width:1%;
            }
            .actions-col{
                width:5%;
            }
            label{
                width:10%;
            }
        </style>
    @endpush

        @push('script>')
        <script>
            $(document).ready(function(){
                $('.navbar-menu-button-div').click(function(){
                    if($('.navbar-menu-button').hasClass('closed')){
                        $('.navbar-menu-button').css({"margin":"auto"})
                        $('.navbar-menu-button').removeClass('closed').addClass('opened');
                        $('.navbar-menu-icon').removeClass('fa-chevron-circle-right').addClass('fa-chevron-circle-left');
                        $('.section-div').css({"width":"15%","overflow":"scroll"});
                        $('.content-div').css({"width":"85%"});
                        $('.sidebar-link').css({"width":"100%","padding":"0 5px"});
                        $('.sidebar-link-text').css({"display":"block","font-size":"13px"});
                        $('.sidebar-link-icon').css({"text-align":"left","width":"auto"});
                    }
                    else{
                        $('.navbar-menu-button').removeClass('opened').addClass('closed');
                        $('.navbar-menu-icon').removeClass('fa-chevron-circle-left').addClass('fa-chevron-circle-right');
                        $('.section-div').css({"width":"5%","overflow":"none"});
                        $('.content-div').css({"width":"95%"});
                        $('.sidebar-link-text').css({"display":"none"});
                        $('.sidebar-link-icon').css({"text-align":"center","width":"100%"});
                    }
                });
                
            });
        </script>
    @endpush

