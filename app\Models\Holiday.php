<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Holiday extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'start_date',
        'end_date',
        'user_id',
        'is_global',
        'region'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_global' => 'boolean'
    ];

    /**
     * Get the user that owns the holiday.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
