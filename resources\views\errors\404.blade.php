<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Not Found</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Playfair+Display:ital@0;1&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Inter", sans-serif;
            background: #111;
            color: #cbcbcb;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        .content {
            position: relative;
            z-index: 2;
            text-align: center;
            padding: 20px;
            max-width: 600px;
        }
        
        h1 {
            font-family: "Futura Std", serif;
            font-weight: 700;
            font-size: 120px;
            color: #ff4c00;
            margin-bottom: 20px;
            line-height: 1;
            text-shadow: 0 0 20px rgba(255, 76, 0, 0.5);
        }
        
        h2 {
            font-family: "Futura Std", serif;
            font-weight: 700;
            font-size: 32px;
            margin-bottom: 20px;
            color: white;
        }
        
        p {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 30px;
        }
        
        .cta {
            display: inline-block;
            border: 2px solid #ff4c00;
            border-radius: 26px;
            font-weight: 600;
            font-size: 14px;
            padding: 15px 30px;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s ease;
            margin-top: 20px;
        }
        
        .cta:hover {
            background-color: #ff4c00;
        }
        
        .person {
            position: absolute;
            bottom: -100px;
            right: -30px;
            transform: rotate(-10deg);
            transition: transform 0.5s ease;
            z-index: 3;
            transform-origin: bottom right;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0% {
                transform: translateY(0) rotate(-10deg);
            }
            50% {
                transform: translateY(-15px) rotate(-8deg);
            }
            100% {
                transform: translateY(0) rotate(-10deg);
            }
        }
        
        .map-marker {
            position: absolute;
            width: 100px;
            height: 100px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            opacity: 0;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 0;
            }
            50% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 0;
            }
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 80px;
            }
            
            h2 {
                font-size: 24px;
            }
            
            .person {
                width: 200px;
                right: -50px;
            }
        }
    </style>
</head>
<body>
    <div id="canvas-container"></div>
    
    <div class="content">
        <h1>404</h1>
        <h2>Destination Not Found</h2>
        <p>Looks like you're trying to reach a place that doesn't exist in our digital universe. The path you're searching for may have moved or vanished into the digital void.</p>
        <a href="/login" class="cta">TAKE ME BACK HOME</a>
    </div>
    
    <div class="person">
        <svg width="300" height="400" viewBox="0 0 300 400" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- Simplified person silhouette -->
            <path d="M150 80C167.673 80 182 65.6731 182 48C182 30.3269 167.673 16 150 16C132.327 16 118 30.3269 118 48C118 65.6731 132.327 80 150 80Z" fill="#ff4c00"/>
            <path d="M222 350L190 200L170 160H130L110 200L78 350L66 400H234L222 350Z" fill="#ff4c00"/>
            <path d="M130 160L150 120L170 160H130Z" fill="#ff4c00"/>
            <path d="M110 200L90 280L60 260L80 180L110 200Z" fill="#ff4c00"/>
            <path d="M190 200L210 280L240 260L220 180L190 200Z" fill="#ff4c00"/>
        </svg>
    </div>
    
    <div class="map-marker">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2C8.13 2 5 5.13 5 9C5 14.25 12 22 12 22C12 22 19 14.25 19 9C19 5.13 15.87 2 12 2ZM12 11.5C10.62 11.5 9.5 10.38 9.5 9C9.5 7.62 10.62 6.5 12 6.5C13.38 6.5 14.5 7.62 14.5 9C14.5 10.38 13.38 11.5 12 11.5Z" fill="#ff4c00"/>
        </svg>
    </div>

    <script>
        // 3D Animation with Three.js
        const container = document.getElementById('canvas-container');
        
        // Scene setup
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x111111, 1);
        container.appendChild(renderer.domElement);
        
        // Create a grid of floating cubes
        const cubes = [];
        const cubeCount = 50;
        const cubeSize = 0.5;
        const spacingFactor = 15;
        
        for (let i = 0; i < cubeCount; i++) {
            const geometry = new THREE.BoxGeometry(cubeSize, cubeSize, cubeSize);
            const material = new THREE.MeshBasicMaterial({ 
                color: i % 5 === 0 ? 0xff4c00 : 0x333333,
                wireframe: true,
                transparent: true,
                opacity: 0.5
            });
            
            const cube = new THREE.Mesh(geometry, material);
            
            // Position cubes in a scattered field
            cube.position.x = (Math.random() - 0.5) * spacingFactor;
            cube.position.y = (Math.random() - 0.5) * spacingFactor;
            cube.position.z = (Math.random() - 0.5) * spacingFactor - 5;
            
            // Store rotation speed
            cube.userData = {
                rotationSpeed: {
                    x: (Math.random() - 0.5) * 0.01,
                    y: (Math.random() - 0.5) * 0.01,
                    z: (Math.random() - 0.5) * 0.01
                },
                floatSpeed: (Math.random() - 0.5) * 0.005,
                originalY: cube.position.y
            };
            
            scene.add(cube);
            cubes.push(cube);
        }
        
        // Create floating path line
        const pathPoints = [];
        for (let i = 0; i < 100; i++) {
            const x = (Math.sin(i * 0.2) * 3);
            const y = (Math.cos(i * 0.3) * 2);
            const z = -i * 0.5 - 5;
            pathPoints.push(new THREE.Vector3(x, y, z));
        }
        
        const pathGeometry = new THREE.BufferGeometry().setFromPoints(pathPoints);
        const pathMaterial = new THREE.LineBasicMaterial({ 
            color: 0xff4c00,
            transparent: true,
            opacity: 0.7
        });
        
        const pathLine = new THREE.Line(pathGeometry, pathMaterial);
        scene.add(pathLine);
        
        // Position camera
        camera.position.z = 10;
        
        // Animation
        let time = 0;
        function animate() {
            requestAnimationFrame(animate);
            
            time += 0.01;
            
            // Animate cubes
            cubes.forEach(cube => {
                cube.rotation.x += cube.userData.rotationSpeed.x;
                cube.rotation.y += cube.userData.rotationSpeed.y;
                cube.rotation.z += cube.userData.rotationSpeed.z;
                
                // Add floating effect
                cube.position.y = cube.userData.originalY + Math.sin(time + cube.position.x) * 0.3;
            });
            
            // Make camera slightly move
            camera.position.x = Math.sin(time * 0.3) * 0.5;
            camera.position.y = Math.cos(time * 0.2) * 0.5;
            
            // Rotate path line
            pathLine.rotation.z = Math.sin(time * 0.1) * 0.2;
            
            renderer.render(scene, camera);
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // Mouse movement effects
        document.addEventListener('mousemove', (event) => {
            const mouseX = (event.clientX / window.innerWidth) * 2 - 1;
            const mouseY = (event.clientY / window.innerHeight) * 2 - 1;
            
            // Subtle camera rotation based on mouse position
            camera.rotation.y = mouseX * 0.1;
            camera.rotation.x = -mouseY * 0.1;
        });
        
        animate();
    </script>
</body>
</html>
