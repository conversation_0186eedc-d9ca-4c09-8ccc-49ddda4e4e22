@font-face {
  font-family: "Futura Std";
  src: url("../fonts/FuturaStd-Bold.woff2") format("woff2"), url("../fonts/FuturaStd-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap
}

@font-face {
  font-family: "Futura Std";
  src: url("../fonts/FuturaStd-BoldOblique.woff2") format("woff2"), url("../fonts/FuturaStd-BoldOblique.woff") format("woff");
  font-weight: 700;
  font-style: italic;
  font-display: swap
}

@font-face {
  font-family: "Futura Std";
  src: url("../fonts/FuturaStd-Medium.woff2") format("woff2"), url("../fonts/FuturaStd-Medium.woff") format("woff");
  font-weight: 500;
  font-style: normal;
  font-display: swap
}

@font-face {
  font-family: "Futura Std";
  src: url("../fonts/FuturaStd-MediumOblique.woff2") format("woff2"), url("../fonts/FuturaStd-MediumOblique.woff") format("woff");
  font-weight: 500;
  font-style: italic;
  font-display: swap
}

.header {
  isolation: isolate;
  position: relative;
  width: 100%;
  z-index: 1000
}

.header .header_float {
  padding-bottom: 15px;
  padding-top: 15px;
  z-index: 500
}

@media(min-width: 992px) {
  .header .header_float {
    height: 120px;
    padding-bottom: 30px;
    padding-top: 30px
  }
}

@media(max-width: 991px) {
  .header .brand img {
    height: 45px;
    width: 115px
  }
}

.header .site-nav .user-pic {
  background: #d9d9d9;
  border-radius: 50%;
  height: 40px;
  overflow: hidden;
  position: relative;
  width: 40px;
  z-index: 1
}

.header .site-nav .user-pic img {
  height: 100%;
  mix-blend-mode: multiply;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.header .site-nav .cta {
  border: 2px solid rgba(255, 255, 255, .3);
  border-radius: 26px;
  color: #fff;
  font: 600 14px/1.45 "Inter", sans-serif;
  padding: 10px 25px;
  text-align: center
}

.header .site-nav .cta:hover {
  background: #ff4c00;
  border-color: #ff4c00
}

.header .site-nav .nav_call {
  border: 2px solid rgba(255, 255, 255, .3);
  border-radius: 50%;
  cursor: pointer;
  height: 40px;
  overflow: hidden;
  position: relative;
  width: 40px
}

.header .site-nav .nav_call .blast {
  background: #111;
  border-radius: 50%;
  display: none;
  height: 100%;
  left: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  -webkit-transition: -webkit-transform 1s ease-in-out;
  transition: -webkit-transform 1s ease-in-out;
  transition: transform 1s ease-in-out;
  transition: transform 1s ease-in-out, -webkit-transform 1s ease-in-out;
  width: 100%;
  z-index: -1
}

.header .site-nav .nav_call .icon {
  height: 100%;
  width: 100%
}

.header .site-nav .nav_call .bar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  width: 40px
}

.header .site-nav .nav_call .bar span {
  background: #fff;
  display: block;
  height: 2px;
  width: 22px
}

.header .site-nav .nav_call .bar span+span {
  margin-top: 6px
}

@media(min-width: 992px) {
  .header .site-nav .nav_call:hover {
    background: #ff4c00;
    border-color: #ff4c00
  }

  .header .site-nav .nav_call:hover .blast {
    display: block
  }
}

@media(min-width: 992px) {
  .header .site-nav .nav_call.clicked {
    overflow: initial
  }
}

.header .site-nav .nav_call.clicked .blast {
  display: block
}

@media(min-width: 992px) {
  .header .site-nav .nav_call.clicked .blast {
    -webkit-transform: scale(50000%);
    transform: scale(50000%)
  }
}

.header h1 {
  color: #fff;
  font: 500 16px "Futura Std", serif;
  left: 50%;
  opacity: .5;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%)
}

.header h1.hub {
  font: 700 20px "Futura Std", serif;
  opacity: 1
}

.header h1.hub i {
  font-family: "Playfair Display", serif
}

@media(max-width: 991px) {
  .header.overlap .header_float {
    background: #111
  }
}

.header.overlap .navmain.open {
  background: #111;
  padding: 30px 0
}

@media(min-width: 992px) {
  .header.overlap .navmain.open {
    opacity: 1;
    padding: 0
  }
}

.header.bg-white h1 {
  color: #111
}

.header.bg-white .site-nav .cta {
  border-color: rgba(17, 17, 17, .3);
  color: #111
}

.header.bg-white .site-nav .cta:hover {
  border-color: #ff4c00;
  color: #fff
}

.header.bg-white .site-nav .nav_call {
  border-color: rgba(17, 17, 17, .3)
}

.header.bg-white .site-nav .nav_call .bar span {
  background: #111
}

@media(min-width: 992px) {
  .header.bg-white .site-nav .nav_call:hover {
    border-color: #ff4c00
  }

  .header.bg-white .site-nav .nav_call:hover .bar span {
    background: #fff
  }
}

.navmain {
  display: none;
  left: 0;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 1000
}

@media(min-width: 992px) {
  .navmain {
    min-height: calc(100vh - 120px);
    opacity: 0;
    top: 120px;
    -webkit-transition: opacity .2s ease-in-out;
    transition: opacity .2s ease-in-out
  }
}

.navmain .links-wrap {
  height: calc(100vh - 75px);
  overflow-x: hidden;
  overflow-y: auto
}

@media(min-width: 992px) {
  .navmain .links-wrap {
    height: calc(100vh - 120px)
  }
}

.navmain .links li {
  color: #cbcbcb;
  font: 700 24px "Futura Std", serif;
  margin-bottom: 15px
}

@media(min-width: 992px) {
  .navmain .links li {
    margin-bottom: 10px
  }
}

.navmain .links li.active a {
  color: #fff
}

.navmain .links li.has-sub .arrow {
  font-size: 16px;
  padding: 10px
}

.navmain .links li.has-sub .links {
  display: none;
  padding: 25px 25px 0
}

.navmain .links li.has-sub .links li {
  font-size: 18px
}

.navmain .links li.has-sub.clicked .arrow {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg)
}

.navmain .links li.has-sub.clicked .links {
  display: block
}

.navmain .links a:hover {
  color: #fff
}

.navmain .links.main li {
  font-size: 32px
}

@media(min-width: 992px) {
  .navmain .links.main li {
    font-size: 40px
  }
}

.navmain .bottom {
  color: #cbcbcb;
  font: 14px/1.5 "Inter", sans-serif
}

.navmain [class*=col-]:nth-child(2) {
  border-left: 1px solid rgba(255, 255, 255, .3);
  border-right: 1px solid rgba(255, 255, 255, .3)
}

.navmain a {
  color: inherit;
  text-decoration: none
}

.navmain.open {
  display: block
}

.client-header .back-page,
.client-header .meta {
  margin-block: 2rem
}

.client-header .back-page a,
.client-header .meta a {
  color: #fff;
  font: 500 16px/1 "Inter", sans-serif
}

.client-header .back-page .cta,
.client-header .meta .cta {
  font-size: 14px
}

.client-header h1 {
  font: 700 28px "Futura Std", serif
}

.client-header h1 i {
  font-family: "Playfair Display", serif
}

.client-header h1.heavy {
  font-size: 32px
}

.client-header .page-links a {
  color: rgba(255, 255, 255, .5);
  font: 500 16px "Inter", sans-serif;
  text-decoration: none;
  -webkit-transition: color .2s ease;
  transition: color .2s ease
}

.client-header .page-links a:hover {
  color: #fff
}

.client-header .page-links a.active {
  color: #ff4c00;
  text-decoration: underline
}

.client-header .page-links a+a {
  margin-left: 1.5rem
}

.client-header .cta:hover .img svg path {
  stroke: #fff
}

.client-header.main-header h1 {
  font-size: 32px
}

.client-project {
  padding-block: 4rem 2rem
}

.client-project .go-back a {
  color: #111;
  font: 500 16px/1 "Inter", sans-serif
}

.client-project .heading h2 {
  font-size: 28px
}

.client-project .heading .links a {
  color: rgba(0, 0, 0, .5);
  cursor: pointer;
  font: 500 16px "Inter", sans-serif;
  text-decoration: none
}

.client-project .heading .links a:hover {
  color: #000
}

.client-project .heading .links a.active {
  color: #ff4c00;
  text-decoration: underline
}

.client-project .heading .links a+a {
  margin-left: 1.25rem
}

.client-project .status {
  padding-top: 2rem
}

.client-project .status h2 {
  color: #ff4c00;
  font-size: 16px
}

.client-project .status h3 {
  font-size: 28px
}

.project-timeline {
  overflow: hidden;
  padding-block: 5rem
}

.project-timeline .head h2 {
  font-size: 46px
}

.project-timeline .card-row {
  display: grid;
  grid-column-gap: 16px;
  grid-row-gap: 0px;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: 1fr
}

.project-timeline .card-row .card-timeline .flag {
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  z-index: 1
}

.project-timeline .card-row .card-timeline .flag .head {
  padding: 1rem
}

.project-timeline .card-row .card-timeline .flag .head h2 {
  font-size: 80px
}

.project-timeline .card-row .card-timeline .flag .head h3 {
  font-size: 20px
}

.project-timeline .card-row .card-timeline .flag .text {
  background: #fff;
  padding: 1.25rem 1rem 1.5rem
}

.project-timeline .card-row .card-timeline .flag .text p {
  line-height: normal;
  margin: 0
}

.project-timeline .card-row .card-timeline .flag .text .h3 {
  font-size: 18px
}

.project-timeline .card-row .card-timeline .icon-wrap {
  isolation: isolate;
  padding-bottom: 20px;
  position: relative;
  width: 52px
}

.project-timeline .card-row .card-timeline .icon-wrap .line {
  height: 100%;
  left: 50%;
  position: absolute;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  width: 1px;
  z-index: -1
}

.project-timeline .card-row .card-timeline .icon-wrap .icon {
  background: #fff;
  border-radius: 50%;
  height: 52px;
  width: 52px
}

.project-timeline .card-row .card-timeline.green .flag {
  border: 1px solid #65ccb0
}

.project-timeline .card-row .card-timeline.green .flag .head {
  background: #65ccb0
}

.project-timeline .card-row .card-timeline.green .flag .text .h3 {
  color: #65ccb0
}

.project-timeline .card-row .card-timeline.green .icon-wrap .line {
  background: #65ccb0
}

.project-timeline .card-row .card-timeline.green .icon-wrap .icon {
  border: 1px solid #65ccb0
}

.project-timeline .card-row .card-timeline.orange .flag {
  border: 1px solid #ff4c00
}

.project-timeline .card-row .card-timeline.orange .flag .head {
  background: #ff4c00
}

.project-timeline .card-row .card-timeline.orange .flag .text .h3 {
  color: #ff4c00
}

.project-timeline .card-row .card-timeline.orange .icon-wrap .line {
  background: #ff4c00
}

.project-timeline .card-row .card-timeline.orange .icon-wrap .icon {
  border: 1px solid #ff4c00
}

.project-timeline .card-row .card-timeline.pink .flag {
  border: 1px solid #f45689
}

.project-timeline .card-row .card-timeline.pink .flag .head {
  background: #f45689
}

.project-timeline .card-row .card-timeline.pink .flag .text .h3 {
  color: #f45689
}

.project-timeline .card-row .card-timeline.pink .icon-wrap .line {
  background: #f45689
}

.project-timeline .card-row .card-timeline.pink .icon-wrap .icon {
  border: 1px solid #f45689
}

.project-timeline .card-row .card-timeline.purple .flag {
  border: 1px solid #a15cd4
}

.project-timeline .card-row .card-timeline.purple .flag .head {
  background: #a15cd4
}

.project-timeline .card-row .card-timeline.purple .flag .text .h3 {
  color: #a15cd4
}

.project-timeline .card-row .card-timeline.purple .icon-wrap .line {
  background: #a15cd4
}

.project-timeline .card-row .card-timeline.purple .icon-wrap .icon {
  border: 1px solid #a15cd4
}

.project-timeline .card-row .card-timeline:first-child .icon-wrap {
  height: 100px
}

.project-timeline .card-row .card-timeline:nth-child(2) .icon-wrap {
  height: 120px
}

.project-timeline .card-row .card-timeline:nth-child(3) .icon-wrap {
  height: 150px
}

.project-timeline .card-row .card-timeline:nth-child(4) .icon-wrap {
  height: 170px
}

.project-timeline .card-row .card-timeline:last-child .icon-wrap {
  height: 200px
}

.project-timeline .timeline-bar .bar:before {
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0);
  border-bottom: 0 none;
  border-radius: 3px 3px 0 0;
  bottom: calc(100% + 10px);
  content: "";
  height: 10px;
  position: absolute;
  width: 100%
}

.project-timeline .timeline-bar .bar.green:before {
  border-color: #65ccb0
}

.project-timeline .timeline-bar .bar.orange:before {
  border-color: #ff4c00
}

.project-timeline .timeline-bar .bar.pink:before {
  border-color: #f45689
}

.project-timeline .timeline-bar .bar.purple:before {
  border-color: #a15cd4
}

.timeline-status {
  overflow: hidden;
  padding-block: 5rem
}

.timeline-status .timeline-bar .bar .icon-bar {
  -webkit-transition: width 1s ease-in-out;
  transition: width 1s ease-in-out;
  width: 0
}

.timeline-status .timeline-bar .bar .icon-bar .icon-circle {
  border: 3px solid #fff;
  border-radius: 50%;
  height: 1rem;
  left: calc(100% - .5rem);
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 1rem
}

.timeline-status .timeline-bar .bar .icon-bar .icon-circle:after {
  bottom: 1rem;
  content: "";
  height: 50px;
  position: absolute;
  right: 50%;
  width: 1px
}

.timeline-status .timeline-bar .bar .icon-bar .icon {
  bottom: calc(100% + 1.5rem);
  height: 52px;
  left: 100%;
  position: absolute;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 52px
}

.timeline-status .status-meta {
  margin-top: 2.5rem
}

.timeline-status .status-meta .head {
  border: 1px solid #fff;
  border-radius: 10px 10px 0 0;
  padding: 1.25rem 1rem
}

.timeline-status .status-meta .head h2 {
  font-size: 16px
}

.timeline-status .status-meta .head h3 {
  font-size: 24px
}

.timeline-status .status-meta .head h2,
.timeline-status .status-meta .head h3 {
  color: #fff
}

.timeline-status .status-meta .content-meta {
  border: 1px solid #fff;
  border-radius: 0 0 10px 10px;
  padding: 1.25rem 1rem
}

.timeline-status .status-meta .actions .circle-icon {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 2rem;
  flex: 0 0 2rem;
  font-size: 20px;
  height: 2rem;
  width: 2rem
}

.timeline-status .status-meta .actions h2 {
  font-size: 20px
}

.timeline-status .status-meta.green .head {
  background: #65ccb0
}

.timeline-status .status-meta.green .actions h2 {
  color: #65ccb0
}

.timeline-status .status-meta.green .head,
.timeline-status .status-meta.green .content-meta {
  border-color: #65ccb0
}

.timeline-bar {
  background: #eee;
  position: relative
}

.timeline-bar .bar {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 1.55rem;
  position: relative
}

.timeline-bar .bar .icon-bar {
  height: 100%;
  position: relative;
  width: 100%
}

.timeline-bar .bar.first,
.timeline-bar .bar.last {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 52px;
  flex: 0 0 52px
}

.timeline-bar .bar.second {
  -webkit-box-flex: .6;
  -ms-flex: .6;
  flex: .6
}

.timeline-bar .bar.green {
  background: rgba(101, 204, 176, .2)
}

.timeline-bar .bar.green .icon-bar {
  background: #65ccb0
}

.timeline-bar .bar.green .icon-bar .icon-circle {
  background: #65ccb0
}

.timeline-bar .bar.green .icon-bar .icon-circle:after {
  background: #65ccb0
}

.timeline-bar .bar.green .icon-bar .icon {
  border: 1px solid #65ccb0
}

.timeline-bar .bar.orange {
  background: rgba(255, 76, 0, .2)
}

.timeline-bar .bar.orange .icon-bar {
  background: #ff4c00
}

.timeline-bar .bar.orange .icon-bar .icon-circle {
  background: #ff4c00
}

.timeline-bar .bar.orange .icon-bar .icon-circle:after {
  background: #ff4c00
}

.timeline-bar .bar.orange .icon-bar .icon {
  border: 1px solid #ff4c00
}

.timeline-bar .bar.pink {
  background: rgba(244, 86, 137, .2)
}

.timeline-bar .bar.pink .icon-bar {
  background: #f45689
}

.timeline-bar .bar.pink .icon-bar .icon-circle {
  background: #f45689
}

.timeline-bar .bar.pink .icon-bar .icon-circle:after {
  background: #f45689
}

.timeline-bar .bar.pink .icon-bar .icon {
  border: 1px solid #f45689
}

.timeline-bar .bar.purple {
  background: rgba(161, 92, 212, .2)
}

.timeline-bar .bar.purple .icon-bar {
  background: #a15cd4
}

.timeline-bar .bar.purple .icon-bar .icon-circle {
  background: #a15cd4
}

.timeline-bar .bar.purple .icon-bar .icon-circle:after {
  background: #a15cd4
}

.timeline-bar .bar.purple .icon-bar .icon {
  border: 1px solid #a15cd4
}

.timeline-bar .bar+.bar {
  margin-left: 1.25rem
}

.timeline-bar:before,
.timeline-bar:after {
  background: #eee;
  content: "";
  height: 100%;
  position: absolute;
  top: 0;
  width: 100vw
}

.timeline-bar:after {
  right: 100%
}

.timeline-bar:before {
  left: 100%
}

.actions-need {
  padding-bottom: 5rem
}

.actions-need .heading h2 {
  font-size: 16px
}

.actions-need .heading h3 {
  font-size: 28px
}

.actions-need .check-actions {
  margin-top: 2rem
}

.actions-need .check-actions .check-icon {
  color: #ff4c00;
  font-size: 20px;
  margin-right: 1.25rem
}

.actions-need .check-actions p {
  line-height: 1.35
}

.actions-need .check-actions .cta {
  border-width: 1px;
  color: #ff4c00;
  font-size: 12px;
  min-height: 36px
}

.actions-need .check-actions .cta i {
  font-size: 18px
}

.actions-need .check-actions .cta:hover {
  color: #fff
}

.actions-need .check-actions .cta+.cta {
  margin-left: 1.25rem
}

@media(min-width: 1200px) {
  .actions-need .message-box .message {
    max-width: 1100px
  }
}

.message-box .pic {
  background: #d9d9d9;
  border-radius: 50%;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 110px;
  flex: 0 0 110px;
  height: 110px;
  margin-block: 1rem;
  margin-right: -1.25rem;
  overflow: hidden;
  position: relative;
  width: 110px;
  z-index: 1
}

.message-box .pic img {
  height: 100%;
  mix-blend-mode: multiply;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.message-box .message {
  border: 1px solid #ff4c00;
  border-radius: 10px;
  padding: 1.5rem 2.5rem
}

.message-box .message h2 {
  color: #000;
  font: 700 24px "Futura Std", serif
}

.message-box .message h2 .quote {
  color: #ff4c00;
  font: 700 40px "Futura Std", serif
}

.message-box .message .meta {
  color: #ff4c00;
  font: 700 16px "Inter", sans-serif
}

.message-box .message .meta a {
  text-decoration: none
}

.message-box .message a {
  color: #ff4c00
}

.message-box .cta {
  border: 1px solid #ff4c00;
  color: #ff4c00;
  font-size: 12px
}

.message-box .cta i {
  color: #ff4c00;
  font-size: 24px
}

.message-box .cta:hover {
  background: #ff4c00;
  color: #fff
}

.message-box .cta:hover i {
  color: #fff
}

.message-box.upload {
  padding-left: 90px
}

.message-box.upload .upload-btn-wrapper .btn-upload {
  border-color: #ff5811;
  color: #ff5811
}

.message-box.upload .cta {
  background: #ff5811;
  color: #fff
}

.message-box .send-along .form-check-label {
  color: #111;
  font: 16px "Inter", sans-serif
}

.message-box .send-along a {
  color: #ff4c00;
  text-decoration: none
}

.message-box+.message-box {
  margin-top: 2.25rem
}

.message-ctas .cta {
  color: #ff4c00
}

.message-ctas .cta i {
  font-size: 24px
}

.message-ctas .cta:hover {
  color: #fff
}

.message-ctas .cta+.cta {
  margin-left: 1.5rem
}

.messages-wrap {
  margin-top: 2rem
}

.messages-wrap .head {
  color: #000;
  font: 700 16px "Inter", sans-serif
}

.messages-wrap .head h2 {
  color: #ff5811;
  font: inherit
}

.messages-wrap .message-row .icons {
  min-width: 84px
}

.messages-wrap .message-row .icons .icon {
  border-radius: 50%;
  color: #fff;
  font-size: 26px;
  height: 44px;
  overflow: hidden;
  width: 44px
}

.messages-wrap .message-row .icons .icon.pic {
  background: #d9d9d9;
  position: relative;
  z-index: 1
}

.messages-wrap .message-row .icons .icon.pic img {
  height: 100%;
  mix-blend-mode: multiply;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.messages-wrap .message-row .icons .icon+.icon {
  margin-left: -20px
}

.messages-wrap .message-row p {
  font-size: 14px;
  margin-bottom: 0;
  white-space: nowrap
}

.messages-wrap .message-row a {
  color: inherit;
  text-decoration: underline
}

.messages-wrap .message-row .name p,
.messages-wrap .message-row .date p {
  font-weight: 700
}

.messages-wrap .message-row .message {
  overflow: hidden
}

.messages-wrap .message-row .message p {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.messages-wrap .message-row .message a {
  color: #ff4c00;
  font: 14px "Inter", sans-serif;
  text-decoration: none;
  white-space: nowrap
}

.messages-wrap .message-row .cta {
  border-color: #ff4c00;
  border-width: 1px;
  color: #ff4c00
}

.messages-wrap .message-row.green .icons .icon:not(.pic) {
  background: #65ccb0
}

.messages-wrap .message-row.green .head h2 {
  color: #65ccb0
}

.messages-wrap .message-row.orange .icons .icon:not(.pic) {
  background: #ff4c00
}

.messages-wrap .message-row.orange .head h2 {
  color: #ff4c00
}

.messages-wrap .message-row.pink .icons .icon:not(.pic) {
  background: #f45689
}

.messages-wrap .message-row.pink .head h2 {
  color: #f45689
}

.messages-wrap .message-row.purple .icons .icon:not(.pic) {
  background: #a15cd4
}

.messages-wrap .message-row.purple .head h2 {
  color: #a15cd4
}

.messages-wrap .message-row.has-attachment .notification {
  position: relative
}

.messages-wrap .message-row.has-attachment .notification .attachment {
  left: 0;
  position: absolute;
  top: 100%;
  width: 100%
}

.circle-icon {
  border: 1px solid #ff4c00;
  color: #ff4c00;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 24px;
  flex: 0 0 24px;
  font: 14px "Inter", sans-serif;
  height: 24px;
  width: 24px
}

.current-timeline .heading h2 {
  font-size: 28px
}

.current-timeline .heading .tab-timeline .tab {
  color: rgba(0, 0, 0, .5);
  cursor: pointer;
  font: 500 16px "Inter", sans-serif
}

.current-timeline .heading .tab-timeline .tab:hover {
  color: #000
}

.current-timeline .heading .tab-timeline .tab.active {
  color: #ff4c00;
  text-decoration: underline
}

.current-timeline .heading .tab-timeline .tab+.tab {
  margin-left: 1.25rem
}

.current-timeline .meta-row .logo {
  border-style: solid;
  border-width: 1px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 52px;
  flex: 0 0 52px;
  height: 52px;
  width: 52px
}

.current-timeline .meta-row .wrap-meta {
  border-style: solid;
  border-width: 1px;
  font: 16px "Futura Std", serif;
  overflow: hidden
}

.current-timeline .meta-row .wrap-meta .title {
  color: #fff;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 8.25rem;
  flex: 0 0 8.25rem;
  font: 700 20px "Futura Std", serif
}

.current-timeline .meta-row .wrap-meta .col-meta {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc((100% - 8.25rem)/3);
  flex: 0 0 calc((100% - 8.25rem)/3);
  font: 16px "Inter", sans-serif
}

@media(min-width: 540px) {
  .current-timeline .meta-row .wrap-meta .col-meta {
    padding-inline: 1.5rem
  }
}

.current-timeline .meta-row .wrap-meta .col-meta strong {
  font-weight: 700
}

.current-timeline .meta-row .wrap-meta .col-meta .text {
  position: relative
}

.current-timeline .meta-row .wrap-meta .col-meta .text .check {
  color: #ff4c00;
  position: absolute;
  right: calc(100% + .5rem)
}

.current-timeline .meta-row .wrap-meta.payment .col-meta {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc((100% - 540px - 8.25rem)/3);
  flex: 0 0 calc((100% - 540px - 8.25rem)/3)
}

.current-timeline .meta-row .wrap-meta.payment .payment-type {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 540px;
  flex: 0 0 540px
}

.current-timeline .meta-row.green .logo,
.current-timeline .meta-row.green .wrap-meta {
  border-color: #65ccb0
}

.current-timeline .meta-row.green .title {
  background-color: #65ccb0
}

.current-timeline .meta-row.orange .logo,
.current-timeline .meta-row.orange .wrap-meta {
  border-color: #ff4c00
}

.current-timeline .meta-row.orange .title {
  background-color: #ff4c00
}

.current-timeline .meta-row.pink .logo,
.current-timeline .meta-row.pink .wrap-meta {
  border-color: #f45689
}

.current-timeline .meta-row.pink .title {
  background-color: #f45689
}

.current-timeline .meta-row.purple .logo,
.current-timeline .meta-row.purple .wrap-meta {
  border-color: #a15cd4
}

.current-timeline .meta-row.purple .title {
  background-color: #a15cd4
}

@media(min-width: 540px) {
  .current-timeline .payment-list {
    padding-left: 68px
  }
}

@media(min-width: 768px) {
  .current-timeline .payment-list {
    padding-left: 200px
  }
}

.current-timeline .payment-list .payment-row .col-meta {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  padding: .5rem
}

@media(min-width: 768px) {
  .current-timeline .payment-list .payment-row .col-meta {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 calc((100% - 540px)/3);
    flex: 0 0 calc((100% - 540px)/3);
    padding: .5rem 1.5rem
  }
}

@media(max-width: 767px) {
  .current-timeline .payment-list .payment-row .payment-type {
    padding-block: 0
  }
}

@media(min-width: 768px) {
  .current-timeline .payment-list .payment-row .payment-type {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 540px;
    flex: 0 0 540px
  }
}

@media(max-width: 767px) {
  .current-timeline .payment-list .payment-row {
    border: 1px solid #ff4c00;
    border-radius: .5rem;
    margin-top: 1.5rem
  }
}

.project-contact {
  padding-block: 5rem
}

.project-contact .cta {
  color: #ff4c00;
  min-width: 225px
}

.project-contact .cta:hover {
  color: #fff
}

.project-contact .cta+.cta {
  margin-left: 2rem
}

.analytics-meta .head .title {
  background: #ff4c00;
  color: #fff;
  padding: .8rem 1rem
}

.analytics-meta .head .title h2 {
  font-size: 16px
}

.analytics-meta .head .select-month {
  position: relative;
  width: 160px
}

.analytics-meta .head .select-month select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font: 500 16px "Inter", sans-serif;
  height: 100%;
  width: 100%
}

.analytics-meta .head .select-month select:focus {
  border: 0 none;
  outline: 0 none
}

.analytics-meta .head .select-month .arrow {
  pointer-events: none;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%)
}

.analytics-meta .copy p,
.analytics-meta .copy li {
  font-size: 18px
}

.analytics-meta .copy ol li+li {
  margin-top: 1rem
}

.analytics-row .box h2 {
  font-size: 52px;
  line-height: .8
}

.analytics-row .box .p {
  line-height: 1.25
}

.analytics-row .box h4 {
  color: rgba(0, 0, 0, .5)
}

.analytics-row .box.graph h2 {
  font-size: 24px
}

.analytics-row .border-left {
  border-left: 1px solid #111
}

.project-row {
  gap: 1.25rem
}

.project-row .project-column {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 20rem;
  flex: 0 0 20rem;
  max-width: 20rem
}

.project-row .project-column h2 {
  color: #ff5811;
  font-size: 16px
}

.projects-row .project-column h2 {
  color: #ff5811;
  font-size: 16px
}

.projects-row .project-column .link {
  color: #ff5811;
  font-size: 14px;
  font-weight: 400
}

.projects-row .project-column .link:hover {
  background: rgba(0, 0, 0, 0);
  color: #fff
}

.projects-row .project-column .project-list {
  max-height: 450px;
  overflow-y: auto
}

.projects-row .project-column .project-list .meta-project p {
  text-wrap: balance
}

@-webkit-keyframes growProgressBar {

  0%,
  33% {
    --pgPercentage: 0
  }

  100% {
    --pgPercentage: var(--value)
  }
}

@keyframes growProgressBar {

  0%,
  33% {
    --pgPercentage: 0
  }

  100% {
    --pgPercentage: var(--value)
  }
}

@property --pgPercentage {
  inherits: false;
  initial-value: 0;
  syntax: "<number>"
}

.meta-project {
  margin-top: 2rem
}

.meta-project .icon-wrap {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 56px;
  flex: 0 0 56px;
  height: 56px;
  isolation: isolate;
  position: relative
}

.meta-project .icon-wrap .progress-bar {
  --fill: var($color);
  -webkit-animation: growProgressBar 1.5s 1 forwards;
  animation: growProgressBar 1.5s 1 forwards;
  background: radial-gradient(closest-side, #111 90%, transparent 0 99.9%, rgba(255, 255, 255, 0.05)), conic-gradient(var(--fill) calc(var(--pgPercentage) * 1%), rgba(255, 255, 255, 0.1) 0)
}

.meta-project .icon-wrap .border-bar {
  border: 3px solid rgba(255, 255, 255, .1)
}

.meta-project .icon-wrap .progress-bar,
.meta-project .icon-wrap .border-bar {
  border-radius: 50%;
  height: 100%;
  left: 0;
  pointer-events: none;
  position: absolute;
  top: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 100%;
  z-index: -1
}

.meta-project .icon-wrap .icon {
  height: 30px;
  width: 30px
}

.meta-project .icon-wrap .icon img {
  max-height: 100%;
  max-width: 100%
}

.meta-project .icon-wrap .over {
  left: 50%;
  opacity: 0;
  position: absolute;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%)
}

.meta-project .icon-wrap .over h2 {
  font-size: 14px
}

.meta-project .checked {
  border: 2px solid #ff4c00;
  border-radius: 50%;
  height: 38px;
  margin-right: 1.25rem;
  width: 38px
}

.meta-project h2 {
  font-size: 16px
}

.meta-project .cta-row a {
  color: #fff;
  font: 14px "Inter", sans-serif;
  text-decoration: underline
}

.meta-project .cta-row a:hover {
  color: #ff4c00
}

.meta-project .cta-row a+a {
  margin-left: 1.25rem
}

.meta-project.green .icon-wrap .progress-bar {
  --fill: #65ccb0
}

.meta-project.green .icon-wrap .over h2 {
  color: #65ccb0
}

.meta-project.green .copy h2 {
  color: #65ccb0
}

.meta-project.orange .icon-wrap .progress-bar {
  --fill: #ff4c00
}

.meta-project.orange .icon-wrap .over h2 {
  color: #ff4c00
}

.meta-project.orange .copy h2 {
  color: #ff4c00
}

.meta-project.pink .icon-wrap .progress-bar {
  --fill: #f45689
}

.meta-project.pink .icon-wrap .over h2 {
  color: #f45689
}

.meta-project.pink .copy h2 {
  color: #f45689
}

.meta-project.purple .icon-wrap .progress-bar {
  --fill: #a15cd4
}

.meta-project.purple .icon-wrap .over h2 {
  color: #a15cd4
}

.meta-project.purple .copy h2 {
  color: #a15cd4
}

.meta-project a {
  color: inherit;
  text-decoration: none
}

.meta-project.hover:hover .icon {
  opacity: 0
}

.meta-project.hover:hover .over {
  opacity: 1
}

.projects-list-table .pages,
.projects-list-table .years {
  color: rgba(255, 255, 255, .5);
  font: 500 16px "Inter", sans-serif
}

.projects-list-table .pages a,
.projects-list-table .years a {
  color: #fff;
  text-decoration: none
}

.projects-list-table .pages a.active,
.projects-list-table .years a.active {
  color: #ff4c00;
  text-decoration: underline
}

.projects-list-table .years a+a {
  margin-left: 1.25rem
}

.projects-list-table .pages a {
  margin-left: .4rem
}

.ask-question h2 {
  color: #fff;
  font-size: 32px
}

.ask-question a+a {
  margin-left: 2rem
}

.upload-cta-row {
  padding-left: 90px
}

.upload-cta-row .cta-file {
  border: 1px solid #ff4c00;
  border-radius: 10px;
  color: #111;
  font: 700 12px "Inter", sans-serif;
  padding: .8rem 1rem
}

.upload-cta-row .cta-file .icon {
  background: #ff4c00;
  border-radius: 50%;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 32px;
  flex: 0 0 32px;
  height: 32px;
  width: 32px
}

.upload-cta-row .more-file {
  color: #ff4c00;
  font: 700 12px "Inter", sans-serif
}

.project-dashboard .col-head .link {
  color: #cbcbcb
}

.project-dashboard .col-head .sort {
  margin-bottom: .5rem
}

.project-dashboard .col-head .sort .sort-by h3 {
  color: rgba(255, 255, 255, .5);
  font: 500 14px "Inter", sans-serif;
  margin: 0
}

.project-dashboard .quick-links .icon-more {
  color: #bcbcbc
}

.project-dashboard .quick-links .statuses-list .flag-status {
  border: 1px solid #fff;
  border-radius: .4rem;
  color: #fff;
  font: 500 14px "Inter", sans-serif;
  max-width: 105px;
  padding: .5rem;
  text-align: center
}

.project-dashboard .quick-links .statuses-list .flag-status.assigned {
  background: #ff5811;
  border-color: #ff5811
}

.project-dashboard .quick-links .statuses-list .flag-status+.flag-status {
  margin-top: .8rem
}

.project-dashboard .quick-links .assigned-to-list .meta {
  color: #fff;
  font: 500 14px "Inter", sans-serif
}

.project-dashboard .quick-links .assigned-to-list .meta .icon {
  color: #ff5811;
  font-size: 16px;
  isolation: isolate;
  margin-right: 10px;
  position: relative
}

.project-dashboard .quick-links .assigned-to-list .meta .icon:before {
  background: #fff;
  content: "";
  height: 80%;
  left: 10%;
  position: absolute;
  top: 10%;
  width: 80%;
  z-index: -1
}

.project-dashboard .quick-links .assigned-to-list .meta+.meta {
  margin-top: .8rem
}

.project-dashboard .quick-links .link-to-project a {
  color: rgba(255, 255, 255, .5);
  font: 500 16px "Inter", sans-serif
}

.project-dashboard .quick-links .link-to-project a:hover {
  color: #fff
}

.project-dashboard .quick-links-list .box .icon {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 20px;
  flex: 0 0 20px;
  height: 20px;
  margin-right: 10px;
  width: 20px
}

.project-dashboard .quick-links-list .box .icon img {
  max-height: 100%;
  max-width: 100%
}

.project-dashboard .quick-links-list .box .text p {
  font-weight: 500
}

.project-dashboard .quick-links-list .box:hover .text p {
  color: #fff
}

.project-dashboard .quick-links-list .box+.box {
  margin-top: 1rem
}

.project-dashboard .quick-links-list .more-links {
  cursor: pointer;
  position: relative
}

.project-dashboard .quick-links-list .more-links .drop-list {
  background: #282828;
  border-left: 1px solid #ff5811;
  border-radius: 0 8px 8px 0;
  display: none;
  left: 100%;
  position: absolute;
  width: 182px
}

.project-dashboard .quick-links-list .more-links .drop-list .box {
  padding: 1rem
}

.project-dashboard .quick-links-list .more-links .drop-list .box+.box {
  border-top: 1px solid #fff;
  margin: 0
}

.project-dashboard .quick-links-list .more-links:hover .drop-list {
  display: block
}

.project-dashboard .task-overview-list .task-project {
  overflow: hidden
}

.project-dashboard .task-overview-list .task-project .icon {
  border: 1px solid #ff5811;
  border-radius: 50%;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 44px;
  flex: 0 0 44px;
  height: 44px;
  margin-right: 1.25rem;
  width: 44px
}

.project-dashboard .task-overview-list .task-project .copy {
  color: #fff;
  font: 14px/1.18 "Inter", sans-serif;
  text-wrap: nowrap
}

.project-dashboard .task-overview-list .task-project .copy h2 {
  font-size: 14px
}

.project-dashboard .task-overview-list .task-project .copy .date-cta {
  font: 500 14px/1 "Inter", sans-serif
}

.project-dashboard .task-overview-list .task-project .copy .detail {
  font-weight: 700
}

.project-dashboard .task-overview-list .task-project .copy .task {
  font-weight: 500
}

.project-dashboard .task-overview-list .task-project .copy p {
  font: inherit;
  line-height: 1.18;
  margin: 0
}

.project-dashboard .task-overview-list .task-project .copy .text a {
  color: #ff5811;
  font-weight: 400
}

.project-dashboard .task-overview-list .task-project a {
  color: #fff
}

.project-dashboard .task-overview-list .task-project.green .copy h2 {
  color: #65ccb0
}

.project-dashboard .task-overview-list .task-project.green .copy .date-cta {
  background: #65ccb0
}

.project-dashboard .task-overview-list .task-project.orange .copy h2 {
  color: #ff4c00
}

.project-dashboard .task-overview-list .task-project.orange .copy .date-cta {
  background: #ff4c00
}

.project-dashboard .task-overview-list .task-project.pink .copy h2 {
  color: #f45689
}

.project-dashboard .task-overview-list .task-project.pink .copy .date-cta {
  background: #f45689
}

.project-dashboard .task-overview-list .task-project.purple .copy h2 {
  color: #a15cd4
}

.project-dashboard .task-overview-list .task-project.purple .copy .date-cta {
  background: #a15cd4
}

.project-dashboard .task-overview-list .task-project+.task-project {
  margin-top: 2rem
}

.project-dashboard .task-overview-list .task-project+.head-sort {
  margin-top: 3rem
}

.project-dashboard .task-overview-list .more-tasks {
  position: relative
}

.project-dashboard .task-overview-list .more-tasks a {
  background: #111;
  color: #ff5811;
  font: 700 12px/1 "Inter", sans-serif;
  padding: 1rem;
  position: relative;
  z-index: 5
}

.project-dashboard .task-overview-list .more-tasks:before {
  background: rgba(255, 255, 255, .25);
  content: "";
  height: 1px;
  left: 0;
  position: absolute;
  top: 50%;
  width: 100%
}

.project-dashboard .task-view .head {
  margin-top: -15px
}

.project-dashboard .task-view .head h2 {
  color: #fff;
  font: 700 28px "Futura Std", serif
}

.project-dashboard .task-view .head .time {
  color: #fff;
  font: 700 28px "Futura Std", serif
}

.project-dashboard .task-view .head .time .icon {
  color: #fe5811;
  font-size: 24px
}

.project-dashboard .task-view .message-task {
  padding: 2rem 0 0 2rem;
  position: relative
}

.project-dashboard .task-view .message-task .icon-user {
  background: #d9d9d9;
  border-radius: 50%;
  height: 4rem;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 4rem
}

.project-dashboard .task-view .message-task .icon-user img {
  height: 100%;
  width: 100%
}

.project-dashboard .task-view .message-task .meta-text {
  background: #282828;
  border-radius: 10px;
  padding: 2rem
}

.project-dashboard .task-view .message-task .meta-text p {
  color: #fff;
  font: 500 14px "Inter", sans-serif
}

.project-dashboard .task-view .reply-task .reply-message .icon-user {
  height: 44px;
  top: 2.2rem;
  width: 44px
}

.project-dashboard .task-view .reply-task .reply-message textarea.meta-text {
  background: #282828;
  border-radius: 10px;
  color: #fff;
  height: 54px;
  overflow: hidden;
  padding: .8rem 2rem;
  resize: none;
  width: 100%
}

.project-dashboard .task-view .reply-task .cta-row {
  padding-left: 2rem
}

.project-dashboard .task-view .reply-task .cta-row .cta {
  background: #ff5811;
  color: #fff
}

.project-dashboard .project-column .project-list {
  max-height: initial;
  overflow: inherit;
  position: relative
}

.project-dashboard .project-column .project-list:before {
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(17, 17, 17, 0)), to(rgb(17, 17, 17)));
  background: linear-gradient(to bottom, rgba(17, 17, 17, 0) 0%, rgb(17, 17, 17) 100%);
  bottom: 0;
  content: "";
  height: 100%;
  left: 0;
  pointer-events: none;
  position: absolute;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 100%;
  z-index: 1
}

.project-dashboard .project-column .meta-project.bor {
  border: 1px solid #fff;
  border-radius: .5rem;
  padding: 10px
}

.project-dashboard .project-column .meta-project.bor .icon-wrap {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 32px;
  flex: 0 0 32px;
  height: 32px;
  width: 32px
}

.project-dashboard .project-column .meta-project.bor .icon-wrap .icon {
  height: 20px;
  width: 20px
}

.project-dashboard .project-column .meta-project.bor .copy h2 {
  font-size: 12px
}

.project-dashboard .project-column .meta-project.bor .copy p {
  font-size: 14px
}

.project-dashboard .project-column .meta-project.bor+.meta-project {
  margin-top: 1rem
}

.project-dashboard .project-column .more a {
  background: #111;
  color: #ff5811;
  font: 700 12px/1 "Inter", sans-serif;
  padding: 1rem;
  position: relative;
  z-index: 5
}

.project-dashboard .statuses-list a+a,
.project-dashboard .statuses-list a+.select-status,
.project-dashboard .statuses-list .select-status+a,
.project-dashboard .statuses-list .select-status+.select-status {
  margin-top: 12px
}

.project-dashboard .statuses-list a {
  text-decoration: none
}

.project-dashboard .templates-list .template+.template {
  margin-top: 12px
}

.project-dashboard .statuses-list a,
.project-dashboard .statuses-list .select-status,
.project-dashboard .templates-list .template {
  color: rgba(255, 255, 255, .5);
  cursor: pointer;
  font: 500 14px "Inter", sans-serif
}

.project-dashboard .statuses-list a.active,
.project-dashboard .statuses-list .active.select-status,
.project-dashboard .templates-list .active.template,
.project-dashboard .statuses-list a:hover,
.project-dashboard .statuses-list .select-status:hover,
.project-dashboard .templates-list .template:hover {
  color: #fff
}

.project-dashboard .statuses-list a .form-check,
.project-dashboard .statuses-list .select-status .form-check,
.project-dashboard .templates-list .template .form-check {
  min-height: initial
}

.project-dashboard .statuses-list a .form-check-label,
.project-dashboard .statuses-list .select-status .form-check-label,
.project-dashboard .templates-list .template .form-check-label {
  margin-top: 2px
}

.upload-btn-wrapper {
  display: inline-block;
  overflow: hidden;
  position: relative
}

.upload-btn-wrapper .btn-upload {
  background: rgba(0, 0, 0, 0);
  border: 1px solid #fff;
  border-radius: 50px;
  color: #fff;
  font: 700 12px "Inter", sans-serif;
  height: 50px;
  padding: 8px 20px
}

.upload-btn-wrapper .btn-upload i {
  color: #ff5811;
  font-size: 22px
}

.upload-btn-wrapper input[type=file] {
  font-size: 100px;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0
}

.add-time {
  position: relative
}

.add-time .meta-label {
  background: rgba(0, 0, 0, 0);
  border: 1px solid #fff;
  border-radius: 50px;
  color: #fff;
  font: 700 12px "Inter", sans-serif;
  height: 50px;
  padding: 8px 20px
}

.add-time .meta-label i {
  color: #ff5811;
  font-size: 22px
}

.add-time .due-date {
  height: 0;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  width: 0
}

form .input-text,
form .form-control,
form .form-select,
.form-wrap .input-text,
.form-wrap .form-control,
.form-wrap .form-select,
.new-task-form .input-text,
.new-task-form .form-control,
.new-task-form .form-select {
  font: 700 20px "Futura Std", serif;
  height: 56px;
  padding: 0 1rem
}

form .textarea,
.form-wrap .textarea,
.new-task-form .textarea {
  font: 500 14px "Inter", sans-serif;
  height: 190px;
  padding: 1rem
}

form .input-text,
form .form-control,
form .form-select,
form .textarea,
.form-wrap .input-text,
.form-wrap .form-control,
.form-wrap .form-select,
.form-wrap .textarea,
.new-task-form .input-text,
.new-task-form .form-control,
.new-task-form .form-select,
.new-task-form .textarea {
  background-color: #282828;
  border-radius: 10px;
  color: #fff;
  width: 100%;
  outline: none !important
}

form .input-text::-webkit-input-placeholder,
form .form-control::-webkit-input-placeholder,
form .form-select::-webkit-input-placeholder,
form .textarea::-webkit-input-placeholder,
.form-wrap .input-text::-webkit-input-placeholder,
.form-wrap .form-control::-webkit-input-placeholder,
.form-wrap .form-select::-webkit-input-placeholder,
.form-wrap .textarea::-webkit-input-placeholder,
.new-task-form .input-text::-webkit-input-placeholder,
.new-task-form .form-control::-webkit-input-placeholder,
.new-task-form .form-select::-webkit-input-placeholder,
.new-task-form .textarea::-webkit-input-placeholder {
  color: rgba(255, 255, 255, .5)
}

form .input-text::-moz-placeholder,
form .form-control::-moz-placeholder,
form .form-select::-moz-placeholder,
form .textarea::-moz-placeholder,
.form-wrap .input-text::-moz-placeholder,
.form-wrap .form-control::-moz-placeholder,
.form-wrap .form-select::-moz-placeholder,
.form-wrap .textarea::-moz-placeholder,
.new-task-form .input-text::-moz-placeholder,
.new-task-form .form-control::-moz-placeholder,
.new-task-form .form-select::-moz-placeholder,
.new-task-form .textarea::-moz-placeholder {
  color: rgba(255, 255, 255, .5)
}

form .input-text:-ms-input-placeholder,
form .form-control:-ms-input-placeholder,
form .form-select:-ms-input-placeholder,
form .textarea:-ms-input-placeholder,
.form-wrap .input-text:-ms-input-placeholder,
.form-wrap .form-control:-ms-input-placeholder,
.form-wrap .form-select:-ms-input-placeholder,
.form-wrap .textarea:-ms-input-placeholder,
.new-task-form .input-text:-ms-input-placeholder,
.new-task-form .form-control:-ms-input-placeholder,
.new-task-form .form-select:-ms-input-placeholder,
.new-task-form .textarea:-ms-input-placeholder {
  color: rgba(255, 255, 255, .5)
}

form .input-text::-ms-input-placeholder,
form .form-control::-ms-input-placeholder,
form .form-select::-ms-input-placeholder,
form .textarea::-ms-input-placeholder,
.form-wrap .input-text::-ms-input-placeholder,
.form-wrap .form-control::-ms-input-placeholder,
.form-wrap .form-select::-ms-input-placeholder,
.form-wrap .textarea::-ms-input-placeholder,
.new-task-form .input-text::-ms-input-placeholder,
.new-task-form .form-control::-ms-input-placeholder,
.new-task-form .form-select::-ms-input-placeholder,
.new-task-form .textarea::-ms-input-placeholder {
  color: rgba(255, 255, 255, .5)
}

form .input-text::placeholder,
form .form-control::placeholder,
form .form-select::placeholder,
form .textarea::placeholder,
.form-wrap .input-text::placeholder,
.form-wrap .form-control::placeholder,
.form-wrap .form-select::placeholder,
.form-wrap .textarea::placeholder,
.new-task-form .input-text::placeholder,
.new-task-form .form-control::placeholder,
.new-task-form .form-select::placeholder,
.new-task-form .textarea::placeholder {
  color: rgba(255, 255, 255, .5)
}

form .input-text:focus,
form .input-text:focus-visible,
form .form-control:focus,
form .form-control:focus-visible,
form .form-select:focus,
form .form-select:focus-visible,
form .textarea:focus,
form .textarea:focus-visible,
.form-wrap .input-text:focus,
.form-wrap .input-text:focus-visible,
.form-wrap .form-control:focus,
.form-wrap .form-control:focus-visible,
.form-wrap .form-select:focus,
.form-wrap .form-select:focus-visible,
.form-wrap .textarea:focus,
.form-wrap .textarea:focus-visible,
.new-task-form .input-text:focus,
.new-task-form .input-text:focus-visible,
.new-task-form .form-control:focus,
.new-task-form .form-control:focus-visible,
.new-task-form .form-select:focus,
.new-task-form .form-select:focus-visible,
.new-task-form .textarea:focus,
.new-task-form .textarea:focus-visible {
  border-color: #ff5811 !important;
  -webkit-box-shadow: 0 0 0 .25rem rgba(255, 88, 17, .25);
  box-shadow: 0 0 0 .25rem rgba(255, 88, 17, .25)
}

form textarea:focus,
form textarea:focus-visible,
.form-wrap textarea:focus,
.form-wrap textarea:focus-visible,
.new-task-form textarea:focus,
.new-task-form textarea:focus-visible {
  border: none !important;
  outline: none !important
}

form .form-select,
.form-wrap .form-select,
.new-task-form .form-select {
  background: #282828;
  color: rgba(255, 255, 255, .5);
  font-size: 16px
}

form .form-select option,
.form-wrap .form-select option,
.new-task-form .form-select option {
  color: #fff
}

form .form-select option:hover,
.form-wrap .form-select option:hover,
.new-task-form .form-select option:hover {
  background: #ff5811
}

form .select-wrap,
.form-wrap .select-wrap,
.new-task-form .select-wrap {
  position: relative
}

form .select-wrap .select-icon,
.form-wrap .select-wrap .select-icon,
.new-task-form .select-wrap .select-icon {
  pointer-events: none;
  position: absolute;
  right: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%)
}

form .form-check-input,
.form-wrap .form-check-input,
.new-task-form .form-check-input {
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #fff
}

form .form-check-input:checked,
.form-wrap .form-check-input:checked,
.new-task-form .form-check-input:checked {
  background-color: #ff4c00;
  border-color: #ff4c00
}

form .form-check-input:focus,
.form-wrap .form-check-input:focus,
.new-task-form .form-check-input:focus {
  -webkit-box-shadow: 0 0 0 .25rem rgba(255, 76, 0, .25);
  box-shadow: 0 0 0 .25rem rgba(255, 76, 0, .25)
}

form .form-check-label,
.form-wrap .form-check-label,
.new-task-form .form-check-label {
  color: #fff;
  font: 500 14px "Inter", sans-serif
}

form .form-text,
.form-wrap .form-text,
.new-task-form .form-text {
  color: #fff;
  font: 14px "Inter", sans-serif
}

form .form-text a,
.form-wrap .form-text a,
.new-task-form .form-text a {
  color: #ff5811;
  text-decoration: none
}

form .input-wrap .icon,
.form-wrap .input-wrap .icon,
.new-task-form .input-wrap .icon {
  border: 1px solid #ff5811;
  border-radius: 50%;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 42px;
  flex: 0 0 42px;
  height: 42px;
  margin-right: 1.25rem;
  padding: 5px;
  width: 42px
}

form .input-wrap .form-control,
.form-wrap .input-wrap .form-control,
.new-task-form .input-wrap .form-control {
  font-size: 18px;
  font-weight: 400;
  height: 42px
}

form .input_color,
.form-wrap .input_color,
.new-task-form .input_color {
  border-radius: 10px
}

form .input_color input,
.form-wrap .input_color input,
.new-task-form .input_color input {
  width: 100%;
  border: none;
  border-color: rgba(0, 0, 0, 0)
}

form .input_color .value,
form .input_color .picker,
.form-wrap .input_color .value,
.form-wrap .input_color .picker,
.new-task-form .input_color .value,
.new-task-form .input_color .picker {
  border-radius: 0
}

form .input_color .picker,
.form-wrap .input_color .picker,
.new-task-form .input_color .picker {
  -webkit-transform: scale(1.5);
  transform: scale(1.5)
}

form .cta,
.form-wrap .cta,
.new-task-form .cta {
  background: #ff5811;
  color: #fff
}

form .cta.dark,
.form-wrap .cta.dark,
.new-task-form .cta.dark {
  background: rgba(0, 0, 0, 0);
  border: 1px solid #ff4c00
}

.timeline-set .meta-row .logo {
  border-style: solid;
  border-width: 1px;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 52px;
  flex: 0 0 52px;
  height: 52px;
  width: 52px
}

.timeline-set .meta-row .wrap-meta {
  background: #282828;
  font: 16px "Futura Std", serif;
  overflow: hidden
}

.timeline-set .meta-row .wrap-meta .title {
  border-right: 10px solid #111;
  color: #fff;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 9.25rem;
  flex: 0 0 9.25rem;
  font: 700 20px "Futura Std", serif;
  padding-right: 1rem
}

.timeline-set .meta-row .wrap-meta a {
  color: #fff
}

.timeline-set .meta-row .wrap-meta .col-meta {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 calc((100% - 8.25rem)/3);
  flex: 0 0 calc((100% - 8.25rem)/3);
  font: 16px "Inter", sans-serif
}

.timeline-set .meta-row .wrap-meta .target {
  color: #fff;
  font-size: 14px;
  font-style: italic;
  opacity: .5
}

.timeline-set .meta-row.green .logo {
  border-color: #65ccb0
}

.timeline-set .meta-row.orange .logo {
  border-color: #ff4c00
}

.timeline-set .meta-row.pink .logo {
  border-color: #f45689
}

.timeline-set .meta-row.purple .logo {
  border-color: #a15cd4
}

.brand-wrap {
  background: #282828;
  border-radius: 10px;
  max-height: 320px;
  overflow-y: auto;
  padding: 1.5rem
}

.brand-wrap .brand-select .name {
  color: #fff;
  font: 700 16px/1.18 "Futura Std", serif
}

.brand-wrap .brand-select .cta {
  background: #282828;
  border: 1px solid #fff;
  border-radius: 6px;
  font: 700 12px/1 "Inter", sans-serif;
  padding: .5rem;
  width: 6rem
}

.brand-wrap .brand-select .cta.added {
  background: #ff4c00;
  border-color: #ff4c00
}

.brand-wrap .brand-select.added .name {
  color: #ff4c00
}

.brand-wrap .brand-select.claimed {
  opacity: .5;
  pointer-events: none
}

.brand-wrap .brand-select+.brand-select {
  margin-top: .8rem
}

.projects-hub {
  background: #fff;
  height: calc(100vh - 180px);
  overflow: hidden
}

.projects-hub .controls-hub {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 76px;
  flex: 0 0 76px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  padding-inline: 1.25rem;
  width: 76px
}

.projects-hub .controls-hub hr {
  border-color: #111;
  opacity: 1
}

.projects-hub .controls-hub .action+.action {
  margin-top: 10px
}

.projects-hub .projects-columns {
  border-left: .5px solid #111;
  gap: 2rem;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 2rem 3rem
}

.projects-hub .projects-columns .project-column {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 320px;
  flex: 0 0 320px;
  height: 100%;
  width: 320px
}

.projects-hub .projects-columns .project-column .head {
  border-bottom: .5px solid #111
}

.projects-hub .projects-columns .project-column .head h2 {
  font-size: 16px
}

.projects-hub .projects-columns .project-column .head .sort {
  color: rgba(17, 17, 17, .5);
  cursor: pointer;
  font: 500 14px "Inter", sans-serif
}

.projects-hub .projects-columns .project-column .head .icon-more {
  color: #bcbcbc;
  cursor: pointer
}

.projects-hub .projects-columns .project-column .project-list {
  height: calc(100vh - 336px);
  overflow-y: auto
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag-list {
  width: 1rem
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag {
  border-radius: 10px 0 0 10px;
  height: .5rem;
  width: 1rem
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag.gold {
  background: #e9a302
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag.green {
  background: #21b583
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag.blue {
  background: #405fc1
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag.orange {
  background: #ff5811
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag.dull {
  background: #bcbcbc
}

.projects-hub .projects-columns .project-column .project-list .project-box__flag+.project-box__flag {
  margin-top: 5px
}

.projects-hub .projects-columns .project-column .project-list .project-box__meta {
  background: #f8f7f4;
  border-radius: .5rem;
  padding: 1rem 1rem .625rem
}

.projects-hub .projects-columns .project-column .project-list .project-box__phase {
  margin-bottom: 10px
}

.projects-hub .projects-columns .project-column .project-list .project-box__phase h2 {
  color: #bcbcbc;
  font-size: 12px
}

.projects-hub .projects-columns .project-column .project-list .project-box__name h2 {
  color: #111;
  font: 16px "Inter", sans-serif
}

.projects-hub .projects-columns .project-column .project-list .project-box__info {
  color: rgba(17, 17, 17, .5);
  font: 500 14px "Inter", sans-serif
}

.projects-hub .projects-columns .project-column .foot {
  border-top: .5px solid #111;
  padding: 1.25rem 1rem
}

.projects-hub .projects-columns .project-column .foot .add-project-cta {
  background: #f8f7f4;
  border-radius: .5rem;
  color: #000;
  font: 500 16px/.8 "Inter", sans-serif;
  padding: .8rem 1rem
}

.projects-hub .projects-columns .project-column .foot .icon {
  height: 18px;
  width: 18px
}

.projects-hub .projects-columns .project-column.stale .head .meta h2,
.projects-hub .projects-columns .project-column.dull .head .meta h2 {
  color: #c5c5c5
}

.projects-hub .projects-columns .project-column.stale .foot .icon svg path,
.projects-hub .projects-columns .project-column.dull .foot .icon svg path {
  stroke: #c5c5c5
}

.projects-hub .projects-columns .project-column.green .head .meta h2 {
  color: #65ccb0
}

.projects-hub .projects-columns .project-column.green .head .icon-more i {
  color: #65ccb0
}

.projects-hub .projects-columns .project-column.green .project-list .project-box__phase h2 {
  color: #65ccb0
}

.projects-hub .projects-columns .project-column.green .project-list .project-box__info .time {
  color: #65ccb0
}

.projects-hub .projects-columns .project-column.green .foot .icon svg path {
  stroke: #65ccb0
}

.projects-hub .projects-columns .project-column.orange .head .meta h2 {
  color: #ff4c00
}

.projects-hub .projects-columns .project-column.orange .head .icon-more i {
  color: #ff4c00
}

.projects-hub .projects-columns .project-column.orange .project-list .project-box__phase h2 {
  color: #ff4c00
}

.projects-hub .projects-columns .project-column.orange .project-list .project-box__info .time {
  color: #ff4c00
}

.projects-hub .projects-columns .project-column.orange .foot .icon svg path {
  stroke: #ff4c00
}

.projects-hub .projects-columns .project-column.pink .head .meta h2 {
  color: #f45689
}

.projects-hub .projects-columns .project-column.pink .head .icon-more i {
  color: #f45689
}

.projects-hub .projects-columns .project-column.pink .project-list .project-box__phase h2 {
  color: #f45689
}

.projects-hub .projects-columns .project-column.pink .project-list .project-box__info .time {
  color: #f45689
}

.projects-hub .projects-columns .project-column.pink .foot .icon svg path {
  stroke: #f45689
}

.projects-hub .projects-columns .project-column.purple .head .meta h2 {
  color: #a15cd4
}

.projects-hub .projects-columns .project-column.purple .head .icon-more i {
  color: #a15cd4
}

.projects-hub .projects-columns .project-column.purple .project-list .project-box__phase h2 {
  color: #a15cd4
}

.projects-hub .projects-columns .project-column.purple .project-list .project-box__info .time {
  color: #a15cd4
}

.projects-hub .projects-columns .project-column.purple .foot .icon svg path {
  stroke: #a15cd4
}

.uploaded-files {
  padding-block: 5rem
}

.uploaded-files .head h2 {
  color: #000;
  font: 700 16px "Inter", sans-serif
}

.uploaded-files .files-wrap {
  border-left: 1px solid rgba(0, 0, 0, .5);
  border-top: 1px solid rgba(0, 0, 0, .5)
}

.uploaded-files .files-wrap .box-wrap {
  border-bottom: 1px solid rgba(0, 0, 0, .5);
  border-right: 1px solid rgba(0, 0, 0, .5)
}

.uploaded-files .files-wrap .box .img {
  aspect-ratio: 230/175;
  background: #f3f3f3
}

.uploaded-files .files-wrap .box .img.pic {
  overflow: hidden
}

.uploaded-files .files-wrap .box .img.pic img {
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.uploaded-files .files-wrap .box .img.icon img {
  mix-blend-mode: multiply
}

.uploaded-files .files-wrap .box h2 {
  color: #111;
  font: 700 12px "Inter", sans-serif
}

.uploaded-files .files-wrap .box h3 {
  color: rgba(17, 17, 17, .5);
  font: 12px "Inter", sans-serif
}

.uploaded-files .pages {
  color: #000;
  font: 500 16px "Inter", sans-serif
}

.uploaded-files .pages a {
  color: rgba(0, 0, 0, .5);
  margin-left: .4rem;
  text-decoration: none
}

.uploaded-files .pages a.active {
  color: #ff4c00;
  text-decoration: underline
}

.team-header {
  padding-bottom: 2.25rem
}

.team-header .meta {
  margin-block: 2rem
}

.team-header .meta a {
  color: #fff;
  font: 500 16px/1 "Inter", sans-serif
}

.team-header .meta .cta {
  font-size: 14px
}

.team-header .member-meta .pic {
  margin-right: 1.25rem;
  position: relative
}

.team-header .member-meta .pic .img {
  border-radius: 50%;
  height: 7rem;
  overflow: hidden;
  width: 7rem
}

.team-header .member-meta .pic .img img {
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.team-header .member-meta .pic .change {
  background: #ff4c00;
  border: 3px solid #111;
  border-radius: 50%;
  bottom: 0;
  height: 36px;
  position: absolute;
  right: 0;
  width: 36px
}

.team-header .member-meta .meta a {
  color: #ff4c00;
  font: 500 14px "Inter", sans-serif
}

.team-header h1 {
  font: 700 32px "Futura Std", serif
}

.team-header h1 i {
  font-family: "Playfair Display", serif
}

.team-header .cta:hover .img svg path {
  stroke: #fff
}

.team-dashboard {
  padding-block: 4rem 2rem
}

.team-dashboard .team-col .col-head h2 {
  color: #ff5811;
  font-size: 16px
}

.team-dashboard .team-col .team-box .img {
  border-radius: 50%;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 40px;
  flex: 0 0 40px;
  height: 40px;
  margin-right: 1.25rem;
  overflow: hidden;
  width: 40px
}

.team-dashboard .team-col .team-box .img img {
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.team-dashboard .team-col .team-box .name {
  color: #fff;
  font: 700 16px "Inter", sans-serif
}

.team-dashboard .team-col .team-box a {
  color: #ff5811;
  font: 16px "Inter", sans-serif
}

.team-dashboard .edit-member .pic,
.team-dashboard .edit-member .upload-icon {
  border-radius: 50%;
  height: 96px;
  overflow: hidden;
  width: 96px
}

.team-dashboard .edit-member .pic img {
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.team-dashboard .edit-member .upload-icon {
  background: #ff4c00
}

.team-dashboard .edit-member .form-label {
  color: #fff;
  font: 16px "Inter", sans-serif
}

.team-dashboard .edit-member .form-control,
.team-dashboard .edit-member .form-select {
  color: #fff;
  font: 16px "Inter", sans-serif
}

.team-dashboard .edit-member .form-control:focus,
.team-dashboard .edit-member .form-select:focus {
  background: #fff;
  color: #111
}

.team-dashboard .edit-member .form-select option {
  color: #000
}

.team-dashboard .edit-member .edit-icon {
  position: relative
}

.team-dashboard .edit-member .edit-icon .icon {
  color: #ff4c00;
  position: absolute;
  right: 2rem;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%)
}

.footer {
  background: #111;
  padding: 20px 0
}

.footer .confetti_box {
  bottom: -50px;
  height: 600px;
  left: 50%;
  margin-left: -300px;
  max-width: 600px;
  pointer-events: none;
  position: absolute
}

.footer .location h2 {
  color: #ff4c00;
  font-size: 20px
}

.footer .location p {
  color: #817f7f;
  font-size: 13px
}

.footer .black {
  display: none
}

.footer p {
  color: rgba(255, 255, 255, .5);
  font: 14px/1.429 "Inter", sans-serif
}

.footer a {
  color: inherit;
  text-decoration: none
}

.footer hr {
  border-color: rgba(136, 136, 136, .4);
  opacity: 1
}

h2,
.h2 {
  font: 700 40px "Futura Std", serif
}

@media(min-width: 992px) {

  h2,
  .h2 {
    font-size: 72px
  }
}

h2 i,
h2 em,
.h2 i,
.h2 em {
  font-family: "Playfair Display", serif
}

h2.h2-big,
.h2.h2-big {
  font-size: 36px
}

@media(min-width: 992px) {

  h2.h2-big,
  .h2.h2-big {
    font-size: 48px
  }
}

h3,
.h3 {
  font: 700 18px "Futura Std", serif
}

@media(min-width: 992px) {

  h3,
  .h3 {
    font-size: 32px
  }
}

h3 i,
h3 em,
.h3 i,
.h3 em {
  font-family: "Playfair Display", serif
}

p,
.p,
li {
  color: #cbcbcb;
  font: 16px/1.5 "Inter", sans-serif
}

p a,
.p a,
li a {
  color: #ff4c00;
  text-decoration: none
}

p:last-child {
  margin-bottom: 0
}

.bg-white p,
.bg-white .p,
.bg-white li {
  color: #323232
}

.cta {
  border: 2px solid #ff4c00;
  border-radius: 26px;
  font: 600 14px/1 "Inter", sans-serif;
  margin-top: 30px;
  padding: 15px 30px
}

@media(min-width: 992px) {
  .cta {
    margin-top: 50px
  }
}

.cta:hover {
  background-color: #ff4c00
}

.text-dark {
  color: #111 !important
}

.text-orange {
  color: #ff4c00 !important
}

.text-ocean {
  color: #5bc4d2 !important
}

.font-futura {
  font-family: "Futura Std", serif !important
}

.font-inter {
  font-family: "Inter", sans-serif !important
}

.font-play {
  font-family: "Playfair Display", serif !important
}

.text-balance {
  text-wrap: balance
}

.text-pretty {
  text-wrap: pretty
}

.text-stable {
  text-wrap: stable
}

.cursor {
  background: #ff4c00;
  border-radius: 50%;
  height: 10px;
  margin-left: -5px;
  margin-top: -5px;
  pointer-events: none;
  position: absolute;
  -webkit-transition: -webkit-transform 350ms, -webkit-box-shadow 150ms;
  transition: -webkit-transform 350ms, -webkit-box-shadow 150ms;
  transition: transform 350ms, box-shadow 150ms;
  transition: transform 350ms, box-shadow 150ms, -webkit-transform 350ms, -webkit-box-shadow 150ms;
  width: 10px;
  z-index: 10000
}

.cursor:nth-child(1) {
  -webkit-animation: scale 2s infinite;
  animation: scale 2s infinite;
  z-index: 1
}

.cursor:nth-child(2) {
  -webkit-animation: pulse 2s infinite;
  animation: pulse 2s infinite;
  -webkit-box-shadow: 0 0 0 #ff4c00;
  box-shadow: 0 0 0 #ff4c00;
  opacity: .2
}

.cursor.animation-link {
  -webkit-box-shadow: 0 0 50px rgba(255, 76, 0, .4) !important;
  box-shadow: 0 0 50px rgba(255, 76, 0, .4) !important;
  -webkit-transform: scale(5) !important;
  transform: scale(5) !important
}

.cursor.animation-link-button {
  background: #000;
  -webkit-box-shadow: 0 0 50px rgba(0, 0, 0, .8) !important;
  box-shadow: 0 0 50px rgba(0, 0, 0, .8) !important;
  -webkit-transform: scale(5) !important;
  transform: scale(5) !important
}

@-webkit-keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 76, 0, .8);
    box-shadow: 0 0 0 0 rgba(255, 76, 0, .8)
  }

  70% {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 76, 0, 0);
    box-shadow: 0 0 0 30px rgba(255, 76, 0, 0)
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 76, 0, 0);
    box-shadow: 0 0 0 0 rgba(255, 76, 0, 0)
  }
}

@keyframes pulse {
  0% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 76, 0, .8);
    box-shadow: 0 0 0 0 rgba(255, 76, 0, .8)
  }

  70% {
    -webkit-box-shadow: 0 0 0 30px rgba(255, 76, 0, 0);
    box-shadow: 0 0 0 30px rgba(255, 76, 0, 0)
  }

  100% {
    -webkit-box-shadow: 0 0 0 0 rgba(255, 76, 0, 0);
    box-shadow: 0 0 0 0 rgba(255, 76, 0, 0)
  }
}

@-webkit-keyframes scale {
  0% {
    opacity: 1;
    -webkit-transform: scale(0.8);
    transform: scale(0.8)
  }

  60% {
    opacity: .6;
    -webkit-transform: scale(0.6);
    transform: scale(0.6)
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(0.8);
    transform: scale(0.8)
  }
}

@keyframes scale {
  0% {
    opacity: 1;
    -webkit-transform: scale(0.8);
    transform: scale(0.8)
  }

  60% {
    opacity: .6;
    -webkit-transform: scale(0.6);
    transform: scale(0.6)
  }

  100% {
    opacity: 1;
    -webkit-transform: scale(0.8);
    transform: scale(0.8)
  }
}

body {
  background: #111
}

body:has(.hero) .header {
  position: absolute
}

body.navopened {
  overflow: hidden
}

body.navopened .header h1 {
  color: #fff
}

body.navopened .header .site-nav .cta {
  border-color: rgba(255, 255, 255, .3);
  color: #fff
}

body.navopened .header .site-nav .nav_call {
  border-color: rgba(255, 255, 255, .3)
}

.sec {
  padding: 100px 0
}

@media(min-width: 992px) {
  .sec {
    padding: 135px 0
  }
}

.container-xxl.wide {
  max-width: 1440px
}

.imgpocket,
.img_pocket {
  padding: 50px 0
}

@media(min-width: 768px) {

  .imgpocket,
  .img_pocket {
    padding: 100px 0
  }
}

.imgpocket .img:not(.no-cover),
.img_pocket .img:not(.no-cover) {
  height: 100%
}

.imgpocket .img:not(.no-cover) img,
.img_pocket .img:not(.no-cover) img {
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover
}

@media(min-width: 768px) {

  .imgpocket .map,
  .img_pocket .map {
    height: 100%
  }
}

.imgpocket .map iframe,
.img_pocket .map iframe {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%
}

@media(max-width: 991px) {

  .imgpocket .map:before,
  .img_pocket .map:before {
    content: "";
    display: block;
    padding-top: 100%
  }
}

.taglist a {
  border-radius: 4px;
  font: 12px "Inter", sans-serif;
  padding: 10px
}

.hover-underline {
  position: relative
}

.hover-underline:after {
  background-color: #ff4c00;
  bottom: 0;
  content: "";
  height: 1px;
  left: 0;
  position: absolute;
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
  -webkit-transform-origin: bottom right;
  transform-origin: bottom right;
  -webkit-transition: -webkit-transform .25s ease-out;
  transition: -webkit-transform .25s ease-out;
  transition: transform .25s ease-out;
  transition: transform .25s ease-out, -webkit-transform .25s ease-out;
  width: 100%
}

.hover-underline:hover:after {
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transform-origin: bottom left;
  transform-origin: bottom left
}

.has-bg {
  isolation: isolate;
  position: relative
}

.has-bg .bg-img {
  background-position: center top;
  background-repeat: no-repeat;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: -1
}

.thankyou-popup .btn-close {
  border: 1px solid rgba(0, 0, 0, .25);
  font-size: 12px;
  right: -10px;
  top: -10px
}

.ribbon {
  aspect-ratio: 800/22;
  background: #282828;
  border-radius: 20px;
  gap: 1rem;
  width: 100%
}

.ribbon .dot {
  aspect-ratio: 1;
  background: #111;
  border-radius: 50%;
  height: 60%;
  margin: 0 6px
}

.ribbon .bar {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%
}

.ribbon .bar.orange {
  background: #ff4c00;
  -webkit-box-flex: .125;
  -ms-flex: .125;
  flex: .125
}

.ribbon .bar.green {
  background: #65ccb0;
  -webkit-box-flex: .6;
  -ms-flex: .6;
  flex: .6
}

.ribbon .bar.pink {
  background: #f45689
}

.ribbon .bar.purple {
  background: #a15cd4
}

body.loading {
  height: 100%;
  overflow: hidden;
  position: fixed;
  width: 100%
}

body.loading:before {
  background: #111;
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 10000
}

.loader {
  height: 60px;
  left: 50%;
  position: fixed;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 200px;
  z-index: 15000
}

.loader:after {
  color: #fff;
  content: "LOADING";
  font: 18px "Inter", sans-serif;
  left: 50%;
  position: absolute;
  top: 125%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%)
}

.loader .circle,
.loader .shadow {
  background-color: #fff;
  border-radius: 50%;
  left: 15%;
  position: absolute;
  width: 20px
}

.loader .circle:nth-child(2),
.loader .circle:nth-child(4),
.loader .shadow:nth-child(2),
.loader .shadow:nth-child(4) {
  -webkit-animation-delay: .2s;
  animation-delay: .2s;
  left: 45%
}

.loader .circle:nth-child(3),
.loader .circle:nth-child(5),
.loader .shadow:nth-child(3),
.loader .shadow:nth-child(5) {
  -webkit-animation-delay: .3s;
  animation-delay: .3s;
  left: auto;
  right: 15%
}

.loader .circle {
  -webkit-animation: circlekey .5s alternate infinite ease;
  animation: circlekey .5s alternate infinite ease;
  height: 20px;
  -webkit-transform-origin: 50%;
  transform-origin: 50%
}

.loader .shadow {
  -webkit-animation: shadowkey .5s alternate infinite ease;
  animation: shadowkey .5s alternate infinite ease;
  background-color: #000;
  -webkit-filter: blur(1px);
  filter: blur(1px);
  height: 4px;
  top: 62px;
  -webkit-transform-origin: 50%;
  transform-origin: 50%;
  z-index: -1
}

@-webkit-keyframes circlekey {
  0% {
    border-radius: 50px 50px 25px 25px;
    height: 5px;
    top: 60px;
    -webkit-transform: scaleX(1.7);
    transform: scaleX(1.7)
  }

  40% {
    border-radius: 50%;
    height: 20px;
    -webkit-transform: scaleX(1);
    transform: scaleX(1)
  }

  100% {
    top: 0%
  }
}

@keyframes circlekey {
  0% {
    border-radius: 50px 50px 25px 25px;
    height: 5px;
    top: 60px;
    -webkit-transform: scaleX(1.7);
    transform: scaleX(1.7)
  }

  40% {
    border-radius: 50%;
    height: 20px;
    -webkit-transform: scaleX(1);
    transform: scaleX(1)
  }

  100% {
    top: 0%
  }
}

@-webkit-keyframes shadowkey {
  0% {
    -webkit-transform: scaleX(1.5);
    transform: scaleX(1.5)
  }

  40% {
    opacity: .7;
    -webkit-transform: scaleX(1);
    transform: scaleX(1)
  }

  100% {
    opacity: .4;
    -webkit-transform: scaleX(0.2);
    transform: scaleX(0.2)
  }
}

@keyframes shadowkey {
  0% {
    -webkit-transform: scaleX(1.5);
    transform: scaleX(1.5)
  }

  40% {
    opacity: .7;
    -webkit-transform: scaleX(1);
    transform: scaleX(1)
  }

  100% {
    opacity: .4;
    -webkit-transform: scaleX(0.2);
    transform: scaleX(0.2)
  }
}

/*# sourceMappingURL=app.min.css.map */