<?php

namespace App\Http\Controllers;

use App\Models\Brand;
use App\Models\User;
use App\Models\Client;
use App\Models\Phase;
use App\Models\Role;
use App\Models\SocialDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class BrandController extends Controller
{
    //
    public function index()
    {
        $brands = DB::table('brands')->paginate(1);
        // $brands = Brand::all()->paginate(1);
        return view('brand.index', compact('brands'));
    }

    public function add_brand()
    {
        $admins = User::all();
        $socialDetails = SocialDetail::all();
        $clients = Client::all();
        return view('brand.add_brand', compact('clients', 'admins', 'socialDetails'));
    }

    public function save_brand(Request $request)
    {


        $request->validate([
            'job_code' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'admin_id' => 'nullable|exists:users,id',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'social_detail' => 'required|array',
        ]);

        try {
            DB::beginTransaction();
            $brand = new Brand();
            $brand->name = $request->name;
            $brand->job_code = $request->job_code;
            $brand->description = $request->description;
            $brand->admin_id = $request->admin_id;
            if ($request->file('logo')) {
                $logo = $request->file('logo');
                $filename = time() . '.' . $logo->getClientOriginalExtension();
                $logo->storeAs('images', $filename, 'public');
                $brand->logo = $filename;
            }
            $brand->save();


            if ($request->filled('social_detail') && is_array($request->social_detail)) {

                $result = $this->sendCredentialsToBrand($request, $brand);
                if (!$result['success']) {
                    DB::rollBack();
                    return back()->withErrors($result['errors'])->withInput();
                }
            }

            // decode Array Input 
            $clients = json_decode($request->clients, true);
            if ($clients) {
                if (gettype($clients) == 'array') {
                    foreach ($clients as $client_id) {
                        $brand->clients()->attach($client_id);
                    }
                } else {
                    $brand->clients()->attach($clients);
                }
            }
            DB::commit();
            return redirect()->route('admin-brands')->with('success', 'Brand created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to create Enterprise: ' . $e->getMessage());
        }
    }


    protected function sendCredentialsToBrand(Request $request, Brand $brand)
    {


        $validator = Validator::make($request->all(), [
            'social_detail' => 'required|array',
            'social_detail.*' => 'email|distinct',
        ]);

        if ($validator->fails()) {

            return ['success' => false, 'errors' => $validator->errors()];
        }

        foreach ($request->social_detail as $email) {


            try {
                $social_detail =  SocialDetail::create([
                    'value' => $email,
                    'assigend_to_model' => 'Brand',
                    'type' => 'email',
                    'assigned_to_id' => $brand->id,
                ]);

                DB::table('social_details_pivot')->insert([
                    'social_detail_id' => $social_detail->id,
                    'brand_id' => $brand->id,
                ]);

                $randomPassword = Str::random(12);
                $brandRole = Role::where('name', 'Brand Member')->first();

                $brandUser = User::create([
                    'name' => $request->name,
                    'email' => $email,
                    'password' => Hash::make($randomPassword),
                    'role_id' => $brandRole->id,
                ]);

                db::table('brand_user_links')->insert([
                    'brand_id' => $brand->id,
                    'user_id' => $brandUser->id,
                ]);



                // $brandUser->notify(new \App\Notifications\BrandCreatedNotification($brandUser, $randomPassword));
            } catch (\Exception $e) {
                throw $e;
            }
        }

        return ['success' => true];
    }








    public function show_brand(Brand $brand)
    {
        return view('brand.show', compact('brand'));
    }

    public function edit_brand($id)
    {
        $admins = User::all();

        $brand = Brand::findOrFail($id);
        $social_details = $brand->socialDetails;
        $clients = Client::all();
        return view('brand.edit_brand', compact('brand', 'admins', 'social_details', 'clients'));
    }

    public function update_brand(Request $request, $brandId)
    {
        $brand = Brand::findOrFail($brandId);


        $request->validate([
            'job_code' => 'required|string|max:255',
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'admin_id' => 'nullable|exists:users,id',
            'social_detail' => 'required|array',
        ]);

        try {
            DB::beginTransaction();

            $brand->update($request->all());

            if ($request->file('logo')) {
                $logo = $request->file('logo');
                $filename = time() . '.' . $logo->getClientOriginalExtension();
                $logo->storeAs('images', $filename, 'public');
                $brand->logo = $filename;
                $brand->save();
            }

            // Get existing social details
            $existingSocialDetails = SocialDetail::where('assigned_to_id', $brand->id)
                ->where('assigend_to_model', 'Brand')
                ->where('type', 'email')
                ->pluck('value')
                ->toArray();

            // Get new social details
            $newSocialDetails = $request->social_detail;

            // Find emails to remove (in existing but not in new)
            $emailsToRemove = array_diff($existingSocialDetails, $newSocialDetails);

            // Find emails to add (in new but not in existing)
            $emailsToAdd = array_diff($newSocialDetails, $existingSocialDetails);

            // Remove old social details and their pivot entries
            if (!empty($emailsToRemove)) {
                SocialDetail::where('assigned_to_id', $brand->id)
                    ->where('assigend_to_model', 'Brand')
                    ->where('type', 'email')
                    ->whereIn('value', $emailsToRemove)
                    ->delete();

                DB::table('social_details_pivot')
                    ->where('brand_id', $brand->id)
                    ->whereIn('social_detail_id', function ($query) use ($emailsToRemove) {
                        $query->select('id')
                            ->from('social_details')
                            ->whereIn('value', $emailsToRemove);
                    })
                    ->delete();
            }

            // Add new social details and send credentials
            if (!empty($emailsToAdd)) {
                $result = $this->sendCredentialsToBrand($request, $brand);
                if (!$result['success']) {
                    DB::rollBack();
                    return back()->withErrors($result['errors'])->withInput();
                }
            }

            // Handle clients
            $clients = json_decode($request->clients, true);
            if ($clients) {
                // Remove existing client associations
                $brand->clients()->detach();

                if (gettype($clients) == 'array') {
                    foreach ($clients as $client_id) {
                        $brand->clients()->attach($client_id);
                    }
                } else {
                    $brand->clients()->attach($clients);
                }
            }

            DB::commit();

            if (Auth::user()->role->name == 'SuperAdmin') {
                return redirect()->route('admin-brands')->with('success', 'Brand updated successfully.');
            }
            return redirect()->route('brands')->with('success', 'Brand updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Failed to update Brand: ' . $e->getMessage());
        }
    }

    public function destroy_brand(Brand $brand)
    {
        $brand->delete();
        return redirect()->route('brand.index')->with('success', 'Brand deleted successfully.');
    }

    public function dashboard(Request $request)
    {
        $user = Auth::user();


        $brand = Brand::whereHas('linkedUsersWithBrand', callback: function ($query) use ($user) {
            $query->where('users.id', $user->id);
        })->first();
        $brandClients = $brand ? $brand->clients : collect();
        $clientProjects = collect();

        if ($brandClients->isNotEmpty()) {
            $clientIds = $brandClients->pluck('id');
            $clientProjects = \App\Models\Project::whereIn('client_id', $clientIds)
                ->with(['phases', 'timeline'])
                ->where('archived', false)
                ->whereHas('timeline', function ($query) {
                    $query->where('type', '!=', 'voyager');
                })
                ->get();
        }
        $allPhases = Phase::all();

        // Helper function to calculate project progress
        $calculateProgress = function ($projects, $phaseId) {
            return $projects->map(function ($project) use ($phaseId) {
                $phase = Phase::find($phaseId);
                $total_steps = $phase ? count($phase->categories) : 0;
                $current_step = optional($project->category)->order ?? 0;

                $project->progress_percentage = $total_steps > 0
                    ? round(($current_step / $total_steps) * 100)
                    : 0;

                return $project;
            });
        };

        // Get projects for each phase and calculate their progress
        $definePhase = $allPhases->where('name', 'Define')->first();
        $projectInDefinePhase = $definePhase
            ? $calculateProgress($clientProjects->where('phase_id', $definePhase->id), $definePhase->id)
            : collect();

        $contentPhase = $allPhases->where('name', 'Content, sitemap, wireframes')->first();
        $projectInContentPhase = $contentPhase
            ? $calculateProgress($clientProjects->where('phase_id', $contentPhase->id), $contentPhase->id)
            : collect();

        $designPhase = $allPhases->where('name', 'Design')->first();
        $projectInDesignPhase = $designPhase
            ? $calculateProgress($clientProjects->where('phase_id', $designPhase->id), $designPhase->id)
            : collect();

        $codePhase = $allPhases->where('name', 'Code')->first();
        $projectInCodePhase = $codePhase
            ? $calculateProgress($clientProjects->where('phase_id', $codePhase->id), $codePhase->id)
            : collect();

        $deployPhase = $allPhases->where('name', 'Deploy and Manage')->first();
        $projectInDeployPhase = $deployPhase
            ? $calculateProgress($clientProjects->where('phase_id', $deployPhase->id), $deployPhase->id)
            : collect();

        return view('brand.dashboard', compact('user', 'brand', 'brandClients', 'clientProjects', 'projectInDefinePhase', 'projectInContentPhase', 'projectInDesignPhase', 'projectInCodePhase', 'projectInDeployPhase',));
    }



    public function projectsByClient(Request $request)
    {
        $user = Auth::user();

        $brand = Brand::whereHas('linkedUsersWithBrand', callback: function ($query) use ($user) {
            $query->where('users.id', $user->id);
        })->first();

        $brandClients = $brand ? $brand->clients : collect();
        $clientProjects = collect();

        if ($brandClients->isNotEmpty()) {
            $clientIds = $brandClients->pluck('id');
            $clientProjects = \App\Models\Project::whereIn('client_id', $clientIds)
                ->with(['phases', 'timeline', 'category', 'client'])
                ->where('archived', false)
                ->whereHas('timeline', function ($query) {
                    $query->where('type', '!=', 'voyager');
                })
                ->get();
        }

        $allPhases = Phase::all();

        // Helper function to calculate project progress
        $calculateProgress = function ($projects, $phaseId) {
            return $projects->map(function ($project) use ($phaseId) {
                $phase = Phase::find($phaseId);
                $total_steps = $phase ? count($phase->categories) : 0;
                $current_step = optional($project->category)->order ?? 0;

                $project->progress_percentage = $total_steps > 0
                    ? round(($current_step / $total_steps) * 100)
                    : 0;

                return $project;
            });
        };

        // Get phases
        $definePhase = $allPhases->where('name', 'Define')->first();
        $contentPhase = $allPhases->where('name', 'Content, sitemap, wireframes')->first();
        $designPhase = $allPhases->where('name', 'Design')->first();
        $codePhase = $allPhases->where('name', 'Code')->first();
        $deployPhase = $allPhases->where('name', 'Deploy and Manage')->first();

        // Sort clients by name alphabetically
        $sortedBrandClients = $brandClients->sortBy('name');

        // Organize projects by client and phase
        $clientsWithProjects = $sortedBrandClients->map(function ($client) use ($clientProjects, $calculateProgress, $definePhase, $contentPhase, $designPhase, $codePhase, $deployPhase) {
            $clientSpecificProjects = $clientProjects->where('client_id', $client->id);

            // Get projects for each phase and calculate their progress for this specific client
            $client->projectInDefinePhase = $definePhase
                ? $calculateProgress($clientSpecificProjects->where('phase_id', $definePhase->id), $definePhase->id)
                : collect();

            $client->projectInContentPhase = $contentPhase
                ? $calculateProgress($clientSpecificProjects->where('phase_id', $contentPhase->id), $contentPhase->id)
                : collect();

            $client->projectInDesignPhase = $designPhase
                ? $calculateProgress($clientSpecificProjects->where('phase_id', $designPhase->id), $designPhase->id)
                : collect();

            $client->projectInCodePhase = $codePhase
                ? $calculateProgress($clientSpecificProjects->where('phase_id', $codePhase->id), $codePhase->id)
                : collect();

            $client->projectInDeployPhase = $deployPhase
                ? $calculateProgress($clientSpecificProjects->where('phase_id', $deployPhase->id), $deployPhase->id)
                : collect();

            // Total projects for this client
            $client->totalProjects = $clientSpecificProjects;

            return $client;
        });

        return view('brand.projects_by_client', compact('user', 'brand', 'brandClients', 'clientProjects', 'clientsWithProjects'));
    }
}
