<?php

    namespace App\Livewire;

    use Livewire\Component;
    use Illuminate\Support\Facades\Auth;
    use App\Models\Task;
    use App\Models\Project;
    use App\Models\Status;
    use Livewire\WithPagination;
    use Illuminate\Support\Collection;
    use Illuminate\Support\Facades\DB;

    class TaskOverview extends Component
    {
        use WithPagination;
        protected $paginationTheme = 'bootstrap';

        public $selectedStatus = 'all';
        public $sortField = 'created_at';
        public $sortDirection = 'asc';

        public $showSortDropdown = false;

        public $alphabet = null;
        public $search = '';
        public $year = 'all'; // default value

        public $years = [];

        public $selectedMonth = null;
        public $billingMonths = [];

        protected $queryString = [
            'selectedStatus' => ['except' => 'all', 'as' => 'status'],
            'sortField' => ['except' => 'created_at'],
            'search' => ['except' => '', 'as' => 'job'],
            'alphabet' => ['except' => null],
            'year' => ['except' => null],
            'selectedMonth' => ['except' => null],
        ];

        public $urgentLimit = 10;
        public $regularLimit = 10;

        public function loadMoreUrgent()
        {
            $this->urgentLimit += 10;
        }

        public function loadMoreRegular()
        {
            $this->regularLimit += 10;
        }

        public $showBillingMonths = false;

        public function toggleBillingMonthDropdown()
        {
            $this->showBillingMonths = !$this->showBillingMonths;
        }

        public function selectBillingMonth($month)
        {
            $this->selectedMonth = $month;
        }


        # File: app/Livewire/TaskOverview.php

        public function loadBillingMonths()
        {
            $months = collect();
            $user = Auth::user();
            $adminAccessLevelId = \App\Models\AccessLevel::where('name', 'admin')->value('id');
            $now = now();
            $currentYear = $now->year;

            if ($this->year === 'all') {
                // Get all years where tasks exist
                $minYear = Task::whereHas('project', function ($q) {
                        $q->whereNull('deleted_at');
                    })
                    ->min(DB::raw('YEAR(created_at)'));

                if (!$minYear) {
                    $this->billingMonths = [];
                    return;
                }

                for ($year = $minYear; $year <= $currentYear; $year++) {
                    $endMonth = ($year == $currentYear) ? $now->month : 12;

                    for ($month = 1; $month <= $endMonth; $month++) {
                        $monthKey = \Carbon\Carbon::create($year, $month)->format('Y-m');
                        $originalSelectedMonth = $this->selectedMonth;
                        $this->selectedMonth = $monthKey;

                        $query = Task::query()
                            ->whereNull('tasks.deleted_at')
                            ->whereHas('project', function ($q) {
                                $q->whereNull('deleted_at');

                                if (!empty($this->search)) {
                                    $q->where(function ($subQ) {
                                        $subQ->where('name', 'like', '%' . $this->search . '%')
                                            ->orWhere('job_code', 'like', '%' . $this->search . '%');
                                    });
                                }

                                if (!empty($this->alphabet)) {
                                    $q->whereRaw('LEFT(UPPER(name), 1) = ?', [strtoupper($this->alphabet)]);
                                }
                            });

                        $this->applyBillingMonthFilter($query);

                        if (
                            !(
                                $user->hasRole(['Admin', 'SuperAdmin']) ||
                                ($user->hasRole('Team Member') && $user->access_level_id == $adminAccessLevelId)
                            )
                        ) {
                            $userTaskIds = $user->tasks()->pluck('tasks.id');
                            $query->whereIn('tasks.id', $userTaskIds);
                        }

                        $taskCount = $query->count();

                        $months[$monthKey] = [
                            'label' => \Carbon\Carbon::create($year, $month)->format('F Y'),
                            'count' => $taskCount,
                        ];

                        $this->selectedMonth = $originalSelectedMonth;
                    }
                }

                $this->billingMonths = $months->reverse()->toArray();
                return;
            }

            // Specific year selected
            $selectedYear = (int)$this->year;
            $startMonth = \Carbon\Carbon::create($selectedYear, 1, 1);
            $endMonth = $selectedYear == $currentYear
                ? \Carbon\Carbon::create($selectedYear, $now->month, 1)
                : \Carbon\Carbon::create($selectedYear, 12, 1);

            while ($startMonth->lessThanOrEqualTo($endMonth)) {
                $monthKey = $startMonth->format('Y-m');
                // Temporarily set selectedMonth for the applyBillingMonthFilter
                $originalSelectedBillingMonth = $this->selectedMonth;
                $this->selectedMonth = $monthKey;

                $query = Task::query()
                    ->whereNull('tasks.deleted_at')
                    ->whereHas('project', function ($q) {
                        $q->whereNull('deleted_at')
                        // ->where('archived', 0)
                        ;

                        if (!empty($this->search)) {
                            $q->where(function ($subQ) {
                                $subQ->where('name', 'like', '%' . $this->search . '%')
                                    ->orWhere('job_code', 'like', '%' . $this->search . '%');
                            });
                        }

                        if (!empty($this->alphabet)) {
                            $q->whereRaw('LEFT(UPPER(name), 1) = ?', [strtoupper($this->alphabet)]);
                        }
                    });

                // Apply the consistent billing month filter
                $this->applyBillingMonthFilter($query);

                // User permission filtering as before
                if (
                    !(
                        $user->hasRole(['Admin', 'SuperAdmin']) ||
                        ($user->hasRole('Team Member') && $user->access_level_id == $adminAccessLevelId)
                    )
                ) {
                    $userTaskIds = $user->tasks()->pluck('tasks.id');
                    $query->whereIn('tasks.id', $userTaskIds);
                }

                $taskCount = $query->count();

                $months[$monthKey] = [
                    'label' => $startMonth->format('F Y'),
                    'count' => $taskCount,
                ];

                $startMonth->addMonth();
                // Restore original selectedMonth
                $this->selectedMonth = $originalSelectedBillingMonth;
            }

            $this->billingMonths = $months->reverse()->toArray();
        }
        

        
        public function filterByBillingMonth($monthYear)
        {
            $this->selectedMonth = $monthYear;
        }

        public function getBillingProjectsProperty()
        {
            if (!$this->selectedMonth) {
                return collect();
            }

            return Project::whereNull('deleted_at')
                // ->where('archived', 0)
                ->whereHas('tasks', function ($query) {
                    // *** CHANGED: Apply custom billing month filter to tasks ***
                    $this->applyBillingMonthFilter($query);
                })
                ->with(['tasks' => function ($query) {
                    // *** CHANGED: Apply filter to eager loaded tasks too ***
                    $this->applyBillingMonthFilter($query);
                }])
                ->get();
        }

        public function mount()
        {
            $this->loadBillingMonths();

            if (request()->has('status') && request('status') !== 'all') {
                $this->selectedStatus = request('status');
            }

            if (!empty($this->search)) {
                $this->selectedStatus = 'all'; // show all statuses when searching
            }

            // $this->years = Project::selectRaw('YEAR(created_at) as year')
            //     ->distinct()
            //     ->orderBy('year', 'desc')
            //     ->pluck('year')
            //     ->toArray();

            $this->years = Task::selectRaw('YEAR(created_at) as year')
                        ->distinct()
                        ->orderBy('year', 'desc')
                        ->pluck('year');

            $this->search = '';
        }

        public function updatingYear()
        {
            $this->resetPage();
        }

        public function updatingAlphabet()
        {
            $this->resetPage();
        }

        public function updatingSortColumn()
        {
            $this->resetPage();
        }

        public function updatingSortDirection()
        {
            $this->resetPage();
        }

        public function sortBy($field)
        {
            $this->sortField = $field;
            $this->sortDirection = 'asc';
            $this->resetPage();
        }


        public function filterByStatus($status)
        {
            $this->selectedStatus = $status;
            $this->resetPage();
        }

        public function getStatusesProperty()
        {
            $user = Auth::user();

            $adminAccessLevelId = \App\Models\AccessLevel::where('name', 'admin')->value('id');

            if (
                $user->hasRole(['Admin', 'SuperAdmin']) ||
                ($user->hasRole('Team Member') && $user->access_level_id == $adminAccessLevelId)
            ) {
                return Status::withCount(['tasks' => function ($query) {
                    $query->whereHas('project', function ($q) {
                        $q->whereNull('deleted_at');
                    });
                }])->get();
            }

            return Status::withCount(['tasks as tasks_count' => function ($query) use ($user) {
                $query->whereIn('tasks.id', $user->tasks()->pluck('tasks.id'))
                    ->whereHas('project', function ($q) {
                        $q->whereNull('deleted_at');
                    });
            }])->get();
        }


        public function getAllTaskCountProperty()
        {
            $user = Auth::user();

            $recentlyFinishedStatusId = Status::where('name', 'Recently Finished')->value('id');

            $query = Task::query()
            ->whereNull('tasks.deleted_at')
            ->whereHas('project', function ($projectQuery) {
                $projectQuery->whereNull('deleted_at')
                // ->where('archived', 0)
                ;
            });

            $adminAccessLevelId = \App\Models\AccessLevel::where('name', 'Admin')->value('id');
            if (
                !(
                    $user->hasRole(['Admin', 'SuperAdmin']) ||
                    ($user->hasRole('Team Member') && $user->access_level_id == $adminAccessLevelId)
                )
            ) {
                $query->whereIn('tasks.id', $user->tasks()->pluck('tasks.id'));
            }

            if ($recentlyFinishedStatusId) {
                $query->where('status_id', '!=', $recentlyFinishedStatusId);
            }

            // Apply search filter if present
            if (!empty($this->search)) {
                $query->whereHas('project', function ($q) {
                    $q->where(function ($subQ) {
                        $subQ->where('name', 'like', '%' . $this->search . '%')
                            ->orWhere('job_code', 'like', '%' . $this->search . '%');
                    })->whereNull('deleted_at');
                });
            }

            // Apply alphabet filter if present
            if (!empty($this->alphabet)) {
                $query->whereHas('project', function ($q) {
                    $q->whereRaw('LEFT(UPPER(name), 1) = ?', [$this->alphabet])
                        ->whereNull('deleted_at');
                });
            }

            // Apply year filter if present
            if ($this->year && $this->year !== 'all') {//filter based on created_at date of task 
                // $query->whereHas('project', function ($q) {
                //     $q->whereYear('created_at', $this->year)
                //         ->whereNull('deleted_at');
                // });

                $query->whereYear('tasks.created_at', $this->year);
            }

            if ($this->selectedMonth) {
                // $query->whereRaw("DATE_FORMAT(tasks.updated_at, '%Y-%m') = ?", [$this->selectedMonth]);

                $this->applyBillingMonthFilter($query);
            }

            return $query->count();
        }

        protected function applyBillingMonthFilter($query)
        {
            if (!$this->selectedMonth) {
                return;
            }

            $startOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->startOfMonth();
            $endOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->endOfMonth();

            // Only filter non-recently finished tasks by created_at in the month
            $query->where('tasks.status_id', '!=', Status::where('name', 'Recently Finished')->value('id'))
                ->whereBetween('tasks.created_at', [$startOfMonth, $endOfMonth]);
        }

        public function getStatusNameProperty()
        {
            if ($this->selectedStatus === 'all') {
                return 'All';
            }

            return Status::find($this->selectedStatus)?->name ?? 'All';
        }


        public function getGroupedTasksProperty()
        {
            $tasksQuery = $this->getFilteredTasksQuery()->whereNull('tasks.deleted_at');

            // Eager load projects and their statuses, but only where the project is not deleted
            $tasks = $tasksQuery->with(['project' => function ($query) {
                $query->whereNull('deleted_at');

                if ($this->selectedStatus === 'archived') {
                    $query->where('archived', 1);
                } else {
                    // $query->where('archived', 0);
                }
            }, 'project.status', 'status'])
                ->get()
                ->filter(fn($task) => $task->project !== null); // Filter out tasks with deleted projects

            $urgentStatusId = Status::where('name', 'Urgent')->first()?->id ?? 1;


            //TO ALSO LIST URGENT ARCHIVED TASKS UNDER ARCHIVED CATEGORY 
                // 🧠 Detect if viewing archived
                $isArchivedView = trim(strtolower($this->selectedStatus)) === 'archived';

                // ✅ Show all tasks as regular in archived view (skip urgent separation)
                if ($isArchivedView) {
                    $grouped = collect([
                        'Tasks' => $tasks->sortBy($this->sortField)->take($this->regularLimit),
                    ]);

                    return [
                        'urgent' => collect(), // empty
                        'regular' => $grouped,
                        'hasMoreUrgent' => false,
                        'hasMoreRegular' => $tasks->count() > $this->regularLimit,
                    ];
                }

            $allUrgentTasks = $tasks->filter(fn($task) => $task->status_id == $urgentStatusId);
            $urgentTasks = $allUrgentTasks->take($this->urgentLimit);

            $nonUrgentTasks = $tasks->filter(fn($task) => $task->status_id != $urgentStatusId);

            if ($this->sortField === 'job_code') {
                $grouped = collect([
                    'Tasks' => $nonUrgentTasks
                        ->sortBy(function ($task) {
                            return $task->project->job_code ?? '';
                        }, SORT_REGULAR, $this->sortDirection === 'desc')
                        ->take($this->regularLimit),
                ]);
            } else {
                $grouped = collect([
                    'Tasks' => $nonUrgentTasks->sortBy($this->sortField)->take($this->regularLimit),
                ]);
            }

            return [
                'urgent' => $urgentTasks,
                'regular' => $grouped,
                'hasMoreUrgent' => $allUrgentTasks->count() > $this->urgentLimit,
                'hasMoreRegular' => $nonUrgentTasks->count() > $this->regularLimit,
            ];
        }




        protected function getTasksBaseQuery()
    {
        $user = Auth::user();

        $query = Task::query()
        ->whereNull('tasks.deleted_at')
        ->whereHas('project', function ($projectQuery) {
            $projectQuery->whereNull('deleted_at');

            if ($this->selectedStatus === 'archived') {
                // Show only archived projects
                $projectQuery->where('archived', 1);
            } else {
                // Show only active (non-archived) projects
                // $projectQuery->where('archived', 0);
            }
        });

        // Apply user task filtering only if NOT viewing archived tasks
        if ($this->selectedStatus !== 'archived') {
            $adminAccessLevelId = \App\Models\AccessLevel::where('name', 'admin')->value('id');
        
            if (
                !(
                    $user->hasRole(['Admin', 'SuperAdmin']) ||
                    ($user->hasRole('Team Member') && $user->access_level_id == $adminAccessLevelId)
                )
            ) {
                $userTaskIds = $user->tasks()->pluck('tasks.id');
                $query->whereIn('tasks.id', $userTaskIds);
            }
        }
        

        return $query;
    }




    protected function getFilteredTasksQuery()
    {
        $query = $this->getTasksBaseQuery();

        $recentlyFinishedStatus = Status::where('name', 'Recently Finished')->first();
        $recentlyFinishedStatusId = $recentlyFinishedStatus?->id;

        if ($this->selectedStatus === 'archived') {
            // For archived, no additional status filtering required, already filtered by archived projects

            // Add filters specifically for archived projects
            // $query->whereHas('project', function ($q) {
            //     $q->where('archived', 1)
            //       ->whereNull('deleted_at');

            //     // Search filter
            //     if (!empty($this->search)) {
            //         $q->where(function ($subQ) {
            //             $subQ->where('name', 'like', '%' . $this->search . '%')
            //                  ->orWhere('job_code', 'like', '%' . $this->search . '%');
            //         });
            //     }

            //     // Alphabet filter
            //     if (!empty($this->alphabet)) {
            //         $q->whereRaw('LEFT(UPPER(name), 1) = ?', [strtoupper($this->alphabet)]);
            //     }

            //     // Year filter
            //     if ($this->year && $this->year !== 'all') {
            //         $q->whereYear('created_at', $this->year);
            //     }
            // });
        } elseif ($this->selectedStatus === 'all') {
            // Exclude recently finished tasks entirely when status=all
            if ($recentlyFinishedStatusId) {
                $query->where('status_id', '!=', $recentlyFinishedStatusId);
            }
        } elseif ($this->selectedStatus === 'past_finished') {
            // ✅ Add this block for "Past Finished"
            if ($recentlyFinishedStatusId) {
                $query->where('status_id', $recentlyFinishedStatusId)
                    ->where('tasks.updated_at', '<', now()->subDays(30));
            }
        } elseif (is_numeric($this->selectedStatus) && (int)$this->selectedStatus === (int)$recentlyFinishedStatusId) {
            // Show recently finished tasks updated within last 30 days only
            $query->where('status_id', $recentlyFinishedStatusId)
                ->where('tasks.updated_at', '>=', now()->subDays(30));

            // 🔥 Ensure month filter (created_at within selected month)
            if ($this->selectedMonth) {
                $startOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->startOfMonth();
                $endOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->endOfMonth();

                $query->whereBetween('tasks.created_at', [$startOfMonth, $endOfMonth]);
            }
        } elseif (is_numeric($this->selectedStatus)) {
            // Regular status filtering
            $query->where('status_id', $this->selectedStatus);
        }

        // Search filter on project name or job code
        if (!empty($this->search)) {
            $query->whereHas('project', function ($q) {
                $q->where(function ($subQ) {
                    $subQ->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('job_code', 'like', '%' . $this->search . '%');
                })->whereNull('deleted_at');
            });
        }

        // Alphabet filter
        if (!empty($this->alphabet)) {
            $query->whereHas('project', function ($q) {
                $q->whereRaw('LEFT(UPPER(name), 1) = ?', [$this->alphabet])
                ->whereNull('deleted_at');
            });
        }

        // Year filter
        if ($this->year && $this->year !== 'all') {//filter based on created_at date of task 
            // $query->whereHas('project', function ($q) {
            //     $q->whereYear('created_at', $this->year)
            //     ->whereNull('deleted_at');
            // });

            $query->whereYear('tasks.created_at', $this->year);
        }

        if ($this->selectedMonth) {
            // $query->whereRaw("DATE_FORMAT(tasks.updated_at, '%Y-%m') = ?", [$this->selectedMonth]);

            if (!((int)$this->selectedStatus === (int)$recentlyFinishedStatusId || $this->selectedStatus === 'past_finished')) {
                $this->applyBillingMonthFilter($query);
            }  
        }

        // dd($query->toSql(), $query->getBindings());


        return $query;
    }




    public function getStatusCountsProperty()
    {
        $user = Auth::user();
        $recentlyFinishedStatusId = Status::where('name', 'Recently Finished')->value('id');
        

        // Counts for non-archived projects with statuses (existing code)
        $baseQuery = Task::query()
        ->whereNull('tasks.deleted_at')
        ->whereHas('project', function ($projectQuery) {
            $projectQuery->whereNull('deleted_at')
            // ->where('archived', 0)
            ;
        });
        

        // Get admin access level id before the permission check
        $adminAccessLevelId = \App\Models\AccessLevel::where('name', 'admin')->value('id');

        // permission check for user roles and access level
        // if (
        //     !(
        //         $user->hasRole(['Admin', 'SuperAdmin']) ||
        //         ($user->hasRole('Team Member') && $user->access_level_id == $adminAccessLevelId)
        //     )
        // ) {
        //     $userTaskIds = $user->tasks()->pluck('tasks.id');
        //     $baseQuery->whereIn('tasks.id', $userTaskIds);
        // }

        // Filters for search, alphabet, year on projects
        if (!empty($this->search)) {
            $baseQuery->whereHas('project', function ($q) {
                $q->where(function ($subQ) {
                    $subQ->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('job_code', 'like', '%' . $this->search . '%');
                })->whereNull('deleted_at');
            });
        }
        if (!empty($this->alphabet)) {
            $baseQuery->whereHas('project', function ($q) {
                $q->whereRaw('LEFT(UPPER(name), 1) = ?', [$this->alphabet])
                    ->whereNull('deleted_at');
            });
        }
        if ($this->year && $this->year !== 'all') {
            $baseQuery->where(function ($q) use ($recentlyFinishedStatusId) {
                $q->where('tasks.status_id', '!=', $recentlyFinishedStatusId)
                  ->orWhere(function ($subQ) use ($recentlyFinishedStatusId) {
                      $subQ->where('tasks.status_id', $recentlyFinishedStatusId)
                           ->where('tasks.updated_at', '>=', now()->subDays(30));
                  });
            });
        
            //filter based on created_at date of task and not project
            // $baseQuery->whereHas('project', function ($projectQ) {
            //     $projectQ->whereYear('created_at', $this->year)->whereNull('deleted_at');
            // });
            
            //filter based on created_at date of task 
            $baseQuery->whereYear('tasks.created_at', $this->year);
        }
        

        if ($this->selectedMonth) {
            $baseQuery->where(function ($q) use ($recentlyFinishedStatusId) {
                $q->where(function ($subQ) use ($recentlyFinishedStatusId) {
                    $subQ->where('tasks.status_id', '!=', $recentlyFinishedStatusId)
                         ->where(function ($innerQ) {
                             $this->applyBillingMonthFilter($innerQ);
                         });
                })->orWhere(function ($subQ) use ($recentlyFinishedStatusId) {
                    $subQ->where('tasks.status_id', $recentlyFinishedStatusId)
                         ->where('tasks.updated_at', '>=', now()->subDays(30));
                         $this->applyBillingMonthFilter($subQ); // ✅ Add month filter here too
                });
            });
        }

        // Exclude old recently finished for non-archived
        $tasksWithFilteredRecentlyFinished = (clone $baseQuery)
        ->where(function ($q) use ($recentlyFinishedStatusId) {
            $q->where('tasks.status_id', '!=', $recentlyFinishedStatusId)
                ->orWhere(function ($subQ) use ($recentlyFinishedStatusId) {
                    $subQ->where('tasks.status_id', $recentlyFinishedStatusId)
                        ->where('tasks.updated_at', '>=', now()->subDays(30)); // ✅ Line Updated
                });
        });

    $counts = $tasksWithFilteredRecentlyFinished
        ->select('tasks.status_id', DB::raw('COUNT(*) as count'))
        ->groupBy('tasks.status_id')
        ->pluck('count', 'status_id')
        ->toArray();

    $allStatusIds = Status::pluck('id')->toArray();
    foreach ($allStatusIds as $id) {
        if (!isset($counts[$id])) {
            $counts[$id] = 0;
        }
    }

    $recentlyFinishedQuery = $this->getTasksBaseQuery()
        ->where('tasks.status_id', $recentlyFinishedStatusId)
        ->where('tasks.updated_at', '>=', now()->subDays(30))
        ->whereHas('project', function ($q) {
            $q->whereNull('deleted_at');
        });

        if ($this->selectedMonth) {
            $startOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->startOfMonth();
            $endOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->endOfMonth();
        
            $recentlyFinishedQuery->whereBetween('tasks.created_at', [$startOfMonth, $endOfMonth]);
        }

    // Permissions check for recently finished tasks
    if (
        !(
            $user->hasRole(['Admin', 'SuperAdmin']) ||
            ($user->hasRole('Team Member') && $user->access_level_id == $adminAccessLevelId)
        )
    ) {
        $userTaskIds = $user->tasks()->pluck('tasks.id');
        $recentlyFinishedQuery->whereIn('tasks.id', $userTaskIds);
    }

    // Apply filters to recently finished tasks as well
    if (!empty($this->search)) {
        $recentlyFinishedQuery->whereHas('project', function ($q) {
            $q->where(function ($subQ) {
                $subQ->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('job_code', 'like', '%' . $this->search . '%');
            });
        });
    }
    if (!empty($this->alphabet)) {
        $recentlyFinishedQuery->whereHas('project', function ($q) {
            $q->whereRaw('LEFT(UPPER(name), 1) = ?', [$this->alphabet]);
        });
    }
    if ($this->year && $this->year !== 'all') {
        $recentlyFinishedQuery
        // ->whereHas('project', function ($q) {
        //     $q->whereYear('created_at', $this->year);
        // })

        ->whereYear('tasks.created_at', $this->year);
    }
    // if ($this->selectedMonth) {
    //     $recentlyFinishedQuery->whereRaw("DATE_FORMAT(tasks.updated_at, '%Y-%m') = ?", [$this->selectedMonth]);
    // }

    $counts[$recentlyFinishedStatusId] = $recentlyFinishedQuery->count();

        // $archivedProjectsCount = Project::where('archived', 1)
        //     ->whereNull('deleted_at')
        //     ->when($this->search, function ($q) {
        //         $q->where('name', 'like', '%' . $this->search . '%')
        //         ->orWhere('job_code', 'like', '%' . $this->search . '%');
        //     })
        //     ->when($this->alphabet, function ($q) {
        //         $q->whereRaw('LEFT(UPPER(name), 1) = ?', [$this->alphabet]);
        //     })
        //     ->when($this->year && $this->year !== 'all', function ($q) {
        //         $q->whereYear('created_at', $this->year);
        //     })
        //     ->count();
        

        // // Add archived count with a special key to counts array
        // $counts['archived'] = $archivedProjectsCount;
        


        $archivedTasksCountQuery = Task::whereHas('project', function ($q) {
            $q->where('archived', 1)
            ->whereNull('deleted_at');

            if (!empty($this->search)) {
                $q->where(function ($subQ) {
                    $subQ->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('job_code', 'like', '%' . $this->search . '%');
                });
            }

            if (!empty($this->alphabet)) {
                $q->whereRaw('LEFT(UPPER(name), 1) = ?', [strtoupper($this->alphabet)]);
            }

            if ($this->year && $this->year !== 'all') {
                $q->whereYear('created_at', $this->year);
            }

            if ($this->selectedMonth) {
                $q->whereRaw("DATE_FORMAT(tasks.updated_at, '%Y-%m') = ?", [$this->selectedMonth]);
            }
        });

        // Count all tasks in archived projects (including urgent and all statuses)
        $archivedTasksCount = $archivedTasksCountQuery->count();

        // Add archived count with a special key to counts array
        $counts['archived'] = $archivedTasksCount;




        $pastFinishedCount = Task::where('status_id', $recentlyFinishedStatusId)
            ->where('updated_at', '<', now()->subDays(30))
            ->whereHas('project', function ($q) {
                $q->whereNull('deleted_at')
                // ->where('archived', 0)
                ;
            });

        // if (!$user->hasRole(['Admin', 'SuperAdmin'])) {
        //     $userTaskIds = $user->tasks()->pluck('tasks.id');
        //     $pastFinishedCount->whereIn('id', $userTaskIds);
        // }

        if (!empty($this->search)) {
            $pastFinishedCount->whereHas('project', function ($q) {
                $q->where(function ($subQ) {
                    $subQ->where('name', 'like', '%' . $this->search . '%')
                        ->orWhere('job_code', 'like', '%' . $this->search . '%');
                });
            });
        }

        if (!empty($this->alphabet)) {
            $pastFinishedCount->whereHas('project', function ($q) {
                $q->whereRaw('LEFT(UPPER(name), 1) = ?', [$this->alphabet]);
            });
        }

        if ($this->year && $this->year !== 'all') {
            $pastFinishedCount->whereHas('project', function ($q) {
                $q->whereYear('created_at', $this->year);
            });
        }

        $counts['past_finished'] = $pastFinishedCount->count();


        return $counts;
    }










        public function getProjectsByFirstLetter()
        {
            $query = $this->getTasksBaseQuery()->whereNull('tasks.deleted_at');

            // Apply search filter
            // if (!empty($this->search)) {
            //     $query->whereHas('project', function ($q) {
            //         $q->where(function ($subQ) {
            //             $subQ->where('name', 'like', '%' . $this->search . '%')
            //                 ->orWhere('job_code', 'like', '%' . $this->search . '%');
            //         })->whereNull('deleted_at');
            //     });
            // }
            //changed above code to filter recently finished tasks also when their project name/job_code searched
            if (!empty($this->search)) {
                $query->whereHas('project', function ($q) {
                    $q->where(function ($subQ) {
                        $subQ->where('name', 'like', '%' . $this->search . '%')
                            ->orWhere('job_code', 'like', '%' . $this->search . '%');
                    })->whereNull('deleted_at');
                });
            }

            // Apply year filter
            if ($this->year !== 'all') { //filter based on created_at date of task 
                // $query->whereHas('project', function ($q) {
                //     $q->whereYear('created_at', $this->year)
                //         ->whereNull('deleted_at');
                // });

                $query->whereYear('tasks.created_at', $this->year);
            }

            // Filter out "Recently Finished" tasks older than 30 days
            $recentlyFinishedStatusId = Status::where('name', 'Recently Finished')->value('id');
                // if ($recentlyFinishedStatusId) {
                //     $query->where(function ($q) use ($recentlyFinishedStatusId) {
                //         $q->where('tasks.status_id', '!=', $recentlyFinishedStatusId)
                //             ->orWhere(function ($q2) use ($recentlyFinishedStatusId) {
                //                 $q2->where('tasks.status_id', $recentlyFinishedStatusId)
                //                     ->where('tasks.updated_at', '>=', now()->subDays(30));

                //                     if ($this->selectedMonth) {
                //                         $startOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->startOfMonth();
                //                         $endOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->endOfMonth();
                        
                //                         $q2->whereBetween('tasks.created_at', [$startOfMonth, $endOfMonth]);
                //                     }
                //             });
                //     });
                // }

                // if ($this->selectedMonth) {
                //     $query->whereRaw("DATE_FORMAT(tasks.created_at, '%Y-%m') = ?", [$this->selectedMonth]);

                //     $this->applyBillingMonthFilter($query);          
                // }    
                
                if ($recentlyFinishedStatusId) {
                    $query->where(function ($q) use ($recentlyFinishedStatusId) {
                        $q->where('tasks.status_id', '!=', $recentlyFinishedStatusId)
                          ->orWhere(function ($q2) use ($recentlyFinishedStatusId) {
                              $q2->where('tasks.status_id', $recentlyFinishedStatusId)
                                  ->where('tasks.updated_at', '>=', now()->subDays(30));
                          });
                    });
                }
            
                // Filter by selected month
                if ($this->selectedMonth) {
                    $startOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->startOfMonth();
                    $endOfMonth = \Carbon\Carbon::createFromFormat('Y-m', $this->selectedMonth)->endOfMonth();
            
                    $query->where(function ($q) use ($recentlyFinishedStatusId, $startOfMonth, $endOfMonth) {
                        $q->where(function ($subQ) use ($recentlyFinishedStatusId, $startOfMonth, $endOfMonth) {
                            $subQ->where('tasks.status_id', '!=', $recentlyFinishedStatusId)
                                 ->whereBetween('tasks.created_at', [$startOfMonth, $endOfMonth]);
                        })->orWhere(function ($subQ) use ($recentlyFinishedStatusId, $startOfMonth, $endOfMonth) {
                            $subQ->where('tasks.status_id', $recentlyFinishedStatusId)
                                 ->where('tasks.updated_at', '>=', now()->subDays(30))
                                 ->whereBetween('tasks.created_at', [$startOfMonth, $endOfMonth]);
                        });
                    });
                }

            // Join with projects and get counts grouped by first letter
            $letterCounts = $query
                ->join('projects', 'tasks.project_id', '=', 'projects.id')
                ->select(DB::raw('UPPER(LEFT(projects.name, 1)) as first_letter'), DB::raw('COUNT(DISTINCT tasks.id) as count'))
                ->groupBy('first_letter')
                ->orderBy('first_letter')
                ->pluck('count', 'first_letter')
                ->toArray();

            return $letterCounts;
        }



        public function getFilteredTaskCountProperty()
        {
            return $this->allTaskCount;
        }



        public function render()
        {
            // Add a small delay to make the loading effect noticeable
            if ($this->alphabet) {
                usleep(300000); // 300ms delay
            }

            $groupedTasks = $this->groupedTasks;

            return view('livewire.task-overview', [
                'groupedTasks' => $groupedTasks,
                'statuses' => $this->statuses,
                'statusCounts' => $this->statusCounts,
                'allTaskCount' => $this->allTaskCount,
                'statusName' => $this->statusName,
                'hasUrgentTasks' => $groupedTasks['urgent']->isNotEmpty(),
                'hasMoreUrgent' => $groupedTasks['hasMoreUrgent'] ?? false,
                'hasMoreRegular' => $groupedTasks['hasMoreRegular'] ?? false,
                'letterCounts' => $this->getProjectsByFirstLetter(),
                'filteredTaskCount' => $this->filteredTaskCount,
            ]);
        }
    }
