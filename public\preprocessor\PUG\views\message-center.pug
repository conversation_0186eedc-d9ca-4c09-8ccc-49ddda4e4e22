- let pageName = 'Client Portal';
- let mainPage = 'Message Center';
- let pageTitle = `${pageName}: Track Project`;

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>[SP-003-25] Sample Project 3</i>', false, true, true, 'Back to Client Portal')

        section.client-project.bg-white
            .container-xxl
                .heading.d-flex.align-items-center.justify-content-between
                    h2.mb-0 Message #[i Center]
                    .links
                        each link, index in ['Messages', 'Project Files (23)']
                            a(href="#" class=(index === 0 ? 'active' : ''))=link

        section.actions-need.bg-white.pb-4
            .container-xxl
                .heading
                    h2.text-uppercase.text-orange.mb-1 LATEST MESSAGE
                    h3.d-flex.align-items-center.mb-2 Message from Developer
                        a.circle-icon.align-items-center.d-inline-flex.justify-content-center.lh-1.rounded-circle.text-decoration-none.ms-2(href="#") !
        section.bg-white
            .container-xxl
                .message-box.d-flex
                    .pic
                        img(src='images/drew-pic.jpg', alt='')
                    .message-wrap.d-flex.align-items-start
                        .message
                            h2 #[span.quote "]You’re six days behind schedule, buddy.#[span.quote "]
                            .copy
                                p Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam viverra dolor eget velit suscipit dapibus. Suspendisse posuere elit ut arcu facilisis semper. Nunc accumsan ut justo sed sollicitudin. Fusce vulputate, nibh eget aliquam interdum, ipsum metus tempor nisl..#[a(href='message.html') See More ]
                                .meta.d-flex
                                    a.user.me-4(href="#") Drew M.
                                    span.date 12/12/25

                .message-ctas.text-center.d-flex.justify-content-center.pt-5.pb-0
                    a.cta.d-inline-flex.align-items-center.text-decoration-none.text-uppercase.py-2.mt-4.mt-md-0(href='new-thread.html') #[i.bi.bi-envelope.me-2] POST A NEW MESSAGE
                    a.cta.d-inline-flex.align-items-center.text-decoration-none.text-uppercase.py-2.mt-4.mt-md-0(href='#') #[i.bi.bi-upload.me-2] UPLOAD FILES
                    a.cta.d-inline-flex.align-items-center.text-decoration-none.text-uppercase.py-2.mt-4.mt-md-0(href='#') SEE ALL FILES

                .messages-wrap
                    .head
                        span#moreMessages More Messages
                    .messages-list
                        .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.has-attachment.pb-4.orange
                            .col-md-auto.icons.d-flex
                                .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                                .icon.pic #[img(src='images/drew-pic.jpg', alt='')]
                            .col-md-auto.name
                                p Drew M.
                            .col-md-auto.date
                                p 12/12/25
                            .col-md-auto.notification
                                p #[a(href="message.html") You’re six days behind schedule, buddy]
                                .attachment.pt-2
                                    p #[a.text-decoration-none.text-orange(href="#") #[i.bi.bi-link-45deg] Files attached: Image.jpg, Test.pdf, Copy.docx]
                            .col-md.message.d-flex.align-items-center
                                .over-text.d-grid
                                    p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                a.ms-2(href="#") Read More
                            .col-md-auto.reply
                                a.cta.d-inline-flex.align-items-center.text-decoration-none.py-2.mt-0(href='#') #[i.bi.bi-envelope.me-2] REPLY

                        .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.orange
                            .col-md-auto.icons.d-flex
                                .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                                .icon.pic #[img(src='images/sally-pic.png', alt='')]
                            .col-md-auto.name
                                p Sally  M.
                            .col-md-auto.date
                                p 12/11/25
                            .col-md-auto.notification
                                p #[a(href="#") Just checking in]
                            .col-md.message.d-flex.align-items-center
                                .over-text.d-grid
                                    p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                a.ms-2(href="#") Read More
                            .col-md-auto.reply
                                a.cta.d-inline-flex.align-items-center.text-decoration-none.py-2.mt-0(href='#') #[i.bi.bi-envelope.me-2] REPLY

                        .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.orange
                            .col-md-auto.icons.d-flex
                                .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                                .icon.pic #[img(src='images/drew-pic.jpg', alt='')]
                            .col-md-auto.name
                                p Drew M.
                            .col-md-auto.date
                                p 12/11/25
                            .col-md-auto.notification
                                p #[a(href="#") You’re six days behind schedule, buddy]
                            .col-md.message.d-flex.align-items-center
                                .over-text.d-grid
                                    p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                a.ms-2(href="#") Read More
                            .col-md-auto.reply
                                a.cta.d-inline-flex.align-items-center.text-decoration-none.py-2.mt-0(href='#') #[i.bi.bi-envelope.me-2] REPLY

                .message-ctas.text-center.d-flex.justify-content-center.py-5
                    a.cta.d-inline-flex.align-items-center.text-decoration-none.text-uppercase.mt-4.mt-md-0(href='#') GET EMAIL UPDATES
                    a.cta.d-inline-flex.align-items-center.text-decoration-none.text-uppercase.mt-4.mt-md-0(href='#') NEED HELP? CONTACT

        include ../partials/footer.pug
        +footer()