<?php

namespace Database\Seeders;
use App\Models\Team;
use App\Models\AccessLevel;
use Illuminate\Support\Facades\DB;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        $teams = [
            [
                'name' => 'Dev Team',
                'description' => 'Development Team', 
                'access_level_id' => AccessLevel::where('name', 'Team Member')->first()->id,
            ],
            [
                'name' => 'Design Team',
                'description' => 'Design Team',
                'access_level_id' => AccessLevel::where('name', 'Team Member')->first()->id,
            ],
            [
                'name' => 'Admin',
                'description' => 'Administrator Team',
                'access_level_id' => AccessLevel::where('name', 'Admin')->first()->id,    
            ],

        ];
        foreach ($teams as $team) {
            Team::create($team);
        }
    }
}
