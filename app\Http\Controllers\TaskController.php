<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Status;
use App\Models\Task;
use App\Models\TaskAttachment;
use App\Models\User;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

use App\Notifications\TaskAssigned;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaskController extends Controller
{

    public function add_task(Request $request)
    {

        if ($request->input("fetch_project_users")) {


            $project = Project::where('id', '=', $request->input('project_id'))->first();
            if ($project) {
                $assigned_users = $project->users;
            }

            return $assigned_users;
        }

        $statuses = Status::all();

        $projects = Project::all()->sortBy(function ($project) {
            $name = $project->name;
            if (is_numeric($name[0])) {
                return '0' . $name; // Prefix with '0' to ensure numbers come first
            } else {
                return '1' . $name; // Prefix with '1' to ensure letters come after
            }
        });

        return view('task.add_task', compact('statuses', 'projects'));
    }

    public function view_task(Request $request)
    {
        $task = Task::where('id', '=', $request->route('task_id'))->with('taskAttachments')->first();
        // dd( $task );
        if ($task) {
            $task_status = Status::where('id', '=', $task->status_id)->first();
            $statuses = Status::select('id', 'name')->orderBy('id')->get();

            // return $statuses;

            $project = $task->project()->first();
            $project_client = Client::find($project->client_id);
            $users = $project->users->collect();
            return view('view-task', compact('task', 'users', 'task_status', 'statuses'));
        }
        abort(404);
    }

    // public function tasks(Request $request)
    // {   

    //     $status = $request->input('status');

    //     $user = Auth::user();
    //     $statuses = Status::all();

    //     // Base query
    //     $taskQuery = Task::query();
    //     $status_name = 'All';

    //     // Filter by status if needed
    //     if ($status && $status !== 'all') {
    //         $taskQuery->where('status_id', $status);
    //         $status_name = Status::find($status)->name;
    //     }

    //     if ($user->role->name === 'SuperAdmin') {
    //         $tasks = $taskQuery->get();

    //         // Get all projects that have the filtered tasks
    //         $projectIds = $tasks->pluck('project_id')->unique();

    //         $projects = Project::with(['status', 'tasks' => function ($query) use ($status) {
    //             if ($status && $status !== 'all') {
    //                 $query->where('status_id', $status);
    //             }
    //         }])->whereIn('id', $projectIds)->get();

    //     } else {
    //         $taskQuery->where('user_id', $user->id);
    //         $tasks = $taskQuery->get();

    //         //IMP
    //         $projectIds = $tasks->pluck('project_id')->unique();
    //         $projects = Project::with(['status', 'tasks' => function ($query) use ($status, $user) {
    //             $query->where('user_id', $user->id);
    //             if ($status && $status !== 'all') {
    //                 $query->where('status_id', $status);
    //             }
    //         }])->whereIn('id', $projectIds)->get();
    //     }
    //     $allTasks = Task::whereHas('users', function ($query) use ($user) {
    //         $query->where('user_id', $user->id);
    //     })->get();

    //     $taskCounts = $allTasks->groupBy('status_id')->map(fn($group) => $group->count());

    //     return view('admin.tasks.index', compact('statuses', 'taskCounts', 'projects', 'status_name'));
    // }


    public function tasks(Request $request)
    {
        return view('admin.tasks.index',);
    }


    public function show_project_tasks(Request $request, $project_id)
    {

        $project = Project::findOrFail($project_id);
        return view('project.tasks', compact('project'));
    }




    public function save_task(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'task_status' => 'required|string|max:255',
            'task_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('tasks', 'name')->where(function ($query) use ($request) {
                    return $query->where('project_id', $request->project_id);
                })
            ],
            'task_description' => 'required',
            'task_assigned_to' => 'required|array',
            'task_assigned_to.*' => 'exists:users,id',
            'project_id' => 'nullable|exists:projects,id', // project_id can be null if not assigned

            // Validation for multiple attachments
            'task_attachments' => 'array', // Must be an array (for multiple files)
            'task_attachments.*' => 'file|max:5120|mimes:jpeg,png,jpg,gif,pdf,doc,docx,zip,txt', // Individual file validation (max 5MB, allowed types)
        ], [
            'task_name.unique' => 'A task with this name already exists in this project.',
            'task_attachments.*.max' => 'Each attachment must not exceed 5MB.',
            'task_attachments.*.mimes' => 'Only image, PDF, document, zip, or text files are allowed for attachments.'
        ]);
        if ($validate->fails()) {
            // *** CRUCIAL CHANGE FOR AJAX: Return JSON errors ***
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors' => $validate->errors(),
            ], 422); // 422 Unprocessable Entity
        }


        DB::beginTransaction();

        try {
            $task = new Task();
            $task->name = $request->input('task_name');
            $task->description = $request->input('task_description');
            $task->status_id = $request->input('task_status');
            $task->admin_id = Auth::user()->id;
            if ($request->project_id !== 'null') {
                $task->project_id = $request->input('project_id');
            }


            $project = Project::where('id', $task->project_id)->first();

            if ($request->input('due-date')) {

                $task->due_date = $request->input('due-date');
            }

            $task->save();

            // Attach multiple users to the task
            if ($request->input('task_assigned_to')) {
                $task->users()->attach($request->input('task_assigned_to'));
            }



            $users = $request->input('task_assigned_to', []);
            $usersToAttach = is_array($users) ? $users : [(int) $users];

            foreach ($usersToAttach as $user) {
                try {
                    $user = User::find($user);
                    $user->notify(new TaskAssigned($task, $project));
                } catch (\Exception $e) {
                    Log::error("Notification failed for user ID $user->name: " . $e->getMessage());
                    // Optional: show debug error temporarily

                }
            }

            if ($request->hasFile('task_attachments')) {
                $i = 0; // Initialize incremental counter
                foreach ($request->file('task_attachments') as $file) {
                    if ($file->isValid()) {
                        $extension = $file->getClientOriginalExtension();
                        // Generate the new file name: timestamp followed by the incremental value
                        $newFileName = time() . '(' . $i . ')' . '.' . $extension;

                        // Store the file using storeAs()
                        // The directory remains 'images', but the filename changes
                        $filePath = $file->storeAs('images', $newFileName, 'public');

                        $taskAttachment = new TaskAttachment();
                        $taskAttachment->task_id = $task->id;
                        // Store the new filename in the database (e.g., '1716510400_0.jpg', '1716510400_1.png')
                        $taskAttachment->task_attachment = $newFileName;

                        $taskAttachment->save();
                        $i++; // Increment the counter for the next file
                    } else {
                        // Log a warning if any individual file is invalid (should be caught by validation, but a safeguard)
                        Log::warning("Invalid file skipped during task creation for task ID: " . $task->id . ", original name: " . ($file->getClientOriginalName() ?? 'Unknown filename'));
                    }
                }
            }

            DB::commit();

            // 6. Return Success Response for AJAX
            return response()->json([
                'message' => 'Task created successfully!',
                'task_id' => $task->id // Return the ID of the newly created task
            ], 201); // HTTP 201 Created (resource created)
        } catch (\Exception $e) {
            // --- ROLLBACK TRANSACTION ---
            // If any exception occurs in the try block, rollback all database changes
            DB::rollBack();

            // Log the detailed error for debugging
            Log::error("Failed to create task: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

            // Return a generic error response for AJAX
            return response()->json([
                'message' => 'An internal server error occurred while creating the task. Please try again later.',
                // For production, avoid exposing $e->getMessage() directly to users.
                // 'debug_error' => $e->getMessage() // Uncomment for local debugging
            ], 500); // HTTP 500 Internal Server Error
        }
    }



    public function fetchAdmins()
    {
        $admins = DB::table('users')
            ->join('roles', 'users.role_id', '=', 'roles.id')
            ->whereIn('roles.name', ['Admin', 'SuperAdmin'])
            ->select('users.id', 'users.name', 'users.email')
            ->get();




        return response()->json($admins);
    }


    public function markComplete(Request $request)
    {
        $task = Task::find($request->input('task_id'));
        $task->status_id = Status::where('name', 'Recently Finished')->first()->id;
        $task->save();

        return response()->json(['success' => true]);
    }
}
