<div>
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">

                <!--[if BLOCK]><![endif]--><?php if(admin_superadmin_permissions()): ?>
                    <a class="d-inline-flex align-items-center text-decoration-none" href="<?php echo e(route('admin-dashboard')); ?>">
                        <img class="me-2" src="<?php echo e(asset('images/back-arrow-icon.svg')); ?>" alt="" /> Back to Team Portal
                    </a>
                <?php else: ?>
                    <a class="d-inline-flex align-items-center text-decoration-none" href="<?php echo e(route('dashboard')); ?>">
                        <img class="me-2" src="<?php echo e(asset('images/back-arrow-icon.svg')); ?>" alt="" /> Back to Team Portal
                    </a>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            </div>

            <!--[if BLOCK]><![endif]--><?php if(admin_superadmin_permissions()): ?>
                <div class="meta d-flex">
                    <h1 class="heavy text-white mb-0">Tasks <i>Overview</i></h1>
                    <div class="add-task ms-auto d-flex">
                        <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0"
                            href="<?php echo e(route('add-new-task')); ?>">
                            NEW TASK
                            <span class="img ms-2">
                                <svg width="21" height="21" viewBox="0 0 21 21" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                                    <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5">
                                    </path>
                                </svg>
                            </span>
                        </a>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </section>
    <div class="search-container">
        <div class="search-wrapper">
            <i class="bi bi-search search-icon"></i>
            <input type="text" class="form-control search-input background-dark" placeholder="Search by project name or job code" wire:model.live.debounce.500ms="search" />
            <i class="bi bi-x-circle clear-icon"></i>
        </div>
    </div>
    
    <section class="client-project pt-0">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="projects-list-table">
                <div class="head d-flex align-items-center justify-content-between">
                    
                    
                    <div class="years d-flex align-items-center gap-2">
                        <label for="yearSelect" class="mb-0 text-white">Select year:</label>
                        <select id="yearSelect" onchange="window.location.href=this.value" class="form-select" style="width: auto;">
                            <option value="<?php echo e(route('admin-tasks', ['year' => null])); ?>"
                                <?php echo e(($year === null || !$year) ? 'selected' : ''); ?>>
                                All
                            </option>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $years; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $y): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e(route('admin-tasks', ['year' => $y])); ?>"
                                    <?php echo e(($year == $y) ? 'selected' : ''); ?>>
                                    <?php echo e($y); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                    </div>                   
                
                    
                    <div class="alphabet d-flex flex-wrap">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = range('A', 'Z'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $letter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $letterCount = $letterCounts[$letter] ?? 0;
                                $hasClients = $letterCount > 0;
                            ?>
                    
                            <a wire:click="<?php echo e($hasClients ? '$set(\'alphabet\', \''.$letter.'\')' : 'null'); ?>"
                               wire:loading.class="loading-blur"
                               href="javascript:void(0)"
                               class="alphabet-letter me-2 mb-2 px-2 py-1 <?php echo e($alphabet === $letter ? 'alphabet-active' : ''); ?> <?php echo e(!$hasClients ? 'disabled-letter' : ''); ?>">
                                <?php echo e($letter); ?>

                                <!--[if BLOCK]><![endif]--><?php if($letterCount > 0): ?>
                                    <span class="letter-count"><?php echo e($letterCount); ?></span>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    
                        
                        <a wire:click="$set('alphabet', null)"
                           wire:loading.class="loading-blur"
                           href="javascript:void(0)"
                           class="alphabet-letter me-2 mb-2 px-2 py-1 <?php echo e(is_null($alphabet) ? 'alphabet-active' : ''); ?>">
                            All
                        </a>
                    </div>
                    

                </div>
            </div>
        </div>
    </section>


    <section class="client-project project-dashboard">
        <div class="container-xxl">
            <div class="row projects-row">
                <!-- Status Filter Sidebar -->
                <div class="col-md-3 col-xl-2 project-column quick-links">
                    <h2 class="text-uppercase">STATUSES</h2>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="statuses-list d-flex flex-column">
                        <a class="<?php echo e($selectedStatus === 'all' ? 'active' : ''); ?>"
                            wire:click.prevent="filterByStatus('all')" href="#">
                            All (<?php echo e($filteredTaskCount); ?>)
                        </a>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a class="<?php echo e($selectedStatus == $status->id ? 'active' : ''); ?>"
                                wire:click.prevent="filterByStatus(<?php echo e($status->id); ?>)" href="#">
                                <?php echo e($status->name); ?> (<?php echo e($statusCounts[$status->id] ?? 0); ?>)
                            </a>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        
                        
                        <br>
                        <label class="text-white mt-3">See monthly tasks:</label>
                        
                        <div class="mt-2">
                            
                            <div class="d-flex justify-content-between align-items-center text-secondary" style="cursor: pointer;" wire:click="toggleBillingMonthDropdown">
                                <span class="">
                                    <?php echo e($selectedMonth && isset($billingMonths[$selectedMonth]) 
                                        ? $billingMonths[$selectedMonth]['label'] . ' (' . $billingMonths[$selectedMonth]['count'] . ')' 
                                        : 'Select Month'); ?>

                                </span>
                                <i class="bi bi-chevron-down"></i> 
                            </div>

                            
                            <!--[if BLOCK]><![endif]--><?php if($showBillingMonths): ?>
                                <div class="border rounded mt-2 p-2" style="max-height: 250px; overflow-y: auto;">
                                    <div class="py-1">
                                        <a class="text-secondary d-block text-decoration-none<?php echo e($selectedMonth === null ? 'fw-bold text-light' : ''); ?>" wire:click="selectBillingMonth(null)">
                                            Select Month
                                        </a>
                                    </div>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $billingMonths; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="py-1">
                                            <a 
                                                href="#"
                                                class="text-secondary d-block text-decoration-none <?php echo e($selectedMonth === $value ? 'fw-bold text-light' : ''); ?>"
                                                wire:click.prevent="filterByBillingMonth('<?php echo e($value); ?>')"
                                            >
                                                <?php echo e($info['label']); ?> (<?php echo e($info['count']); ?>)
                                            </a>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>                                                                                 
                    </div>                    
                </div>



                <!-- Task List -->
                <div wire:loading.class="blur-sm" wire:target="filterByStatus"
                    class="col-md-9 col-xl-10 project-column task-overview">

                    <div class="col-head align-items-center d-flex justify-content-between">
                        <h2 class="text-uppercase">TASKS OVERVIEW</h2>
                        <div class="sort d-flex align-items-center">
                            <div class="sort-by position-relative d-flex align-items-center">
                                <h3 class="mb-0">Sort By: <?php echo e($sortField === 'job_code' ? 'Job' : 'Date'); ?></h3>

                                <span class="ms-2 sort-arrow" style="cursor: pointer;" x-data="{ open: false }"
                                    @click="open = !open">
                                    <img src="<?php echo e(asset('images/down-arrow-orange.svg')); ?>" alt="Sort" />

                                    
                                    <div x-show="open" @click.away="open = false"
                                        class="dropdown-menu show position-absolute mt-2"
                                        style="min-width: 100px; background: white; border: 1px solid #ddd; z-index: 1000;">
                                        <a href="#" wire:click.prevent="sortBy('created_at')"
                                            class="dropdown-item <?php echo e($sortField === 'created_at' ? 'active' : ''); ?>">
                                            Date
                                        </a>
                                        <a href="#" wire:click.prevent="sortBy('job_code')"
                                            class="dropdown-item <?php echo e($sortField === 'job_code' ? 'active' : ''); ?>">
                                            Job
                                        </a>
                                    </div>
                                </span>
                            </div>

                            <div class="more-icon ms-3"><img src="<?php echo e(asset('images/three-dots-more.svg')); ?>"
                                    alt="" /></div>
                        </div>
                    </div>
                    <hr class="mt-0 mb-4 border-white" />

                    <?php
                        $urgentStatusId = \App\Models\Status::where('name', 'Urgent')->value('id');
                    ?>

                    <!-- Task content -->
                    <div class="task-overview-list">
                        <!-- Urgent Tasks Section -->
                        

                        <!--[if BLOCK]><![endif]--><?php if((int) $selectedStatus === (int) $urgentStatusId || $selectedStatus=='all'): ?>                            
                            <div class="head-sort mb-4">
                                <h2 class="text-white text-capitalize">Urgent</h2>
                            </div>
                                <!--[if BLOCK]><![endif]--><?php if($hasUrgentTasks): ?>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $groupedTasks['urgent']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="task-project d-flex purple">
                                        <div class="star-check">
                                            <div class="icon-star d-flex me-3">
                                                <img src="<?php echo e(asset('images/star-icon.svg')); ?>" alt=""
                                                    height="20" width="20" />
                                            </div>
                                            
                                        </div>
                                        <div class="copy d-flex flex-column flex-grow-1">
                                            <h2 class="text-uppercase">
                                                <?php echo e($task->project->job_code); ?> <?php echo e($task->project->name); ?> -
                                                <!--[if BLOCK]><![endif]--><?php if($selectedStatus !== 'past_finished'): ?>
                                                    <?php echo e($task->status->name); ?>

                                                <?php else: ?>
                                                    PAST FINISHED
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </h2>

                                            <div class="detail d-flex align-items-center w-100" style="height:20px">
                                                <div class="date-cta py-1 px-2 rounded-1 d-flex align-items-center me-3">
                                                    <i class="bi bi-clock me-1"></i>
                                                    <?php echo e($task->due_date ? \Carbon\Carbon::parse($task->due_date)->format('M j') : '—'); ?>

                                                </div>
                                                <div class="task me-3">
                                                    <a href="<?php echo e(route('view-task', ['task_id' => $task->id])); ?>">
                                                        <?php echo e($task->name); ?>

                                                    </a>
                                                </div>
                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                    <div class="text over-text d-grid">
                                                        <p class="text-truncate"><?php echo e(strip_tags(html_entity_decode($task->description))); ?></p>
                                                    </div>
                                                    <a class="read-more text-decoration-none ms-3"
                                                        href="<?php echo e(route('view-task', ['task_id' => $task->id])); ?>">
                                                        Read More
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                <!--[if BLOCK]><![endif]--><?php if($hasMoreUrgent): ?>
                                    <div class="text-center my-3">
                                        <button wire:click="loadMoreUrgent" class="btn btn-outline-light">See More Urgent Tasks</button>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php else: ?>
                                    <div class="text-center text-dark alert alert-light p-1 mb-5">No results found in "Urgent" status !!</div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        

                        <?php
                            if($selectedStatus!='all') //selectedStatus=all is not found in the database so currrentStatusName will be "All" just for printing when no results found
                                if ($selectedStatus === 'archived') {
                                    $currentStatusName = "Archived";
                                }
                                else if ($selectedStatus === 'past_finished') {
                                    $currentStatusName = "Past Finished";
                                }
                                else {
                                    $currentStatusName = \App\Models\Status::where('id', '=', $selectedStatus)->value('name');
                                }
                            else
                                    $currentStatusName = "All"
                        ?>

                        <!-- Regular Tasks Section -->
                        <!--[if BLOCK]><![endif]--><?php if($groupedTasks['regular']->isEmpty() && $groupedTasks['urgent']->isEmpty()): ?>
                            <div class="empty-state text-center py-5 bg-dark bg-opacity-50 rounded-4 shadow-sm">
                                <div class="d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-file-earmark-x fa-3x mb-3 text-warning"
                                        style="color: #ff4c00 !important;"></i>
                                    <div class="text-center text-dark alert alert-light p-1 mb-5">No results found in "<span class="text-capitalize"><?php echo e($currentStatusName); ?></span>" status !!</div>
                                    <p class="text-light mb-0">Try changing the filters or create a new task to get started.</p>
                                </div>
                            </div>
                        <?php else: ?>
                            
                            <?php if((int) $selectedStatus !== (int) $urgentStatusId): ?>
                                <!--[if BLOCK]><![endif]--><?php if($selectedStatus === 'all'): ?>
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize">All</h2>
                                    </div>
                                <?php elseif($hasUrgentTasks): ?>
                                    <!--  don't re-mention urgent tasks in the view file as urgent tasks are mentioned previously in the starting of this view file -->
                                <?php elseif($selectedStatus === 'archived'): ?> 
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize">Archived</h2>
                                    </div>
                                <?php elseif($selectedStatus === 'past_finished'): ?> 
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize">Past Finished</h2>
                                    </div>
                                <?php else: ?>
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize"><?php echo e($statusName); ?></h2>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            
                                <!--[if BLOCK]><![endif]--><?php if($sortField!='job_code'): ?>
                                    <!--[if BLOCK]><![endif]--><?php if(!empty($groupedTasks['regular'])): ?>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $groupedTasks['regular']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $projectName => $tasks): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <!--[if BLOCK]><![endif]--><?php if($tasks->isNotEmpty()): ?>
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="task-project d-flex purple">
                                                        
                                                        <div class="copy d-flex flex-column flex-grow-1">
                                                            <h2 class="text-uppercase">
                                                                 <?php echo e($task->project->job_code); ?>  <?php echo e($task->project->name); ?> -
                                                                <!--[if BLOCK]><![endif]--><?php if($selectedStatus !== 'past_finished'): ?>
                                                                    <?php echo e($task->status->name); ?>

                                                                <?php else: ?>
                                                                    PAST FINISHED
                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                            </h2>

                                                            <div class="detail d-flex align-items-center w-100" style="height:20px">
                                                                <div class="date me-3">
                                                                    <?php echo e($task->due_date ? \Carbon\Carbon::parse($task->due_date)->format('M j') : '—'); ?>

                                                                </div>
                                                                <div class="task me-3">
                                                                    <a href="<?php echo e(route('view-task', ['task_id' => $task->id])); ?>">
                                                                        <?php echo e($task->name); ?>

                                                                    </a>
                                                                </div>
                                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                                    <div class="text over-text d-grid">
                                                                        <p class="text-truncate"><?php echo e(strip_tags(html_entity_decode($task->description))); ?></p>
                                                                    </div>
                                                                    <a class="read-more text-decoration-none ms-3"
                                                                        href="<?php echo e(route('view-task', ['task_id' => $task->id])); ?>">
                                                                        Read More
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                            <?php else: ?>
                                                <div class="text-center text-dark alert alert-light p-1 mb-5">
                                                    No results found in "<span class="text-capitalize"><?php echo e($currentStatusName); ?></span>" status !!
                                                </div>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if($hasMoreRegular): ?>
                                            <div class="text-center my-3">
                                                <button wire:click="loadMoreRegular" class="btn btn-outline-light">See More Tasks</button>
                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php else: ?>
                                        <div class="text-center text-dark alert alert-light p-1 mb-5">
                                            No results found in "<span class="text-capitalize"><?php echo e($currentStatusName); ?></span>" status !!
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php elseif($sortField=='job_code'): ?>
                                    <!--[if BLOCK]><![endif]--><?php if($groupedTasks['regular']->isNotEmpty() && $groupedTasks['regular']->has('Tasks') && $groupedTasks['regular']['Tasks']->isNotEmpty()): ?>
                                        <?php
                                            $tasksGroupedByJobCode = $groupedTasks['regular']['Tasks']->groupBy(fn($task) => $task->project->job_code ?? 'Unknown');
                                        ?>

                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $tasksGroupedByJobCode; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $jobCode => $tasks): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="head-sort">
                                                <h2 class="text-light text-capitalize my-3"><?php echo e($jobCode); ?> <?php echo e($tasks->first()->project->name ?? 'No Name'); ?></h2>
                                            </div>

                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="task-project d-flex purple ps-5">
                                                    <div class="copy d-flex flex-column flex-grow-1">
                                                        <h2 class="text-uppercase">
                                                            <?php echo e($task->project->job_code); ?> <?php echo e($task->project->name); ?> -
                                                            <!--[if BLOCK]><![endif]--><?php if($selectedStatus !== 'past_finished'): ?>
                                                                <?php echo e($task->status->name); ?>

                                                            <?php else: ?>
                                                                PAST FINISHED
                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        </h2>

                                                        <div class="detail d-flex align-items-center w-100" style="height:20px">
                                                            <div class="date me-3">
                                                                <?php echo e($task->due_date ? \Carbon\Carbon::parse($task->due_date)->format('M j') : '—'); ?>

                                                            </div>
                                                            <div class="task me-3">
                                                                <a href="<?php echo e(route('view-task', ['task_id' => $task->id])); ?>">
                                                                    <?php echo e($task->name); ?>

                                                                </a>
                                                            </div>
                                                            <div class="text d-flex flex-grow-1 align-items-center">
                                                                <div class="text over-text d-grid">
                                                                    <p class="text-truncate"><?php echo e(strip_tags(html_entity_decode($task->description))); ?></p>
                                                                </div>
                                                                <a class="read-more text-decoration-none ms-3"
                                                                    href="<?php echo e(route('view-task', ['task_id' => $task->id])); ?>">
                                                                    Read More
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                        <!--[if BLOCK]><![endif]--><?php if($hasMoreRegular): ?>
                                            <div class="text-center my-3">
                                                <button wire:click="loadMoreRegular" class="btn btn-outline-light">See More Tasks</button>
                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php else: ?>
                                        <div class="text-center text-dark alert alert-light p-1 mb-5">
                                            No results found in "<span class="text-capitalize"><?php echo e($currentStatusName); ?></span>" status !!
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Loading Spinner -->
                        <div wire:loading wire:target="loadMoreUrgent,loadMoreRegular" class="text-center my-3">
                            <div class="spinner-border text-light" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->startPush('styles'); ?>
    <style>
        .star-check{
            width:3.5%
        }
        .copy{
            width:96.5%
        }
    </style>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\SGF-Portal\resources\views/livewire/task-overview.blade.php ENDPATH**/ ?>