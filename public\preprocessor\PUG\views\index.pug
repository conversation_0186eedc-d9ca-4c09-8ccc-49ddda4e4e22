- let pageName = 'Client Portal';
- let mainPage = 'Home';
- let clientPage = 'Brand';
- let pageTitle = `${pageName}: ${clientPage}`;

include ../layouts/svgIcon.pug
include ../layouts/projectBoxMeta.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Welcome to your brand hub,<br> <i>Megryco</i>', true, false, false, false, true)


        section.client-project.pt-0
            .container-xxl
                hr.mt-0.mb-4.border-white
                .project-row.overflow-x-auto.d-flex
                    .project-column.mb-5
                        h2.text-uppercase BEAR TIDE
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('orange', 'define', '[BT-003-25] - PHASE 1/5', 'Bear Tide Sample Project', false, 1)
                            +projectBoxMeta('green', 'content', '[BT-006-25] - PHASE 2/5', 'Bear Tide Sample Project', false, 2)
                            +projectBoxMeta('pink', 'design', '[BT-003-25] - PHASE 3/5', 'Bear Tide Sample Project', false, 3)
                            +projectBoxMeta('pink', 'design', '[BT-003-25] - PHASE 3/5', 'Bear Tide Sample Project', false, 3)
                            +projectBoxMeta('orange', 'publishing', '[BT-003-25] - PHASE 5/5 - PUBLISHING', 'Bear Tide Sample Project', false, 5)
                    .project-column.mb-5
                        h2.text-uppercase DAVIS MANOR
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('green', 'content', '[DM-003-25] - PHASE 2/5', 'Davis Manor Sample Project', false, 2)
                            +projectBoxMeta('pink', 'design', '[DM-004-25] - PHASE 3/5', 'Davis Manor Sample Project', false, 3)
                            +projectBoxMeta('pink', 'design', '[DM-004-25] - PHASE 3/5', 'Davis Manor Sample Project', false, 3)
                    .project-column.mb-5
                        h2.text-uppercase MEGRYCO
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('green', 'content', '[MR-003-25] - PHASE 2/5', 'MEGRYCO Sample Project', false, 2)
                    .project-column.mb-5
                        h2.text-uppercase PLIMOTH GENERAL STORE
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('pink', 'design', '[PG-004-25] - PHASE 3/5', 'Plimouth General Store', false, 3)
                            +projectBoxMeta('purple', 'code', '[PG-003-25] - PHASE 4/5', 'Plimouth General Store', false, 4)
                            +projectBoxMeta('orange', 'print', '[PG-004-25] - PHASE 5/5 - PRINTING', 'Plimouth General Store', false, 5)
                            +projectBoxMeta('orange', 'deploy', '[PG-003-25] - PHASE 5/5 - DEPLOYING', 'Plimouth General Store', false, 5)
                    .project-column.mb-5
                        h2.text-uppercase SALTASH
                        hr.mt-0.mb-4.border-white
                        .project-list
                            +projectBoxMeta('pink', 'design', '[SA-003-25] - PHASE 3/5', 'Saltash Sample Project', false, 3)
                            +projectBoxMeta('orange', 'define', '[SA-004-25] - PHASE 1/5', 'Saltash Sample Project', false, 1)

        include ../partials/footer.pug
        +footer()