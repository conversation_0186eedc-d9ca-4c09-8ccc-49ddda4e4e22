@extends('admin.layouts.app')
@section('title', 'Dashboard')
@section('content')


<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="meta">
            <h1 class="heavy text-white mb-0">Happy <i>@php echo date('l'); @endphp</i>, @php echo auth()->user()->name
                @endphp</h1>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl">
        <div class="row projects-row">
            <div class="col-md-3 col-xl-2 project-column quick-links">
                <h2 class="text-uppercase">Quick LINKS</h2>
                <hr class="mt-0 mb-4 border-white" />
                <div class="quick-links-list">
                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('admin-clients') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/status-hub-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Clients List</p>
                        </div>
                    </a>
                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('admin-brands') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/status-hub-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Enterprises List</p>
                        </div>
                    </a>
                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('admin-projects') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/project-list-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Project List</p>
                        </div>
                    </a>

                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('admin-projects-list-view') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/project-list-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Project List View</p>
                        </div>
                    </a>
                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('admin-tasks') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/tasks-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Tasks List</p>
                        </div>
                    </a>
                    <div class="more-links box d-flex align-items-center">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/add-new-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>New...</p>
                        </div>
                        <div class="drop-list">
                            <a class="box d-flex align-items-center text-decoration-none" href="{{ route('add-project') }}">
                                <div class="icon d-flex align-items-center justify-content-center"><img
                                        src="{{ asset('images/new-project-icon.svg') }}" alt="" /></div>
                                <div class="text">
                                    <p>New Project</p>
                                </div>
                            </a><a class="box d-flex align-items-center text-decoration-none" href="{{ route('add-client') }}">
                             <div class="icon d-flex align-items-center justify-content-center"><img
                                        src="{{ asset('images/new-client-icon.svg') }}" alt="" /></div>
                                <div class="text">
                                    <p>New Client</p>
                                </div>
                            </a><a class="box d-flex align-items-center text-decoration-none" href="{{ route('add-brand') }}">
                                <div class="icon d-flex align-items-center justify-content-center"><img
                                        src="{{ asset('images/new-brand-icon.svg') }}" alt="" /></div>
                                <div class="text">
                                    <p>New Enterprise</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                <hr class="border-white" />
                <div class="quick-links-list">
                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('teams') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/edit-teams-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Team Members</p>
                        </div>
                    </a>
                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('statushub') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/status-hub-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Status Hub</p>
                        </div>
                    </a>
                    <a class="box d-flex align-items-center text-decoration-none" href="https://shieldssgfinc.harvestapp.com/" target="_blank">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/harvest-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Harvest</p>
                        </div>
                    </a>
                    <a class="box d-flex align-items-center text-decoration-none" href="{{ route('admin-calender') }}">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/pto-calendar-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>PTO Calendar</p>
                        </div>
                    </a><a class="box d-flex align-items-center text-decoration-none" href="#">
                        <div class="icon d-flex align-items-center justify-content-center"><img
                                src="{{ asset('images/hub-settings-icon.svg') }}" alt="" /></div>
                        <div class="text">
                            <p>Hub Settings</p>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-md-6 col-xl-7 project-column task-overview">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">TASKS OVERVIEW</h2>
                    <a class="cta link border-0 p-0 mt-0 mb-2" href="{{ route('admin-tasks') }}">See All</a>
                </div>
                <hr class="mt-0 mb-4 border-white" />

                <div class="task-overview-list">
                    <!-- Urgent Tasks -->
                    @php
                        $visibleTaskCount = 5; // Number of tasks to show initially
                        $totalTasks = count($groupedTasks['urgent']) + count($groupedTasks['regular']);
                        $displayedCount = 0;
                    @endphp
                
                    @foreach($groupedTasks['urgent'] as $task)
                        @php
                            $project = $task->project;
                            $statusName = strtolower($task->status->name ?? '');
                        @endphp
                        @if($statusName !== 'recently finished')
                            @if($displayedCount < $visibleTaskCount)
                                <div class="task-project d-flex purple">
                                    <div class="icon d-flex align-items-center justify-content-center">
                                        <img src="{{ asset('images/star-icon.svg') }}" alt="" width="20" height="20" />
                                    </div>
                                    <div class="copy d-flex flex-column flex-grow-1">
                                        <h2 class="text-uppercase">{{ $project->job_code }} {{ $project->name }} - URGENT</h2>
                                        <div class="detail d-flex align-items-center w-100">
                                            <div class="date me-3">{{ \Carbon\Carbon::parse($task->due_date)->format('d/m/y') }}</div>
                                            <div class="task me-3"><a href="{{ route('view-task', $task->id) }}">{{ $task->name }}</a></div>
                                            <div class="text d-flex flex-grow-1 align-items-center">
                                                <div class="text over-text d-grid">
                                                    <p class="text-truncate"> {{ strip_tags(html_entity_decode($task->description)) }}</p>
                                                </div>
                                                <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', $task->id) }}">Read More</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @php $displayedCount++; @endphp
                            @endif
                        @endif
                    @endforeach
                
                    <!-- Regular Tasks -->
                    @foreach($groupedTasks['regular'] as $task)
                        @php
                            $project = $task->project;
                            $statusName = strtolower($task->status->name ?? '');
                        @endphp
                        @if($statusName !== 'recently finished')
                            @if($displayedCount < $visibleTaskCount)
                                <div class="task-project d-flex purple">
                                    <div class="icon d-flex align-items-center justify-content-center">
                                        <img src="{{ asset('images/alert-icon.svg') }}" alt="" width="5" height="4" />
                                    </div>
                                    <div class="copy d-flex flex-column flex-grow-1">
                                        <h2 class="text-uppercase">{{ $project->job_code }} {{ $project->name }}</h2>
                                        <div class="detail d-flex align-items-center w-100">
                                            <div class="date me-3">{{ \Carbon\Carbon::parse($task->created_at)->format('d/m/y') }}</div>
                                            <div class="task me-3"><a href="{{ route('view-task', $task->id) }}">{{ $task->name }}</a></div>
                                            <div class="text d-flex flex-grow-1 align-items-center">
                                                <div class="text over-text d-grid">
                                                    <p class="text-truncate"> {{ strip_tags(html_entity_decode($task->description)) }}</p>
                                                </div>
                                                <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', $task->id) }}">Read More</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @php $displayedCount++; @endphp
                            @endif
                        @endif
                    @endforeach
                
                    <!-- More Tasks button with remaining count -->
                    @if($totalTasks > $visibleTaskCount)
                        @php
                            $remainingTasks = $totalTasks - $visibleTaskCount;
                        @endphp
                        <div class="more-tasks mt-5 text-center">
                            <a class="text-decoration-none" href="{{ route('admin-tasks') }}"> {{ $remainingTasks }} MORE  +</a>
                        </div>
                    @endif
                </div>
            </div>
                

             













           







{{-- 
                <div class="task-overview-list">
                    @foreach( $projects as $project )
                        @if( $project->tasks->count() > 0 ) 
                            
                        <div class="task-project d-flex purple">
                            <div class="icon d-flex align-items-center justify-content-center">
                                <img src="{{ asset('images/star-icon.svg') }}" alt="" width="20" height="20" />
                            </div>
                            <div class="copy d-flex flex-column flex-grow-1">
                                <h2 class="text-uppercase">{{ $project->job_code }} {{ $project->name }} - {{ $project->status->name }}</h2>
                                @foreach( $project->tasks as $task )

                                  @if($task->status->name !== 'Recently Finished')
                                    <div class="detail my-1 d-flex align-items-center w-100">
                                        <div class="date me-3">{{ $task->created_at->format('m/d/Y') }}</div>
                                        <div class="task me-3">
                                            <a href="{{ route('view-task', ['task_id'=>$task->id]) }}">{{ $task->name }}</a>
                                        </div>
                                        <div class="text d-flex flex-grow-1 align-items-center">
                                            <div class="text over-text d-grid">
                                                <p class="text-truncate">{{ $task->description }}</p>
                                            </div>
                                            <a class="read-more text-decoration-none ms-3" href="{{ route('view-task', ['task_id' => $task->id]) }}">Read More</a>
                                        </div>
                                    </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                        @endif
                    @endforeach

                    @if( count( $projects ) > 5 )
                    <div class="more-tasks mt-5 text-center"><a class="text-decoration-none" href="#">{{ count($projects) - 5 }} MORE +</a>
                    </div>
                    @endif
                </div> --}}






           
            <div class="col-md-3 project-column task-overview">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">YOUR PROJECTS</h2>
                    <a class="cta link border-0 p-0 mt-0 mb-2" href="{{ route('admin-projects') }}">See All</a>
                </div>
                <hr class="mt-0 mb-4 border-white" />
                <div class="project-list">

                    @foreach($projects->slice(0, 5)  as $project)
                        <div class="meta-project bor d-flex green">
                            <div class="icon-wrap d-flex align-items-center justify-content-center me-3">
                                <div class="progress-bar" data-steps="40" style="--value: 40"></div>
                                <div class="icon d-flex align-items-center justify-content-center"><img
                                        src="{{asset('images/content-project-icon.svg')}}" alt="" /></div>
                                <div class="over">
                                    <h2 class="mb-0">40%</h2>
                                </div>
                            </div>
                            <div class="copy">
                                <h2 class="mb-1"><a href="{{ route('edit-project', [ 'project_id' => $project->id ] ) }}">[ {{ $project->job_code }} ]</a></h2>
                                <p class="text-white">{{ $project->name }}</p>
                            </div>
                        </div>
                    @endforeach
                </div>
                @if( count( $projects ) > 5 )
                <div class="more text-center text-center mt-2"><a class="text-decoration-none" href="{{ route('admin-projects') }}">{{ count($projects) - 5 }} MORE</a></div>
                @endif
            </div>
        </div>
    </div>
</section>

{{-- <section class="messages-wrap">
    <div class="container-xxl">
        <div class="head">
            <h2>LATEST MESSAGES</h2>
        </div>
        <hr class="mt-0 mb-4 border-white" />
        <div class="messages-list">
            <div class="message-row row flex-nowrap align-items-center gx-md-3 mt-4 green">
                <div class="col-md-auto icons d-flex">
                    <div class="icon icon-envelop d-flex align-items-center justify-content-center"><i
                            class="bi bi-envelope-fill"></i></div>
                    <div class="icon pic"><img src="images/drew-pic.jpg" alt="" /></div>
                </div>
                <div class="col-md">
                    <div class="head text-uppercase">
                        <h2>SP-003-25 Sample project 3 - MEssage</h2>
                    </div>
                    <div class="row">
                        <div class="col-md-auto name">
                            <p>Drew M.</p>
                        </div>
                        <div class="col-md-auto date">
                            <p>12/12/25</p>
                        </div>
                        <div class="col-md-auto notification">
                            <p><a href="#">You’re six days behind schedule, buddy</a></p>
                        </div>
                        <div class="col-md message d-flex align-items-center">
                            <div class="over-text d-grid">
                                <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit
                                    unde molestiae facere magni est nobis labore pariatur ullam.</p>
                            </div>
                            <a class="ms-3" href="#">Read More</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="message-row row flex-nowrap align-items-center gx-md-3 mt-4 green">
                <div class="col-md-auto icons d-flex">
                    <div class="icon icon-alert d-flex align-items-center justify-content-center"><img
                            src="images/alert-white-logo.svg" alt="" /></div>
                </div>
                <div class="col-md">
                    <div class="head text-uppercase">
                        <h2>SP-003-25 Sample project 3 - Missing CONTENT</h2>
                    </div>
                    <div class="row">
                        <div class="col-md-auto name">
                            <p>Sally M.</p>
                        </div>
                        <div class="col-md-auto date">
                            <p>12/11/25</p>
                        </div>
                        <div class="col-md-auto notification">
                            <p><a href="#">Just checking in</a></p>
                        </div>
                        <div class="col-md message d-flex align-items-center">
                            <div class="over-text d-grid">
                                <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit
                                    unde molestiae facere magni est nobis labore pariatur ullam.</p>
                            </div>
                            <a class="ms-3" href="#">Read More</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="message-row row flex-nowrap align-items-center gx-md-3 mt-4 green">
                <div class="col-md-auto icons d-flex">
                    <div class="icon icon-alert d-flex align-items-center justify-content-center"><img
                            src="images/alert-white-logo.svg" alt="" /></div>
                </div>
                <div class="col-md">
                    <div class="head text-uppercase">
                        <h2>SP-003-25 Sample project 3 - NEED APPROVAL</h2>
                    </div>
                    <div class="row">
                        <div class="col-md-auto name">
                            <p>Drew M.</p>
                        </div>
                        <div class="col-md-auto date">
                            <p>12/11/25</p>
                        </div>
                        <div class="col-md-auto notification">
                            <p><a href="#">You’re six days behind schedule, buddy</a></p>
                        </div>
                        <div class="col-md message d-flex align-items-center">
                            <div class="over-text d-grid">
                                <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit
                                    unde molestiae facere magni est nobis labore pariatur ullam.</p>
                            </div>
                            <a class="ms-3" href="#">Read More</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="message-row row flex-nowrap align-items-center gx-md-3 mt-4 purple">
                <div class="col-md-auto icons d-flex">
                    <div class="icon icon-envelop d-flex align-items-center justify-content-center"><i
                            class="bi bi-envelope-fill"></i></div>
                    <div class="icon pic"><img src="images/drew-pic.jpg" alt="" /></div>
                </div>
                <div class="col-md">
                    <div class="head text-uppercase">
                        <h2>SP-002-25 Sample project 2</h2>
                    </div>
                    <div class="row">
                        <div class="col-md-auto name">
                            <p>Drew M.</p>
                        </div>
                        <div class="col-md-auto date">
                            <p>12/11/25</p>
                        </div>
                        <div class="col-md-auto notification">
                            <p><a href="#">You’re six days behind schedule, buddy</a></p>
                        </div>
                        <div class="col-md message d-flex align-items-center">
                            <div class="over-text d-grid">
                                <p>Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit
                                    unde molestiae facere magni est nobis labore pariatur ullam.</p>
                            </div>
                            <a class="ms-3" href="#">Read More</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <hr class="mt-4 mb-0 border-white" />
    </div>
</section> --}}



@endsection