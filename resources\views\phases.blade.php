@extends('layout.app')
@section('title', 'loginorg')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">

                    <h4>
                        @if(request()->route()->named('phases'))
                            Phases Information
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('phases'))
                        @if(Auth::user()->role_id=='1')
                            <div class="add-project-btn-div">
                                <a href="{{ route('add-phase') }}"><button>Add Phase <i class="fa fa-plus"></i></button></a>
                            </div>
                        @endif

                        @if(isset($no_phase_found_error) && $no_phase_found_error!='')
                            <div class="no_phase_found_error">{{ $no_phase_found_error }}</div>
                        @else
                            <div class="content-body-content">
                                <table class="table table-responsive">
                                    <thead>
                                        <tr>
                                            <td>Id</td>
                                            <td>Name</td>
                                            <td>order</td>
                                            <td>Duration</td>
                                            <td>Description</td>
                                            <td>Created At</td>
                                            <td>Updated At</td>
                                            <td>Actions</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(isset($phases) && !empty($phases))
                                            @foreach($phases as $phase)
                                                <tr>
                                                    <td class="project-id-col">
                                                        {{ $phase->id }}
                                                    </td>
                                                    <td class="project-name-col">
                                                        {{ $phase->name }}
                                                    </td>
                                                    <td class="project-status-col">
                                                        {{ $phase->order }}
                                                    </td>
                                                    <td class="project-status-col">
                                                        {{ $phase->duration }}
                                                    </td>
                                                    <td class="project-status-col">
                                                        {{ $phase->description }}
                                                    </td>
                                                    <td class="project-created_at-col">
                                                        {{ $phase->created_at }}
                                                    </td>
                                                    <td class="project-updated_at-col">
                                                        {{ $phase->updated_at }}
                                                    </td>
                                                    <td class="actions-col">
                                                        <form method="post" action="{{ route('edit-phase', ['phase_id' => $phase->id]) }}" class="edit-project-form d-inline" name="edit-project-form">
                                                            @csrf
                                                            <a href="" class="p-1" onclick="event.preventDefault(); this.closest('form').submit()"><i class='fa fa-edit'></i></a>
                                                        </form>
                                                        <form method="post" action="{{ route('delete-phase', ['phase_id' => $phase->id]) }}" class="delete-project-form d-inline" name="delete-project-form">
                                                            @csrf
                                                            @method("delete")
                                                            <a href="" class="p-1" onclick="event.preventDefault(); this.closest('form').submit()"><i class='fa fa-trash'></i></a>
                                                        </form>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>No Projects Found</tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }

        .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;
            overflow:scroll;

            left: 0;
            z-index: 0;
            height: calc(100vh - 50px);
            overflow:scroll;
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:95%;
            padding:10px;
            /* overflow:scroll; */
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        /* .content-body{
        } */
        .add-project-btn-div{
            display: flex;
            justify-content: end;
        }
        .no_projects_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
        .content-body-content{
            overflow:scroll;
            margin-top:5px;
            height: calc(100vh - 145px);
        }
        .table{
            border:1px solid grey;
        }
        thead{
            font-weight:bold;
        }
        .project-id-col{
            width:3%;
        }
        .project-name-col{
            width:10%;
        }
        .actions-col{
            width:3%;
            overflow:scroll;
        }
    </style>
@endpush

@push('style')
    <script>
        $(document).ready(function(){

        });
    </script>
@endpush