<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    public function permissions(){
        return $this->belongsToMany(Permission::class, 'role_permissions', 'role_id', 'permission_id')->withTimestamps();
    }

    public function users(){
        return $this->hasMany(User::class);
    }
    
    public function hasPermission($permission){
        return $this->permissions()->where('name', $permission)->exists();
    }
}
