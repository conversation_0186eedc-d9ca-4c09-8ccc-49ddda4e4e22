<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Website Performance Analytics Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        :root {
            --primary: #4361ee;
            --secondary: #3f37c9;
            --success: #4cc9f0;
            --info: #4895ef;
            --warning: #f72585;
            --danger: #e63946;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background-color: #f9fafb;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .dashboard {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 30px;
            position: relative;
        }
        
        .header::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, var(--warning), var(--info));
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .project-details {
            display: flex;
            justify-content: space-between;
            padding: 20px 30px;
            background-color: var(--light);
            border-bottom: 1px solid #edf2f7;
        }
        
        .detail-group {
            padding: 10px 15px;
        }
        
        .detail-label {
            font-weight: 600;
            color: var(--gray);
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-size: 18px;
            font-weight: 500;
            color: var(--dark);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 30px;
        }
        
        .metric-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #edf2f7;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .metric-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--gray);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .metric-title i {
            margin-right: 8px;
            color: var(--primary);
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--dark);
        }
        
        .metric-description {
            margin-top: 10px;
            font-size: 13px;
            color: var(--gray);
        }
        
        .full-width {
            grid-column: 1 / -1;
        }
        
        .section-title {
            margin: 10px 30px;
            padding-bottom: 10px;
            font-size: 20px;
            font-weight: 600;
            color: var(--dark);
            border-bottom: 2px solid #edf2f7;
        }
        
        .table-responsive {
            padding: 0 30px 30px;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        table th, table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #edf2f7;
        }
        
        table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: var(--gray);
            text-transform: uppercase;
            font-size: 13px;
            letter-spacing: 0.5px;
        }
        
        table tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
            color: white;
        }
        
        .badge-success {
            background-color: var(--success);
        }
        
        .badge-warning {
            background-color: var(--warning);
        }
        
        .badge-danger {
            background-color: var(--danger);
        }
        
        .text-box {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin: 0 30px 30px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid #edf2f7;
            margin-bottom: 15px;
        }
        
        .text-box h3 {
            font-size: 18px;
            color: var(--dark);
            font-weight: 600;
        }
        
        .text-box p {
            font-size: 15px;
            color: var(--dark);
            font-weight: 100;
        }
        
        .progress-container {
            margin-top: 10px;
            background-color: #e9ecef;
            border-radius: 4px;
            height: 8px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(to right, var(--info), var(--primary));
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: var(--gray);
            font-size: 12px;
            border-top: 1px solid #edf2f7;
        }
        
        /* Responsive adjustments */
        @media (max-width: 992px) {
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .project-details {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="dashboard">
            <div class="header">
                <h1>Website Performance Analytics</h1>
                <p>Comprehensive metrics analysis for {{ $project->name }} - {{ $metric->essentialMonth->name ?? date('F Y') }}</p>
            </div>
            
            <div class="d-flex justify-content-between">
                <div class="project-details col-8">
                    <div class="detail-group">
                        <div class="detail-label">Project</div>
                        <div class="detail-value">{{ $project->name }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Month</div>
                        <div class="detail-value">{{ $metric->essentialMonth->name ?? date('F Y') }}</div>
                    </div>
                    <div class="detail-group">
                        <div class="detail-label">Report Generated</div>
                        <div class="detail-value">{{ date('M d, Y H:i') }}</div>
                    </div>
                </div>
                <div class="project-details col-4">
                    <div class="detail-group">
                        <img class="white" src="{{public_path('images/logo.svg')}}" alt="logo image" height="64" width="140" />
                    </div>
                </div>
            </div>
            
            @if (in_array('performance', $content_to_print) || in_array('accessibility', $content_to_print) || in_array('best_practice', $content_to_print) || in_array('seo', $content_to_print) || in_array('site_health', $content_to_print) || in_array('engaged_traffic_rate', $content_to_print)) 
            <h2 class="section-title">Core Web Vitals</h2>
            <div class="metrics-grid">
                @if (in_array('performance', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>🚀</i> Performance
                        </div>
                        <div class="metric-value">{{ $metric->performance ?? 'N/A' }}</div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {{ $metric->performance ?? 0 }}%"></div>
                        </div>
                        <div class="metric-description">
                            Overall performance score based on loading, interactivity, and visual stability metrics.
                        </div>
                    </div>
                @endif
                
                @if (in_array('accessibility', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>♿</i> Accessibility
                        </div>
                        <div class="metric-value">{{ $metric->accessibility ?? 'N/A' }}</div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {{ $metric->accessibility ?? 0 }}%"></div>
                        </div>
                        <div class="metric-description">
                            How accessible your website is to all users, including those with disabilities.
                        </div>
                    </div>
                @endif
                
                @if (in_array('best_practice', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>✓</i> Best Practices
                        </div>
                        <div class="metric-value">{{ $metric->best_practice ?? 'N/A' }}</div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {{ $metric->best_practice ?? 0 }}%"></div>
                        </div>
                        <div class="metric-description">
                            Adherence to web development best practices for security and quality.
                        </div>
                    </div>
                @endif
                
                @if (in_array('seo', $content_to_print))        
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>🔍</i> SEO
                        </div>
                        <div class="metric-value">{{ $metric->seo ?? 'N/A' }}</div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {{ $metric->seo ?? 0 }}%"></div>
                        </div>
                        <div class="metric-description">
                            Search engine optimization score measuring how well your site can be discovered.
                        </div>
                    </div>
                @endif
                
                @if (in_array('site_health', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>💓</i> Site Health
                        </div>
                        <div class="metric-value">{{ $metric->site_health ?? 'N/A' }}</div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {{ $metric->site_health ?? 0 }}%"></div>
                        </div>
                        <div class="metric-description">
                            Overall health score based on security, performance, and maintenance factors.
                        </div>
                    </div>
                @endif
                
                @if (in_array('engaged_traffic_rate', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>👥</i> Engaged Traffic Rate
                        </div>
                        <div class="metric-value">{{ $metric->engaged_traffic_rate ?? 'N/A' }}</div>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: {{ $metric->engaged_traffic_rate ?? 0 }}%"></div>
                        </div>
                        <div class="metric-description">
                            Percentage of visitors who actively engage with your site content.
                        </div>
                    </div>
                @endif
            </div>
            @endif
            
            @if (in_array('site_traffic', $content_to_print) || in_array('site_contacts', $content_to_print) || in_array('engaged_traffic', $content_to_print) || in_array('site_popular_pages', $content_to_print)) 
            <h2 class="section-title">Traffic Analytics</h2>
            <div class="metrics-grid">
                @if (in_array('site_traffic', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>👁️</i> Site Traffic
                        </div>
                        <div class="metric-value">{{ $metric->site_traffic ?? 'N/A' }}</div>
                        <div class="metric-description">
                            Total number of visitors during the reporting period.
                        </div>
                    </div>
                @endif
                
                @if (in_array('site_contacts', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>📱</i> Site Contacts
                        </div>
                        <div class="metric-value">{{ $metric->site_contacts ?? 'N/A' }}</div>
                        <div class="metric-description">
                            Total number of contact form submissions or inquiries.
                        </div>
                    </div>
                @endif
                
                @if (in_array('engaged_traffic', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>🔥</i> Engaged Traffic
                        </div>
                        <div class="metric-value">{{ $metric->engaged_traffic ?? 'N/A' }}</div>
                        <div class="metric-description">
                            Number of visitors who spend significant time or interact with multiple pages.
                        </div>
                    </div>
                @endif

                @if (in_array('site_popular_pages', $content_to_print))  
                    <div class="metric-card">
                        <div class="metric-title">
                            <i>🌐</i>Popular Pages
                        </div>
                        <div class="metric-description ps-5">
                            @if(isset($metric->site_popular_pages) && !empty($metric->site_popular_pages))
                                {!! $metric->site_popular_pages !!}
                            @else
                                No popular pages data available
                            @endif
                        </div>
                    </div>
                @endif
            </div>
            @endif
            
            
            
            @if (in_array('site_monthly_insight', $content_to_print) || in_array('focus_areas', $content_to_print)) 
                <h2 class="section-title">Analysis & Recommendations</h2>
                @if (in_array('site_monthly_insight', $content_to_print))
                    <div class="text-box">
                        <h3>Monthly Insights</h3>
                        {!! $metric->site_monthly_insight ?? '<p>No monthly insights available for this period.</p>' !!} <!-- {{-- {!! $content !!} --}} => (IMP) this syntax is used to render html (in pdf) as html and not as text wrapped in styling tags -->
                    </div>
                @endif
                @if (in_array('focus_areas', $content_to_print)) 
                    <div class="text-box">
                        <h3>Focus Areas</h3>
                        {!! $metric->focus ?? '<p>No focus areas defined for this period.</p>' !!} <!-- {{-- {!! $content !!} --}} => (IMP) this syntax is used to render html (in pdf) as html and not as text wrapped in styling tags -->
                    </div>
                @endif
            @endif  

            <div class="footer">
                <p>This report was automatically generated on {{ date('F d, Y') }} at {{ date('H:i') }}. For questions or support, please contact your account manager.</p>
                <p>&copy; {{ date('Y') }} All Rights Reserved</p>
            </div>
            
        </div>
    </div>
</body>
</html>