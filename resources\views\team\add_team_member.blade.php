@extends('layout.app')
@section('title', 'Add Team Member')
@section('content')


@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
@endif
@if (session('error'))
    <script>
        errorToast(@json(session('error')));
    </script>
@endif
@if ($errors->any())
    @foreach ($errors->all() as $error)
        <script>
            errorToast(@json($error));
        </script>
    @endforeach
@endif


<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            {{-- @if(admin_superadmin_permissions()) --}}
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('teams') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Teams List</a>
            {{-- @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('projects') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @endif --}}
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">New <i>Team Member</i></h1>
        </div>
    </div>
</section>
<section class="team-dashboard pt-0 pb-5">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <form class="edit-member row" action="{{ route('save-team-member') }}" method="POST" enctype="multipart/form-data" id="add-team-memeber-form" >
            @csrf
            <div class="col-md-auto">
                <div class="upload-icon d-flex align-items-center justify-content-center">
                    <div class="pic" id="pic">
                        <img src="{{ asset('images/upload-member-icon.svg') }}" alt="" height="60" width="60"/>
                        <input type="file" name="profile_image" class="form-control d-none" id="" accept="image/jpeg, image/png, image/jpg, image/webp">
                    </div>
                </div>
            </div>
            <div class="col-md">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="name">Name</label>
                        <input class="form-control border-0" type="text" id="name" name="name" placeholder="" value="{{ old('name') }}" />
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="setColorPicker">User Color</label>
                        <div class="row gx-0 input_color overflow-hidden">
                            <div class="col-md-6">
                                <input class="set_color value form-control border-0" name="color_code" id="setColorValue" type="text" value="#FE5811" />
                            </div>
                            <div class="col-md-6 overflow-hidden">
                                <input class="set_color picker form-control border-0 px-0" id="setColorPicker" type="color" value="#FE5811" />
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="email">Email</label>
                        <input class="form-control border-0" type="email" id="email" name="email" placeholder="" value="{{ old('email') }}" />
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="temporaryPassword">Temporary Password</label>
                        <input class="form-control border-0" type="password" id="temporaryPassword" name="temporaryPassword" placeholder="" value="{{ old('temporaryPassword') }}" />
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label mb-2" for="setTeam">Set Team</label>
                        <div class="select-wrap">
                            <select class="form-select border-0" name="setTeam" id="setTeam">
                                <option value="">Select Team</option>
                                @foreach( $teams as $team)
                                    <option value="{{ $team->id }}" {{ old('setTeam') == $team->id ? 'selected' : '' }}>{{ $team->name }}</option>
                                @endforeach
                            </select>
                            <div class="select-icon"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" width="18" height="10" /></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label mb-2" for="setAccessLevel">Set Access Level</label>
                        <div class="select-wrap">
                            <select class="form-select border-0" name="setAccessLevel" id="setAccessLevel">
                                <option value="">Select</option>
                                @foreach( $accessLevels as $access_level )
                                    <option value="{{ $access_level->id }}" {{ old('setAccessLevel') == $access_level->id ? 'selected' : '' }}>{{ $access_level->name }}
                                    </option>
                                @endforeach
                            </select>
                            <div class="select-icon"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" width="18" height="10" /></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label mb-2" for="team">Team Member Location</label>
                        <div class="select-wrap">
                            <select class="form-select border-0" name="team" id="team">
                                <option value="">Select</option>
                                <option value="US">US</option>
                                <option value="India">India</option>
                            </select>
                            <div class="select-icon"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" width="18" height="10" /></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="pm_hours">PM Hours/Week</label>
                        <input class="form-control border-0" type="number" id="pm_hours" name="pm_hours" placeholder="0" value="{{ old('pm_hours', 0) }}" min="0" />
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="designer_hours">Designer Hours/Week</label>
                        <input class="form-control border-0" type="number" id="designer_hours" name="designer_hours" placeholder="0" value="{{ old('designer_hours', 0) }}" min="0" />
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="developer_hours">Developer Hours/Week</label>
                        <input class="form-control border-0" type="number" id="developer_hours" name="developer_hours" placeholder="0" value="{{ old('developer_hours', 0) }}" min="0" />
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="cs_hours">CS Hours/Week</label>
                        <input class="form-control border-0" type="number" id="cs_hours" name="cs_hours" placeholder="0" value="{{ old('cs_hours', 0) }}" min="0" />
                    </div>
                    <div class="col-12 cta-row text-center">
                        <button type="submit" class="cta orange ms-auto">CREATE TEAM MEMBER</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        let upload_btn = document.querySelector('.upload-icon');
        let upload_input = document.querySelector('.upload-icon input[type="file"]');
        let upload_icon = document.querySelector('.upload-icon img');

        upload_btn.addEventListener('click', function() {
            upload_input.click();
        });
        upload_input.addEventListener('change', function () {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    upload_icon.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });


        /**
         * validate form
         */
        const form_id = 'add-team-memeber-form';
        let args = {
                rules : {
                    '#name': GlobalFormValidator.validators.required,
                    '#color_code': GlobalFormValidator.validators.required,
                    '#email': GlobalFormValidator.validators.required,
                    '#temporaryPassword': GlobalFormValidator.validators.required,
                    '#setTeam': GlobalFormValidator.validators.required,
                    '#setAccessLevel': GlobalFormValidator.validators.required,
                    '#team': GlobalFormValidator.validators.required,
                },
                messages: {
                    '#name': "Please add a Name",
                    '#color_code': "Please add a Color code",
                    '#email': "Please add a Email Address",
                    '#temporaryPassword': "Please add a Password",
                    '#setTeam': "Please Assign a Team",
                    '#setAccessLevel': "Please Assign a Access Level",
                    '#team': "Please select a Team Location",
                },
                onSuccess: (form) => {
                    form.submit();
                }
            }
        let Validator = new GlobalFormValidator(form_id, args);
        Validator.initialize();

    });
</script>

@endsection