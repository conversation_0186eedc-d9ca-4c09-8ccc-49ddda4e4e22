- let pageName = 'Client Portal';
- let mainPage = 'Billing History';
- let pageTitle = `${pageName}: ${mainPage}`;

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>[SP-003-25] Sample Project 3</i>', false, true, true, 'Back to Client Portal')

        section.client-project.bg-white
            .container-xxl
                .heading.d-flex.align-items-center.justify-content-between
                    h2.mb-0 Billing #[i History]

        section.current-timeline.bg-white.pb-5
            .container-xxl
                .meta-row.d-flex.mt-b(class='orange')
                    .logo.d-flex.align-items-center.justify-content-center.rounded-circle #[img(src='images/billing-project-icon.svg', alt='')]
                    .wrap-meta.payment.ms-3.d-flex.flex-grow-1.rounded-2
                        .title.d-flex.align-items-center.justify-content-center BILLING
                        .payment-type.col-meta.d-none.d-lg-block.align-self-center #[strong Payment Type]
                        .due-date.col-meta.d-none.d-lg-block.align-self-center.text-center #[strong Due Date]
                        .amount.col-meta.d-none.d-lg-block.align-self-center.text-center #[strong Amount]
                        .received.col-meta.d-none.d-lg-block.align-self-center.text-center #[strong Received]
                .payment-list
                    mixin paymentRow(type, dueDate, amount='$2500', received='-', blame=false, done=false)
                        .payment-row.d-flex.flex-column.flex-sm-row
                            .payment-type.col-meta.align-self-center.text-center.text-md-start
                                .col-meta.px-0.d-md-none #[strong Payment Type]

                                if blame && done
                                    .d-flex
                                        span.check.text-orange.me-1 #[i.bi.bi-check2-circle]
                                        .text #[span.text #[strong #{type}]] #[br] #[i.blame.text-orange #{blame}]
                                else if done
                                    span.check.text-orange.me-1 #[i.bi.bi-check2-circle]
                                    span.text #[strong #{type}]
                                else
                                    strong #{type}

                            .due-date.col-meta.align-self-center.text-center.d-flex.flex-column
                                .col-meta.px-0.d-md-none #[strong Due Date]
                                | #{dueDate}
                            .amount.col-meta.align-self-center.text-center.d-flex.flex-column
                                .col-meta.px-0.d-md-none #[strong Amount]
                                | #{amount}
                            .received.col-meta.align-self-center.text-center.d-flex.flex-column
                                .col-meta.px-0.d-md-none #[strong Received]
                                | #{received}

                    +paymentRow('Initial Deposit', '12/31/25', '$2500', '12/30/25', false, true)
                    +paymentRow('Monthly Payment: January', '1/1/25', '$2500', '1/10/25', false, true)
                    +paymentRow('Monthly Payment: February', '2/1/25', '$2500', '-', '18 Days Past Due', true)
                    +paymentRow('Monthly Payment: March', '3/1/25', '$2500', '-')
                    +paymentRow('Monthly Payment: April', '4/1/25', '$2500', '-')
                    +paymentRow('Monthly Payment: May', '5/1/25', '$2500', '-')
                    +paymentRow('Monthly Payment: June', '6/1/25', '$2500', '-')
                    +paymentRow('Project Launch Payment', '6/10/25', '$2500', '-')
                    +paymentRow('Post-Launch: Monthly Maintenance Retainer', 'Ongoing', '$100', '-')

        include ../partials/footer.pug
        +footer()