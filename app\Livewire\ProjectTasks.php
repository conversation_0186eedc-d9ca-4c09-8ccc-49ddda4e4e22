<?php

namespace App\Livewire;

use App\Models\AccessLevel;
use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Task;
use App\Models\Project;
use App\Models\Status;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProjectTasks extends Component
{
    use WithPagination;

    public $project_id;
    public $selectedStatus = 'all';
    public $sortField = 'created_at';
    public $sortDirection = 'asc';
    public $showSortDropdown = false;

    public $redirect = null;

    protected $queryString = [
        'selectedStatus' => ['except' => 'all', 'as' => 'status'],
        'sortField' => ['except' => 'created_at'],
        'redirect' => ['except' => null],
    ];

    public $urgentLimit = 10;
    public $regularLimit = 10;

    public function loadMoreUrgent()
    {
        $this->urgentLimit += 10;
    }

    public function loadMoreRegular()
    {
        $this->regularLimit += 10;
    }

    public function mount($project_id = null)
    {
        $this->redirect = request()->query('redirect');

        $this->project_id = $project_id ?? request()->route('project_id');

        if (request()->has('status') && request('status') !== 'all') {
            $this->selectedStatus = request('status');
        }
    }

    public function sortBy($field)
    {
        $this->sortField = $field;
        $this->sortDirection = 'asc';
        $this->resetPage();
    }

    public function filterByStatus($status)
    {
        $this->selectedStatus = $status;
        $this->resetPage();
    }

    public function getStatusesProperty()
    {
        $user = Auth::user();

        $adminAccessLevelId = AccessLevel::where('name', 'Admin')->value('id');

        $userhasAdminAccessLevel = $user->access_level_id === $adminAccessLevelId;

        // If no project_id, return all statuses without counts
        if (!$this->project_id) {
            return Status::all();
        }

        $project = Project::find($this->project_id);

        if (!$project) {
            return collect();
        }

        $hasRoleAdmin = $user->role && in_array($user->role->name, ['Admin', 'SuperAdmin']);

        if ($hasRoleAdmin || $userhasAdminAccessLevel) {
            return Status::withCount(['tasks' => function ($query) {
                $query->where('project_id', $this->project_id)
                    ->whereHas('project', function ($q) {
                        $q->whereNull('deleted_at');
                    });
            }])->get();
        }

        $userTaskIds = DB::table('task_user')->where('user_id', $user->id)->pluck('task_id');

        return Status::withCount(['tasks as tasks_count' => function ($query) use ($userTaskIds) {
            $query->where('project_id', $this->project_id)
                ->whereIn('tasks.id', $userTaskIds)
                ->whereHas('project', function ($q) {
                    $q->whereNull('deleted_at');
                });
        }])->get();
    }

    public function getAllTaskCountProperty()
    {
        $user = Auth::user();

        // Base query: only count tasks where the project is not soft deleted
        $query = Task::query()->whereHas('project', function ($q) {
            $q->whereNull('deleted_at');
        });

        // Add project_id filter if available
        if ($this->project_id) {
            $query->where('project_id', $this->project_id);
        }

        $adminAccessLevelId = AccessLevel::where('name', 'Admin')->value('id');
        $hasRoleAdmin = $user->role && in_array($user->role->name, ['SuperAdmin', 'Admin']);
        $hasAccessLevelAdmin = $user->access_level_id === $adminAccessLevelId;

        if (!$hasRoleAdmin && !$hasAccessLevelAdmin) {
            $userTaskIds = DB::table('task_user')->where('user_id', $user->id)->pluck('task_id');
            $query->whereIn('id', $userTaskIds);
        }

        // Clone query for reuse
        $allTasksQuery = clone $query;

        // Prepare Recently Finished logic
        $recentlyFinishedCount = 0;
        $recentlyFinishedStatus = Status::where('name', 'Recently Finished')->first();


        // if ($recentlyFinishedStatus) {
        //     $recentlyFinishedQuery = Task::whereHas('project', function ($q) {
        //             $q->whereNull('deleted_at');
        //         })
        //         ->where('status_id', $recentlyFinishedStatus->id)
        //         ->where('updated_at', '>=', now()->subDays(30));

        //     if (!($user->role && in_array($user->role->name, ['SuperAdmin', 'Admin']))) {
        //         $userTaskIds = $user->tasks()->pluck('tasks.id');
        //         $recentlyFinishedQuery->whereIn('id', $userTaskIds);
        //     }

        //     if ($this->project_id) {
        //         $recentlyFinishedQuery->where('project_id', $this->project_id);
        //     }

        //     $recentlyFinishedCount = $recentlyFinishedQuery->count();
        // }

        // // Count all tasks excluding "Recently Finished"
        // $otherTasksCount = $allTasksQuery->where('status_id', '!=', $recentlyFinishedStatus?->id)->count();

        // return $otherTasksCount + $recentlyFinishedCount;

        if ($recentlyFinishedStatus) {
            $allTasksQuery->where('status_id', '!=', $recentlyFinishedStatus->id);
        }

        return $allTasksQuery->count();
    }

    public function getStatusNameProperty()
    {
        if ($this->selectedStatus === 'all') {
            return 'All';
        }

        return Status::find($this->selectedStatus)?->name ?? 'All';
    }

    public function getGroupedTasksProperty()
    {
        $tasks = $this->getFilteredTasksQuery()
            ->with(['project', 'status', 'users'])
            ->get();

        $urgentStatusId = Status::where('name', 'Urgent')->first()?->id ?? 1;

        $allUrgentTasks = $tasks->filter(fn($task) => $task->status_id == $urgentStatusId);
        $urgentTasks = $allUrgentTasks->take($this->urgentLimit);

        $nonUrgentTasks = $tasks->filter(fn($task) => $task->status_id != $urgentStatusId);

        if ($this->sortField === 'job') {
            $grouped = $nonUrgentTasks
                ->groupBy(fn($task) => optional($task->project)->name ?? 'No Project')
                ->map(fn($group) => $group->take($this->regularLimit));
        } else {
            $grouped = collect([
                'Tasks' => $nonUrgentTasks->sortBy($this->sortField)->take($this->regularLimit),
            ]);
        }

        return [
            'urgent' => $urgentTasks,
            'regular' => $grouped,
            'hasMoreUrgent' => $allUrgentTasks->count() > $this->urgentLimit,
            'hasMoreRegular' => $nonUrgentTasks->count() > $this->regularLimit,
        ];
    }

    protected function getTasksBaseQuery()
    {
        $user = Auth::user();
        $completedStatusId = Status::where('name', 'Recently Finished')->first()?->id;

        $query = Task::query();

        if ($this->project_id) {
            $query->where('project_id', $this->project_id)
                ->whereHas('project', function ($projectQuery) {
                    $projectQuery->whereNull('deleted_at');
                });
        } else {
            // If no project_id is provided, just ensure we're not showing tasks from deleted projects
            $query->whereHas('project', function ($projectQuery) {
                $projectQuery->whereNull('deleted_at');
            });
        }

        $adminAccessLevelId = AccessLevel::where('name', 'Admin')->value('id');
        $hasRoleAdmin = $user->role && in_array($user->role->name, ['SuperAdmin', 'Admin']);
        $hasAccessLevelAdmin = $user->access_level_id === $adminAccessLevelId;

        if (!$hasRoleAdmin && !$hasAccessLevelAdmin) {
            // For regular users, we need to ensure their assigned tasks belong to this project and non-deleted projects
            $userTaskIds = DB::table('task_user')->where('user_id', $user->id)->pluck('task_id');
            $query->whereIn('id', $userTaskIds);
        }

        if ($this->selectedStatus === 'all' && $completedStatusId) {
            $query->where('status_id', '!=', $completedStatusId);
        }

        return $query;
    }

    protected function getFilteredTasksQuery()
    {
        $query = $this->getTasksBaseQuery();

        if ($this->selectedStatus !== 'all') {
            $query->where('status_id', $this->selectedStatus);

            // Apply 30-day filter if the selected status is "Recently Finished"
            $recentlyFinishedStatus = Status::where('name', 'Recently Finished')->first();
            if ($this->selectedStatus == $recentlyFinishedStatus?->id) {
                $query->where('updated_at', '>=', now()->subDays(30));
            }
        }

        return $query;
    }

    public function getStatusCountsProperty()
    {
        $user = Auth::user();

        // Base query with project not soft-deleted
        $query = Task::query()->whereHas('project', function ($q) {
            $q->whereNull('deleted_at');
        });

        // Add project_id filter if available
        if ($this->project_id) {
            $query->where('project_id', $this->project_id);
        }

        // Apply user-specific filtering
        $adminAccessLevelId = AccessLevel::where('name', 'Admin')->value('id');
        $hasRoleAdmin = $user->role && in_array($user->role->name, ['SuperAdmin', 'Admin']);
        $hasAccessLevelAdmin = $user->access_level_id === $adminAccessLevelId;

        if (!$hasRoleAdmin && !$hasAccessLevelAdmin) {
            $userTaskIds = DB::table('task_user')->where('user_id', $user->id)->pluck('task_id');
            $query->whereIn('id', $userTaskIds);
        }

        // Clone query BEFORE get()
        $clonedQuery = clone $query;

        // Get counts for all statuses initially
        $statusCounts = $query->get()
            ->groupBy('status_id')
            ->map(fn($group) => $group->count());

        // Get the ID of the "Recently Finished" status
        $recentlyFinishedStatus = Status::where('name', 'Recently Finished')->first();

        if ($recentlyFinishedStatus) {
            // Use the CLONED query, not already executed one
            $recentlyFinishedCountQuery = $clonedQuery
                ->where('status_id', $recentlyFinishedStatus->id)
                ->where('updated_at', '>=', now()->subDays(30));

            $recentlyFinishedCount = $recentlyFinishedCountQuery->count();

            // Override the count for Recently Finished
            $statusCounts[$recentlyFinishedStatus->id] = $recentlyFinishedCount;
        }

        // Ensure all statuses are present even if count = 0
        $allStatusIds = Status::pluck('id');
        foreach ($allStatusIds as $statusId) {
            if (!isset($statusCounts[$statusId])) {
                $statusCounts[$statusId] = 0;
            }
        }

        return $statusCounts;
    }

    public function getProjectProperty()
    {
        return $this->project_id ? Project::find($this->project_id) : null;
    }

    public function render()
    {
        $groupedTasks = $this->groupedTasks;
        $project = $this->project;

        return view('livewire.project_tasks', [
            'groupedTasks' => $groupedTasks,
            'statuses' => $this->statuses,
            'statusCounts' => $this->statusCounts,
            'allTaskCount' => $this->allTaskCount,
            'statusName' => $this->statusName,
            'project' => $project,
            'hasUrgentTasks' => $groupedTasks['urgent']->isNotEmpty(),
            'hasMoreUrgent' => $groupedTasks['hasMoreUrgent'] ?? false,
            'hasMoreRegular' => $groupedTasks['hasMoreRegular'] ?? false,
        ]);
    }
}
