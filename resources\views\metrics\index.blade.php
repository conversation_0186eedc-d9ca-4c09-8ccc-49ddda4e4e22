@extends('layout.app')
@section('title', 'Voyager Site Analytics | Shieldssgf')
@section('content')



@push('styles')
<style>
    .tox-dialog {
    z-index: 1055 !important; /* higher than modal z-index (e.g., Bootstrap default is 1050) */
}

.tox-dialog input,
.tox-dialog textarea {
    pointer-events: auto !important;
    z-index: 1060 !important;
}

canvas {
    position: relative;
    z-index: 1;
    display:block;
}

#monthly_analysis_chart { /* by default, initially the height and width of canvas is 0px both */
    width: 300px !important;
    height: 300px !important; /* Or any height you need */
}

#yearly_analysis_chart { /* by default, initially the height and width of canvas is 0px both */
    width: 300px !important;
    height: 300px !important; /* Or any height you need */
}

.monthly-chart-container {
    overflow: visible;
    position: relative;
    width: 100%;
    height: 280px;
}

.yearly-chart-container {
    overflow: visible;
    position: relative;
    width: 100%;
    height: 280px;
}
</style>

@endpush

<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ set_user_image('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
        </div>
        <div class="meta d-flex justify-content-between align-items-center">
            <div class="copy">
                <h1 class="text-white mb-0">Project: <i>{{ $project->name }}</i></h1>
                <div class="page-links mt-3"><a class="active" href="{{ url()->current() }}">Site Analytics</a><a href="#">Plan Details</a></div>
            </div>
            <div class="logo">
                <a href="#"><img src="{{ set_user_image($client->logo) }}" alt="" width="72" height="80" /></a>
            </div>
        </div>
    </div>
</section>
<section class="client-project bg-white">
    <div class="container-xxl">
        <div class="heading d-md-flex align-items-md-baseline justify-content-between">
            <h2 class="mb-0">Site <i>Analytics</i></h2>
            <div class="links">
                <a class="data-tab active" data-target="analysis-row" data-header="analytics-head" href="#">Essential Metrics</a>
                <a class="data-tab" data-target="audience-row" data-header="analytics-head" href="#">Audience and Reach</a>
                <a class="data-tab" data-target="insight-row" data-header="insight-head"href="#">Monthly Insight &amp; Next Focus</a>
            </div>
        </div>
    </div>
</section>

<input type="hidden" class="client_name" value="{{ $client->name }}">

<!-- Essential Metrics -->
<section class="analytics-meta bg-white py-5">
    <div class="container-xxl">
        <hr class="my-0" />
        <div class="head d-flex analytics-head" >
            <div class="title">
                <h2 class="mb-0 lh-1 text-uppercase tab-head">Essential Metrics</h2>
            </div>
            <div class="select-month">
                <select class="ps-3 border-0 month-select" id="month">
                    @foreach ($months as $month)
                        <option value="{{ $month->id }}" {{ $month->slug == $currentMonthSlug ? 'selected' : '' }}>{{ $month->name }}</option>
                    @endforeach
                </select
                ><span class="arrow d-inline-flex"><img src="{{ set_user_image('images/select-down-arrow.svg') }}" alt="" width="14" height="9" /></span>
            </div>
            <a class="circle-icon ms-auto align-self-center" href="#">?</a>
        </div>
        <!-- Analytic Row -->
        <div class="row analysis-row analytics-rows">
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/performance-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->performance ?? 00 }}" data-duration="2000" data-tag="performance">0</h2>
                        <input type="hidden" name="performance" id="performance" value="{{ $essentialMetrics->performance ?? '' }}" />
                        <h3 class="text-black mb-0 p">Performance</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/accessibility-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->accessibility ?? 00 }}" data-duration="2000" data-tag="accessibility">0</h2>
                        <input type="hidden" name="accessibility" id="accessibility" value="{{ $essentialMetrics->accessibility ?? '' }}" />
                        <h3 class="text-black mb-0 p">Accessibility</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/best-practices-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->best_practice ?? 00 }}" data-duration="2000" data-tag="best_practice" >0</h2>
                        <input type="hidden" name="best_practice" id="best_practice" value="{{ $essentialMetrics->best_practice ?? '' }}" />
                        <h3 class="text-black mb-0 p">Best Practices</h3>
                        <h4 class="p">Perfect!</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/seo-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->seo ?? 00 }}" data-duration="2000" data-tag="seo">0</h2>
                        <input type="hidden" name="seo" id="seo" value="{{ $essentialMetrics->seo ?? '' }}" />
                        <h3 class="text-black mb-0 p">SEO</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
            <div class="col-md my-5">
                <div class="box d-flex">
                    <div class="icon me-3"><img src="{{ set_user_image('images/site-health-icon.svg') }}" alt="" /></div>
                    <div class="text">
                        <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->site_health ?? 00 }}" data-duration="2000" data-tag="site_health" >0</h2>
                        <input type="hidden" name="site_health_score" id="site_health_score" value="{{ $essentialMetrics->site_health ?? '' }}" />
                        <h3 class="text-black mb-0 p">Site Health Score</h3>
                        <h4 class="p">+3 mo/mo</h4>
                    </div>
                </div>
            </div>
        </div>
        <!-- Audience Row -->
        <div class="row audience-row analytics-rows">
            <div class="container-fluid">
                <div class="row analytics-row">
                    <div class="col-md my-5">
                        <div class="box d-flex">
                            <div class="icon me-3"><img src="{{ set_user_image('images/total-users-icon.svg') }}" alt="" /></div>
                            <div class="text">
                                <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->site_traffic ?? 00 }}" data-duration="2000" data-tag="site_traffic">0</h2>
                                <input type="hidden" name="site_traffic" id="site_traffic" value="{{ $essentialMetrics->site_traffic ?? '' }}" />
                                <h3 class="text-black mb-0 p">Total Users in <span class="current_month_text"></span> <span class="current_year_text"></span></h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md my-5 border-left d-flex justify-content-center">
                        <div class="box graph">
                            <p class="mb-2"><span class="current_month_previous_month_text"></span> visits: <strong><span class="monthly_site_traffic_diff"></span></strong></p>
                            {{-- <h2 class="mb-0"><span class="monthly_site_traffic_diff_percentage">{{ $essentialMetrics->site_traffic_analysis->percentage_change ?? 00 }}</span>% mo/mo</h2> --}}
                            <h2 class="mb-0"><span class="monthly_site_traffic_diff_percentage"></span>% mo/mo</h2>
                            {{-- <div class="figure mt-4"><img src="{{ set_user_image('images/graph1.svg') }}" alt="" /></div> --}}
                            <div class="figure monthly-chart-container"><canvas id="monthly_analysis_chart" width="300" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md my-5 border-left d-flex justify-content-center">
                        <div class="box graph">
                            <p class="mb-2"><span class="current_month_current_year_and_previous_year_text"></span> visits: <strong><span class="yearly_site_traffic_diff"></span></strong></p>
                            <h2 class="mb-0"><span class="yearly_site_traffic_diff_percentage"></span>% yr/yr</h2>
                            {{-- <div class="figure mt-4"><img src="{{ set_user_image('images/graph2.svg') }}" alt="" /></div> --}}
                            <div class="figure yearly-chart-container"><canvas id="yearly_analysis_chart" width="300" height="300"></canvas></div>
                        </div>
                    </div>
                </div>
                <div class="row analytics-row">
                    <div class="col-md my-5">
                        <div class="box d-flex">
                            <div class="icon me-3"><img src="{{ set_user_image('images/site-contacts-icon.svg') }}" alt="" /></div>
                            <div class="text">
                                <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->site_contacts ?? 00 }}" data-duration="2000" data-tag="site_contacts">0</h2>
                                <input type="hidden" name="site_contacts" id="site_contacts" value="{{ $essentialMetrics->site_contacts ?? '' }}" />
                                <h3 class="text-black mb-0 p">Site Contacts</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md my-5">
                        <div class="box d-flex">
                            <div class="icon me-3"><img src="{{ set_user_image('images/popular-pages-icon.svg') }}" alt="" /></div>
                            <div class="text">
                                <p class="mb-2">Most <strong>Popular Pages</strong></p>
                                <div class="copy flex-grow-1 popular-pages-info">
                                {!! $essentialMetrics->site_popular_pages ?? '' !!}
                                </div>
                                <a href="#" data-bs-toggle="modal" data-bs-target="#staticBackdrop" class="text-orange mt-4 fw-bold text-uppercase ">Set Popular Pages <i class="fas fa-edit"></i></a>
                                
                            </div>
                        </div>
                    </div>
                    <div class="col-md my-5">
                        <div class="box d-flex">
                            <div class="icon me-3"><img src="{{ set_user_image('images/engaged-users-icon.svg') }}" alt="" /></div>
                            <div class="text">
                                <h2 class="counter text-orange mb-2" data-countTo="{{ $essentialMetrics->engaged_traffic ?? 00 }}" data-duration="2000" data-tag="engaged_traffic">0</h2>
                                <input type="hidden" name="engaged_traffic" id="engaged_traffic" value="{{ $essentialMetrics->engaged_traffic ?? '' }}" />
                                <h3 class="text-black mb-0 p">Engaged Users</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md my-5">
                        <div class="box d-flex">
                            <div class="icon me-3"><img src="{{ set_user_image('images/engagement-rate-icon.svg') }}" alt="" /></div>
                            <div class="text">
                                <h2 class="counter text-orange mb-2"  data-countTo="{{ $essentialMetrics->engaged_traffic_rate ?? 00 }}" data-duration="2000" data-tag="engaged_traffic_rate">0</h2>
                                <input type="hidden" name="engaged_traffic_rate" id="engaged_traffic_rate" value="{{ $essentialMetrics->engaged_traffic_rate ?? '' }}" />
                                <h3 class="text-black mb-0 p">Engagement Rate</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- insight Row -->
        <div class="row insight-row analytics-rows">
            <div class="col-md-6 d-flex flex-column">
                <hr class="my-0" />
                <div class="head d-flex insight-head">
                    <div class="title">
                        <h2 class="mb-0 lh-1 text-uppercase">MONTHLY INSIGHT</h2>
                    </div>
                    <div class="select-month">
                        <select class="ps-3 border-0 month-select" >
                            @foreach ($months as $month)
                                <option value="{{ $month->id }}" {{ $month->slug == $currentMonthSlug ? 'selected' : '' }}>{{ $month->name }}</option>
                            @endforeach
                        </select
                        ><span class="arrow d-inline-flex"><img src="{{ set_user_image('images/select-down-arrow.svg') }}" alt="" width="14" height="9" /></span>
                    </div>
                    <a class="circle-icon ms-auto align-self-center" href="#">?</a>
                </div>

                <div class="copy flex-grow-1 py-5 site-monthly-insight-info">
                    {!! $essentialMetrics->site_monthly_insight ?? '' !!}
                </div>
                <a href="#" data-bs-toggle="modal" data-bs-target="#staticBackdropMonthlyInsight" class="text-orange mt-4 fw-bold text-uppercase ">Set Monthly Insight <i class="fas fa-edit"></i></a>
                <hr class="my-0" />
            </div>
            <div class="col-md-6 d-flex flex-column">
                <hr class="my-0" />
                <div class="head d-flex insight-head">
						<div class="title">
							<h2 class="mb-0 lh-1 text-uppercase"><span class="next_month_text"> - </span> FOCUS</h2>
						</div>
						<a class="circle-icon ms-auto align-self-center" href="#">?</a>
					</div>
                <div class="copy flex-grow-1 py-5 focus-info">
                    {!! $essentialMetrics->focus ?? '' !!}
                </div>
                <a href="#" data-bs-toggle="modal" data-bs-target="#staticBackdropFocus" class="text-orange mt-4 fw-bold text-uppercase ">Set Next Month Focus <i class="fas fa-edit"></i></a>
                <hr class="my-0" />
            </div>
        </div>

        <hr class="my-0" />
        <div class="row">
            <div class="col-md-3">
            <a href="javascript:void(0)" class="btn cta text-uppercase" id="save-report">Save Report</a>
           

            </div>

            {{-- <input type="hidden" name="repo_file" id="report_file" value="{{ $essentialMetrics->report_file ?? '' }}" />> --}}

            <input type="file" name="report_file" id="report_file" class="d-none" accept=".pdf,.doc,.docx">

<div class="col-md-9 d-flex justify-content-end gx-5 align-items-center" id="report-upload-section">
    <a href="javascript:void(0)" class="btn cta text-uppercase mx-3" id="set-report">Upload Report By PDF</a>
    
    <!-- Placeholder for selected file -->
    <div id="selected-file-display" class="d-flex align-items-center d-none">
        <span id="file-icon" style="font-size: 1.5rem; margin-right: 10px;"></span>
        <span id="file-name" class="text-muted"></span>
        <a href="javascript:void(0)" id="remove-file" class="text-danger ms-3" title="Remove">&times;</a>
    </div>

    <a href="javascript:void(0)" class="btn cta text-uppercase" id="download-report">Download Report In PDF</a>
</div>

        </div>
    </div>
</section>



<!-- Modal -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5 text-orange" id="staticBackdropLabel">Set Popular Pages</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <textarea name="popular_pages" id="popular_pages"  rows="7" data-tag="popular_pages" >{!! $essentialMetrics->site_popular_pages ?? '' !!}</textarea>
      </div>
      <div class="modal-footer">
            <button type="button" class="cta m-0 save-changes" data-target="popular-pages-info">Save changes</button>
            <button type="button" class="btn btn-secondary d-none" data-bs-dismiss="modal">Close</button>
	   </div>
    </div>
  </div>
</div>

<div class="modal fade" id="staticBackdropFocus" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5 text-orange" id="staticBackdropLabel">Set Focus</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <textarea name="focus" id="focus" value="{{ $essentialMetrics->focus ?? '' }}" rows="7" data-tag="focus" >{{ $essentialMetrics->focus ?? '' }}</textarea>
      </div>
      <div class="modal-footer">
            <button type="button" class="cta m-0 save-changes" data-target="focus-info">Save changes</button>
            <button type="button" class="btn btn-secondary d-none" data-bs-dismiss="modal">Close</button>
	   </div>
    </div>
  </div>
</div>

<div class="modal fade" id="staticBackdropMonthlyInsight" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5 text-orange" id="staticBackdropLabel">Set Monthly Insight</h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <textarea name="site_monthly_insight" id="site_monthly_insight" value="{{ $essentialMetrics->site_monthly_insight ?? '' }}" rows="7" data-tag="site_monthly_insight" >{{ $essentialMetrics->site_monthly_insight ?? '' }}</textarea>
      </div>
      <div class="modal-footer">
           <button type="button" class="cta m-0 save-changes" data-target="site-monthly-insight-info">Save changes</button>
           <button type="button" class="btn btn-secondary d-none" data-bs-dismiss="modal">Close</button>
	   </div>
    </div>
  </div>
</div>



<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Select the content you want to print in PDF</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            @php
                $desiredKeys = [
                    'performance1',
                    'accessibility2',
                    'best_practice3',
                    'seo4',
                    'site_health5',
                    'engaged_traffic_rate6',
                    'site_traffic7',
                    'site_contacts8',
                    'engaged_traffic9',
                    'site_popular_pages10',
                    'site_monthly_insight11',
                    'focus_areas12',
                ];
            @endphp
            
            <div class="h-100">
                @foreach ($desiredKeys as $key)
                    @php
                        $labelText = ucfirst(str_replace('_', ' ', $key));
                        $labelText = preg_replace('/\d+$/', '', $labelText);
                    @endphp
                    <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="pdf_content[]" value="{{ $key }}" id="{{ Str::slug($key) }}">
                            <label class="form-check-label text-nowrap" for="{{ Str::slug($key) }}">{{ trim($labelText) }}</label>
                        </div>
                    </div>
                @endforeach
                <div class="text-danger px-3 download_report_modal_error">
                </div>
            </div>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary download_report_modal_save_button">Save changes</button>
        </div>
      </div>
    </div>
  </div>

  
@push('styles')
<style>
#selected-file-display{
    margin-top:2.5rem;
    margin-right:1rem;
}

</style>

@endpush

@push('script')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    let monthly_analysis_chart = null;
    let yearly_analysis_chart = null;
    
    function createMonthLineChart(labels, trafficData) {
        // console.log("in the function")
        const ctx = document.getElementById('monthly_analysis_chart').getContext('2d');

        // Dynamically calculate Y-axis step size
        const maxTraffic = Math.max(...trafficData);
        const minTraffic = Math.min(...trafficData);
        const range = maxTraffic - minTraffic;
        const stepSize = Math.max(Math.ceil(range / 4), 100); // Minimum step size of 100
        const suggestedMax = maxTraffic + stepSize;

        // If chart exists and needs to be fully refreshed
        if (monthly_analysis_chart) {
            // console.log("destroying existing chart");
            monthly_analysis_chart.destroy(); // Destroy the previous instance
        }
        

        // console.log("creating new chart");
        monthly_analysis_chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Monthly Website Traffic',
                    data: trafficData,
                    borderColor: '#ff4c00',
                    backgroundColor: 'white',
                    fill: true,
                    tension: 0.4,
                    radius: 5,
                    pointRadius: 5,
                    pointHoverRadius: 8,
                    hitRadius: 30,
                    hoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true
                    },
                    legend: { // Add this legend object
                        display: true, // Keep display true to show the legend
                        labels: {
                            boxWidth: 0, // Set boxWidth to 0 to hide the colored box
                            // You can also adjust padding or font styling here if needed
                            // padding: 10,
                            // font: {
                            //     size: 12
                            // }
                        }
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function (context) {
                                return context.dataset.label + ': ' + context.formattedValue + ' visits';
                            }
                        }
                    }
                },
                layout: {
                    // padding: {
                    //     top: 10,
                    //     bottom: 20
                    // }
                },
                scales: {
                    y: {
                        min: 0,
                        beginAtZero: true,
                        suggestedMax: suggestedMax,
                        ticks: {
                            stepSize: stepSize,
                            padding: 10
                        },
                        border: {
                            display: true,
                            color: 'black',
                            width: 2
                        },
                        grid: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'Visits'
                        }
                    },
                    x: {
                        ticks: {
                            padding: 20
                        },
                        border: {
                            display: true,
                            color: 'black',
                            width: 2
                        },
                        grid: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'Month'
                        }
                    }
                }
            }
        });
    }

    function createYearLineChart(labels, trafficData) {
        // console.log("in the function")
        const ctx = document.getElementById('yearly_analysis_chart').getContext('2d');

        // Dynamically calculate Y-axis step size
        const maxTraffic = Math.max(...trafficData);
        const minTraffic = Math.min(...trafficData);
        const range = maxTraffic - minTraffic;
        const stepSize = Math.max(Math.ceil(range / 4), 100); // Minimum step size of 100
        const suggestedMax = maxTraffic + stepSize;

        // If chart exists and needs to be fully refreshed
        if (yearly_analysis_chart) {
            // console.log("destroying existing chart");
            yearly_analysis_chart.destroy(); // Destroy the previous instance
        }
        

        // console.log("creating new chart");
        yearly_analysis_chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Yearly Website Traffic',
                    data: trafficData,
                    borderColor: '#ff4c00',
                    backgroundColor: 'white',
                    fill: true,
                    tension: 0.4,
                    radius: 5,
                    pointRadius: 5,
                    pointHoverRadius: 8,
                    hitRadius: 30,
                    hoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true
                    },
                    legend: { // Add this legend object
                        display: true, // Keep display true to show the legend
                        labels: {
                            boxWidth: 0, // Set boxWidth to 0 to hide the colored box
                            // You can also adjust padding or font styling here if needed
                            // padding: 10,
                            // font: {
                            //     size: 12
                            // }
                        }
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            label: function (context) {
                                return context.dataset.label + ': ' + context.formattedValue + ' visits';
                            }
                        }
                    }
                },
                layout: {
                    // padding: {
                    //     top: 10,
                    //     bottom: 20
                    // }
                },
                scales: {
                    y: {
                        min: 0,
                        beginAtZero: true,
                        suggestedMax: suggestedMax,
                        ticks: {
                            stepSize: stepSize,
                            padding: 10
                        },
                        border: {
                            display: true,
                            color: 'black',
                            width: 2
                        },
                        grid: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'Visits'
                        }
                    },
                    x: {
                        ticks: {
                            padding: 20
                        },
                        border: {
                            display: true,
                            color: 'black',
                            width: 2
                        },
                        grid: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'Year'
                        }
                    }
                }
            }
        });
    }

    function prepareMonthChartData(data){
        //DISPLAYING PERCENTAGE CHANGE ABOVE THE CHART/GRAPH
        var percentage_change = data.monthly_site_traffic_analysis.percentage;
        var difference = data.monthly_site_traffic_analysis.difference;

        if(percentage_change !== '' && percentage_change !== null && percentage_change !== undefined){
            if (percentage_change > 0) { // Check if the number is positive and not zero
                percentage_change = '+' + percentage_change.toFixed(2);
            }
            // console.log("Monthly traffic Percentage: ", percentage_change);
            $('.monthly_site_traffic_diff_percentage').html(percentage_change);
        }
        else{
            $('.monthly_site_traffic_diff_percentage').html("N/A");
        }

        if(difference !== '' && difference !== null && difference !== undefined){
            if (difference > 0) { // Check if the number is positive and not zero
                difference = '+' + difference;
            }
            // console.log("Monthly traffic Difference:", difference);
            $('.monthly_site_traffic_diff').html(difference); // Assuming you have an element with this class
        }
        else{
            $('.monthly_site_traffic_diff').html("N/A"); // Assuming you have an element with this class
        }


        //STORING CHART/GRAPH VARIABLES
        let currentMonth = data.current_month_with_year.month;
        let previousMonth = data.previous_month_with_year.month;
        let currentTraffic = data.monthly_site_traffic_analysis.current_month_traffic;
        let previousTraffic = data.monthly_site_traffic_analysis.previous_month_traffic;
        $('.current_month_previous_month_text').text(data.previous_month_with_year.month + ", " + data.previous_month_with_year.year + " - " + data.current_month_with_year.month + ", " + data.current_month_with_year.year);
        $('.current_month_text').text(data.current_month_with_year.month);
        $('.current_year_text').text(data.current_month_with_year.year);
        // console.log("currentMonth: " + currentMonth)
        // console.log("previousMonth: " + previousMonth)
        // console.log("currentTraffic: " + currentTraffic)
        // console.log("previousTraffic: " + previousTraffic)

        // Prepare the chart data
        let labels = [previousMonth, currentMonth]; // Months
        let trafficData = [previousTraffic, currentTraffic]; // Traffic data

        // console.log(labels, trafficData)
        createMonthLineChart(labels, trafficData);
    }

    function prepareYearChartData(data){
        //DISPLAYING PERCENTAGE CHANGE ABOVE THE CHART/GRAPH
        var percentage_change = data.yearly_site_traffic_analysis.percentage;
        var difference = data.yearly_site_traffic_analysis.difference;

        if(percentage_change !== '' && percentage_change !== null && percentage_change !== undefined){
            if (percentage_change > 0) { // Check if the number is positive and not zero
                percentage_change = '+' + percentage_change.toFixed(2);
            }
            $('.yearly_site_traffic_diff_percentage').html(percentage_change);
        }
        else{
            $('.yearly_site_traffic_diff_percentage').html("N/A");
        }


        if(difference !== '' && difference !== null && difference !== undefined){
            if (difference > 0) { // Check if the number is positive and not zero
                difference = '+' + difference;
            }
            $('.yearly_site_traffic_diff').html(difference); // Assuming you have an element with this class
        }
        else{
            $('.yearly_site_traffic_diff').html("N/A"); // Assuming you have an element with this class
        }


        //STORING CHART/GRAPH VARIABLES
        let currentYear = data.current_year;
        let previousYear = data.previous_year;
        let currentTraffic = data.yearly_site_traffic_analysis.current_month_traffic_current_year;
        let previousTraffic = data.yearly_site_traffic_analysis.current_month_traffic_previous_year;
        $('.current_month_current_year_and_previous_year_text').text(data.current_month_with_year.month + ", " + data.previous_year + " - " + data.current_month_with_year.month + ", " + data.current_year)
        // console.log("currentYear: " + currentYear)
        // console.log("previousYear: " + previousYear)
        // console.log("currentTraffic: " + currentTraffic)
        // console.log("previousTraffic: " + previousTraffic)

        // Prepare the chart data
        let labels = [previousYear, currentYear]; // Months
        let trafficData = [previousTraffic, currentTraffic]; // Traffic data

        // console.log(labels, trafficData)
        createYearLineChart(labels, trafficData);
    }

function updateReportFileDisplay(fileUrl) {
    const setReportBtn = document.getElementById('set-report');
    const fileDisplay = document.getElementById('selected-file-display');
    const fileNameSpan = document.getElementById('file-name');
    const fileIcon = document.getElementById('file-icon');

    if (fileUrl) {
        const filename = fileUrl.split('/').pop();
        const ext = filename.split('.').pop().toLowerCase();
        const reportUrl = `/storage/reports/${filename}`; // <-- the file path will be fetched from storage/reports/ and downloaded (IMP)

        fileIcon.innerHTML = ext === 'pdf' ? '📄' : '📃';
        fileNameSpan.textContent = $('.client_name').val() + " " + $('.insight-head .month-select').find('option:selected').text() + " " + "Report";

        // fileNameSpan.onclick = () => window.open(reportUrl, '_blank');
        fileNameSpan.onclick = () => {
            fetch(reportUrl)
                .then(response => {
                    if (!response.ok) throw new Error('Network response was not ok');
                    return response.blob();
                })
                .then(blob => {
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = $('.client_name').val() + " " + $('.insight-head .month-select').find('option:selected').text() + " " + "Report"; // filename for download

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(link.href);
                })
                .catch(error => {
                    console.error('Error fetching or downloading the pdf:', error);
                    alert('Error fetching or downloading the pdf');
                });
        };

        setReportBtn.classList.add('d-none');
        fileDisplay.classList.remove('d-none');
    } else {
        setReportBtn.classList.remove('d-none');
        fileDisplay.classList.add('d-none');
    }
}


window.addEventListener('DOMContentLoaded', () => {
    const currentMonth = document.querySelector('.month-select').value;

    fetch("{{ route('get-metrics-by-month', ['project_id' => $project->id, 'month' => ':month']) }}".replace(':month', currentMonth), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            project_id: {{ $project->id }},
            month: currentMonth
        })
    })
    .then(data => data.json())
    .then(data => {
        if (data.success) {
            // console.log("this is success message")
            const fileUrl = data.data[0]?.report_file ?? null;
            updateReportFileDisplay(fileUrl);
        }  
        

        prepareMonthChartData(data);
        prepareYearChartData(data);
    })
    .catch(error => {
        console.error('Error:', error);
    });
});


    let reportFile=document.getElementById('report_file');
    const selectedFileDisplay = document.getElementById('selected-file-display');

 
document.getElementById('set-report').addEventListener('click', function () {
    document.getElementById('report_file').click();
});

document.getElementById('report_file').addEventListener('change', function () {
     reportFile = this.files[0];

     console.log(reportFile);

    if (reportFile) {
        const fileName = reportFile.name;
        const extension = fileName.split('.').pop().toLowerCase();

        
        let icon = '📄'; 
        if (extension === 'doc' || extension === 'docx') icon = '📃';

       
        document.getElementById('file-icon').textContent = icon;
        document.getElementById('file-name').textContent = fileName;

        document.getElementById('selected-file-display').classList.remove('d-none');
        document.getElementById('set-report').classList.add('d-none');
    }
});


document.getElementById('remove-file').addEventListener('click', function () {
    document.getElementById('report_file').value = ''; // clear input
    document.getElementById('selected-file-display').classList.add('d-none');
    document.getElementById('set-report').classList.remove('d-none');
});


document.addEventListener('DOMContentLoaded', function () {
    let selectedMonth;

   
    const monthSelects = document.querySelectorAll('.month-select');
    if (monthSelects.length > 0) {
        selectedMonth = monthSelects[0].value;
    }

    monthSelects.forEach(select => {
        select.addEventListener('change', function () {
            selectedMonth = this.value;

            
            monthSelects.forEach(otherSelect => {
                if (otherSelect !== this) {
                    otherSelect.value = selectedMonth;
                }
            });
        });
    });

    // document.getElementById('exampleModal').addEventListener('show.bs.modal', function (event) {
    const checkboxes = this.querySelectorAll('.modal-body input[type="checkbox"]');
    checkboxes.forEach(function(checkbox) {
        checkbox.checked = true;
    // });
});

    document.getElementById('download-report').addEventListener('click', function (e) {
        e.preventDefault(); // Just in case it's inside a form or link

        var myModal = new bootstrap.Modal(document.getElementById('exampleModal'));
        myModal.show();

        const saveChangesButton = document.querySelector('#exampleModal .modal-footer .download_report_modal_save_button');

        if (saveChangesButton) {
            const handleDownloadButtonClick = function() {
                const checkedValues = [];
                const checkboxes = document.querySelectorAll('#exampleModal .modal-body input[type="checkbox"]:checked');

                checkboxes.forEach(function(checkbox) {
                    checkedValues.push(checkbox.value);
                });

                if (checkedValues.length === 0) {
                    $('.download_report_modal_error').text('Please select at least one content option!');
                    return; // Stop the function here, preventing the AJAX request
                }

                fetch("{{ route('download-metrics-by-month') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        project_id: {{ $project->id }},
                        month: selectedMonth,
                        content_to_print: checkedValues
                    })
                })
                .then(async response => {
                    if (!response.ok) {
                        // Try to read JSON error message
                        const errorData = await response.json().catch(() => null);
                        let message = errorData?.message || 'An error occurred while downloading the report.';
                        throw new Error(message);
                    }

                    // Successful response: download PDF
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = $('.client_name').val() + " " + $('.insight-head .month-select').find('option:selected').text() + " " + "Report";
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);

                    // Remove the event listener after the AJAX call completes (success or failure)
                    saveChangesButton.removeEventListener('click', handleDownloadButtonClick);
                })
                .catch(error => {
                    alert(error.message); // Show error to user
                    console.error('Download error:', error);
                    // Even on error, remove the event listener to prevent multiple calls
                    saveChangesButton.removeEventListener('click', handleDownloadButtonClick);
                });

                myModal.hide();
            };

            saveChangesButton.addEventListener('click', handleDownloadButtonClick);
        } else {
            console.error('The "Save changes" button in the modal footer was not found.');
        }
    });
});


    </script>


<script src="{{ asset('js/tinymce/tinymce.min.js') }}" ></script>
<script>
  

    setTinyMceEditor('textarea#popular_pages');
    setTinyMceEditor('textarea#site_monthly_insight');
    setTinyMceEditor('textarea#focus');

    function setTinyMceEditor(selector) {

        tinymce.init({
            selector: selector,
            plugins: 'link lists code',
            toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link | code',
            menubar: false,
            branding: false,
            height: 300
        });
    }

</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
       
        const getCurrentMonthNameFromNumber = (num) => ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"][(num - 1) % 12] || "Invalid";

        const getNextMonthNameFromNumber = (num) => getCurrentMonthNameFromNumber(num % 12 + 1);


        //if month value is already selected in monthly insight tab, fetch the next month name and display on next focus tab
        if ($('.month-select').val() !== '' && $('.month-select').val() !== "undefined") {
            const initialSelectedMonth = $('.month-select').val();
            $('.next_month_text').text(getNextMonthNameFromNumber(initialSelectedMonth));
            // $('.current_month_text').text(getCurrentMonthNameFromNumber(initialSelectedMonth));
        }

        
        
        // Sync all .month-select dropdowns when one changes
        document.querySelectorAll('.month-select').forEach(select => {
            select.addEventListener('change', function () {
                const selectedMonth = this.value;

                // Sync other dropdowns
                document.querySelectorAll('.month-select').forEach(otherSelect => {
                    if (otherSelect !== this) {
                        otherSelect.value = selectedMonth;
                    }
                });
                
                $('.next_month_text').text(getNextMonthNameFromNumber(selectedMonth));
                // $('.current_month_text').text(getCurrentMonthNameFromNumber(selectedMonth));

                // Build and send the fetch request
                let url = "{{ route('get-metrics-by-month', ['project_id' => $project->id, 'month' => ':month']) }}".replace(':month', selectedMonth);

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        month: selectedMonth,
                        project_id: {{ $project->id }}
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const metricsData = data.data[0] || {};
                        successToast("Metrics fetched successfully!");

                        const counterTags = [
                            'performance', 'accessibility', 'best_practice', 'seo',
                            'site_health', 'site_traffic', 'site_contacts',
                            'engaged_traffic', 'engaged_traffic_rate',
                            'popular_pages', 'site_monthly_insight', 'focus'
                        ];

                        counterTags.forEach(tag => {
                            const value = metricsData[tag] ?? 0;

                            const counter = document.querySelector(`.counter[data-tag="${tag}"]`) || document.querySelector(`[data-tag="${tag}"]`);
                            if (counter) {
                                counter.setAttribute('data-countTo', value);
                                counter.textContent = value;
                            }

                            const input = document.getElementById(tag === 'site_health' ? 'site_health_score' : tag);
                            if (input) {
                                input.value = value;
                            }
                        });

                        const editorTags = ['popular_pages', 'site_monthly_insight', 'focus'];
                        editorTags.forEach(tag => {
                            const value = metricsData[`site_${tag}`] ?? metricsData[tag] ?? '';
                            if (tinymce.get(tag)) tinymce.get(tag).setContent(value);
                            const info = document.querySelector(`.${tag.replace(/_/g, '-')}-info`);
                            if (info) info.innerHTML = value;
                        });

                        console.log("hello")

                        


                        //report file code 

                        if(!metricsData.report_file){
                            console.log('djfdkfjd');
                        }

                        if(metricsData.report_file){
                            console.log('iam here');
                        }
                        const fileUrl = metricsData.report_file;
                        const setReportBtn = document.getElementById('set-report');
                        const fileDisplay = document.getElementById('selected-file-display');
                        const fileNameSpan = document.getElementById('file-name');
                        const fileIcon = document.getElementById('file-icon');

                        if (fileUrl) {
                            // Show file name and icon
                            setReportBtn.classList.add('d-none');
                            fileDisplay.classList.remove('d-none');
                            
                            const filename = fileUrl.split('/').pop();
                            const ext = filename.split('.').pop().toLowerCase();
                            const reportUrl = `/storage/reports/${filename}`; // <-- the file path will be fetched from storage/reports/ and downloaded (IMP)

                            // Simple icon logic
                            fileIcon.innerHTML = ext === 'pdf' ? '📄' : '📃';
                            fileNameSpan.textContent = $('.client_name').val() + " " + $('.insight-head .month-select').find('option:selected').text() + " " + "Report";

                            // Attach download link to filename if needed
                            // fileNameSpan.onclick = () => window.open(reportUrl, '_blank');
                            fileNameSpan.onclick = () => {
                                fetch(reportUrl)
                                    .then(response => {
                                        if (!response.ok) throw new Error('Network response was not ok');
                                        return response.blob();
                                    })
                                    .then(blob => {
                                        const link = document.createElement('a');
                                        link.href = window.URL.createObjectURL(blob);
                                        link.download = $('.client_name').val() + " " + $('.insight-head .month-select').find('option:selected').text() + " " + "Report"; // filename for download

                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                        URL.revokeObjectURL(link.href);
                                    })
                                    .catch(error => {
                                        console.error('Error fetching or downloading the pdf:', error);
                                        alert('Error fetching or downloading the pdf');
                                    });
                            };
                            setReportBtn.classList.add('d-none');
                            fileDisplay.classList.remove('d-none');
                        } else {
                            // No file: show upload button again
                            setReportBtn.classList.remove('d-none');
                            fileDisplay.classList.add('d-none');
                        }





                    } 
                    else {
                        const setReportBtn = document.getElementById('set-report');
                        const fileDisplay = document.getElementById('selected-file-display');
                        setReportBtn.classList.remove('d-none');
                        fileDisplay.classList.add('d-none');
                        infoToast("No metrics found for the selected month.");
                        
                        const resetTags = [
                            'performance', 'accessibility', 'best_practice', 'seo',
                            'site_health', 'site_traffic', 'site_contacts',
                            'engaged_traffic', 'engaged_traffic_rate',
                            'popular_pages', 'site_monthly_insight', 'focus'
                        ];

                        resetTags.forEach(tag => {
                            const counter = document.querySelector(`.counter[data-tag="${tag}"]`) || document.querySelector(`[data-tag="${tag}"]`);
                            if (counter) {
                                counter.setAttribute('data-countTo', 0);
                                counter.textContent = tag.includes('popular') || tag.includes('insight') || tag === 'focus' ? '' : 0;
                            }

                            const input = document.getElementById(tag === 'site_health' ? 'site_health_score' : tag);
                            if (input) input.value = tag.includes('popular') || tag.includes('insight') || tag === 'focus' ? '' : 0;
                        });

                        const editorTags = ['popular_pages', 'site_monthly_insight', 'focus'];
                        editorTags.forEach(tag => {
                            if (tinymce.get(tag)) tinymce.get(tag).setContent('');
                            const info = document.querySelector(`.${tag.replace(/_/g, '-')}-info`);
                            if (info) info.innerHTML = '';
                        });
                    }

                    prepareMonthChartData(data);
                    prepareYearChartData(data);

                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });


        // Make Content Editable
        document.querySelectorAll('.text h2.counter').forEach(function(counter) {
            counter.addEventListener('click', function() {
                this.setAttribute('contenteditable', 'true');
                this.focus();
            });

            counter.addEventListener('blur', function() {
                this.removeAttribute('contenteditable'); // Optional: make it non-editable again after editing
                const input = this.closest('.text').querySelector('input[type="hidden"]');
                if (input) {
                input.value = this.textContent.trim();
                }
            });

            counter.addEventListener('keydown', function(event) {
                if (event.key === 'Enter') {
                event.preventDefault();
                this.blur(); 
                }
            });
        });

        // Save Data 
document.getElementById('save-report').addEventListener('click', function() {
    var performance = document.getElementById('performance').value;
    var accessibility = document.getElementById('accessibility').value;
    var best_practice = document.getElementById('best_practice').value;
    var seo = document.getElementById('seo').value;
    var site_health_score = document.getElementById('site_health_score').value;
    var month = document.getElementById('month').value;
    var popular_pages = document.getElementById('popular_pages').value;
    var site_monthly_insight = tinymce.get('site_monthly_insight')?.getContent() || '';
    var focus = tinymce.get('focus')?.getContent() || '';
    var site_traffic = parseInt((document.getElementById('site_traffic').value).replace(/,/g, ''), 10);
    var site_contacts = document.getElementById('site_contacts').value;
    var engaged_traffic = document.getElementById('engaged_traffic').value;
    var engaged_traffic_rate = document.getElementById('engaged_traffic_rate').value;
    
    

    
    // Create a FormData object to handle the file upload
    var formData = new FormData();
    
    // Add all form fields to FormData
    formData.append('performance', performance);
    formData.append('accessibility', accessibility);
    formData.append('best_practice', best_practice);
    formData.append('seo', seo);
    formData.append('site_health_score', site_health_score);
    formData.append('project_id', {{ $project->id }});
    formData.append('month_id', month);
    formData.append('site_traffic', site_traffic);
    formData.append('site_contacts', site_contacts);
    formData.append('engaged_traffic', engaged_traffic);
    formData.append('engaged_traffic_rate', engaged_traffic_rate);
    formData.append('popular_pages', popular_pages);
    formData.append('site_monthly_insight', site_monthly_insight);
    formData.append('focus', focus);
    
    
    if (reportFile) {
        formData.append('reportFile', reportFile);
    }
    
   

    
    fetch("{{ route('save-metrics-by-month') }}", {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          
        },
        body: formData
    })
    .then(async response => {
        const contentType = response.headers.get("content-type");
        if (!response.ok) {
            const text = await response.text();
            throw new Error(`Server error: ${text}`);
        }

        if (contentType && contentType.includes("application/json")) {
            return response.json();
        } else {
            const text = await response.text();
            throw new Error(`Expected JSON, got: ${text}`);
        }
    })
    .then(data => {
        if (data.success) {
            console.log(data);
            // Show toast
            successToast("Report saved successfully!");
        } else {
            console.error('Error saving report:', data.message);
        }
    })
    .catch(error => {
        console.error("Error:", error);
    });
});

        // Tab functionality
        document.querySelectorAll('.data-tab').forEach(function (tab) {
            tab.addEventListener('click', function (event) {
                event.preventDefault();

                // Remove 'active' from all tabs
                document.querySelectorAll('.data-tab').forEach(function (t) {
                    t.classList.remove('active');
                });

                // Add 'active' to clicked tab
                this.classList.add('active');

                // Hide all tab content rows
                document.querySelectorAll('.analytics-rows').forEach(function (row) {
                    row.style.display = 'none';
                });


                // Update tab header label
                document.querySelector('.tab-head').textContent = this.textContent;

                // Show the target content row
                var target = this.getAttribute('data-target');
                var targetElement = document.querySelector('.' + target);
                if (targetElement) {
                    targetElement.style.display = 'flex'; 
                }

                 // Hide all headers
                 document.querySelectorAll('.head').forEach(function (element) {
                    element.classList.add('d-none');
                });

                // Show the selected header
                let header = this.getAttribute('data-header');
                document.querySelectorAll('.' + header).forEach(function (element) {
                    element.classList.remove('d-none'); 
                });
            });
        });


        // Trigger click on default active tab on page load
        const defaultTab = document.querySelector('.data-tab.active');
        if (defaultTab) defaultTab.click();
        
    });
    
</script>
<script>
     document.addEventListener('DOMContentLoaded', function () {
        const modal = document.getElementById('staticBackdrop');
        modal.addEventListener('shown.bs.modal', function () {
            setTinyMceEditor('#popular_pages');
        });

        modal.addEventListener('hidden.bs.modal', function () {
            if (tinymce.get('popular_pages')) {
                tinymce.get('popular_pages').remove();
            }
        });

        document.addEventListener('focusin', (e) => {
            if (e.target.closest(".tox-tinymce-aux, .moxman-window, .tam-assetmanager-root") !== null) {
            e.stopImmediatePropagation();
            }
        });
        document.querySelectorAll('.save-changes').forEach(button => {
            button.addEventListener('click', function () {
                let modal = this.closest('.modal');
                const target = this.getAttribute('data-target');
                const editortab = modal.querySelector('textarea').getAttribute('id');
                const editorContent = tinymce.get(editortab).getContent();
                document.querySelector("." + target).innerHTML = editorContent;
                modal.querySelector('button[data-bs-dismiss="modal"]').click();
            });
        });

    });
</script>


@endpush






@endsection
