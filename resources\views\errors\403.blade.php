<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>403 Forbidden</title>
  <style>
    * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #0a0a0a;
  color: white;
}

canvas#bg-canvas {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  font-family: "Futura Std", sans-serif;
  color: #fff;
  max-width: 90%;
}

.overlay h1 {
  font-size: 8rem;
  font-weight: 700;
  animation: glitch 1s infinite;
  color: #ff4c00; 
}

.overlay p {
  font-size: 1.5rem;
  margin-bottom: 10px;
  font-weight: 500;
  animation: fadeInUp 0.8s ease-in-out;
  opacity: 0.9;
}

.overlay a {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  color: #fff;
  border: 2px solid #ff4c00;
  transition: all 0.3s ease;
}

.overlay a:hover {
  background-color: #ff4c00;
  color: #0a0a0a;
  transform: scale(1.05);
}


.overlay a:hover {
  background: white;
  color: black;
}

@keyframes glitch {
  0% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
  100% { transform: translate(0); }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

  </style>
</head>
<body>
  <canvas id="bg-canvas"></canvas>
  <div class="overlay">
    <h1>403</h1>
    <p>Agent, your clearance level is not high enough.</p>
    <p>Mission access: DENIED 🚫</p>
    <a href="{{ url()->previous() }}">Retreat to Base</a>
    <form action="{{ route('logout') }}" method="post" style="margin-top: 20px;">
        @csrf
        @method('POST')
        <div class="navcall me-3 d-flex justify-content-center align-items-center" ><a href="#" id="btn-logout" class="cta call text-decoration-none text-uppercase mt-0 text-white">Logout</a> </div>
    </form>

  </div>
  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.160.0/three.min.js"></script>
  <script src="script.js"></script>



  <script>
    const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(75, window.innerWidth/window.innerHeight, 0.1, 1000);
camera.position.z = 30;

const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('bg-canvas'), antialias: true, alpha: true });
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setPixelRatio(window.devicePixelRatio);


window.addEventListener('resize', () => {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
});



const particlesCount = 8000;
const geometry = new THREE.BufferGeometry();
const positions = new Float32Array(particlesCount * 3);

for (let i = 0; i < particlesCount * 3; i++) {
  positions[i] = (Math.random() - 0.5) * 300;
}

geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

const material = new THREE.PointsMaterial({
  color: 0xff4e4e,
  size: 0.5,
  transparent: true,
  blending: THREE.AdditiveBlending
});

const particles = new THREE.Points(geometry, material);
scene.add(particles);


const ringGeometry = new THREE.RingGeometry(10, 12, 64);
const ringMaterial = new THREE.MeshBasicMaterial({ color: 0xffffff, side: THREE.DoubleSide, wireframe: true });
const ring = new THREE.Mesh(ringGeometry, ringMaterial);
scene.add(ring);


let mouseX = 0, mouseY = 0;
document.addEventListener('mousemove', (event) => {
  mouseX = (event.clientX / window.innerWidth) * 2 - 1;
  mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
});

let clock = new THREE.Clock();

function animate() {
  requestAnimationFrame(animate);

  const elapsed = clock.getElapsedTime();

  particles.rotation.y += 0.0005;
  particles.rotation.x += 0.0003;

  ring.rotation.x = Math.sin(elapsed * 0.3) * 0.5;
  ring.rotation.y = elapsed * 0.2;


  camera.position.x += (mouseX * 5 - camera.position.x) * 0.05;
  camera.position.y += (mouseY * 5 - camera.position.y) * 0.05;
  camera.lookAt(scene.position);

  renderer.render(scene, camera);
}

animate();

  </script>

<script>
    const logoutBtn = document.getElementById("btn-logout");
    if (logoutBtn) {
        logoutBtn.addEventListener("click", function (e) {
            e.preventDefault();
            const form = this.closest("form");
            if (form) {
                form.submit();
            }
        });
    }
    
</script>
</body>
</html>

