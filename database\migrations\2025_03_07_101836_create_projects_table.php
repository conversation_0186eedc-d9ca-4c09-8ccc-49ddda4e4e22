<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('job_code')->nullable();
            $table->foreignId('status_id')->nullable()->constrained('status')->onDelete('set null');
            $table->foreignId('client_id')->nullable()->constrained('clients')->onDelete('set null');
            $table->foreignId('assigned_by')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('timeline_id')->nullable()->constrained('timeline')->onDelete('set null');
            $table->foreignId('category_id')->nullable()->constrained('categories')->onDelete('set null');
            $table->foreignId('phase_id')->nullable()->constrained('phases')->onDelete('set null');
            $table->text('harvest_link')->nullable();
            $table->string('invoice_schedule')->nullable();
            $table->enum('kickoff_type', ['yes', 'no'])->nullable()->default('no');
            $table->string('kickoff_title')->nullable();
            $table->text('kickoff_description')->nullable();
            $table->string('kickoff_file')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropForeign(['client_id']);
            $table->dropForeign(['timeline_id']);
            $table->dropForeign(['assigned_by']);
            $table->dropForeign(['category_id']);
            $table->dropForeign(['phase_id']);
        });
        Schema::dropIfExists('projects');
    }
};
