<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document - Home Page</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <!-- Latest compiled and minified CSS -->
    <!-- <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css"> -->

    <!-- jQuery library -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <!-- Latest compiled JavaScript -->
    <!-- <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script> -->

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }

        .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;
            overflow:scroll;

            left: 0;
            z-index: 0;
            height: calc(100vh - 50px);
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        /* .content-body{
        } */
        .add-user-btn-div{
            display: flex;
            justify-content: end;
        }
        .content-body-empty{
            padding:5px 30px;
            /* height: 30px; */
            text-align: center;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
        .not_authorized_error_msg{
            text-align: center;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
        .content-body-content{
            overflow:scroll;
            margin-top:5px;
            /* height: calc(100vh - 110px); */
        }
        .table{
            border:1px solid grey;
        }
        thead{
            font-weight:bold;
        }
        .user-id-col{
            width:3%;
        }
        .user-name-col{
            width:10%;
        }
        .user-email-col{
            width:1%;
            overflow:scroll;
        }
        .user-permissions-col{
            overflow:scroll;
        }
        .actions-col{
            width:3%;
            overflow:scroll;
        }
        label{
            width:10%;
        }
    </style>
</head>
<body>
    <div class="@if(Session::get('logged-in-message')) loggedInMsg @endif">
        {{ Session::get('logged-in-message') }}
    </div>

    <div class="navbar">
        <div class="flt-lft">

        </div>
        <div class="flt-rgt">
            <!-- <div class="dropdown">
                <span class="dropdown-btn">Welcome! {{ Auth::user()->name }}
                    <i class="fa fa-caret-down"></i>
                </span>
                <div class="dropdown-content">
                    <a href="{{ route('profile') }}" class="">Your Profile</a>
                    <a href="{{ route('logout') }}">Logout</a>
                </div>
            </div> -->
            <div class="dropdown">
                <span class="dropdown-toggle" data-bs-toggle="dropdown">
                    Welcome! {{ Auth::user()->name }}
                </span>
                <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ route('profile') }}">Your Profile</a></li>
                <li><a class="dropdown-item" href="{{ route('logout') }}">Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <div class="main-div">

        <div class="content-div">
                <div class="content-heading d-flex">
                        <h4>
                        @if(request()->route()->named('add-user'))
                            Accounts Information
                        @endif
                    </h4>
                </div>
            <div class="content-body">
                @if(request()->route()->named('add-user'))
                    @if(Auth::user()->role_id=='1')
                        <div class="add-user-btn-div">
                            <a href={{ route('add-user') }}><button>Add User <i class="fa fa-plus"></i></button></a>
                        </div>
                    @endif
                    @if(isset($not_authorized_error_msg) && $not_authorized_error_msg!='')
                        <div class="content-body-empty">
                            <div class="not_authorized_error_msg">{{ $not_authorized_error_msg }}</div>
                        </div>
                    @else
                        <div class="content-body-content">
                            <table class="table table-responsive">
                                <thead>
                                    <tr>
                                        <td>Id</td>
                                        <td>Name</td>
                                        <td>Email</td>
                                        <td>Role</td>
                                        <td>Permissions</td>
                                        <td>Actions</td>
                                    </tr>
                                </thead>
                                <tbody class="table-body">
                                    <!-- @if(isset($users) && !empty($users))
                                        @foreach($users as $users)
                                            <tr>
                                                <td class="user-id-col">
                                                    {{ $users->id }}
                                                </td>
                                                <td class="user-name-col">
                                                    {{ $users->name }}
                                                </td>
                                                <td class="user-email-col">
                                                    {{ $users->email }}
                                                </td>
                                                <td class="user-role-col">
                                                    {{ $users->role }}
                                                </td>
                                                <td class="user-permissions-col">
                                                    {{ $users->permissions }}
                                                </td>
                                                <td class="actions-col">
                                                    <form method="post" action="{{ route('edit-user', ['id' => $users->id]) }}" class="edit-project-form d-inline" name="edit-project-form">
                                                        @csrf
                                                        <a href="" class="p-1" onclick="event.preventDefault(); this.closest('form').submit()"><i class='fa fa-edit'></i></a>
                                                    </form>
                                                    <form method="post" action="{{ route('delete-user', ['id' => $users->id]) }}" class="delete-project-form d-inline" name="delete-project-form">
                                                        @csrf
                                                        <a href="" class="p-1" onclick="event.preventDefault(); this.closest('form').submit()"><i class='fa fa-trash'></i></a>
                                                    </form>
                                                </td>
                                            </tr>
                                        @endforeach
                                        @else
                                            <tr>No User Found</tr>
                                    @endif -->
                                </tbody>
                            </table>
                        </div>
                    @endif
                @endif
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function(){



            function load_users_in_dropdown(){
                $.ajax({
                    url: "{{ route('accounts') }}",
                    method: "get",
                    data: {"fetch_all_users_data":1},
                    success: function(response){
                        // console.log(response);
                        if (response.length === 0) {
                            $('.table-body').append('<tr><td colspan="6">No User Found</td></tr>');
                        }
                        else{
                            $.each(response, function(index, users) {
                                var editUserRoute = "{{ route('edit-user', ['id' => 'temp']) }}".replace('/temp', '');
                                var deleteUserRoute = "{{ route('delete-user', ['id' => 'temp']) }}".replace('/temp', '');
                                $('.table-body').append(
                                    $('<tr>')
                                        .append('<td class="user-id-col">' + users.id + '</td>')
                                        .append('<td class="user-name-col">' + users.name + '</td>')
                                        .append('<td class="user-email-col">' + users.email + '</td>')
                                        .append('<td class="user-role-col">' + users.role + '</td>')
                                        .append('<td class="user-permissions-col">' + users.permissions + '</td>')
                                        .append(
                                            $('<td class="actions-col">')
                                            .append(
                                                $('<form method="post" action="' + editUserRoute + '/' + users.id + '" class="edit-project-form d-inline" name="edit-project-form">')
                                                    .append('<input type="hidden" name="_token" value="' + $('meta[name="csrf-token"]').attr('content') + '">')
                                                    .append('<a href="" class="p-1 edit-link"><i class="fa fa-edit"></i></a>')
                                                    .on('click', '.edit-link', function(event) {
                                                        event.preventDefault();
                                                        $(this).closest('form').submit();
                                                    })
                                            )
                                            .append(
                                                $('<form method="post" action="' + deleteUserRoute + '/' + users.id + '" class="delete-project-form d-inline" name="delete-project-form">')
                                                    .append('<input type="hidden" name="_token" value="' + $('meta[name="csrf-token"]').attr('content') + '">')
                                                    .append('<input type="hidden" name="_method" value="DELETE">')
                                                    .append('<a href="" class="p-1 delete-link"><i class="fa fa-trash"></i></a>')
                                                    .on('click', '.delete-link', function(event) {
                                                        event.preventDefault();
                                                        $(this).closest('form').submit();
                                                    })
                                            )
                                        )
                                );
                            });
                        }
                    }
                })
            }
            load_users_in_dropdown();
        });
    </script>
</body>
</html>

