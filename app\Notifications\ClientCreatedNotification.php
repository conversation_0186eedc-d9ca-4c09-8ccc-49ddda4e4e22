<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class ClientCreatedNotification extends Notification //implements ShouldQueue we will set it when queues will working
{
    use Queueable;

    protected $client;

    protected $randomPassword;

    public function __construct($client, $randomPassword)
    {
        $this->client = $client;
        $this->randomPassword = $randomPassword;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $url = route('login');

        return (new MailMessage)
            ->subject('New Client Created: ' . $this->client->name)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have been added as a new client named ' . $this->client->name . '.')
            ->line('Email: ' . $this->client->email)
            ->line('Password: ' . $this->randomPassword)
            ->action('Login here', $url)
            ->line('Thank you for using our application!');
    }
}
