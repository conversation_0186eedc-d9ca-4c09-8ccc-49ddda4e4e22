@extends('layout.app')
@section('title', 'Edit Task')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">

                    <h4>
                        @if(request()->route()->named('edit-task'))
                            Edit Task
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('edit-task'))
                        @if(isset($task_not_found_error) && $task_not_found_error!='')
                            <div class="task_not_found_error">{{ $task_not_found_error }}</div>
                        @else
                        {{-- @dd($task) --}}
                            @if(Session::has('task_updated_success_message'))
                                <div class="bg-success text-light text-center mx-auto my-1 p-2 w-50 rounded">
                                    {{ Session::get('task_updated_success_message') }}
                                </div>
                            @elseif(Session::has('task_not_updated_success_message'))
                                <div class="bg-danger text-light text-center mx-auto my-1 p-2 w-50 rounded">
                                    {{ Session::get('task_not_updated_success_message') }}
                                </div>
                            @endif
                            <div class="task-details-div mx-auto rounded">
                                <form action="{{ route('update-task', ['task_id'=>request()->route("task_id")]) }}" method="post" name="edit-task-form">
                                    @csrf
                                    @method("put")
                                    <div class="task-details-div-row1 d-flex mb-1">
                                        <label class="task-details-div-label task_name_label">Name: </label>
                                        <input type="text" class="task-details-div-span task_name_span @error('task_name') is-invalid @enderror" name="task_name" value="{{ $task->name }}">
                                        @error('task_name')
                                            <div>{{ $message }}</div>
                                        @enderror
                                    </div>
                                    @if(Auth::user()->role_id == '1')
                                        <div class="task-details-div-row1 d-flex mb-1">
                                            
                                            <label class="task-details-div-label task_status_label">Status: </label>
                                            <div class="task-details-div-span input-group d-flex align-items-center gap-2">
                                            @foreach($status as $s)
                                                <input type="radio" name="task_status" id="{{ $s->name }}" value="{{ $s->id }}" @if($task->status == 'Started') checked @endif><label for="{{ $s->name }}">{{ $s->name }}</label>
                                            @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    <button type="submit">Update</button>
                                </form>
                            </div>

                            <br>

                            <div class="comment-section mx-auto col-10">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="comment-section-heading">Comment Section</div>
                                    <button class="btn refresh-comments-button" title="Refresh Comments"><a>Refresh <i class="fa fa-refresh"></i></a></button>
                                </div>

                                <div class="comment-section-body p-1 mx-auto rounded border border-secondary">
                                    <div class="comment-section-comments">

                                    </div>
                                    <div class="comment-section-input">
                                        <div class="mention-users-div">
                                            <div class="d-flex justify-content-end mx-auto col-11">
                                                <button class="btn-close d-none fs-6"></button>
                                            </div>
                                            <div class="comment-input-user-selection col-11 mx-auto border border-secondary rounded d-none" placeholder="Comment Anything!" name="comment-body" autofocus>
                                                
                                            </div>
                                        </div>
                                        <form method="post"  name="comment-form" class="d-flex mt-1 w-100">
                                            @csrf
                                            <div class="d-flex border rounded col-12">
                                                <button type="button" class="mention-btn btn btn-primary border-0 rounded-0 rounded-start" title="Mention User">
                                                    <i class="fa fa-at"></i>
                                                </button>
                                                <textarea class="comment-input form-control border-0 rounded-0" placeholder="Comment Anything!" name="comment-body" autofocus></textarea>
                                                <button type="submit" class="comment-send-button btn btn-primary border-0 rounded-0 rounded-end" title="Publish Comment">
                                                    <i class="fa fa-paper-plane"></i>
                                                </button>
                                            </div>
                                        </form>
                                        @if($errors->has('comment-body'))
                                            <div class="text-center text-danger">{{ $errors->first('comment-body') }}</div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection

    @push('styles')
        <style>
            body{
                color:black;
                margin:0px;
            }
            .navbar{
                height:50px;
                padding:10px 50px;
                margin:0px;
                align-items:center;
                background-color:lightgrey;
            }
            .flt-lft{
                float:left;
                margin-right:5px;
                align-items:center;
            }
            .flt-rgt{
                float:right;
            }
            .flt-rgt div{
                padding:0 5px;
                margin:0px 3px;
            }
            .flt-rgt a:hover{
                text-decoration:none;
            }
            .active-page{
                font-weight:bold;
            }
            .dropdown-content {
                display: none;
                position: absolute;
                background-color: #f9f9f9;
                min-width: 160px;
                box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
                z-index: 1;
            }
            .dropdown-content a {
                float: none;
                color: black;
                padding: 12px 16px;
                text-decoration: none;
                display: block;
                text-align: left;
            }
            .dropdown-content a:hover {
                background-color: #ddd;
            }
            .dropdown:hover .dropdown-content {
                display: block;
            }
            .loggedInMsg{
                font-size: 13px;
                padding: 2px 0px;
                margin-bottom:3px;
                text-align: center;
                color: white;
                background-color: #1a8234;
            }

            .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
            .section-div{
                width:5%;
                box-sizing: border-box;
                border-right:0.5px solid grey;
                background-color:lightgrey;
                overflow:scroll;

                left: 0;
                z-index: 0;
                height: calc(100vh - 50px);
            }
            .section-div a{
                text-decoration:none;
            }
            .section-div a:hover{
                background-color:lightblue;
            }
            .section-div div{
                color:black;
                padding:10px;
            }
            .navbar-menu-button-div{
                display:flex;
            }
            .navbar-menu-button{
                margin:auto;
            }
            .sidebar-link-icon{
                text-align:center;
                font-size:20px;
                padding:10px 0px;
                width:100%;
            }
            .sidebar-link-text{
                display:none;
            }
            .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
                transition: all 0s ease;
            }
            .content-div{
                width:100%;
                padding:10px;
                overflow:scroll;
            }
            /* .content-heading{
                didplay:flex;
            } */
            .back-btn{
                background-color: black;
                color: white;
                height: 27px;
                width: 25px;
                border-radius: 50%;
                font-size: 15px;
                padding: 5px;
                margin-right: 10px;
            }
            .content-body{
                /* padding:0px 50px;
                display:flex;
                justify-content:center; */
            }
            .see-all-tasks-btn-div{
                display: flex;
                justify-content: end;
            }
            form[name="edit-task-form"]{
                width:100%;
                text-align:center;
            }
            .task-details-div{
                margin-top:10px;
                padding:10px;
                width:50%;
                border:1px solid grey;
                display: flex;
                justify-content: center;
            }
            .task-details-div-label{
                width:25%;
                text-align:left;
            }
            .task-details-div-span{
                width:75%;
                text-align:left;
            }
            .task_not_found_error{
                text-align: center;
                margin-top:10px;
                padding: 10px;
                background-color: pink;
                color: red;
                font-weight: bold;
            }
            .comment-section-body {
                /* height: 200px; */
                position: relative;
            }
            .comment-section-comments{
                height: 300px;
                overflow: scroll;
            }
            .comment-section-comment-row {
                /* display: flex;
                height: 45px;
                border-bottom: 1px solid black;
                padding: 10px 0; */
                margin:3px 0px;
            }
            .comment-user-time{
                font-size:13px;
            }
            .reply-body-div {
                padding-left:10%;
            }
            
            button.small-font{
                font-size:13px;
                padding:0px 3px;
            }
            .small-font{
                font-size:13px;
            }
            .edit-comment-input {
                height:30px;
                min-height:30px;
                max-height:50px;
                display:none;
                border: none;
                border-bottom: 1px solid black; /* Initial bottom border */
                outline: none;
            }
            .edit-comment-send-btn{
                display:none;
            }
            .edit-comment-cancel-input{
                display:none;
            }
            .comment-reply-input {
                height:30px;
                min-height:30px;
                max-height:50px;
                display:none;
                border: none;
                border-bottom: 1px solid black; /* Initial bottom border */
                outline: none;
            }
            .comment-reply-send-btn{
                display:none;
            }
            .comment-reply-cancel-input{
                display:none;
            }
            .comment-input-user-selection {
                height:auto;
                max-height:100px;
                overflow:scroll;
            }
            .comment-input-user-selection div:hover, label:hover {
                background-color:orange;
                cursor:pointer;
            }
            .comment-input {
                /* position: absolute;
                bottom: 0; */
                height:30px;
                min-height:30px;
                max-height:100px;
                padding:3px 5px;
            }
            .mention-users-div{
                position: absolute;
                bottom: 50px;
                left: 0;
                width: 100%;
                background-color: white;
            }
            form[name="edit-comment-form"], form[name="comment-reply-form"]{
                display:inline;
            }
        </style>
    @endpush

    @push('script')
    <script>
        var currentUserId = {{ auth()->id() }};
        // console.log("auth user: "+currentUserId);
    </script>

    <script>

        $(document).ready(function(){
            function open_dropdown(){
                $('.btn-close').removeClass('d-none').addClass('d-block');
                $('.comment-input-user-selection').removeClass('d-none').addClass('d-block');
            }
            function close_dropdown(){
                $('.btn-close').removeClass('d-block').addClass('d-none');
                $('.comment-input-user-selection').removeClass('d-block').addClass('d-none');
            }

            $('.mention-btn').on('click', function(event){
                event.preventDefault();

                open_dropdown();
                load_task_users_in_dropdown();
            })

           $('.comment-input').on('input focus', function(){
                // console.log($(this).val() + " " + $(this).val().length);
                
                if($('.comment-input').val().slice(-1)==='@'){//if latest character is '@'
                    load_task_users_in_dropdown();
                    open_dropdown()
                }
                else{
                    close_dropdown();
                }

                if($('.comment-input').val() == ''){ //if input is empty
                    close_dropdown();
                }
           })

           $('.btn-close').on('click', function(){
               if($('.btn-close').hasClass('d-block')){
                    close_dropdown();
               }
           })

           let mention_users_in_comment = []; // Array to hold user objects
           var index=0;
           let checked_checkbox = '';
           function load_task_users_in_dropdown(){
                $.ajax({
                    url: "{{ route('load-task-users', ['task_id' => request()->route('task_id')]) }}",
                    method: "get",
                    success: function(response){
                        $('.comment-input-user-selection').html("");
                        if (response !== '' && Array.isArray(response)) {
                            response.forEach(function(user) {
                                console.log(typeof mention_users_in_comment);
                                let checked_checkbox = '';

                                // Check if the current 'user' from the response is present in the
                                // 'mention_users_in_comment' array based on their IDs.
                                const isMentioned = mention_users_in_comment.some(mentionedUser => mentionedUser.id == user.id);

                                if (isMentioned) {
                                    console.log("checked");
                                    checked_checkbox = 'checked';
                                } else {
                                    checked_checkbox = '';
                                }

                                $('.comment-input-user-selection').append(`<div class='p-1'><input class='me-3' type='checkbox' id="${user.name}" value="${user.name}" data-user_id="${user.id}" data-user_name="${user.name}" ${checked_checkbox}><label for="${user.name}">${user.name}</label></div>`);
                            });
                        }
                    }
                })
            }

            $(document).on('click', 'div.comment-input-user-selection>div>label', function(event){
    event.preventDefault();
    close_dropdown();

    var event_input = $(this).prev('input');
    event_input.prop('checked', true);
    console.log($(this).prev('input').data("user_name"));

    if (event_input.data("user_id") && event_input.data("user_name")) {
        const userId = event_input.data("user_id");
        const userName = event_input.data("user_name") || '';

        const existingUserIndex = mention_users_in_comment.findIndex(user => user.id == userId);

        if (existingUserIndex === -1) {
            mention_users_in_comment.push({ id: userId, name: userName });
            console.log("Added mentioned user:", mention_users_in_comment);
        } else {
            mention_users_in_comment[existingUserIndex].name = userName;
            console.log("Updated mentioned user:", mention_users_in_comment);
        }
    }

    // TO DO - append more values in mention_users_in_comment object

    // Problematic section: Trying to access 'name' directly on the array
    if ($('.comment-input').val().length > 0) {
        if($('.comment-input').val().slice(-1) === '@'){
            // This line is incorrect because mention_users_in_comment is an array
            // $('.comment-input').val($('.comment-input').val().slice(0, -1) + mention_users_in_comment['name']);

            // You likely want to append the selected user's name here
            const selectedUserName = event_input.data("user_name");
            if (selectedUserName) {
                $('.comment-input').val($('.comment-input').val().slice(0, -1) + selectedUserName + ' '); // Append with a space
            }
        }
    }

    console.log(event.target);
    console.log(mention_users_in_comment);
    index++; // Assuming 'index' is defined elsewhere
});
            $(document).on('click', '.comment-send-button', function(event){
                event.preventDefault();

                var formData = new FormData($(this).closest('form')[0]);
                $.ajax({
                    url: "{{ route('save-comment', ['task_id' => request()->route('task_id')]) }}",
                    method: "post",
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        load_comments(false);
                        $('.comment-input').val('');
                    },
                    error: function(xhr, status, error) {
                        console.error('Error saving comment:', error);
                    }
                });
            })

            function formatChatTime(isoTimestamp) {
                const now = new Date();
                const date = new Date(isoTimestamp);

                const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                const yesterday = new Date(today);
                yesterday.setDate(today.getDate() - 1);
                const tomorrow = new Date(today);
                tomorrow.setDate(today.getDate() + 1);

                const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

                const diffInMilliseconds = now.getTime() - date.getTime();
                const diffInMinutes = Math.floor(diffInMilliseconds / (1000 * 60));
                const diffInHours = Math.floor(diffInMilliseconds / (1000 * 60 * 60));
                const diffInDays = Math.floor(diffInHours / 24);

                if (messageDate.getTime() === today.getTime() && diffInMinutes < 60 && diffInMinutes > 0) {
                    // Today and within 1 hour: Show "X min ago HH:MM AM/PM"
                    return diffInMinutes + (diffInMinutes===1 ? " min ago " : " mins ago ") + date.toLocaleTimeString([], { hour: "numeric", minute: "numeric", hour12: true });
                } else if (messageDate.getTime() === today.getTime() && diffInHours < 24 && diffInHours > 0) {
                    // Today and within 24 hours: Show "X hrs ago HH:MM AM/PM"
                    return diffInHours + (diffInHours===1 ? " hr ago " : " hrs ago ") + date.toLocaleTimeString([], { hour: "numeric", minute: "numeric", hour12: true });
                } else if (messageDate.getTime() === yesterday.getTime()) {
                    // Yesterday: Show "Yesterday" and time (12-hour format with AM/PM)
                    return "Yesterday " + date.toLocaleTimeString([], { hour: "numeric", minute: "numeric", hour12: true });
                } else if (messageDate.getTime() === today.getTime()) {
                    // Today, but older than 24 hours or 0 minutes: Show HH:MM AM/PM
                    return date.toLocaleTimeString([], { hour: "numeric", minute: "numeric", hour12: true });
                } else if (messageDate.getTime() === tomorrow.getTime()) {
                    // Tomorrow: Show "Tomorrow" and time (12-hour format with AM/PM)
                    return "Tomorrow " + date.toLocaleTimeString([], { hour: "numeric", minute: "numeric", hour12: true });
                } else if (diffInDays < 7 && diffInDays > 0) {
                    // Within the past week: Show day of the week, date, and time (12-hour format with AM/PM)
                    return date.toLocaleDateString([], { weekday: "short", month: "short", day: "numeric" }) + " " + date.toLocaleTimeString([], { hour: "numeric", minute: "numeric", hour12: true });
                } else {
                    // Older than a week: Show full date (month, day, year)
                    return date.toLocaleDateString([], { month: "short", day: "numeric", year: "numeric" });
                }
            }

            function load_reply_counts(comment_id){
                return $.ajax({
                    url: "{{ route('show-comments', ['task_id' => request()->route('task_id')]) }}",
                    method: "get",
                    data: {'comment_id': comment_id, 'show-reply-count' : 1},
                    // success: function(response) {
                        // console.log("comment_id: "+ comment_id + " has " + response + " replies");
                        // console.log(response);
                    //     return response;
                    // }
                });
            }

            function checkChildComments(comment_id){
                return $.ajax({
                    url: "{{ route('show-comments', ['task_id' => request()->route('task_id')]) }}",
                    method: "get",
                    data: {'comment_id': comment_id},
                    // success: function(response) {
                    //     console.log(response);
                    //     return response;
                    // }
                });
            }

            let refreshEnabled = true; // Global flag to control refresh

            $(document).on('click', '.see-replies-btn', async function (event) {
                event.preventDefault();

                var event_comment_id = $(event.target).data("comment_id");
                var $seeRepliesButton = $(".see-replies-btn[data-comment_id='" + event_comment_id + "']");
                var $icon = $seeRepliesButton.find('i');
                var $replySection = $(".comment-reply-section[data-comment_id='" + event_comment_id + "']");

                if ($icon.hasClass("fa-caret-down")) {
                    $icon.removeClass("fa-caret-down").addClass('fa-caret-up');

                    let level = 1;
                    let parentReplySection = $replySection.parent().closest('.comment-reply-section');
                    // console.log(parentReplySection)

                    while(parentReplySection.length){
                        level++;
                        parentReplySection = parentReplySection.parent().closest('.comment-reply-section');
                    }

                    await fetchAndDisplayReplies(event_comment_id, currentUserId, level);
                } 
                else {
                    $icon.removeClass("fa-caret-up").addClass('fa-caret-down');
                    $replySection.empty();
                }
            });

            // Scroll event listener for infinite scrolling
            $('.comment-section-comments').scroll(function() {
                if ($(this).scrollTop() + $(this).innerHeight() >= $(this)[0].scrollHeight) {
                    load_comments(true); // Load more comments
                }
            });

            let offset = 0;
            const limit = 10;
            let allCommentsLoaded = false;

            async function load_comments(loadMore = false) {
                if (!refreshEnabled && !loadMore) return;

                let $commentsContainer = $('.comment-section-comments');
                let scrollPosition = 0;

                if ($('.comment-reply-input:visible').length === 0) {
                    scrollPosition = $commentsContainer.scrollTop();
                }

                if (allCommentsLoaded && loadMore) {
                    return; // No more comments to load
                }

                let url = "{{ route('show-comments', ['task_id' => request()->route('task_id')]) }}";

                if (loadMore) {
                    url += `?offset=${offset}&limit=${limit}`;
                }

                $.ajax({
                    url: url,
                    method: "get",
                    data: {},
                    success: async function (response) {
                        if (!loadMore) {
                            $('.comment-section-comments').html("");
                            offset = 0;
                            allCommentsLoaded = false;
                        }

                        if (response.length === 0 && !loadMore) {
                            $('.comment-section-comments').append("<div class='comment-section-comments d-flex align-items-center justify-content-center'>No Comments Yet..</div>");
                            allCommentsLoaded = true;
                        } else if(response.length === 0 && loadMore){
                            allCommentsLoaded = true;
                            return;
                        }
                        else {
                            for (const comment_details of response) {
                                var comment_reply_div = $("<div class='comment-reply-div' data-comment_id='" + comment_details.id + "'></div>");
                                $('.comment-section-comments').append(comment_reply_div);

                                var comment_row = $("<div class='comment-section-comment-row py-2 d-flex' id='" + comment_details.id + "'></div>");
                                var updated_at_date = new Date(comment_details.updated_at);
                                var formattedTime = formatChatTime(updated_at_date);
                                comment_details.user_name = (comment_details.user_id === currentUserId) ? "You" : comment_details.user_name;

                                if (comment_details.user_name === 'You') {
                                    comment_row.css({ "background-color": "lightgreen" });
                                } else {
                                    comment_row.css({ "background-color": "#ffda98" });
                                }

                                var userTimeDiv = $("<div class='comment-user-time ps-1 d-block col-2'></div>");
                                userTimeDiv.append("<div><a href='/users/" + comment_details.user_id + "/edit' title='" + comment_details.user_name + "'>" + comment_details.user_name + "</a><span>" + (comment_details.created_at !== comment_details.updated_at ? " (edited)" : "") + "</span></div>" + formattedTime + "</div>");
                                comment_row.append(userTimeDiv);

                                var comment_body_div = $("<div class='comment-body-div col-10 px-1 d-block'><span class='comment-body-text' data-comment_id=" + comment_details.id + ">" + comment_details.comment_body + "</span></div>");
                                comment_body_div.append("<br>");
                                if (comment_details.user_name === 'You') //authenticated user can't edit oher's messages
                                {
                                    comment_body_div.append("<button class='edit-comment-btn me-2 small-font' data-comment_id=" + comment_details.id + ">Edit</button>");
                                    comment_body_div.append("<form data-comment_id=" + comment_details.id + " name='edit-comment-form'>" + "<textarea type='text' class='edit-comment-input small-font' name='edit-comment-input'  data-comment_id=" + comment_details.id + " placeholder='Edit..' autofocus></textarea><button class='mx-1 edit-comment-send-btn small-font' data-comment_id=" + comment_details.id + ">Send</button><button class='mx-1 edit-comment-cancel-input small-font' data-comment_id=" + comment_details.id + ">Cancel</button></form>");
                                }
                                comment_body_div.append("<button class='comment-reply-btn me-2 small-font' data-comment_id=" + comment_details.id + ">Reply</button>");
                                comment_body_div.append("<form data-comment_id=" + comment_details.id + " name='comment-reply-form'>" + "<textarea type='text' class='comment-reply-input small-font' name='comment-reply-input'  data-comment_id=" + comment_details.id + " placeholder='Reply..' autofocus></textarea><button class='mx-1 comment-reply-send-btn small-font' data-comment_id=" + comment_details.id + ">Send</button><button class='mx-1 comment-reply-cancel-input small-font' data-comment_id=" + comment_details.id + ">Cancel</button></form>");
                                comment_body_div.append("<button data-comment_id=" + comment_details.id + " class='see-replies-btn me-2 small-font'><span>0</span> Replies <i class='fa fa-caret-down'></i></button");
                                var reply_count = await load_reply_counts(comment_details.id);

                                comment_row.append(comment_body_div);
                                comment_reply_div.append(comment_row);

                                $('button.see-replies-btn[data-comment_id=' + comment_details.id + '] span').text(reply_count);
                                comment_reply_div.append("<div class='comment-reply-section' data-comment_id=" + comment_details.id + "></div>");
                            }

                            if ($('.comment-reply-input:visible').length === 0) {
                                $commentsContainer.scrollTop(scrollPosition);
                            }
                            if(response.length === limit){
                                offset += limit;
                            }else{
                                allCommentsLoaded = true;
                            }
                        }
                    }
                });
            }

            async function fetchAndDisplayReplies(commentId, userId, level = 1) {
                $.ajax({
                    url: `/comment/${commentId}/replies`,
                    method: 'GET',
                    success: function (response) {
                        var $replySection = $(`.comment-reply-section[data-comment_id="${commentId}"]`);
                        $replySection.empty();

                        if (response.childCommentsData && response.childCommentsData.length > 0) {
                            response.childCommentsData.forEach(function (reply) {

                                var reply_row = $("<div class='comment-section-comment-row py-2 d-flex' id='" + reply.id + "'></div>");
                                var updated_at_date = new Date(reply.updated_at);
                                var formattedTime = formatChatTime(updated_at_date);
                                reply.user_name = (reply.user_id === userId) ? "You" : reply.user_name;

                                if (reply.user_name === 'You') {
                                    reply_row.css({ "background-color": "lightgreen" });
                                } else {
                                    reply_row.css({ "background-color": "#ffda98" });
                                }

                                var userTimeDiv = $("<div class='comment-user-time ps-1 d-block col-2'></div>");
                                userTimeDiv.append("<div><a href='/users/" + reply.user_id + "/edit' title='" + reply.user_name + "'>" + reply.user_name + "</a><span>" + (reply.created_at !== reply.updated_at ? " (edited)" : "") + "</span></div>" + formattedTime + "</div>");
                                reply_row.append(userTimeDiv);

                                var comment_body_div = $("<div class='comment-body-div col-10 d-block'><span class='comment-body-text' data-comment_id=" + reply.id + ">" + reply.comment_body + "</span></div>");
                                comment_body_div.append("<div class='edit-comment-reply-section' data-comment_id=" + reply.id + "></div>");
                                comment_body_div.append("<button class='edit-comment-btn me-2 small-font' data-comment_id=" + reply.id + ">Edit</button>");
                                comment_body_div.append("<form data-comment_id=" + reply.id + " name='edit-comment-form'>" + "<textarea type='text' class='edit-comment-input small-font' name='edit-comment-input'  data-comment_id=" + reply.id + " placeholder='Edit..' autofocus></textarea><button class='mx-1 edit-comment-send-btn small-font' data-comment_id=" + reply.id + ">Send</button><button class='mx-1 edit-comment-cancel-input small-font' data-comment_id=" + reply.id + ">Cancel</button></form>");
                                comment_body_div.append("<button class='comment-reply-btn me-2 small-font' data-comment_id=" + reply.id + ">Reply</button>");
                                comment_body_div.append("<form data-comment_id=" + reply.id + " name='comment-reply-form'>" + "<textarea type='text' class='comment-reply-input small-font' name='comment-reply-input'  data-comment_id=" + reply.id + " placeholder='Reply..' autofocus></textarea><button class='mx-1 comment-reply-send-btn small-font' data-comment_id=" + reply.id + ">Send</button><button class='mx-1 comment-reply-cancel-input small-font' data-comment_id=" + reply.id + ">Cancel</button></form>");
                                comment_body_div.append("<button data-comment_id=" + reply.id + " class='see-replies-btn me-2 small-font'><span>0</span> Replies <i class='fa fa-caret-down'></i></button");

                                reply_row.append(comment_body_div);
                                comment_body_div.css('padding-left', (level * 20) + 'px');
                                reply_row.css('border-left', '1px dotted #ccc');

                                $replySection.append(reply_row);
                                $('button.see-replies-btn[data-comment_id=' + reply.id + '] span').text(reply.nestedReplies.length);

                                $replySection.append("<div class='comment-reply-section' data-comment_id=" + reply.id + "></div>");

                                // Removed the recursive call here
                                // fetchAndDisplayReplies(reply.id, userId, level + 1);
                            });
                        }
                    }
                });
            }

            load_comments(false);
            // setInterval(load_comments, 1000); // 1 second refresh

            $('.refresh-comments-button').click(function (event) {
                refreshEnabled = true;
                load_comments(false);
            });

            $(document).on('click', '.edit-comment-btn', function (event) {
                event.preventDefault();

                var event_id = $(event.target).data('comment_id');
                // alert("edit button of comment-id: "+ event_id + " clicked");


                $('.edit-comment-input, .edit-comment-send-btn, .edit-comment-cancel-input').hide();
                $('.comment-reply-input, .comment-reply-send-btn, .comment-reply-cancel-input').hide();
                $('.edit-comment-input').val(""); //when I click on another reply button, the current reply input value should be erased

                $('.edit-comment-btn, .comment-reply-btn').show();
                $('.edit-comment-btn[data-comment_id="' + event_id + '"], .comment-reply-btn[data-comment_id="' + event_id + '"]').hide();

                $('.edit-comment-input[data-comment_id="' + event_id + '"]').show();
                $('.edit-comment-send-btn[data-comment_id="' + event_id + '"]').show();
                $('.edit-comment-cancel-input[data-comment_id="' + event_id + '"]').show();

                $('.edit-comment-input[data-comment_id="' + event_id + '"]').val($('span[data-comment_id="' + event_id + '"]').html()).attr("autofocus", true).css({"background-color":"inherit"});                
            });
            
            $(document).on('click', '.edit-comment-send-btn', function (event) {
                event.preventDefault();

                var event_id = $(event.target).data("comment_id");
                var reply_body = $('.edit-comment-input[data-comment_id="' + event_id + '"]').val();

                if (reply_body.length === 0) {
                    alert("Please enter something to edit");
                }
                else {
                    var formData = new FormData($(this).closest('form')[0]);
                    formData.append("comment_id", event_id);
                    formData.append('edit-comment' , 1);
                    $.ajax({
                        url: "{{ route('edit-comment', ['comment_id' => 'placeholder']) }}".replace('placeholder', event_id),
                        method: "post",
                        data: formData,
                        contentType: false,
                        processData: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response){
                            if(response == "1"){
                                $('.edit-comment-input[data-comment_id="' + event_id + '"]').val("").hide();
                                $('.edit-comment-send-btn[data-comment_id="' + event_id + '"]').hide();
                                $('.edit-comment-cancel-input[data-comment_id="' + event_id + '"]').hide();

                                $('.edit-comment-btn[data-comment_id="' + event_id + '"]').show();
                                $('.comment-reply-btn[data-comment_id="' + event_id + '"]').show();

                                load_comments(false);
                            }
                            // console.log(response);
                        }
                    })
                }
            });

            $(document).on('click', '.edit-comment-cancel-input', function (event) {
                event.preventDefault();

                var event_id = $(event.target).data("comment_id");
                $('.edit-comment-btn, .comment-reply-btn').show();

                $('.edit-comment-input[data-comment_id="' + event_id + '"]').hide();
                $('.edit-comment-send-btn[data-comment_id="' + event_id + '"]').hide();
                $('.edit-comment-cancel-input[data-comment_id="' + event_id + '"]').hide();


                

                refreshEnabled = true;
            });

            $(document).on('click', '.comment-reply-btn', function (event) {
                event.preventDefault();

                refreshEnabled = false;

                $('.edit-comment-input, .edit-comment-send-btn, .edit-comment-cancel-input').hide();
                $('.comment-reply-input, .comment-reply-send-btn, .comment-reply-cancel-input').hide();
                $('.comment-reply-input').val("").css({"background-color":"inherit"}); //when I click on another reply button, the current reply input value should be erased
                $('.edit-comment-btn, .comment-reply-btn').show();

                var event_id = $(event.target).data("comment_id");
                $('.edit-comment-btn[data-comment_id="' + event_id + '"]').hide();
                $('.comment-reply-btn[data-comment_id="' + event_id + '"]').hide();

                $('.comment-reply-input[data-comment_id="' + event_id + '"]').show();
                $('.comment-reply-send-btn[data-comment_id="' + event_id + '"]').show();
                $('.comment-reply-cancel-input[data-comment_id="' + event_id + '"]').show();
            });

            $(document).on('keyup', '.comment-reply-input', function (event) {
                var event_id = $(event.target).data("comment_id");
                // console.log($('.comment-reply-input[data-comment_id="' + event_id + '"]').val().length);
            });

            $(document).on('click', '.comment-reply-send-btn', function (event) {
                event.preventDefault();

                var event_id = $(event.target).data("comment_id");
                var reply_body = $('.comment-reply-input[data-comment_id="' + event_id + '"]').val();

                if (reply_body.length === 0) {
                    alert("Please enter some reply");
                }
                else {
                    var formData = new FormData($(this).closest('form')[0]);
                    formData.append("comment_id", event_id);
                    $.ajax({
                        url: "{{ route('save-reply', ['comment_id' => 'placeholder']) }}".replace('placeholder', event_id), // Corrected line
                        method: "post",
                        data: formData,
                        contentType: false,
                        processData: false,
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response){
                            console.log(response);
                            if (response.success) {
                                refreshEnabled = true;
                                load_comments(false);
                            } 
                            else {
                                console.error('Error saving reply:', response.message);
                            }
                        }
                    });
                }
            });

            $(document).on('click', '.comment-reply-cancel-input', function (event) {
                event.preventDefault();

                var event_id = $(event.target).data("comment_id");
                $('.edit-comment-btn, .comment-reply-btn').show();

                $('.comment-reply-input[data-comment_id="' + event_id + '"]').hide();
                $('.comment-reply-send-btn[data-comment_id="' + event_id + '"]').hide();
                $('.comment-reply-cancel-input[data-comment_id="' + event_id + '"]').hide();
            });


        });
    </script>
    @endpush
