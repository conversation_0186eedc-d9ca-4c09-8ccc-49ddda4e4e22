mixin progressBorder()
    svg.progress-bar(width='58', height='58', viewBox='0 0 58 58', fill='none', xmlns='http://www.w3.org/2000/svg')
        circle(cx='29.4497', cy='28.7594', r='26.5841', stroke='#D9D9D9', stroke-width='0.371195')
        path.path(d='M2.67993 28.7594C2.67993 43.5438 14.6651 55.529 29.4496 55.529C44.2341 55.529 56.2193 43.5438 56.2193 28.7594C56.2193 13.9749 44.2341 1.98969 29.4496 1.98969', stroke='', stroke-width='3.4137')


mixin plusIcon(color)
    svg(width="21", height="21", viewBox="0 0 21 21", fill="none", xmlns="http://www.w3.org/2000/svg")
        path(d="M10.6992 0.573242V20.1846", stroke=color, stroke-width="1.5")
        path(d="M20.5049 10.3789L0.893555 10.3789", stroke=color, stroke-width="1.5")


mixin addProjectPlusIcon(color)
    svg(width="21", height="21", viewBox="0 0 21 21", fill="none", xmlns="http://www.w3.org/2000/svg")
        path(d="M10.5634 0.836914V20.4482", stroke=color, stroke-width="1.5")
        path(d="M20.369 10.6426L0.75769 10.6426", stroke=color, stroke-width="1.5")
