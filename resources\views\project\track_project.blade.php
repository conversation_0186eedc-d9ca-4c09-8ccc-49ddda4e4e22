@extends('layout.app')
@section('title', 'Track Project')
@section('content')

<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
           @if ( admin_superadmin_permissions() && !Auth::user()->has<PERSON><PERSON><PERSON><PERSON>('Client Member') )
                <a class="d-inline-flex align-items-center text-decoration-none"
                    href="{{ route('statushub') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to StatusHub
                </a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{route('client.dashboard')}}"><img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Client Portal</a>
            @endif
        </div>
        <div class="meta d-flex justify-content-between align-items-center">
            <div class="copy">
                <h1 class="text-white mb-0">Project: <i>[{{$project->job_code}}] {{$project->name}}</i></h1>
                <div class="page-links mt-3"><a class="active" href="{{ route('track-project', ['id'=> $project->id]) }}">Track Project</a>
                    <a href="{{ route('message-centre', ['id'=> $project->id]) }}">Message Center</a><a href="{{ route('file-upload', ['id'=> $project->id]) }}">File Upload</a>
                    {{-- <a href="{{ route('billing-history', ['id'=> $project->id]) }}">Billing History</a> --}}
                </div>
            </div>
            <div class="logo">
                <a href="#"><img  src="{{ $client->logo ? set_user_image($client->logo) : asset('images/default-user.jpg') }}"alt="" width="72" height="80" /></a>
            </div>
        </div>
    </div>
</section>
<section class="client-project bg-white">
    <div class="container-xxl">
        <div class="heading">
            <h2 class="mb-0">Track <i>Project</i></h2>
        </div>
        <div class="status d-flex justify-content-between">
            <div class="text">

                  @if($currentStep)
                <h2 class="text-uppercase mb-2">CURRENT STATUS</h2>
                <h3 class="mb-0 d-flex align-items-center">{{$currentStep->title}}<a class="circle-icon ms-2 phase-{{$phase->id}}" href="#">!</a></h3>
                @else

                <h2 class="text-uppercase mb-2">CURRENT STATUS</h2>
                <h3 class="mb-0 d-flex align-items-center">No Current Status Available<a class="circle-icon ms-2 phase-{{$phase->id}}" href="#">!</a></h3>
               

                @endif
            </div>
            <div class="text text-md-end mt-4 mt-md-0">
                <h2 class="text-uppercase mb-2">CURRENT LAUNCH ESTIMATE</h2>

                
               <h3 class="mb-0 d-flex align-items-center">
                    @if(!empty($completionDate))
                        {{ \Carbon\Carbon::parse($completionDate)->format('F jS, Y') }}
                    @else
                        Not Available
                    @endif
                    <a class="circle-icon ms-2 phase-{{$phase->id}}" href="#">?</a>
                </h3>
            </div>
        </div>
    </div>
</section>
<section class="project-timeline bg-white d-none">
    <div class="container-xxl">
        <div class="head d-flex align-items-center mb-5">
            <h2 class="mb-0">SSGF Project <i>Timeline</i></h2>
            <img class="ms-4" src="images/head-arrow-icon.svg" alt="" width="100" height="46" />
        </div>
        <div class="card-row">
            <div class="card-timeline d-flex flex-column align-self-end orange">
                <div class="flag">
                    <div class="head">
                        <h2 class="text-white lh-1 mb-0">01</h2>
                        <h3 class="text-white text-uppercase mb-0">Define the Project</h3>
                    </div>
                    <div class="text">
                        <p class="text-black"><strong>End-in-mind planning.</strong></p>
                        <p>Starting off the project right by deciding deliverables, parameters and metrics for success</p>
                        <p class="text-uppercase h3 mt-3">DEFINE: 1 week</p>
                    </div>
                </div>
                <div class="icon-wrap d-flex align-items-center">
                    <span class="line"></span>
                    <div class="icon d-flex align-items-center justify-content-center"><img src="images/define-project-icon.svg" alt="" /></div>
                </div>
            </div>
            <div class="card-timeline d-flex flex-column align-self-end green">
                <div class="flag">
                    <div class="head">
                        <h2 class="text-white lh-1 mb-0">02</h2>
                        <h3 class="text-white text-uppercase mb-0">Content, sitemap wireframes</h3>
                    </div>
                    <div class="text">
                        <p class="text-black"><strong>Content organization and creation.</strong></p>
                        <p>Unifying your message across copy and visuals.</p>
                        <p class="text-uppercase h3 mt-3">content: 3-4 weeks</p>
                    </div>
                </div>
                <div class="icon-wrap d-flex align-items-center">
                    <span class="line"></span>
                    <div class="icon d-flex align-items-center justify-content-center"><img src="images/content-project-icon.svg" alt="" /></div>
                </div>
            </div>
            <div class="card-timeline d-flex flex-column align-self-end pink align-items-center">
                <div class="flag">
                    <div class="head">
                        <h2 class="text-white lh-1 mb-0">03</h2>
                        <h3 class="text-white text-uppercase mb-0">Design</h3>
                    </div>
                    <div class="text">
                        <p class="text-black"><strong>The message, visualized.</strong></p>
                        <p>Making sure your content shines through supporting imagery, visuals and branding.</p>
                        <p class="text-uppercase h3 mt-3">design: 4-8 weeks</p>
                    </div>
                </div>
                <div class="icon-wrap d-flex align-items-center">
                    <span class="line"></span>
                    <div class="icon d-flex align-items-center justify-content-center"><img src="images/design-project-icon.svg" alt="" /></div>
                </div>
            </div>
            <div class="card-timeline d-flex flex-column align-self-end purple align-items-center">
                <div class="flag">
                    <div class="head">
                        <h2 class="text-white lh-1 mb-0">04</h2>
                        <h3 class="text-white text-uppercase mb-0">Code</h3>
                    </div>
                    <div class="text">
                        <p class="text-black"><strong>HTML, CMS, etc.</strong></p>
                        <p>Taking everything we've developed together and implementing it in a live environment.</p>
                        <p class="text-uppercase h3 mt-3">code: 4-8 weeks</p>
                    </div>
                </div>
                <div class="icon-wrap d-flex align-items-center">
                    <span class="line"></span>
                    <div class="icon d-flex align-items-center justify-content-center"><img src="images/code-project-icon.svg" alt="" /></div>
                </div>
            </div>
            <div class="card-timeline d-flex flex-column align-self-end orange align-items-end">
                <div class="flag">
                    <div class="head">
                        <h2 class="text-white lh-1 mb-0">05</h2>
                        <h3 class="text-white text-uppercase mb-0">deploy and manage</h3>
                    </div>
                    <div class="text">
                        <p class="text-black"><strong>Launch, SEO, ongoing maintenance.</strong></p>
                        <p>Finishing off the project as strong as we started it.</p>
                        <p class="text-uppercase h3 mt-3">DEploy: 1 week</p>
                    </div>
                </div>
                <div class="icon-wrap d-flex align-items-center">
                    <span class="line"></span>
                    <div class="icon d-flex align-items-center justify-content-center"><img src="images/deploy-project-icon.svg" alt="" /></div>
                </div>
            </div>
        </div>
        <div class="timeline-bar d-flex">
            <div class="bar first orange" data-color="orange">
                <div class="icon-bar"></div>
            </div>
            <div class="bar second green" data-color="green">
                <div class="icon-bar"></div>
            </div>
            <div class="bar pink" data-color="pink">
                <div class="icon-bar"></div>
            </div>
            <div class="bar purple" data-color="purple">
                <div class="icon-bar"></div>
            </div>
            <div class="bar orange last" data-color="orange">
                <div class="icon-bar"></div>
            </div>
        </div>
    </div>
</section>
<section class="timeline-status bg-white">
    <div class="container-xxl">

        @if($project->timeline->type!="voyager")
        <div class="timeline-bar d-flex" id="timelineBar">
            @else
              <div class="timeline-bar d-flex" id="timelineBar" style="background-color:orangered">
                @endif

            @php
                $currentPhaseId = $phase->id;
                $date =  Carbon\Carbon::parse($currentPhaseCompletionDate);
                    $month = $date->format('n'); 
                    $day   = $date->format('j'); 

                
                $total_steps = count($phase->categories);
                $current_step = optional($project->category)->order ?? 0;
               $percentage = $total_steps > 0 
                ? min(100, round(($current_step / $total_steps) * 100)) 
                : 0;
                        @endphp

            @if($project->timeline->type == 'web' || $project->timeline->type == 'maintenance')
                {{-- Web Project Timeline Bar --}}
                <!-- Phase 1 - Define -->
                <div class="bar first orange" data-color="orange" data-phase="Define">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 1 ? '100%' : ($currentPhaseId == 1 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 1)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/define-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

                <!-- Phase 2 - Content -->
                <div class="bar second green {{ $currentPhaseId == 2 ? 'current' : '' }}" data-color="green" data-phase="Content">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 2 ? '100%' : ($currentPhaseId == 2 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 2)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/content-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

                <!-- Phase 3 - Design -->
                <div class="bar pink {{ $currentPhaseId == 3 ? 'current' : '' }}" data-color="pink" data-phase="Design">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 3 ? '100%' : ($currentPhaseId == 3 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 3)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/design-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                
                </div>

                <!-- Phase 4 - Code -->
                <div class="bar purple {{ $currentPhaseId == 4 ? 'current' : '' }}" data-color="purple" data-phase="Code">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 4 ? '100%' : ($currentPhaseId == 4 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 4)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/code-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                
                </div>

                <!-- Phase 5 - Deploy -->
                <div class="bar orange last {{ $currentPhaseId == 5 ? 'current' : '' }}" data-color="orange" data-phase="Deploy">
                    <div class="icon-bar" style="width: {{ $currentPhaseId == 5 ? $percentage . '%' : '0%' }}">
                        @if($currentPhaseId == 5)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/deploy-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

            @elseif($project->timeline->type == 'video')
                {{-- Video Project Timeline Bar --}}
                <!-- Phase 1 - Define -->
                <div class="bar first orange" data-color="orange" data-phase="Define">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 1 ? '100%' : ($currentPhaseId == 1 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 1)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/define-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

                <!-- Phase 2 - Script -->
                <div class="bar second green {{ $currentPhaseId == 2 ? 'current' : '' }}" data-color="green" data-phase="Script">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 2 ? '100%' : ($currentPhaseId == 2 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 2)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/content-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

                <!-- Phase 3 - Edit -->
                <div class="bar pink {{ $currentPhaseId == 3 ? 'current' : '' }}" data-color="pink" data-phase="Edit">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 3 ? '100%' : ($currentPhaseId == 3 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 3)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/design-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

                <!-- Phase 4 - Publish -->
                <div class="bar orange last {{ $currentPhaseId == 4 ? 'current' : '' }}" data-color="purple" data-phase="Publish">
                    <div class="icon-bar" style="width: {{ $currentPhaseId == 4 ? $percentage . '%' : '0%' }}">
                        @if($currentPhaseId == 4)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/deploy-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

            @elseif($project->timeline->type == 'publication' || $project->timeline->type == 'print')
                {{-- Publication/Print Project Timeline Bar --}}
                <!-- Phase 1 - Define -->
                <div class="bar first orange" data-color="orange" data-phase="Define">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 1 ? '100%' : ($currentPhaseId == 1 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 1)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/define-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

                <!-- Phase 2 - Content -->
                <div class="bar second green {{ $currentPhaseId == 2 ? 'current' : '' }}" data-color="green" data-phase="Content">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 2 ? '100%' : ($currentPhaseId == 2 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 2)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/content-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

                <!-- Phase 3 - Design -->
                <div class="bar pink {{ $currentPhaseId == 3 ? 'current' : '' }}" data-color="pink" data-phase="Design">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 3 ? '100%' : ($currentPhaseId == 3 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 3)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/design-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

                <!-- Phase 4 - Print -->
                <div class="bar purple last {{ $currentPhaseId == 4 ? 'current' : '' }}" data-color="purple" data-phase="Print">
                    <div class="icon-bar" style="width: {{ $currentPhaseId == 4 ? $percentage . '%' : '0%' }}">
                        @if($currentPhaseId == 4)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/deploy-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

            @elseif($project->timeline->type == 'social_media')
                {{-- Social Media Project Timeline Bar --}}
                <!-- Phase 1 - Define -->
                <div class="bar first orange" data-color="orange" data-phase="Define">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 1 ? '100%' : ($currentPhaseId == 1 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 1)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/define-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

                <!-- Phase 2 - Content -->
                <div class="bar second green {{ $currentPhaseId == 2 ? 'current' : '' }}" data-color="green" data-phase="Content">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 2 ? '100%' : ($currentPhaseId == 2 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 2)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/content-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

                <!-- Phase 3 - Design -->
                <div class="bar pink {{ $currentPhaseId == 3 ? 'current' : '' }}" data-color="pink" data-phase="Design">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 3 ? '100%' : ($currentPhaseId == 3 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 3)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/design-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                 
                </div>

                <!-- Phase 4 - Publish -->
                <div class="bar purple last {{ $currentPhaseId == 4 ? 'current' : '' }}" data-color="purple" data-phase="Publish">
                    <div class="icon-bar" style="width: {{ $currentPhaseId == 4 ? $percentage . '%' : '0%' }}">
                        @if($currentPhaseId == 4)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/deploy-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
               
                </div>

            @elseif($project->timeline->type == 'voyager')
                {{-- Voyager Project Timeline Bar --}}
                <!-- Single Voyager Phase -->
                  <div class="bar first orange last" data-color="orange" data-phase="Voyager">
                    <div class="icon-bar" style="width: 100%">
                        {{-- <div class="icon-circle"></div> --}}
                        {{-- <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                            <img src="{{ $phase->icon }}" alt="" />
                        </div> --}}
                    </div>
                </div>

            @else
                {{-- Default Timeline Bar (fallback) --}}
                <!-- Phase 1 - Define -->
                <div class="bar first orange" data-color="orange" data-phase="Define">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 1 ? '100%' : ($currentPhaseId == 1 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 1)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/define-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

                <!-- Phase 2 - Content -->
                <div class="bar second green {{ $currentPhaseId == 2 ? 'current' : '' }}" data-color="green" data-phase="Content">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 2 ? '100%' : ($currentPhaseId == 2 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 2)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/content-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                 
                </div>

                <!-- Phase 3 - Design -->
                <div class="bar pink {{ $currentPhaseId == 3 ? 'current' : '' }}" data-color="pink" data-phase="Design">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 3 ? '100%' : ($currentPhaseId == 3 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 3)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/design-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                  
                </div>

                <!-- Phase 4 - Code -->
                <div class="bar purple {{ $currentPhaseId == 4 ? 'current' : '' }}" data-color="purple" data-phase="Code">
                    <div class="icon-bar" style="width: {{ $currentPhaseId > 4 ? '100%' : ($currentPhaseId == 4 ? $percentage . '%' : '0%') }}">
                        @if($currentPhaseId == 4)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/code-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                   
                </div>

                <!-- Phase 5 - Deploy -->
                <div class="bar orange last {{ $currentPhaseId == 5 ? 'current' : '' }}" data-color="orange" data-phase="Deploy">
                    <div class="icon-bar" style="width: {{ $currentPhaseId == 5 ? $percentage . '%' : '0%' }}">
                        @if($currentPhaseId == 5)
                            <div class="icon-circle"></div>
                            <div class="icon d-flex align-items-center justify-content-center bg-white rounded-circle">
                                <img src="{{ asset('images/deploy-project-icon.svg') }}" alt="" />
                            </div>
                        @endif
                    </div>
                    
                </div>
            @endif
        </div>
        @php
            $colorMap = [
                '#65CCB0' => 'green',    
                '#F45689' => 'pink',    
                '#A15CD4' => 'purple',    
                '#FF5811' => 'orange', 
                '#F8B73F' => 'orange',    
            ];
            $colorClass = $colorMap[$phase->color_code] ?? 'default';
                
             @endphp

        <div class="row status-meta justify-content-center green {{ $colorClass }}">
            <div class="col-lg-10">
                <div class="head d-flex justify-content-between">
                    <div class="text">
                        <h2 class="text-uppercase mb-2">Current Step</h2>
                        <h3 class="mb-0">{{$phase->name}}</h3>
                    </div>
                    <div class="text text-md-end mt-4 mt-md-0">
                        <h2 class="text-uppercase mb-2">Current STATUS:</h2>
                        <h3 class="mb-0"><span class="counter" data-countTo="{{$percentage}}" data-duration="1000">0</span>%</h3>
                    </div>
                </div>
                <div class="content-meta">
                    @if($currentStep)
                    <p><strong class="d-block">{{$currentStep->title}}</strong>{!! $currentStep->description !!}
                    </p>
                    @endif
                    <div class="actions d-flex align-items-center justify-content-between mt-4">
                        <div class="text d-flex align-items-center">
                           @if($currentActionMessage)
                            <a class="circle-icon me-3 phase-{{$phase->id}}" href="#">!</a>
                            @endif
                            <div class="copy">
                                 @if($currentActionMessage)
                                <p><strong class="d-block">Action needed</strong> {!!$currentActionMessage->title!!}</p>
                                @endif
                            </div>
                        </div>
                        <div class="meta">
                            @if($project->timeline->type!="voyager")
                            <h2 class="text-uppercase mb-0">PHASE ENDS {{$month}}/{{$day}}</h2>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- Loop through each group of messages by their reason --}}


@if ($groupedMessagesByReason->isNotEmpty())
    @foreach ($groupedMessagesByReason as $reasonText => $messagesForThisReason)
        <section class="actions-need bg-white mb-0"> 
            <div class="container-xxl">
                <div class="heading">
                    <h2 class="text-uppercase text-orange mb-1">ACTION NEEDED</h2>
                   
                    <h3 class="d-flex align-items-center mb-2">{{ $reasonText }} <a class="circle-icon ms-2 phase-{{$phase->id}}" href="#">!</a></h3>
                    <h4 class="p fst-italic fw-medium mb-0">Please note the following items related to this requirement:</h4>
                </div>
                
          
                @foreach ($messagesForThisReason as $message)
                    <div class="check-actions d-flex align-items-center mt-3">
                        <div class="check-icon"><i class="bi bi-square"></i></div>
                        <div class="text w-50">
                            <p>
                                <strong class="d-block">{{ $message->title ?? 'Action Item' }}</strong>


                                @if($message->due_date)
                                    <strong class="d-block text-orange">Due {{ \Carbon\Carbon::parse($message->due_date)->format('m/d/y') }}</strong>
                                     
                                @endif
                                 {{ $message->content ?? '' }}
                              
                              
                            </p>
                        </div>
                        <div class="cta-row ms-auto d-flex">
                           
                            @if ($message->type == 'upload')
                                <a class="cta cta-action" href="#"><i class="me-2 bi bi-upload"></i>UPLOAD</a>
                            @elseif ($message->type == 'review')
                                <a class="cta cta-action" href="#">VIEW DESIGNS</a>
                                <a class="cta cta-action" href="#"><i class="me-2 bi bi-check2-circle"></i>APPROVE</a>
                                <a class="cta cta-action" href="#"><i class="me-2 bi bi-envelope"></i>SUBMIT EDIT</a>
                            @endif
                          
                        </div>
                    </div>
                @endforeach
            </div>
        </section>
    @endforeach
@endif


<section class="actions-need bg-white">
    <div class="container-xxl">
        <div class="heading">
            <h2 class="text-uppercase text-orange mb-1">LATEST MESSAGES</h2>
              @if($messages->isNotEmpty())
            <h3 class="d-flex align-items-center mb-2">Message from {{ $messages->first()->postedBy->name }} </h3>
            @endif
        </div>
        @if($messages->isNotEmpty())
            @if($messages->first())
                <div class="message-box d-flex mt-3">
                    <div class="pic">
                        <img src="{{ set_user_image($messages->first()->postedBy->profile_image) }}" alt="{{ $messages->first()->postedBy->name }}" />
                    </div>
                    <div class="message-wrap d-flex align-items-start">
                        <div class="message">
                            <h2>{{ $messages->first()->subject }}</h2>
                            <div class="copy">
                                <p>{!! Str::limit(strip_tags(trim($messages->first()->message)), 200) !!}...<a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $messages->first()->id]) }}">See More</a></p>
                                <div class="meta d-flex">
                                    <a class="user me-4" href="#">{{ $messages->first()->postedBy->name }}</a>
                                    <span class="date">{{ $messages->first()->created_at->format('m/d/y') }}</span>
                                </div>
                            </div>
                        </div>
                        <a class="cta d-inline-flex align-items-center text-decoration-none py-2 ms-5 mt-0" href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $messages->first()->id]) }}">
                            <i class="bi bi-envelope me-2"></i> REPLY
                        </a>
                    </div>
                </div>
            @endif
            <div class="messages-wrap">

                @if($messages->count()>1)
               <div class="head">
						<span id="moreMessages">More Messages <img src="{{asset('images/down-arrow-orange.svg')}}" alt="" width="18" height="10" /></span>
					</div>
                    @endif
                <div class="messages-list">
                    @foreach($messages->skip(1) as $message)
                        <div class="message-row row flex-nowrap align-items-center gx-md-3 mt-4 {{ $message->files->isNotEmpty() ? 'has-attachment' : '' }} orange">
                            <div class="col-md-auto icons d-flex">
                                <div class="icon icon-envelop d-flex align-items-center justify-content-center">
                                    <i class="bi bi-envelope-fill"></i>
                                </div>
                                <div class="icon pic">
                                    <img src="{{ set_user_image($message->postedBy->profile_image) }}" alt="{{ $message->postedBy->name }}" />
                                </div>
                            </div>
                            <div class="col-md-auto name">
                                <p>{{ $message->postedBy->name }}</p>
                            </div>
                            <div class="col-md-auto date">
                                <p>{{ $message->created_at->format('m/d/y') }}</p>
                            </div>
                            <div class="col-md-auto notification">
                                <p><a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}">{{ $message->subject }}</a></p>
                                @if($message->files->isNotEmpty())
                                    <div class="attachment pt-2">
                                        <p>
                                            <i class="bi bi-link-45deg"></i> 
                                            Files attached: 
                                            @foreach($message->files->take(3) as $file)
                                                <a href="{{ Storage::url($file->file_path) }}" class="text-decoration-none text-orange">
                                                    {{ $file->file_name }}
                                                </a>@if(!$loop->last), @endif
                                            @endforeach
                                            @if($message->files->count() > 3)
                                                <a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}" class="text-decoration-none text-orange">... See All</a>
                                            @endif
                                        </p>
                                    </div>
                                @endif
                            </div>
                            <div class="col-md message d-flex align-items-center">
                                <div class="over-text d-grid">
                                    <p>{!! Str::limit(strip_tags(trim($message->message)), 200) !!}</p>
                                </div>
                                <a class="ms-2" href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}">Read More</a>
                            </div>
                            <div class="col-md-auto reply">
                                <a class="cta d-inline-flex align-items-center text-decoration-none py-2 mt-0" href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $message->id]) }}">
                                    <i class="bi bi-envelope me-2"></i> REPLY
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="text-center project-contact pt-5 pb-0">
                <a class="cta d-inline-block text-decoration-none text-uppercase mt-4 mt-md-0" href="{{ route('message-centre', ['id' => $project->id]) }}">GO TO MESSAGE CENTER</a>
            </div>
        @else
            <div class="no-message-div">No Messages</div>
            <div class="message-ctas text-center d-flex justify-content-center pt-5 pb-0">
                <a class="cta d-inline-flex align-items-center text-decoration-none text-uppercase py-2 mt-4 mt-md-0" href="{{ route('new-message-thread', ['project_id' => $project->id]) }}">
                    <i class="bi bi-envelope me-2"></i> POST A NEW MESSAGE
                </a>
            </div>
        @endif
    </div>
</section>





{{-- Separate Section for Message from Developer --}}
{{-- <section class="actions-need bg-white mt-0"> 
<div class="container-xxl">
    <div class="heading">
        <h3 class="d-flex align-items-center mb-2">Message from Developer</h3>
    </div>

        @if ($featuredDeveloperMessage)
            @php
                $created_by_user_name = \App\Models\User::where('id', '=', $featuredDeveloperMessage->created_by_user_id)->first()->name;
                $created_by_user_profile_image = \App\Models\User::where('id', '=', $featuredDeveloperMessage->created_by_user_id)->first()->profile_image;
            @endphp
            <div class="message-box d-flex mt-3">
                <div class="pic">
                    <img src="{{ set_user_image($created_by_user_profile_image) }}" alt="{{ $featuredDeveloperMessage->sender_name ?? 'Developer' }}'s Profile Picture" />
                </div>
                <div class="message-wrap w-100 d-flex align-items-start">
                    <div class="message w-100">
                        <h2><span class="quote">"</span>{{ $featuredDeveloperMessage->subject ?? $featuredDeveloperMessage->title ?? 'No Subject' }}<span class="quote">"</span></h2>
                        <div class="copy">
                            <p>{{ strip_tags($featuredDeveloperMessage->description) ?? 'No content available.' }}<a href="{{ route('view-message', ['project_id'=>$project->id, 'message_id'=>$featuredDeveloperMessage->id]) }}">...See More </a></p>
                            <div class="meta d-flex">
                                <a class="user me-4" href="#">{{ $created_by_user_name }}</a>
                                <span class="date">{{ \Carbon\Carbon::parse($featuredDeveloperMessage->created_at)->format('m/d/y') }}</span>
                            </div>
                        </div>
                    </div>
                    <a class="cta d-inline-flex align-items-center text-decoration-none py-2 ms-5 mt-0" href=""><i class="bi bi-envelope me-2"></i> REPLY</a>
                </div>
            </div>
        @else
            <p class="mt-3 no-message-div">No messages</p>
        @endif

        @if ($moreDeveloperMessages->isNotEmpty())
            <div class="messages-wrap">
                <div class="head">
                    <span id="moreMessagesTrigger">More Messages <img src="{{ asset('images/down-arrow-orange.svg') }}" alt="Down Arrow" width="18" height="10" /></span>
                </div>
                <div class="messages-list">
                    @foreach ($moreDeveloperMessages as $message)
                        <div class="message-row row flex-nowrap align-items-center gx-md-3 mt-4 orange">
                            <div class="col-md-auto icons d-flex">
                                <div class="icon icon-envelop d-flex align-items-center justify-content-center"><i class="bi bi-envelope-fill"></i></div>
                                <div class="icon pic">
                                    <img src="{{ set_user_image($created_by_user_profile_image) }}" alt="{{ $message->sender_name ?? 'User' }} Profile Picture" />
                                </div>
                            </div>
                            <div class="col-md-auto name">
                                <p>{{ $created_by_user_name  }}</p>
                            </div>
                            <div class="col-md-auto date">
                                <p>{{ \Carbon\Carbon::parse($message->created_at)->format('m/d/y') }}</p>
                            </div>
                            <div class="col-md-auto notification">
                                <p><a href="{{ route('view-message', ['project_id'=>$project->id, 'message_id'=>$message->id]) }}">{{ $message->subject ?? $message->title ?? 'No Subject' }}</a></p>
                            </div>
                            <div class="col-md message d-flex align-items-center">
                                <div class="over-text d-grid">
                                    <p>{{ strip_tags($message->description ?? 'No content available.') }}</p>
                                </div>
                                <a class="ms-2" href="{{ route('view-message', ['project_id'=>$project->id, 'message_id'=>$message->id]) }}">...Read More</a>
                            </div>
                            <div class="col-md-auto reply">
                                <a class="cta d-inline-flex align-items-center text-decoration-none py-2 mt-0" href=""><i class="bi bi-envelope me-2"></i> REPLY</a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <p class="mt-3 no-message-div">No messages</p>
        @endif

    <div class="text-center project-contact pt-5 pb-0"><a class="cta d-inline-block text-decoration-none text-uppercase mt-4 mt-md-0" href="{{ route('message-centre', ['id'=> $project->id]) }}">GO TO MESSAGE CENTER</a></div>
</div>
</section> --}}



<section class="current-timeline bg-white">
    <div class="container-xxl">

        @if($project->timeline->type != 'voyager')
        <div class="heading mb-4 d-md-flex align-items-md-baseline justify-content-between">
            <h2 class="mb-0">Current Timeline</h2>
            <div class="tab-timeline" id="tabTimeline">
                <span class="tab active" data-timeline-tab="project-phases">Project Phases</span>
                {{-- <span class="tab" data-timeline-tab="billing-history">Billing History</span> --}}
            </div>
        </div>

        @else
         <div class="heading  d-md-flex align-items-md-baseline justify-content-between">
            <h2 class="mb-0">Current Timeline</h2>
            <div class="tab-timeline" id="tabTimeline">
                <span class="tab active" data-timeline-tab="project-phases">Project Phases</span>
                {{-- <span class="tab" data-timeline-tab="billing-history">Billing History</span> --}}
            </div>
        </div>
        @endif

        <div class="tab-timeline-content" data-timeline-tab="project-phases">
            @if($project->timeline->type == 'web' || $project->timeline->type == 'maintenance')
                {{-- Web Project Timeline --}}
                @foreach($phases as $index => $phase)
                    @if($index === count($phases) - 1)
                        @continue
                    @endif
                    @php
                        $phase_icon = $phase->icon;
                        $uppercase_name = strtoupper(trim(explode(preg_match('/[\s,]+/', $phase->name, $match) ? $match[0] : ' ', $phase->name, 2)[0])); 
                        $project_phase = $project->phases->firstWhere('id', $phase->id);
                        $project_duration = $project_phase->pivot->project_duration ?? null;
                        $project_target = $project_phase->pivot->project_target ?? null;
                        $completion = $project->phaseCompletions->firstWhere('phase_id', $phase->id); 
                    @endphp

                    <div class="meta-row d-flex mt-3 {{ $phase->getColorClass() }}">
                        <div class="logo d-flex align-items-center justify-content-center rounded-circle">
                            <img src="{{$phase_icon}}" alt="" />
                        </div>
                        <div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
                            <div class="title d-flex align-items-center justify-content-center">{{$uppercase_name}}</div>
                            <div class="duration col-meta align-self-center">
                                <strong>Duration:</strong> {{ $project_duration ?? '-' }}
                            </div>
                            <div class="target col-meta align-self-center">
                                <div class="text d-inline-block">
                                    <span class="check"><i class="bi bi-check2-circle"></i></span>
                                    <strong>Target:</strong>
                                    @if($project_target)
                                        @php
                                            $targetDate =  \Carbon\Carbon::parse($project_target);
                                            $today =  \Carbon\Carbon::today();
                                        @endphp
                                        {{ $targetDate->format('m/d/Y') }}
                                        @if($targetDate->isPast() && !$targetDate->isToday() && !$completion?->is_completed)
                                            <i class="blame ms-3 text-orange">{{ $targetDate->diffInDays($today) }} Days Past Due</i>
                                        @endif
                                    @else
                                        <strong>-</strong>
                                    @endif
                                </div>
                            </div>
                            @if($completion && $completion->is_completed)
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block">
                                        <span class="check"><i class="bi bi-check2-circle"></i></span>
                                        <strong>Completed:</strong> {{ $completion->completed_at->format('m/d/y') }}
                                    </div>
                                </div>
                            @else
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block"><strong>-</strong></div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach

            @elseif($project->timeline->type == 'video')
                {{-- Video Project Timeline --}}
                @foreach($phases as $index => $phase)
                    @if($index === count($phases) - 1|| $index==3)
                        @continue
                    @endif

                    
                    @php

                            if($index == 0){
                            $uppercase_name = "DEFINE";
                        } elseif($index == 1){
                            $uppercase_name = "SCRIPT";
                        } elseif($index == 2){
                            $uppercase_name = "EDIT";
                        } elseif($index == 4){
                            $uppercase_name = "PUBLISH";
                        } else {
                            $uppercase_name = strtoupper(trim(explode(preg_match('/[\s,]+/', $phase->name, $match) ? $match[0] : ' ', $phase->name, 2)[0])); 
                        }
                       
                      


                        $phase_icon = $phase->icon;
                       
                        $project_phase = $project->phases->firstWhere('id', $phase->id);
                        $project_duration = $project_phase->pivot->project_duration ?? null;
                        $project_target = $project_phase->pivot->project_target ?? null;
                        $completion = $project->phaseCompletions->firstWhere('phase_id', $phase->id); 
                    @endphp

                    <div class="meta-row d-flex mt-3 {{ $phase->getColorClass() }}">
                        <div class="logo d-flex align-items-center justify-content-center rounded-circle">
                            <img src="{{$phase_icon}}" alt="" />
                        </div>
                        <div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
                            <div class="title d-flex align-items-center justify-content-center">{{$uppercase_name}}</div>
                            <div class="duration col-meta align-self-center">
                                <strong>Duration:</strong> {{ $project_duration ?? '-' }}
                            </div>
                            <div class="target col-meta align-self-center">
                                <div class="text d-inline-block">
                                    <span class="check"><i class="bi bi-check2-circle"></i></span>
                                    <strong>Target:</strong>
                                    @if($project_target)
                                        @php
                                            $targetDate =  \Carbon\Carbon::parse($project_target);
                                            $today =  \Carbon\Carbon::today();
                                        @endphp
                                        {{ $targetDate->format('m/d/Y') }}
                                        @if($targetDate->isPast() && !$targetDate->isToday() && !$completion?->is_completed)
                                            <i class="blame ms-3 text-orange">{{ $targetDate->diffInDays($today) }} Days Past Due</i>
                                        @endif
                                    @else
                                        <strong>-</strong>
                                    @endif
                                </div>
                            </div>
                            @if($completion && $completion->is_completed)
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block">
                                        <span class="check"><i class="bi bi-check2-circle"></i></span>
                                        <strong>Completed:</strong> {{ $completion->completed_at->format('m/d/y') }}
                                    </div>
                                </div>
                            @else
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block"><strong>-</strong></div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach

            @elseif($project->timeline->type == 'publication' || $project->timeline->type == 'print')
                {{-- Publication/Print Project Timeline --}}
                @foreach($phases as $index => $phase)
                    @if($index === count($phases) - 1 || $index ==3)
                        @continue
                    @endif
                    @php

                        if($index == 0){
                            $uppercase_name = "DEFINE";
                        } elseif($index == 1){
                            $uppercase_name = "CONTENT";
                        } elseif($index == 2){
                            $uppercase_name = "DESIGN";
                        } elseif($index == 4){
                            $uppercase_name = "PRINT";
                        } else {
                            $uppercase_name = strtoupper(trim(explode(preg_match('/[\s,]+/', $phase->name, $match) ? $match[0] : ' ', $phase->name, 2)[0])); 
                        }
                        $phase_icon = $phase->icon;
                       
                        $project_phase = $project->phases->firstWhere('id', $phase->id);
                        $project_duration = $project_phase->pivot->project_duration ?? null;
                        $project_target = $project_phase->pivot->project_target ?? null;
                        $completion = $project->phaseCompletions->firstWhere('phase_id', $phase->id); 
                    @endphp

                    <div class="meta-row d-flex mt-3 {{ $phase->getColorClass() }}">
                        <div class="logo d-flex align-items-center justify-content-center rounded-circle">
                            <img src="{{$phase_icon}}" alt="" />
                        </div>
                        <div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
                            <div class="title d-flex align-items-center justify-content-center">{{$uppercase_name}}</div>
                            <div class="duration col-meta align-self-center">
                                <strong>Duration:</strong> {{ $project_duration ?? '-' }}
                            </div>
                            <div class="target col-meta align-self-center">
                                <div class="text d-inline-block">
                                    <span class="check"><i class="bi bi-check2-circle"></i></span>
                                    <strong>Target:</strong>
                                    @if($project_target)
                                        @php
                                            $targetDate =  \Carbon\Carbon::parse($project_target);
                                            $today =  \Carbon\Carbon::today();
                                        @endphp
                                        {{ $targetDate->format('m/d/Y') }}
                                        @if($targetDate->isPast() && !$targetDate->isToday() && !$completion?->is_completed)
                                            <i class="blame ms-3 text-orange">{{ $targetDate->diffInDays($today) }} Days Past Due</i>
                                        @endif
                                    @else
                                        <strong>-</strong>
                                    @endif
                                </div>
                            </div>
                            @if($completion && $completion->is_completed)
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block">
                                        <span class="check"><i class="bi bi-check2-circle"></i></span>
                                        <strong>Completed:</strong> {{ $completion->completed_at->format('m/d/y') }}
                                    </div>
                                </div>
                            @else
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block"><strong>-</strong></div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach

            @elseif($project->timeline->type == 'social_media')
                {{-- Social Media Project Timeline --}}
                @foreach($phases as $index => $phase)
                    @if($index === count($phases) - 1 || $index === count($phases)-3)
                        @continue
                    @endif
                    @php
                      if($index == 0){
                            $uppercase_name = "DEFINE";
                        } elseif($index == 1){
                            $uppercase_name = "CONTENT";
                        } elseif($index == 2){
                            $uppercase_name = "DESIGN";
                        } elseif($index == 4){
                            $uppercase_name = "PUBLISH";
                        } else {
                            $uppercase_name = strtoupper(trim(explode(preg_match('/[\s,]+/', $phase->name, $match) ? $match[0] : ' ', $phase->name, 2)[0])); 
                        }

                    
                        $phase_icon = $phase->icon;
                        $project_phase = $project->phases->firstWhere('id', $phase->id);
                        $project_duration = $project_phase->pivot->project_duration ?? null;
                        $project_target = $project_phase->pivot->project_target ?? null;
                        $completion = $project->phaseCompletions->firstWhere('phase_id', $phase->id); 
                    @endphp

                    <div class="meta-row d-flex mt-3 {{ $phase->getColorClass() }}">
                        <div class="logo d-flex align-items-center justify-content-center rounded-circle">
                            <img src="{{$phase_icon}}" alt="" />
                        </div>
                        <div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
                            <div class="title d-flex align-items-center justify-content-center">{{$uppercase_name}}</div>
                            <div class="duration col-meta align-self-center">
                                <strong>Duration:</strong> {{ $project_duration ?? '-' }}
                            </div>
                            <div class="target col-meta align-self-center">
                                <div class="text d-inline-block">
                                    <span class="check"><i class="bi bi-check2-circle"></i></span>
                                    <strong>Target:</strong>
                                    @if($project_target)
                                        @php
                                            $targetDate =  \Carbon\Carbon::parse($project_target);
                                            $today =  \Carbon\Carbon::today();
                                        @endphp
                                        {{ $targetDate->format('m/d/Y') }}
                                        @if($targetDate->isPast() && !$targetDate->isToday() && !$completion?->is_completed)
                                            <i class="blame ms-3 text-orange">{{ $targetDate->diffInDays($today) }} Days Past Due</i>
                                        @endif
                                    @else
                                        <strong>-</strong>
                                    @endif
                                </div>
                            </div>
                            @if($completion && $completion->is_completed)
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block">
                                        <span class="check"><i class="bi bi-check2-circle"></i></span>
                                        <strong>Completed:</strong> {{ $completion->completed_at->format('m/d/y') }}
                                    </div>
                                </div>
                            @else
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block"><strong>-</strong></div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach

            {{-- @elseif($project->timeline->type == 'voyager') --}}
                {{-- Voyager Project Timeline --}}
                @foreach($phases as $index => $phase)
                    @if($phase->name !== 'Voyager')
                        @continue
                    @endif
                    @php
                        $phase_icon = $phase->icon;
                        $uppercase_name = strtoupper(trim(explode(preg_match('/[\s,]+/', $phase->name, $match) ? $match[0] : ' ', $phase->name, 2)[0])); 
                        $project_phase = $project->phases->firstWhere('id', $phase->id);
                        $project_duration = $project_phase->pivot->project_duration ?? null;
                        $project_target = $project_phase->pivot->project_target ?? null;
                        $completion = $project->phaseCompletions->firstWhere('phase_id', $phase->id); 
                    @endphp

                    <div class="meta-row d-flex mt-3 {{ $phase->getColorClass() }}">
                        <div class="logo d-flex align-items-center justify-content-center rounded-circle">
                            <img src="{{$phase_icon}}" alt="" />
                        </div>
                        <div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
                            <div class="title d-flex align-items-center justify-content-center">{{$uppercase_name}}</div>
                            <div class="duration col-meta align-self-center">
                                <strong>Duration:</strong> {{ $project_duration ?? '-' }}
                            </div>
                            <div class="target col-meta align-self-center">
                                <div class="text d-inline-block">
                                    <span class="check"><i class="bi bi-check2-circle"></i></span>
                                    <strong>Target:</strong>
                                    @if($project_target)
                                        @php
                                            $targetDate =  \Carbon\Carbon::parse($project_target);
                                            $today =  \Carbon\Carbon::today();
                                        @endphp
                                        {{ $targetDate->format('m/d/Y') }}
                                        @if($targetDate->isPast() && !$targetDate->isToday() && !$completion?->is_completed)
                                            <i class="blame ms-3 text-orange">{{ $targetDate->diffInDays($today) }} Days Past Due</i>
                                        @endif
                                    @else
                                        <strong>-</strong>
                                    @endif
                                </div>
                            </div>
                            @if($completion && $completion->is_completed)
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block">
                                        <span class="check"><i class="bi bi-check2-circle"></i></span>
                                        <strong>Completed:</strong> {{ $completion->completed_at->format('m/d/y') }}
                                    </div>
                                </div>
                            @else
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block"><strong>-</strong></div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach

            @else
                {{-- Default Timeline (fallback) --}}
                @foreach($phases as $index => $phase)
                    @if($index === count($phases) - 1)
                        @continue
                    @endif
                    @php
                        $phase_icon = $phase->icon;
                        $uppercase_name = strtoupper(trim(explode(preg_match('/[\s,]+/', $phase->name, $match) ? $match[0] : ' ', $phase->name, 2)[0])); 
                        $project_phase = $project->phases->firstWhere('id', $phase->id);
                        $project_duration = $project_phase->pivot->project_duration ?? null;
                        $project_target = $project_phase->pivot->project_target ?? null;
                        $completion = $project->phaseCompletions->firstWhere('phase_id', $phase->id); 
                    @endphp

                    <div class="meta-row d-flex mt-3 {{ $phase->getColorClass() }}">
                        <div class="logo d-flex align-items-center justify-content-center rounded-circle">
                            <img src="{{$phase_icon}}" alt="" />
                        </div>
                        <div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
                            <div class="title d-flex align-items-center justify-content-center">{{$uppercase_name}}</div>
                            <div class="duration col-meta align-self-center">
                                <strong>Duration:</strong> {{ $project_duration ?? '-' }}
                            </div>
                            <div class="target col-meta align-self-center">
                                <div class="text d-inline-block">
                                    <span class="check"><i class="bi bi-check2-circle"></i></span>
                                    <strong>Target:</strong>
                                    @if($project_target)
                                        @php
                                            $targetDate =  \Carbon\Carbon::parse($project_target);
                                            $today =  \Carbon\Carbon::today();
                                        @endphp
                                        {{ $targetDate->format('m/d/Y') }}
                                        @if($targetDate->isPast() && !$targetDate->isToday() && !$completion?->is_completed)
                                            <i class="blame ms-3 text-orange">{{ $targetDate->diffInDays($today) }} Days Past Due</i>
                                        @endif
                                    @else
                                        <strong>-</strong>
                                    @endif
                                </div>
                            </div>
                            @if($completion && $completion->is_completed)
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block">
                                        <span class="check"><i class="bi bi-check2-circle"></i></span>
                                        <strong>Completed:</strong> {{ $completion->completed_at->format('m/d/y') }}
                                    </div>
                                </div>
                            @else
                                <div class="status col-meta align-self-center text-center">
                                    <div class="text d-inline-block"><strong>-</strong></div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            @endif
        </div>
    </div>
</section>
<section class="project-contact bg-white">
    <div class="container text-center"><a class="cta d-inline-block text-decoration-none text-uppercase mt-4 mt-md-0" href="#">GET EMAIL UPDATES</a><a class="cta d-inline-block text-decoration-none text-uppercase mt-4 mt-md-0" href="#">NEED HELP? CONTACT</a></div>
</section>

@endsection


@push('styles')

<style>
    .circle-icon {
  border: 1px solid #ff4c00;
  color: #ff4c00;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 24px;
  flex: 0 0 24px;
  font: 14px "Inter", sans-serif;
  height: 24px;
  width: 24px;
}


.circle-icon.phase-2{

border: 1px solid #65ccb0;
  color: #65ccb0;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 24px;
  flex: 0 0 24px;
  font: 14px "Inter", sans-serif;
  height: 24px;
  width: 24px;


}

.circle-icon.phase-3{

border: 1px solid #F45689;
  color: #F45689;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 24px;
  flex: 0 0 24px;
  font: 14px "Inter", sans-serif;
  height: 24px;
  width: 24px;
}


.circle-icon.phase-4{

border: 1px solid #a15cd4;
  color: #a15cd4;
  -webkit-box-flex: 0;
  -ms-flex: 0 0 24px;
  flex: 0 0 24px;
  font: 14px "Inter", sans-serif;
  height: 24px;
  width: 24px;
}

        .timeline-status .status-meta.orange .head {
          background: #FF5811;
       }
    .timeline-status .status-meta.orange .actions h2 {
    color: #FF5811;
    }
    .timeline-status .status-meta.orange .head,
    .timeline-status .status-meta.orange .content-meta {
    border-color: #FF5811;
    }


       .timeline-status .status-meta.pink .head {
          background: #F45689;
       }
    .timeline-status .status-meta.pink .actions h2 {
    color: #F45689;
    }
    .timeline-status .status-meta.pink .head,
    .timeline-status .status-meta.pink .content-meta {
    border-color: #F45689;
    }




       .timeline-status .status-meta.purple .head {
          background: #A15CD4;
            }
            .timeline-status .status-meta.purple .actions h2 {
            color: #A15CD4;
            }
            .timeline-status .status-meta.purple .head,
            .timeline-status .status-meta.purple .content-meta {
            border-color: #A15CD4;
            }


        .no-message-div{
            text-align:center;
            padding: 5px;
            border-top: 0.5px solid #cacaca;
            border-right: 0.5px solid #cacaca;
            border-bottom: 0.5px solid #cacaca;
            border-left: 5px solid #ff4c00;
        }


         .message-box {
            width: 100%;
        }

        .message-box .message-wrap {
            width: 100%;
        }

        .message-box .message-wrap .message {
            width: 100%;
        }

        .messages-wrap .head {
            display: flex;
            align-items: center;
        }

        .messages-wrap .head span {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .messages-wrap .head img {
            display: inline-block;
            vertical-align: middle;
        }

    </style>


@endpush


    
