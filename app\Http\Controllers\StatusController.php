<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Company;
use App\Models\Permission;
use App\Models\Project;
use App\Models\Role;
use App\Models\Task;
use App\Models\Status;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;

class StatusController extends Controller
{
    public function show_statuses()
    {
        $statuses = Status::all();
        $user = Auth::user();
        if( $user->role()->name == 'SuperAdmin' ){
            return view('admin.statues.index', compact('statuses'));
        }else {
            return view('statuses', compact('statuses'));
        }
    }

    public function add_status()
    {
        $statuses = Project::all();
        return view('add_status', compact('statuses'));
    }

    public function save_status(Request $request)
    {
        $validate = Validator::make($request->all(),
            [
            'status_name' => 'required|string|max:255|unique:status,name',
            // 'status_description' => 'nullable|string|max:255'
            ],
            [
                'status_name.required' => 'The name field is required.',
                // 'status_description.string' => 'The description must be a string.',
                // 'status_description.max' => 'The description may not be greater than 255 characters.',
            ]
        );

        if($validate->fails()){
            return back()->withErrors($validate)->withInput();
        }

        $validate = $validate->validate();
        $status = new Status;
        $status->name = $validate['status_name'];
        // $status->description = $validate['status_description'];
        $status->save();
        if($status){
            return back()->with('status_added_success_message', 'Status Added Successfully');
        }
        else{
            return back()->with("status_added_error_message", 'Status Not Added');
        }

    }

    public function edit_status(Request $request){
        $status = Status::where('id', '=' ,$request->route('status_id'))->first();
        return view('edit-status', ['status' => $status]);

    }

    public function update_status(Request $request){
        $validate = Validator::make($request->all(),
            [
            'status_name' => 'required|string|max:255',
            // 'status_description' => 'nullable|string|max:255'
            ],
            [
                'status_name.required' => 'The name field is required.',
                // 'status_description.string' => 'The description must be a string.',
                // 'status_description.max' => 'The description may not be greater than 255 characters.',
            ]
        );

        if($validate->fails()){
            return back()->withErrors($validate)->withInput();
        }

        $validate = $validate->validate();
        $status = Status::where('id' ,'=', $request->route('status_id'))->first();
        $status->name = $validate['status_name'];
        // $status->description = $validate['status_description'];
        $status->save();
        if($status){
            return back()->with('status_added_success_message', 'Status Added Successfully');
        }
        else{
            return back()->with("status_added_error_message", 'Status Not Added');
        }

    }

    public function delete_status(Request $request){
        $status = Status::where('id' ,'=', $request->route('status_id'))->first();
        $status->delete();
        if($status){
            return redirect('statuses');
        }
    }
}
