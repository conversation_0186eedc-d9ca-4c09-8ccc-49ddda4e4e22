<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if(!Schema::hasTable('comments')){
            Schema::create('comments', function (Blueprint $table) {
                $table->id();
                $table->foreignId('task_id')->nullable()->references('id')->on('tasks');
                $table->foreignId('user_id')->nullable()->references('id')->on('users');
                $table->foreignId('comment_parent_id')->nullable()->references('id')->on('comments');
                $table->text('comment_body')->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('comments', function (Blueprint $table) {
            $table->dropForeign(['task_id']);  // Dropping the foreign key constraint
            $table->dropForeign(['user_id']);  // Dropping the foreign key constraint
        });
        Schema::dropIfExists('comments');
    }
};
