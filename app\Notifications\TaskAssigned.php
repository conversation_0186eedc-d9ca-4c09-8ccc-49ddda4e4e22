<?php

namespace App\Notifications;

use App\Models\Project;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Task;

class TaskAssigned extends Notification
{
    use Queueable;

    public $task;

    public $project;

    /**
     * Create a new notification instance.
     */
    public function __construct(Task $task, Project $project)
    {
        //
        $this->task = $task;

        $this->project = $project;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("[{$this->project->name}] {$this->task->name}")
            ->line('On Project ' . $this->project->name . ' a new task has been added')
            ->line('Please use the following link to view the task details.')
            // ->line('Task Name: ' . $this->task->name)
            ->action('View Project', url('/tasks/' . $this->task->id));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
