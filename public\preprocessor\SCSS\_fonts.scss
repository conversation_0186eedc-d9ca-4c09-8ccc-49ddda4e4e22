$path: '../fonts/';

@font-face {
	font-family: 'Futura Std';
	src: url('#{$path}FuturaStd-Bold.woff2') format('woff2'), url('#{$path}FuturaStd-Bold.woff') format('woff');
	font-weight: 700;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Futura Std';
	src: url('#{$path}FuturaStd-BoldOblique.woff2') format('woff2'), url('#{$path}FuturaStd-BoldOblique.woff') format('woff');
	font-weight: 700;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: 'Futura Std';
	src: url('#{$path}FuturaStd-Medium.woff2') format('woff2'), url('#{$path}FuturaStd-Medium.woff') format('woff');
	font-weight: 500;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'Futura Std';
	src: url('#{$path}FuturaStd-MediumOblique.woff2') format('woff2'), url('#{$path}FuturaStd-MediumOblique.woff') format('woff');
	font-weight: 500;
	font-style: italic;
	font-display: swap;
}
