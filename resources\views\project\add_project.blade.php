@extends('layout.app')
@section('title', 'Add Project')
@section('content')
<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-projects') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Projects List</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('projects') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Projects List</a>
            @endif
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">New <i>Project</i></h1>
        </div>
    </div>

	@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
@endif
@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert" id="backend-error">
        {{ $errors->first() }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif



</section>
<section class="client-project project-dashboard">
    <div class="container-xxl">
        <div class="row projects-row">
            <div class="col-md-3 col-xl-2 project-column templates-links">
                <h2 class="text-uppercase">TEMPLATES</h2>
                <hr class="mt-0 mb-4 border-white" />
                <div class="templates-list d-flex flex-column">
                    <a class="template" href="#">Web Project</a>
                    <a class="template" href="#">Voyager Project</a>
                    <a class="template" href="#">Print Project</a>
                    <a class="template" href="#">Publication Project</a>
                    <a class="template" href="#">Video Project</a>
                    <a class="template" href="#">Social Media Project</a>
                    <a class="template" href="#">ACOMSGF Project</a>
                    <a class="template" href="#">CA Group Project</a>
                </div>
            </div>
            <div class="col-md-9 col-xl-10 project-column task-overview">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">SET UP NEW PROJECT</h2>
                </div>
                <hr class="mt-0 mb-4 border-white" />
				<form id="addNewProject" class="row form-wrap" action="{{ route('save-project') }}" method="post" enctype="multipart/form-data">
					@csrf
					<div class="d-none">
						<input type="hidden" id="category_id" name="category_id" value="{{ old('category_id', request()->get('category_id', null)) }}">
						<input type="hidden" id="phase_id" name="phase_id" value="{{ old('phase_id', request()->get('phase_id', null)) }}">
					</div>
					
					<div class="col-12 mb-4">
						<div class="select-wrap">
							<select class="form-select border-0 select-client" name="client_id" id="client">
								<option value="">SELECT CLIENT</option>
								@foreach($clients as $client)
									<option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
										{{ $client->name }}
									</option>
								@endforeach
							</select>
							
							<div class="select-icon"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" width="18" height="10" /></div>
						</div>
					</div>
					<div class="col-md-4 mb-4">
						<div class="select-wrap">
							<input class="form-control border-0" type="text" name="job_code" id="job_code" value="{{ old('job_code') }}" placeholder="Add Job Code...">
						</div>
					</div>
					<div class="col-md-8 mb-4">
						<input class="form-control border-0" type="text" id="projectTitle" name="project_title" value="{{ old('project_title') }}" placeholder="Add a project title..." />
					</div>
					<div class="col-head col-12 mt-5">
						<h2 class="text-uppercase">PROJECT TIMELINE TYPE</h2>
						<hr class="mt-0 mb-4 border-white" />
					</div>
					<div class="col-12">


						<div class="row">
							@foreach($timelines as $timeline)
								<div class="col-md-3 mb-3">
									<div class="form-check">
										<input class="form-check-input timeline-radio" name="timeline_type" type="radio" value="{{ $timeline->id }}" id="{{ $timeline->name }}" {{ old('timeline_type') == $timeline->id ? 'checked' : '' }} />
										<label class="form-check-label" for="{{ $timeline->name }}">{{ $timeline->name }}</label>
									</div>
								</div>
							@endforeach
						</div>










					</div>
					<div class="col-12 mt-5 color-timeline-bar d-block">
						<div class="ribbon d-flex align-items-center justify-content-between">
							<span class="dot"></span>
							<span class="bar orange"></span>
							<span class="bar second green"></span>
							<span class="bar pink"></span>
							<span class="bar purple"></span>
							<span class="bar orange"></span>
							<span class="dot"></span>
						</div>
					</div>
					<div class="col-head col-12 mt-5 set-timeline d-block">
						<h2 class="text-white">Set Timeline</h2>
						<div class="timeline-set project-phase">
							@foreach($phases as $index => $phase)
								@php
									$phase_icon = $phase->icon;
									$uppercase_name = strtoupper(trim(explode(preg_match('/[\s,]+/', $phase->name, $match) ? $match[0] : ' ', $phase->name, 2)[0])); //IMP
								@endphp
								<div class="meta-row d-flex mt-3 orange ">
									<div class="logo d-flex align-items-center justify-content-center rounded-circle">
										<img src="{{ $phase_icon }}" alt="" />
									</div>
									<div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
										<div class="title d-flex align-items-center justify-content-end fs-6">{{ $uppercase_name }}</div>
										<div class="duration col-meta align-self-center text-center">
											<strong>
												<a href="javascript:void(0)" class="duration-modal-link text-decoration-underline cursor-pointer" data-phase_id="{{ $index }}">
													Set Duration
												</a>
											</strong>
										</div>
										<div class="target col-meta align-self-center text-center" data-phase_id="{{ $index + 1 }}">
											Suggested Duration:
										</div>
										<div class="status col-meta align-self-center text-center pt-0">
											<strong>
												<a href="javascript:void(0)" class="target-modal-link" data-phase_id="{{ $index }}">
													Set Target
												</a>
											</strong>
										</div>
                                        <input type="hidden" name="project_duration{{ $index + 1 }}" value="{{ old('project_duration' . ($index + 1)) }}">
                                        <input type="hidden" name="project_target{{ $index + 1 }}" value="{{ old('project_target' . ($index + 1)) }}">
									</div>
								</div>
							@endforeach
						</div>
						
					</div>
					<!-- Single Duration Modal -->
					<div class="modal fade" id="durationModal">
						<div class="modal-dialog modal-dialog-centered">
							<div class="modal-content" style="background: linear-gradient(135deg, rgb(20, 20, 20) 0%, rgb(10, 10, 10) 100%); border: 1px solid rgba(255, 76, 0, 0.3); border-radius: 16px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);">
								<div class="modal-body text-center">
									<div class="position-relative mb-5">
										<div class="position-absolute" style="width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,76,0,0.15) 0%, rgba(255,76,0,0) 70%); top: -50px; right: -50px; border-radius: 50%;"></div>
										<div class="position-absolute" style="width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,76,0,0.1) 0%, rgba(255,76,0,0) 70%); bottom: -30px; left: -30px; border-radius: 50%;"></div>
										
										<div class="d-flex justify-content-between align-items-center mt-2">
											<h2 class="text-uppercase text-white mb-0" style="font-family: 'Futura Std', sans-serif; font-weight: 700; letter-spacing: 1px;">Project Duration</h2>
											<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
										</div>
										<div class="mt-1" style="height: 3px; width: 60px; background: #ff4c00; border-radius: 2px;"></div>
									</div>
									
									<div class="row mx-2 align-items-center">
										<div class="col-6">
											<div class="position-relative">
												<input class="form-control select-day" type="number" min="1" value="{{ old('duration_value') ?? 1 }}" style="height: 60px; background: rgba(30, 30, 30, 0.6); border: 1px solid rgba(255, 255, 255, 0.1); color: white; font-size: 22px; font-weight: 600; text-align: center; border-radius: 12px; padding-right: 15px; font-family: 'Futura Std', sans-serif;">
												<label class="position-absolute text-uppercase" style="top: -10px; left: 15px; background: rgb(15, 15, 15); padding: 0 10px; color: #ff4c00; font-size: 12px; letter-spacing: 1px; font-family: 'Futura Std', sans-serif;">Duration</label>
											</div>
										</div>
										<div class="col-6">
											<div class="position-relative">
												<select class="form-select select-type" style="height: 60px; background: rgba(30, 30, 30, 0.6); border: 1px solid rgba(255, 255, 255, 0.1); color: white; font-size: 18px; border-radius: 12px; font-family: 'Futura Std', sans-serif; appearance: none; -webkit-appearance: none; background-image: url('data:image/svg+xml;utf8,<svg fill=\"white\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7 10l5 5 5-5z\"/><path d=\"M0 0h24v24H0z\" fill=\"none\"/></svg>'); background-repeat: no-repeat; background-position: right 10px center; padding-right: 30px;">
													<option value="day" {{ old('duration_type') == 'day' ? 'selected' : '' }}>Day</option>
													<option value="week" {{ old('duration_type') == 'week' ? 'selected' : '' }}>Week</option>
													<option value="month" {{ old('duration_type') == 'month' ? 'selected' : '' }}>Month</option>
													<option value="year" {{ old('duration_type') == 'year' ? 'selected' : '' }}>Year</option>
												</select>
												<label class="position-absolute text-uppercase" style="top: -10px; left: 15px; background: rgb(15, 15, 15); padding: 0 10px; color: #ff4c00; font-size: 12px; letter-spacing: 1px; font-family: 'Futura Std', sans-serif;">Unit</label>
											</div>
										</div>
									</div>
									
									<div class="mt-5 mb-2">
										<button class="text-uppercase set-duration-button" type="button" style="background: linear-gradient(90deg, #ff4c00, #ff7e00); border: none; border-radius: 30px; padding: 12px 35px; color: white; font-family: 'Futura Std', sans-serif; font-weight: 600; letter-spacing: 1px; box-shadow: 0 4px 15px rgba(255, 76, 0, 0.3); transition: all 0.3s ease;">Set Duration</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<!-- Single Target Modal -->
					<div class="modal fade" id="targetModal">
						<div class="modal-dialog modal-dialog-centered">
							<div class="modal-content" style="background: linear-gradient(135deg, rgb(20, 20, 20) 0%, rgb(10, 10, 10) 100%); border: 1px solid rgba(255, 76, 0, 0.3); border-radius: 16px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);">
								<div class="modal-body text-center">
									<div class="position-relative mb-5">
										<div class="position-absolute" style="width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,76,0,0.15) 0%, rgba(255,76,0,0) 70%); top: -50px; right: -50px; border-radius: 50%;"></div>
										<div class="position-absolute" style="width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,76,0,0.1) 0%, rgba(255,76,0,0) 70%); bottom: -30px; left: -30px; border-radius: 50%;"></div>
										
										<div class="d-flex justify-content-between align-items-center mt-2">
											<h2 class="text-uppercase text-white mb-0" style="font-family: 'Futura Std', sans-serif; font-weight: 700; letter-spacing: 1px;">Project Target</h2>
											<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
										</div>
										<div class="mt-1" style="height: 3px; width: 60px; background: #ff4c00; border-radius: 2px;"></div>
									</div>
									
									<div class="mx-2">
										<div class="position-relative mb-4">
											<input class="form-control target-date" type="date" value="{{ old('target_date') }}" style="height: 60px; background: rgba(30, 30, 30, 0.6); border: 1px solid rgba(255, 255, 255, 0.1); color: white; font-size: 18px; border-radius: 12px; font-family: 'Futura Std', sans-serif; padding-left: 15px; padding-right: 15px;">
											<label class="position-absolute text-uppercase" style="top: -10px; left: 15px; background: rgb(15, 15, 15); padding: 0 10px; color: #ff4c00; font-size: 12px; letter-spacing: 1px; font-family: 'Futura Std', sans-serif;">Target Date</label>
										</div>
									</div>
									
									<div class="mt-5 mb-2">
										<button class="text-uppercase cta" type="button" style="background: linear-gradient(90deg, #ff4c00, #ff7e00); border: none; border-radius: 30px; padding: 12px 35px; color: white; font-family: 'Futura Std', sans-serif; font-weight: 600; letter-spacing: 1px; box-shadow: 0 4px 15px rgba(255, 76, 0, 0.3); transition: all 0.3s ease;">Set Target</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-head col-12 mt-5">
						<h2 class="text-uppercase">CONTACTS & BILLING</h2>
						<hr class="mt-0 mb-4 border-white" />
					</div>
					
					<div class="col-12 mt-4 d-flex align-items-center justify-content-between">
						<h2 class="text-white mb-0">Add Contacts</h2>
						<button id="addMoreContactsButton" type="button" class="cta ms-3 mt-0">Add More Contact</button>
					</div>
					
					<div id="contactsWrapper">
						@foreach(old('social_detail', ['']) as $index => $email)
                        <div class="col-12 mt-4 contacts-input">
                            <div class="input-wrap d-flex">
                                <div class="icon d-flex align-items-center justify-content-center"><img src="{{ asset('images/email-icon.svg') }}" alt="" /></div>
                                <div class="input flex-grow-1">
                                    <input class="form-control border-0" type="email" id="email" name="social_detail[]" value="{{ $email }}" />
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>

                   
                    <div class="col-12">
                        <div class="row">
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add PM Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="pm_hours" value="{{ old('pm_hours') }}">
                            </div>
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add Designer Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="designer_hours" value="{{ old('designer_hours') }}">
                            </div>
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add Developer Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="developer_hours" value="{{ old('developer_hours') }}">
                            </div>
                        <div>
                        <div class="row">
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add CS Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="cs_hours" value="{{ old('cs_hours') }}">
                            </div>
                        </div>                            
                    </div>

					<div class="col-12 mt-4">
						<h2 class="mt-4 text-white">Add Harvest Estimate Link</h2>
					</div>
					<div class="col-12 mt-4">
						<div class="input-wrap d-flex">
							<div class="icon d-flex align-items-center justify-content-center"><img src="{{ asset('images/harvest-icon.svg') }}" alt="" /></div>
							<div class="input flex-grow-1">
								<input class="form-control border-0" id="harvest" name="harvest_link" value="{{ old('harvest_link') }}" />
							</div>
						</div>
					</div>
					<div class="col-12 mt-4">
						<h2 class="mt-4 text-white">Invoice Schedule</h2>
						<div class="row mt-4 invoice-options">
							<div class="col-md-3 mb-3">
								<div class="form-check">
									<input class="form-check-input" type="radio" name="invoice_schedule" value="by_phase" id="byPhase" {{ old('invoice_schedule') == 'by_phase' ? 'checked' : '' }} />
									<label class="form-check-label" for="byPhase">By Phase</label>
								</div>
							</div>
							<div class="col-md-3 mb-3">
								<div class="form-check">
									<input class="form-check-input" type="radio" name="invoice_schedule" value="by_date" id="byDate" {{ old('invoice_schedule') == 'by_date' ? 'checked' : '' }} />
									<label class="form-check-label" for="byDate">By Date</label>
								</div>
							</div>
							<div class="col-md-3 mb-3">
								<div class="form-check">
									<input class="form-check-input" type="radio" name="invoice_schedule" value="reoccurring" id="reoccurring" {{ old('invoice_schedule') == 'reoccurring' ? 'checked' : '' }} />
									<label class="form-check-label" for="reoccurring">Reccurring</label>
								</div>
							</div>
							<div class="col-md-3 mb-3">
								<div class="form-check">
									<input class="form-check-input" type="radio" name="invoice_schedule" value="other" id="other" {{ old('invoice_schedule') == 'other' ? 'checked' : '' }} />
									<label class="form-check-label" for="other">Other</label>
								</div>
							</div>
						</div>
					</div>
					
					<div class="col-head col-12 mt-5">
						<h2 class="text-uppercase">SEND KICKOFF MESSAGE (OPTIONAL)</h2>
						<hr class="mt-0 mb-4 border-white" />
						<div class="row">
							<div class="col-md-3 mb-3">
								<div class="form-check">
									<input class="form-check-input" type="radio" value="yes" id="yes" name="kickoff_type" {{ old('kickoff_type') == 'yes' ? 'checked' : '' }} />
									<label class="form-check-label" for="yes">Yes</label>
								</div>
							</div>
							<div class="col-md-3 mb-3">
								<div class="form-check">
									<input class="form-check-input" type="radio" value="no" id="no" name="kickoff_type" {{ old('kickoff_type') == 'no' ? 'checked' : '' }} />
									<label class="form-check-label" for="no">No</label>
								</div>
							</div>
						</div>
					</div>

					<div id="messageWrapper">
                        <div class="col-12 mt-4">
                            <input class="form-control border-0" type="text" id="kickoffTitle" name="kickoff_title" value="{{ old('kickoff_title') }}" placeholder="Add a message title here..." />
                        </div>
                        <div class="col-12 mt-4">
                            <textarea class="textarea border-0" name="kickoff_description" placeholder="Type your message here...">{{ old('kickoff_description') }}</textarea>
                            <div class="form-text">Your comment will be sent to Drew McKenna, Sally McCarthy and Client <a href="#">(change)</a> and appear in Message Center</div>
                        </div>
                        <div class="col-12 mt-5">
                            <div class="upload-btn-wrapper" id="fileUploadWrapper">
                                <button class="btn-upload text-uppercase text-white d-flex align-items-center">
                                    <i class="bi bi-upload me-2"></i> Upload a file
                                </button>
                                <input type="file" name="kickoff_file" id="kickoffFileInput" />
                            </div>
                        </div>
                    </div>
					
					<div class="col-12 mt-5">
						<h2 class="text-uppercase">Assign to</h2>
						<hr class="mt-0 mb-4 border-white" />
					</div>
					<div class="col-12">
						<div class="row user-checkbox-row">
							<!-- assigned Users -->
							@if(isset($users))
								@foreach($users as $user)
								<div class="col-md-3 mb-3">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="assigned_users[]" value="{{ $user->id }}" id="user_{{ $user->id }}" 
										{{ is_array(old('assigned_users')) && in_array($user->id, old('assigned_users')) ? 'checked' : '' }} />
										<label class="form-check-label" for="user_{{ $user->id }}">{{ $user->name }}</label>
									</div>
								</div>
								@endforeach
							@else
								<p class="text-danger">No Users Found For Selected Client</p>
							@endif
						</div>
                        <input type="hidden" name="checked_users" value="{{ old('checked_users') }}">
					</div>
					<div class="col-12 mt-5 text-center">
						<button class="cta text-uppercase mt-0" type="submit">CREATE PROJECT</button>
					</div>
				</form>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
	.upload-btn-wrapper input[type="file"] {
    opacity: 0;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
}
.upload-btn-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
}
.btn-upload {
    background-color: #f17713;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

</style>
<link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
@endpush

@push('script')

<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script>


<script>

 let timelineRadioButtons = document.querySelectorAll('.timeline-radio');

timelineRadioButtons.forEach((radio) => {
  radio.addEventListener('change', () => {
    const checkedRadio = document.querySelector('.timeline-radio:checked');

    if(checkedRadio.id=='Video Project'){
        console.log('going');
        handleVideoProjectPhases();
    } else if(checkedRadio.id=='Print Project'){
        console.log('print project');
        handlePrintProjectPhases();
    } else if(checkedRadio.id=='Publication Project'){
        console.log('publication project');
        handlePrintProjectPhases(); // Use same function as Print Project
    } else if(checkedRadio.id=='Social Media Project'){
        console.log('social media project');
        handleSocialMediaProjectPhases();
    } else {
        resetToDefaultPhases();
    }
  });
});

function handleVideoProjectPhases() {
    const metaRows = document.querySelectorAll('.project-phase .meta-row');
    console.log('Found meta rows:', metaRows.length);
    
    // Change phase names for Video Project
    if (metaRows.length >= 4) {
        // Second phase (index 1) -> SCRIPT
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'SCRIPT';
        }
        
        // Third phase (index 2) -> EDIT
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'EDIT';
        }
        
        // Fourth phase (index 3) -> PUBLISH
        

        if(metaRows[3]){

         metaRows[3].style.setProperty('display', 'none', 'important');
            metaRows[3].style.setProperty('visibility', 'hidden', 'important');
            metaRows[3].style.setProperty('opacity', '0', 'important');
            metaRows[3].style.setProperty('height', '0', 'important');
            metaRows[3].style.setProperty('overflow', 'hidden', 'important');
        }

        if (metaRows[4] && metaRows[3].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'PUBLISH';
        }


        
        
        // Hide all phases after the fourth one (index 3)
        for (let i = 5; i < metaRows.length; i++) {
            metaRows[i].style.setProperty('display', 'none', 'important');
            metaRows[i].style.setProperty('visibility', 'hidden', 'important');
            metaRows[i].style.setProperty('opacity', '0', 'important');
            metaRows[i].style.setProperty('height', '0', 'important');
            metaRows[i].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding phase:', i);
        }
    }
}

function handlePrintProjectPhases() {
    const metaRows = document.querySelectorAll('.project-phase .meta-row');
    console.log('Found meta rows for print:', metaRows.length);
    
    // Change phase names for Print Project
    if (metaRows.length >= 4) {
        // First phase (index 0) -> DEFINE
        if (metaRows[0] && metaRows[0].querySelector('.title')) {
            metaRows[0].querySelector('.title').textContent = 'DEFINE';
        }
        
        // Second phase (index 1) -> CONTENT
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'CONTENT';
        }
        
        // Third phase (index 2) -> DESIGN
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'DESIGN';
        }
        
      


        if(metaRows[3]){

            metaRows[3].style.setProperty('display', 'none', 'important');
            metaRows[3].style.setProperty('visibility', 'hidden', 'important');
            metaRows[3].style.setProperty('opacity', '0', 'important');
            metaRows[3].style.setProperty('height', '0', 'important');
            metaRows[3].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:');
        }

          // Fourth phase (index 3) -> PRINT
        if (metaRows[4] && metaRows[3].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'PRINT';

        }
        
        // Hide all phases after the fourth one (index 3)
        for (let i = 5; i < metaRows.length; i++) {
            metaRows[i].style.setProperty('display', 'none', 'important');
            metaRows[i].style.setProperty('visibility', 'hidden', 'important');
            metaRows[i].style.setProperty('opacity', '0', 'important');
            metaRows[i].style.setProperty('height', '0', 'important');
            metaRows[i].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:', i);
        }
    }
}

function handleSocialMediaProjectPhases() {
    const metaRows = document.querySelectorAll('.project-phase .meta-row');
    console.log('Found meta rows for social media:', metaRows.length);
    
    // Change phase names for Social Media Project (same as Print but last phase is PUBLISH)
    if (metaRows.length >= 4) {
        // First phase (index 0) -> DEFINE
        if (metaRows[0] && metaRows[0].querySelector('.title')) {
            metaRows[0].querySelector('.title').textContent = 'DEFINE';
        }
        
        // Second phase (index 1) -> CONTENT
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'CONTENT';
        }
        
        // Third phase (index 2) -> DESIGN
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'DESIGN';
        }
        
    if(metaRows[3]){

            metaRows[3].style.setProperty('display', 'none', 'important');
            metaRows[3].style.setProperty('visibility', 'hidden', 'important');
            metaRows[3].style.setProperty('opacity', '0', 'important');
            metaRows[3].style.setProperty('height', '0', 'important');
            metaRows[3].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:');
        }

          // Fourth phase (index 3) -> PRINT
        if (metaRows[4] && metaRows[3].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'PUBLISH';

        }
        
        // Hide all phases after the fourth one (index 3)
        for (let i = 5; i < metaRows.length; i++) {
            metaRows[i].style.setProperty('display', 'none', 'important');
            metaRows[i].style.setProperty('visibility', 'hidden', 'important');
            metaRows[i].style.setProperty('opacity', '0', 'important');
            metaRows[i].style.setProperty('height', '0', 'important');
            metaRows[i].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:', i);
        }
    }
}

function resetToDefaultPhases() {
    const metaRows = document.querySelectorAll('.project-phase .meta-row');
    
    // Reset all phases to be visible
    metaRows.forEach(row => {
        row.style.removeProperty('display');
        row.style.removeProperty('visibility');
        row.style.removeProperty('opacity');
        row.style.removeProperty('height');
        row.style.removeProperty('overflow');
        row.style.display = 'flex';
    });
    
    // Reset phase names to original
    if (metaRows.length >= 5) {
        // Reset first phase
        if (metaRows[0] && metaRows[0].querySelector('.title')) {
            metaRows[0].querySelector('.title').textContent = 'DEFINE';
        }
        
        // Reset second phase
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'CONTENT';
        }
        
        // Reset third phase
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'DESIGN';
        }
        
        // Reset fourth phase
        if (metaRows[3] && metaRows[3].querySelector('.title')) {
            metaRows[3].querySelector('.title').textContent = 'CODE';
        }
        
        // Reset fifth phase
        if (metaRows[4] && metaRows[4].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'DEPLOY';
        }
    }
}


    </script>


<script>
    $(document).ready(function () {
        function toggleTimelineVisibility() {
            const selectedId = $('input[name="timeline_type"]:checked').attr('id');
            if (selectedId === 'Maintenance Project' || selectedId === 'Voyager Project') {
                $('.color-timeline-bar, .set-timeline').removeClass('d-block').addClass('d-none');
            } else {
                $('.color-timeline-bar, .set-timeline').removeClass('d-none').addClass('d-block');
            }
        }

        
        toggleTimelineVisibility();

       
        $('input[name="timeline_type"]').on('change', function () {
            validateTimelineRows(); 
            toggleTimelineVisibility();
        });
    });
</script>



<script>

const $checkedUsersInput = $('input[name="checked_users"]');
const checkboxesSelector = 'input[name="assigned_users[]"]';


let colorgrid = [
    { index: 1, color: '#ff4c00' },
    { index: 2, color: '#65ccb0' },
    { index: 3, color: '#f45689' },
    { index: 4, color: '#a15cd4' },
    { index: 5, color: '#ff4c00' },
];
	$(document).ready(function() {	

    initializeApp();

	const errorDiv = document.getElementById('backend-error');
        if (errorDiv) {
           
            const firstErrorFieldName = @json(array_key_first($errors->toArray()));
            const field = document.querySelector(`[name="${firstErrorFieldName}"]`);

            if (field) {
                field.scrollIntoView({ behavior: 'smooth', block: 'center' });
                field.focus();
                field.classList.add('is-invalid');
            }
        }
});


 
document.addEventListener('DOMContentLoaded', function () {
    
    restoreProjectSelections();

    // Check if any radio button is already selected and apply phase changes
    const checkedRadio = document.querySelector('.timeline-radio:checked');
    if (checkedRadio) {
        if (checkedRadio.id == 'Video Project') {
            handleVideoProjectPhases();
        } else if (checkedRadio.id == 'Print Project') {
            handlePrintProjectPhases();
        } else if (checkedRadio.id == 'Publication Project') {
            handlePrintProjectPhases();
        } else if (checkedRadio.id == 'Social Media Project') {
            handleSocialMediaProjectPhases();
        }
    }

    function updateCheckedUsersInput() {
        // console.log("Updating input based on checkboxes");
        let checkedValues = [];
        $(checkboxesSelector + ':checked').each(function() {
            checkedValues.push($(this).val());
        });
        $checkedUsersInput.val(checkedValues.join(','));
        // console.log("Input value updated:", $checkedUsersInput.val());
    }

    function initializeCheckedCheckboxes() {
        // console.log("initialize checkbox");
        const initialCheckedValues = $checkedUsersInput.val().split(',');
        // console.log("Initial input value:", $checkedUsersInput.val());
        $(checkboxesSelector).each(function() {
            const checkboxValue = $(this).val();
            // console.log("initial checkbox values: "+initialCheckedValues)
            const shouldBeChecked = initialCheckedValues.includes(checkboxValue) && checkboxValue !== "";
            $(this).prop('checked', shouldBeChecked);
            // console.log(`Checkbox with value '${checkboxValue}' should be checked: ${shouldBeChecked}`);
        });
        // console.log("Checkboxes initialized based on input.");
    }

    // Initialize checkboxes based on input value on page load
    initializeCheckedCheckboxes();

    // Update input value when checkboxes change (delegated event handling for dynamic elements)
    $(document).on('change', checkboxesSelector, updateCheckedUsersInput);


    const wrapper = document.getElementById('fileUploadWrapper');
    const fileInput = wrapper.querySelector('input[type="file"]');
    const uploadBtn = wrapper.querySelector('button');

    uploadBtn.addEventListener('click', function (e) {
        e.preventDefault();
        fileInput.click();
    });

    fileInput.addEventListener('change', function (e) {
        const file = e.target.files[0];
        if (!file) return;

        
        const oldPreview = wrapper.querySelector('.file-preview-wrapper');
        if (oldPreview) oldPreview.remove();

        const previewWrapper = document.createElement('div');
        previewWrapper.className = "file-preview-wrapper mt-3";

        let previewContent = '';
        if (file.type.startsWith('image/')) {
            const imgURL = URL.createObjectURL(file);
            previewContent = `<img src="${imgURL}" class="img-fluid rounded mb-2" style="max-height: 200px;" alt="Preview" />`;
        } else if (file.type === "application/pdf") {
            previewContent = `<i class="bi bi-file-earmark-pdf-fill fs-1 text-danger"></i><p class="mb-1">${file.name}</p>`;
        } else {
            previewContent = `<i class="bi bi-file-earmark-fill fs-1 text-secondary"></i><p class="mb-1">${file.name}</p>`;
        }

        previewWrapper.innerHTML = `
            <div class="d-flex flex-column align-items-start">
                ${previewContent}
                <button type="button" class="btn btn-sm btn-danger mt-2" id="removeFileBtn">
                    <i class="bi bi-x-lg me-1"></i>Remove file
                </button>
            </div>
        `;

        fileInput.classList.add('d-none');
        uploadBtn.classList.add('d-none');

      
        wrapper.appendChild(previewWrapper);

        document.getElementById('removeFileBtn').addEventListener('click', function () {
            fileInput.value = '';
            fileInput.classList.remove('d-none');
            uploadBtn.classList.remove('d-none');
            previewWrapper.remove();
        });
    });
});





function initializeApp() {
  
    flatpickr('input[type="date"]');
    
 
    initializeClientSelection();
    
  
    initializeTimeline();
    
   
    initializeContactManagement();
    

    initializeFormValidation();
}



    
function initializeClientSelection() {
    function handleClientSelection(clientId) {
        if (clientId && clientId !== 'null') {
            loadClientUsers(clientId);
            loadClientSocialDetails(clientId);
        } else {
            // Clear contacts when no client is selected
            populateContactsField([]);
        }
    }

    $('.select-client').on('change', function() {
        handleClientSelection($(this).val());
    });

    // Check for initial value in dropdown(retained value) after page reload and errors
    const initialClientId = $('.select-client').val();
    if (initialClientId && initialClientId !== 'null') {
        handleClientSelection(initialClientId);
    }
}


function loadClientUsers(clientId) {
    $.ajax({
        method: 'POST',
        url: "{{ route('client-users') }}",
        data: {
            client_id: clientId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            renderUserCheckboxes(response.users);
        },
        error: function(err) {
            console.error('Error loading client users:', err);
            console.log(err.responseText);
        }
    });
}

function loadClientSocialDetails(clientId) {
    $.ajax({
        method: 'POST',
        url: "{{ route('client-social-details') }}",
        data: {
            client_id: clientId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            populateContactsField(response.social_details);
        },
        error: function(err) {
            console.error('Error loading client social details:', err);
            console.log(err.responseText);
        }
    });
}

function populateContactsField(socialDetails) {
    const contactsWrapper = document.getElementById('contactsWrapper');
    if (!contactsWrapper) return;
    
    // Clear existing contacts except the first one (keep at least one field)
    const existingContacts = contactsWrapper.querySelectorAll('.contacts-input');
    for (let i = 1; i < existingContacts.length; i++) {
        existingContacts[i].remove();
    }
    
    // Remove any existing notification
    const existingNotification = contactsWrapper.querySelector('.contacts-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    // Populate with client social details
    if (socialDetails && socialDetails.length > 0) {
        // Add notification that contacts were loaded
        const notification = document.createElement('div');
        notification.className = 'contacts-notification alert alert-info alert-dismissible fade show mt-2';
        notification.innerHTML = `
            <i class="fas fa-info-circle me-2"></i>
            <strong>Contacts loaded from client:</strong> ${socialDetails.length} contact(s) automatically populated from client's social details.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        contactsWrapper.insertBefore(notification, contactsWrapper.firstChild);
        
        socialDetails.forEach((detail, index) => {
            if (index === 0) {
                // Update the first existing contact field
                const firstContact = contactsWrapper.querySelector('.contacts-input input[type="email"]');
                if (firstContact) {
                    firstContact.value = detail.value;
                }
            } else {
                // Add new contact fields for additional social details
                addNewContactField(contactsWrapper, index + 1, detail.value);
            }
        });
    } else {
        // If no social details, clear the first field
        const firstContact = contactsWrapper.querySelector('.contacts-input input[type="email"]');
        if (firstContact) {
            firstContact.value = '';
        }
        
        // Add notification that no contacts were found
        const notification = document.createElement('div');
        notification.className = 'contacts-notification alert alert-warning alert-dismissible fade show mt-2';
        notification.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>No contacts found:</strong> This client doesn't have any social details configured.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        contactsWrapper.insertBefore(notification, contactsWrapper.firstChild);
    }
}


function renderUserCheckboxes(users) {
    const container = document.querySelector('.user-checkbox-row');
    if (!container) return;
    
    container.innerHTML = "";


	if(users.length==0){

		container.innerHTML=`<p class="text-danger">No Users Found For Selected Client</p>`;
	}
    
    let allUsersHTML = '';

    users.forEach(user => {
            const isChecked = $checkedUsersInput.val().split(',').includes(String(user.id)) && String(user.id) !== "";
            const checkedAttribute = isChecked ? 'checked' : '';

            const html = `
                <div class="col-md-3 col-xl-2 mb-3">
                    <div class="form-check">
                        <input class="form-check-input" name="assigned_users[]" type="checkbox" value="${user.id}" id="user_${user.id}" ${checkedAttribute} />
                        <label class="form-check-label" for="user_${user.id}">${user.name}</label>
                    </div>
                </div>`;
            allUsersHTML += html;
        });

    container.insertAdjacentHTML('beforeend', allUsersHTML); 
}


function initializeTimeline() {
    let currentPhaseId = null;
    const durationModal = document.getElementById('durationModal');
    const targetModal = document.getElementById('targetModal');
    
   
    setupDurationLinks(durationModal);
    
  
    setupTargetLinks(targetModal);
    

    setupDurationButton(durationModal);
    
   
    setupTargetButton(targetModal);
    
  
    setupEditButtons(durationModal, targetModal);
    
  
    updateProgressBar();
}


function setupDurationLinks(durationModal) {
    const durationLinks = document.querySelectorAll('.duration-modal-link');
    
    durationLinks.forEach(link => {
        link.addEventListener('click', function() {
            const phaseId = this.getAttribute('data-phase_id');
            durationModal.setAttribute('data-active-phase', phaseId);
            
            const phaseIndex = parseInt(phaseId) + 1;
            const hiddenInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
            
           
            if (hiddenInput && hiddenInput.value) {
                const durationText = hiddenInput.value;
                const match = durationText.match(/(\d+)\s+(\w+)/);
                if (match) {
                    const value = match[1];
                    const type = match[2].replace(/s$/, '');
                    
                    document.querySelector('#durationModal .select-day').value = value;
                    document.querySelector('#durationModal .select-type').value = type;
                }
            } else {
               
                const daySelect = document.querySelector('#durationModal .select-day');
                const typeSelect = document.querySelector('#durationModal .select-type');
                if (daySelect) daySelect.value = '';
                if (typeSelect) typeSelect.value = 'day';
            }
            
            new bootstrap.Modal(durationModal).show();
        });
    });
}


function setupTargetLinks(targetModal) {
    const targetLinks = document.querySelectorAll('.target-modal-link');
    
    targetLinks.forEach(link => {
        link.addEventListener('click', function() {
            const phaseId = this.getAttribute('data-phase_id');
            targetModal.setAttribute('data-active-phase', phaseId);
            
            const phaseIndex = parseInt(phaseId) + 1;
            const hiddenInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
            
            
            const targetDateInput = document.querySelector('#targetModal .target-date');
            if (hiddenInput && hiddenInput.value && targetDateInput) {
                targetDateInput.value = hiddenInput.value;
            } else if (targetDateInput) {
                targetDateInput.value = '';
            }
            
            new bootstrap.Modal(targetModal).show();
        });
    });
}


function setupDurationButton(durationModal) {
    const setDurationButton = document.querySelector('.set-duration-button');
    
    if (setDurationButton) {
        setDurationButton.addEventListener('click', function() {
            const activePhase = durationModal.getAttribute('data-active-phase');
            const phaseIndex = parseInt(activePhase) + 1;
            
            const durationValue = document.querySelector('#durationModal .select-day').value;
            const durationType = document.querySelector('#durationModal .select-type').value;
            
            if (durationValue) {
                saveDurationValue(activePhase, phaseIndex, durationValue, durationType);
                bootstrap.Modal.getInstance(durationModal).hide();
                updateProgressBar();
            }
        });
    }
}


function saveDurationValue(activePhase, phaseIndex, durationValue, durationType) {
    // console.log("durationvalue: " + durationValue)
    const durationText = `${durationValue} ${durationType}${durationValue > 1 ? 's' : ''}`;
    // console.log("duration text in savedurationvalue: " + durationText)
  
    let hiddenInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
    if (!hiddenInput) {
        hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = `project_duration${phaseIndex}`;
        document.querySelector('#addNewProject').appendChild(hiddenInput);
    }
    hiddenInput.value = durationText;
    
   
    const metaRow = document.querySelectorAll('.meta-row')[activePhase];
    if (metaRow) {
        const durationCell = metaRow.querySelector('.duration');
	
        if (durationCell) {

			const colorObj = colorgrid.find(item => item.index === parseInt(phaseIndex));
			const textColor = colorObj ? colorObj.color : 'orange';
            durationCell.innerHTML = `<strong style="color:${textColor};">${durationText}</strong>`;
            
          
            let editBtn = durationCell.querySelector('.edit-duration-btn');
            if (!editBtn) {
                editBtn = createEditDurationButton(activePhase);
                durationCell.appendChild(editBtn);
            }
        }
    }
}

//made by me
function restoreProjectSelections() {
    // console.log("restoreProjectSelections");

    // Handle project_duration inputs
    const durationInputs = document.querySelectorAll('input[name^="project_duration"]');
    durationInputs.forEach(input => {
        const match = input.name.match(/project_duration(\d+)/);  // Check if it matches "project_duration1", "project_duration2", etc.
        // console.log(input);
        // console.log("value: " + input.value);

        if (match && input.value) {
            const phaseIndex = parseInt(match[1]); //digit in input name attribute string
            const durationText = input.value.trim();
            // console.log("durationText: " + durationText);

            // Check if input contains both a number and a string (i.e., "1 day", "2 days")
            const parts = durationText.split(' ');

            // Validate that there are exactly 2 parts: a number and a unit (e.g., "1 day" or "2 days")
            if (parts.length === 2) {
                const durationValue = parseInt(parts[0]);  // The number part (e.g., 1, 2)
                let durationType = parts[1];  // The type part (e.g., day, days)

                // Validate that the first part is a number and the second part is a valid string (e.g., day or days)
                if (!isNaN(durationValue) && durationValue > 0 && isNaN(durationType)) {
                    // Adjust the duration type (remove plural 's' if necessary)
                    if (durationType.endsWith('s')) {
                        durationType = durationType.slice(0, -1);  // Remove plural "s" for consistency
                    }

                    // console.log("durationValue: " + durationValue);
                    // console.log("durationType: " + durationType);

                    const activePhase = phaseIndex - 1; // The active phase index (zero-based)
                    const metaRow = document.querySelectorAll('.meta-row')[activePhase];
                    if (metaRow && metaRow.querySelector('.duration')) {
                        saveDurationValue(activePhase, phaseIndex, durationValue, durationType);  // Call the function with the correct arguments
                    }
                } else {
                    // Invalid duration input, so do not change the text.
                    // console.error('Invalid duration input: should be a number followed by a valid string (e.g., "1 day", "2 days").');
                }
            } else {
                // Invalid format: input must contain both a number and a unit (e.g., "1 day", "2 days")
                // console.error('Invalid format: input must contain both a number and a unit (e.g., "1 day", "2 days").');
            }
        }
    });

    // Handle project_target (dates)
    const targetInputs = document.querySelectorAll('input[name^="project_target"]');
    targetInputs.forEach(input => {
        const match = input.name.match(/project_target(\d+)/);  // Match the name pattern like project_target1, project_target2, etc.
        if (match && input.value) {
            const phaseIndex = parseInt(match[1]);
            const targetDate = input.value.trim();

            // Validate the targetDate input to ensure it's a valid date
            if (isValidDate(targetDate)) {
                const activePhase = phaseIndex - 1; // The active phase index (zero-based)
                const metaRow = document.querySelectorAll('.meta-row')[activePhase];
                if (metaRow && metaRow.querySelector('.status')) {
                    saveTargetDate(activePhase, phaseIndex, targetDate);  // Call the function to save the target date
                }
            } else {
                // Invalid date input, so do not call saveTargetDate and do not change the UI
                console.error('Invalid target date input. Please enter a valid date.');
            }
        }
    });

    // Helper function to validate the date format
    function isValidDate(dateString) {
        // Try to parse the date using the Date constructor
        const date = new Date(dateString);
        
        // Check if the date is valid
        return !isNaN(date.getTime());
    }
}




function createEditDurationButton(phaseId) {


	
	const colorObj = colorgrid.find(item => item.index === parseInt(phaseId)+1);
    const iconColor = colorObj ? colorObj.color : 'orange';
    const editBtn = document.createElement('button');
    editBtn.className = 'btn btn-sm btn-outline-secondary edit-duration-btn ms-2';
    editBtn.innerHTML = `<i class="fas fa-edit" style="color:${iconColor};"></i>`;
    editBtn.setAttribute('data-phase_id', phaseId);
    
    editBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const clickedPhaseId = this.getAttribute('data-phase_id');
        const durationModal = document.getElementById('durationModal');
        durationModal.setAttribute('data-active-phase', clickedPhaseId);
        
        const phaseIndex = parseInt(clickedPhaseId) + 1;
        const hiddenInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
        
        if (hiddenInput && hiddenInput.value) {
            const durationText = hiddenInput.value;
            const match = durationText.match(/(\d+)\s+(\w+)/);
            if (match) {
                const value = match[1];
                const type = match[2].replace(/s$/, '');
                
                document.querySelector('#durationModal .select-day').value = value;
                document.querySelector('#durationModal .select-type').value = type;
            }
        }
        
        new bootstrap.Modal(durationModal).show();
    });
    
    return editBtn;
}


function setupTargetButton(targetModal) {
    const setTargetButton = document.querySelector('#targetModal .cta');
    
    if (setTargetButton) {
        setTargetButton.addEventListener('click', function() {
            const activePhase = targetModal.getAttribute('data-active-phase');
            const phaseIndex = parseInt(activePhase) + 1;
            
            const targetDateInput = document.querySelector('#targetModal .target-date');
            const targetDate = targetDateInput ? targetDateInput.value : null;
            
            if (targetDate) {
                saveTargetDate(activePhase, phaseIndex, targetDate);
                bootstrap.Modal.getInstance(targetModal).hide();
            }
        });
    }
}


function saveTargetDate(activePhase, phaseIndex, targetDate) {
    // Format the date for display
    let formattedDate;
    if (typeof dayjs !== 'undefined') {
        formattedDate = dayjs(targetDate).format('MMM D, YYYY');
    } else {
        const date = new Date(targetDate);
        formattedDate = date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
        });
    }
    
    
    let hiddenInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
    if (!hiddenInput) {
        hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = `project_target${phaseIndex}`;
        document.querySelector('#addNewProject').appendChild(hiddenInput);
    }
    hiddenInput.value = targetDate;
    
   
    const metaRow = document.querySelectorAll('.meta-row')[activePhase];
    if (metaRow) {
        const statusCell = metaRow.querySelector('.status');
        if (statusCell) {
            console.log('iam Phase index', phaseIndex);
			const colorObj = colorgrid.find(item => item.index === parseInt(phaseIndex));
			const textColor = colorObj ? colorObj.color : 'orange';
            statusCell.innerHTML = `<strong style="color:${textColor};">${formattedDate}</strong>`;
            
          
            let editBtn = statusCell.querySelector('.edit-target-btn');
            if (!editBtn) {
                editBtn = createEditTargetButton(activePhase);
                statusCell.appendChild(editBtn);
            }
        }
    }
}


function createEditTargetButton(phaseId) {

	const colorObj = colorgrid.find(item => item.index === parseInt(phaseId)+1);
    const iconColor = colorObj ? colorObj.color : 'orange';
    const editBtn = document.createElement('button');
    editBtn.className = 'btn btn-sm btn-outline-secondary edit-target-btn ms-2';
    editBtn.innerHTML = `<i class="fas fa-edit" style="color:${iconColor};"></i>`;
    editBtn.setAttribute('data-phase_id', phaseId);
    
    editBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const clickedPhaseId = this.getAttribute('data-phase_id');
        const targetModal = document.getElementById('targetModal');
        targetModal.setAttribute('data-active-phase', clickedPhaseId);
        
        const phaseIndex = parseInt(clickedPhaseId) + 1;
        const hiddenInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
        
        const targetDateInput = document.querySelector('#targetModal .target-date');
        if (hiddenInput && hiddenInput.value && targetDateInput) {
            targetDateInput.value = hiddenInput.value;
        }
        
        new bootstrap.Modal(targetModal).show();
    });
    
    return editBtn;
}


function updateProgressBar() {
    const durationInputs = [];
    let index = 1;
    let input = document.querySelector(`input[name="project_duration${index}"]`);

  
    while (input) {
        durationInputs.push(input);
        index++;
        input = document.querySelector(`input[name="project_duration${index}"]`);
    }

    if (durationInputs.length === 0) return;

    const durationValues = [];
    let totalDuration = 0;

  
    durationInputs.forEach(input => {
        let days = 0;

        if (input && input.value) {
            const match = input.value.trim().toLowerCase().match(/(\d+(?:\.\d+)?)\s*(day|week|month|year)s?/i);

            if (match) {
                const value = parseFloat(match[1]);
                const unit = match[2];

                switch (unit) {
                    case 'day':
                        days = value;
                        break;
                    case 'week':
                        days = value * 7;
                        break;
                    case 'month':
                        days = value * 30;
                        break;
                    case 'year':
                        days = value * 365;
                        break;
                }
            }
        }

        durationValues.push(days);
        totalDuration += days;
    });


	const bars = document.querySelectorAll('.ribbon .bar');
bars.forEach((bar, i) => {
    const value = durationValues[i] || 0;
    const percentage = totalDuration > 0 ? (value / totalDuration) * 100 : (100 / bars.length);

   
    bar.style.flex = 'unset'; 
    bar.style.width = `${percentage}%`;
    bar.style.transition = 'width 0.5s ease-in-out';
});

}


function setupEditButtons(durationModal, targetModal) {
    document.querySelectorAll('.meta-row').forEach((row, index) => {
        const phaseIndex = index + 1;
        
     
        const durationInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
        if (durationInput && durationInput.value) {
            const durationCell = row.querySelector('.duration');
            if (durationCell && !durationCell.querySelector('.edit-duration-btn')) {
                const editDurationBtn = createEditDurationButton(index);
                durationCell.appendChild(editDurationBtn);
            }
        }
        
       
        const targetInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
        if (targetInput && targetInput.value) {
            const statusCell = row.querySelector('.status');
            if (statusCell && !statusCell.querySelector('.edit-target-btn')) {
                const editTargetBtn = createEditTargetButton(index);
                statusCell.appendChild(editTargetBtn);
            }
        }
    });
}




function initializeContactManagement() {
    const addMoreContactsButton = document.getElementById('addMoreContactsButton');
    const contactsWrapper = document.getElementById('contactsWrapper');
    
    if (addMoreContactsButton && contactsWrapper) {
        let contactCounter = 1;
        
        addMoreContactsButton.addEventListener('click', function() {
            contactCounter++;
            addNewContactField(contactsWrapper, contactCounter);
        });
    }
}


function addNewContactField(contactsWrapper, contactCounter, value = '') {
    const newContactId = `email_${contactCounter}`;
    
    const newContactDiv = document.createElement('div');
    newContactDiv.className = 'col-12 mt-3 contacts-input';
    
    const iconSrc = document.querySelector('.contacts-input .icon img')?.src || '';
    
    newContactDiv.innerHTML = `
        <div class="input-wrap d-flex">
            <div class="icon d-flex align-items-center justify-content-center"><img src="${iconSrc}" alt="" /></div>
            <div class="input flex-grow-1">
                <input class="form-control border-0" type="email" id="${newContactId}" name="social_detail[]" value="${value}" />
            </div>
            <div class="remove-contact ms-2">
                <button type="button" class="btn btn-sm btn-danger">Remove</button>
            </div>
        </div>
    `;
    contactsWrapper.appendChild(newContactDiv);

    // Add validation if validation library is available
    if (typeof validation !== 'undefined') {
        validation.addField(`#${newContactId}`, [
            {
                rule: 'email',
                errorMessage: 'Please provide a valid email address',
            }
        ]);
    }
    
    // Add remove functionality
    const removeButton = newContactDiv.querySelector('.remove-contact button');
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            if (typeof validation !== 'undefined') {
                validation.removeField(`#${newContactId}`);
            }
            contactsWrapper.removeChild(newContactDiv);
        });
    }
}



function validateTimelineRows() {



  


  let isValid = true;
  let errorMessage = '';
  let incompletePhasesFound = [];
  let atLeastOneCompletePhase = false;
  

  const durationInputs = document.querySelectorAll('input[name^="project_duration"]');
  const targetInputs = document.querySelectorAll('input[name^="project_target"]');
  

  const phaseMap = {};
  

  durationInputs.forEach(input => {
  
    const phaseMatch = input.name.match(/project_duration(\d+)/);
    if (phaseMatch && phaseMatch[1]) {
      const phaseNum = phaseMatch[1];
      
      if (!phaseMap[phaseNum]) {
        phaseMap[phaseNum] = {};
      }
      
      phaseMap[phaseNum].hasDuration = !!input.value;
    }
  });
  

  targetInputs.forEach(input => {
    const phaseMatch = input.name.match(/project_target(\d+)/);
    if (phaseMatch && phaseMatch[1]) {
      const phaseNum = phaseMatch[1];
      
      if (!phaseMap[phaseNum]) {
        phaseMap[phaseNum] = {};
      }
      
      phaseMap[phaseNum].hasTarget = !!input.value;
    }
  });
  
 
  Object.keys(phaseMap).forEach(phaseNum => {
    const phase = phaseMap[phaseNum];
    
  
    if (phase.hasDuration && phase.hasTarget) {
      atLeastOneCompletePhase = true;
    }

    else if ((phase.hasDuration && !phase.hasTarget) || (!phase.hasDuration && phase.hasTarget)) {
      isValid = false;
      incompletePhasesFound.push(phaseNum);
    }
  });
  
  if (
  $('input[name="timeline_type"]:checked').attr('id') !== 'Maintenance Project' &&
  $('input[name="timeline_type"]:checked').attr('id') !== 'Voyager Project'
)
{
          
        

        if (!atLeastOneCompletePhase) {
            isValid = false;
            errorMessage = "At least one project phase must have both duration and target date set.";
            
        
            const firstMetaRow = document.querySelector('.meta-row');
            if (firstMetaRow) {
            firstMetaRow.classList.add('incomplete-row');
            
            const durationCell = firstMetaRow.querySelector('.duration');
            const statusCell = firstMetaRow.querySelector('.status');
            
            if (durationCell) durationCell.classList.add('missing-value');
            if (statusCell) statusCell.classList.add('missing-value');
            }
            
            showValidationError(errorMessage);
        }
        // If any phases are incomplete, show error
        else if (incompletePhasesFound.length > 0) {
            errorMessage = `Phase ${incompletePhasesFound.join(', ')} must have both duration and target date filled out.`;
            
            // Highlight the incomplete rows
            incompletePhasesFound.forEach(phaseNum => {
            const index = parseInt(phaseNum) - 1;
            const metaRow = document.querySelectorAll('.meta-row')[index];
            
            if (metaRow) {
                metaRow.classList.add('incomplete-row');
                
                // Check what's missing and highlight it
                if (!phaseMap[phaseNum].hasDuration) {
                const durationCell = metaRow.querySelector('.duration');
                if (durationCell) {
                    durationCell.classList.add('missing-value');
                }
                }
                
                if (!phaseMap[phaseNum].hasTarget) {
                const statusCell = metaRow.querySelector('.status');
                if (statusCell) {
                    statusCell.classList.add('missing-value');
                }
                }
            }
            });
            
            showValidationError(errorMessage);
        } else {
            clearValidationErrors();
        }
    }
    else{
        isValid = true;
    }
  
  return isValid;
}

function showValidationError(message) {

  clearValidationErrors();
  

  const errorDiv = document.createElement('div');
  errorDiv.className = 'alert alert-danger timeline-validation-error mt-3';
  errorDiv.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${message}`;
  

  const timelineSection = document.querySelector('.timeline-section') || document.querySelector('.ribbon');
  if (timelineSection) {
    timelineSection.parentNode.insertBefore(errorDiv, timelineSection.nextSibling);
    
  
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

function clearValidationErrors() {
 
  const existingErrors = document.querySelectorAll('.timeline-validation-error');
  existingErrors.forEach(error => error.remove());
  

  document.querySelectorAll('.incomplete-row').forEach(row => {
    row.classList.remove('incomplete-row');
  });
  
  document.querySelectorAll('.missing-value').forEach(cell => {
    cell.classList.remove('missing-value');
  });
}


function addValidationStyles() {

  if (!document.getElementById('timeline-validation-styles')) {
    const styleElement = document.createElement('style');
    styleElement.id = 'timeline-validation-styles';
    styleElement.textContent = `
      .incomplete-row {
        background-color: rgba(255, 0, 0, 0.05);
      }
      .missing-value {
        position: relative;
      }
      .missing-value::after {
        content: '!';
        position: absolute;
        top: 5px;
        right: 5px;
        color: #dc3545;
        font-weight: bold;
        font-size: 16px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: rgba(220, 53, 69, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      /* Add animation for attention */
      @keyframes highlight-pulse {
        0% { background-color: rgba(255, 0, 0, 0.05); }
        50% { background-color: rgba(255, 0, 0, 0.1); }
        100% { background-color: rgba(255, 0, 0, 0.05); }
      }
      
      .incomplete-row {
        animation: highlight-pulse 2s infinite;
      }
    `;
    document.head.appendChild(styleElement);
  }
}
function setupRealTimeValidation() {

  document.addEventListener('click', function(event) {

    if (event.target.classList.contains('set-duration-button') ||
        event.target.closest('#targetModal .cta')) {
   
      setTimeout(validateTimelineRows, 300);
    }
  });
}


function addTimelineRequirementHint() {
  const timelineSection = document.querySelector('.timeline-section') || document.querySelector('.ribbon');
  if (timelineSection) {
   
    if (!document.querySelector('.timeline-requirement-hint')) {
      const hintDiv = document.createElement('div');
      hintDiv.className = 'timeline-requirement-hint alert alert-info mt-2 d-none';
      hintDiv.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        <strong>Note:</strong> At least one project phase must have both duration and target date set.
      `;
      timelineSection.parentNode.insertBefore(hintDiv, timelineSection.nextSibling);
    }
  }
}


function initializeFormValidation() {
  setupKickoffValidation();
  addValidationStyles();
  setupRealTimeValidation();
  addTimelineRequirementHint();

  const projectForm = document.getElementById('addNewProject');
  if (projectForm) {
    projectForm.addEventListener('submit', function(e) {
      if (!validateTimelineRows()) {
        e.preventDefault();
        return false;
      }
    });
  }

  const projectValidator = new GlobalFormValidator('addNewProject', {
    rules: {
      '#client': GlobalFormValidator.validators.required,
      '#job_code': GlobalFormValidator.validators.required,
      '#projectTitle': GlobalFormValidator.validators.required,
      '#email': GlobalFormValidator.validators.email,
      'input[name="timeline_type"]': GlobalFormValidator.validators.required,
      '.invoice-options input[type="radio"]': GlobalFormValidator.validators.required,  
      '.user-checkbox-row input[type="checkbox"]': GlobalFormValidator.validators.anyChecked
    },
    messages: {
      '#client': "Please select a client",
      '#job_code': "Please add a job code",
      '#projectTitle': "Please add a project title",
      '#email': "Please provide a valid contact email",
      'input[name="timeline_type"]': "Please select a timeline option",
      '.invoice-options input[type="radio"]': "Please select at least one invoice schedule option", 
      '#kickoffTitle': "Please add a kickoff message title",
      '.user-checkbox-row input[type="checkbox"]': "Please assign the project to at least one user"
    },
    onValidate: (form) => {
      // For any custom pre-submission validation logic
      return true;
    },
    onSuccess: (form) => {
      // Run timeline validation before actual submission
      if (!validateTimelineRows()) {
        return false;
      }
      
      console.log('Form is valid, submitting...');
      form.submit();
    }
  }).initialize();
}



function setupKickoffValidation() {
    const yesRadio = document.getElementById('yes');
    const noRadio = document.getElementById('no');
    const kickoffField = document.getElementById('kickoffTitle');

    if (yesRadio) {
        yesRadio.addEventListener('change', function() {
            if (this.checked && kickoffField) {
				let messageWrapper=document.getElementById('messageWrapper');
				messageWrapper.style.display="block";
                kickoffField.classList.add('is-required'); 
            }
        });
    }

    if (noRadio) {
        noRadio.addEventListener('change', function() {
            if (this.checked && kickoffField) {

				let messageWrapper=document.getElementById('messageWrapper');
				messageWrapper.style.display="none";
                kickoffField.classList.remove('is-required');
            }
        });
    }
}


</script>


@endpush