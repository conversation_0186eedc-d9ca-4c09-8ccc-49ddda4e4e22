<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('resources', 'cs_hours')) {
            Schema::table('resources', function (Blueprint $table) {
                $table->integer('cs_hours')->default(0)->after('developer_hours');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('resources', 'cs_hours')) {
            Schema::table('resources', function (Blueprint $table) {
                $table->dropColumn('cs_hours');
            });
        }
    }
};
