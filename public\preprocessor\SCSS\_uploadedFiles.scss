.uploaded-files {
	padding-block: 5rem;

	.head {
		h2 {
			color: #000;
			font: 700 16px $inter;
		}
	}

	.files-wrap {
		border-left: 1px solid rgba(#000, 0.5);
		border-top: 1px solid rgba(#000, 0.5);

		.box-wrap {
			border-bottom: 1px solid rgba(#000, 0.5);
			border-right: 1px solid rgba(#000, 0.5);
		}

		.box {
			.img {
				aspect-ratio: 230/175;
				background: #f3f3f3;

				&.pic {
					overflow: hidden;

					img {
						height: 100%;
						object-fit: cover;
						width: 100%;
					}
				}

				&.icon {
					img {
						mix-blend-mode: multiply;
					}
				}
			}

			h2 {
				color: #111;
				font: 700 12px $inter;
			}

			h3 {
				color: rgba(#111, 0.5);
				font: 12px $inter;
			}
		}
	}

	.pages {
		color: #000;
		font: 500 16px $inter;

		a {
			color: rgba(#000, 0.5);
			margin-left: 0.4rem;
			text-decoration: none;

			&.active {
				color: $orange;
				text-decoration: underline;
			}
		}
	}
}
