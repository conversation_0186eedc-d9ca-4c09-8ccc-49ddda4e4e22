<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('brand_projects', function (Blueprint $table) {
            $table->id();
            $table->foreignId("brand_id")->references("id")->on("brands");
            $table->foreignId("project_id")->references("id")->on("projects");
            $table->foreignId("assigned_by_user")->references("id")->on("users");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('brand_projects', function (Blueprint $table) {
            $table->dropForeign(['brand_id']);  // Dropping the foreign key constraint
            $table->dropForeign(['project_id']);  // Dropping the foreign key constraint
            $table->dropForeign(['assigned_by_user']);  // Dropping the foreign key constraint
        });
        Schema::dropIfExists('brand_projects');
    }
};
