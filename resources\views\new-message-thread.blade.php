@extends('layout.app')
@section('title', 'New Thread')
@section('content')

<section class="client-header">
			<div class="container-xxl">
				<hr class="mt-0 mb-4 border-white" />
				 <div class="back-page">
            @if ( admin_superadmin_permissions() && !Auth::user()->has<PERSON><PERSON><PERSON><PERSON>('Client Member') )
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('statushub') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to StatusHub
                </a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{route('client.dashboard')}}">
                    <img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Client Portal
                </a>
            @endif           
        </div>
				<div class="meta d-flex justify-content-between align-items-center">
					<div class="copy">
						<h1 class="text-white mb-0">Project: <i>[{{$project->job_code}}] {{$project->name}}</i></h1>
						 <div class="page-links mt-3">
                            <a class="" href="{{ route('track-project', ['id'=> $project->id]) }}">Track Project</a>
                            <a class="active" href="{{ route('message-centre', ['id'=> $project->id]) }}">Message Center</a>
                            <a href="{{ route('file-upload', ['id'=> $project->id]) }}">File Upload</a>
                            {{-- <a href="{{ route('billing-history', ['id'=> $project->id]) }}">Billing History</a> --}}
                        </div>
					</div>
					<div class="logo">
                        <a href="#"><img src="{{ $client->logo ? set_user_image($client->logo) : asset('images/default-user.jpg') }}"
                                alt="Client Logo" width="72" height="80" /></a>
                    </div>
				</div>
			</div>
		</section>
		<section class="client-project bg-white">
			<div class="container-xxl">
				<div class="go-back">
					<a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('message-centre', ['id'=> $project->id]) }}"><img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Message Center</a>
				</div>
				<div class="status d-flex justify-content-between w-100">
					<div class="text w-100">
						<h2 class="text-uppercase mb-2">COMPOSE YOUR MESSAGE</h2>
						<div class="message-subject">
							<input type="text" id="message_subject" name="subject" class="form-control" placeholder="Type a message subject here..." required />
						</div>
					</div>
				</div>
			</div>
		</section>
		<section class="bg-white pb-5">
			<form class="container-xxl pb-5" action="{{ route('project.messages.store', $project->id) }}" method="POST" enctype="multipart/form-data" id="messageForm">
				@csrf
				<div class="message-box reply d-flex">
					<div class="pic">
						<img src="{{ set_user_image(auth()->user()->profile_image) }}" alt="Profile Image">
					</div>
					<div class="message-wrap d-flex flex-grow-1">
						<div class="message">
							<div class="copy">

                            <textarea id="message_editor" class="border-0 w-100" name="message" placeholder="Add reply..."></textarea>

                            </div>
						</div>
					</div>
				</div>
				<div class="message-box upload d-flex flex-column mt-3">
					<div class="send-along">
						@if(auth()->user()->hasRole('Client Member'))
							<div class="form-check">
								<input class="form-check-input" id="selectedOnes" type="radio" name="sendMessageAlong" value="all" checked />
								<label class="form-check-label" for="selectedOnes">When I post this message, email it to all <a href="#">{{$usersCount}} people</a> who want emails about this project.</label>
							</div>
							<div class="form-check mt-2 pt-1">
								<input class="form-check-input" id="selectNew" type="radio" name="sendMessageAlong" value="selected" />
								<label class="form-check-label" for="selectNew">Let me choose who should get an email...</label>
							</div>
						@else
							<div class="default-recipients">
								<p class="mb-2">Your message will be sent to:</p>
								<ul class="list-unstyled mb-2">
									@foreach($defaultRecipients as $recipient)
										<li class="d-flex align-items-center mb-1">
											<img src="{{ set_user_image($recipient->profile_image) }}" alt="{{ $recipient->name }}" class="rounded-circle me-2" width="24" height="24">
											<span>{{ $recipient->name }}</span>
										</li>
									@endforeach
                                      @if($clientUser)
                                    <li class="d-flex align-items-center mb-1">
											<img src="{{ $clientUser->profile_image ? Storage::url($clientUser->profile_image) : asset('images/default-user.jpg') }}" class="rounded-circle me-2" width="24" height="24">
											<span>{{ $clientUser->name }}</span>
										</li>
                                        @else

                                         <li class="d-flex align-items-center mb-1">
											<img src="{{ $client->logo ? set_user_image($client->logo): asset('images/default-user.jpg') }}" class="rounded-circle me-2" width="24" height="24">
											<span>{{ $client->name }}</span>
										</li>
                                        @endif


								</ul>
								<a href="#" class="text-decoration-none change-recipients" data-bs-toggle="modal" data-bs-target="#emailRecipientsModal">
									<i class="bi bi-pencil-square me-1"></i>Change
								</a>
							</div>
						@endif
					</div>
					<div class="upload-preview mt-3" id="uploadPreview"></div>
					<div class="cta-row d-flex justify-content-between mt-4">
						<div class="upload-btn-wrapper">
							<button class="btn-upload text-uppercase d-flex align-items-center"><i class="bi bi-upload me-2"></i> Upload a file</button>
							<input type="file" name="files[]" multiple id="fileUpload" />
						</div>
						<button class="cta text-uppercase mt-0" type="submit" id="submitButton">
							<span class="button-text">ADD COMMENT</span>
							<span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
						</button>
					</div>
				</div>
			</form>
		</section>

		<!-- Email Recipients Modal -->
		<div class="modal fade" id="emailRecipientsModal" tabindex="-1" aria-labelledby="emailRecipientsModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-dialog-centered">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="emailRecipientsModalLabel">Select Email Recipients</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="recipients-list">
							@if(auth()->user()->hasRole('Client Member'))
								@foreach($projectUsers as $user)
								<div class="recipient-item d-flex align-items-center mb-3">
									<div class="form-check">
										<input class="form-check-input recipient-checkbox" type="checkbox" value="{{ $user->id }}" id="user{{ $user->id }}" name="email_recipients[]">
										<label class="form-check-label d-flex align-items-center" for="user{{ $user->id }}">
											<img src="{{ set_user_image($user->profile_image) }}" alt="{{ $user->name }}" class="rounded-circle me-2" width="32" height="32">
											<span>{{ $user->name }}</span>
										</label>
									</div>
								</div>
								@endforeach
							@else
								@foreach($defaultRecipients as $recipient)
								<div class="recipient-item d-flex align-items-center mb-3">
									<div class="form-check">
										<input class="form-check-input recipient-checkbox" type="checkbox" value="{{ $recipient->id }}" id="user{{ $recipient->id }}" name="email_recipients[]" checked data-default="true">
										<label class="form-check-label d-flex align-items-center" for="user{{ $recipient->id }}">
											<img src="{{ set_user_image($recipient->profile_image) }}" alt="{{ $recipient->name }}" class="rounded-circle me-2" width="32" height="32">
											<span>{{ $recipient->name }}</span>
										</label>
									</div>
								</div>
								@endforeach
							@endif
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
						<button type="button" class="btn btn-primary" id="saveRecipients">Save Selection</button>
					</div>
				</div>
			</div>
		</div>

@push('styles')

<link rel="stylesheet" href="{{asset('css/custom.css')}}">

@endpush

@push('script')
<script src="{{ asset('js/tinymce/tinymce.min.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
    // Store all files for form submission (both regular uploads and pasted images)
    let allFiles = new Map(); // Map to store unique files by name
    
    // Initialize TinyMCE
    tinymce.init({
        selector: '#message_editor',
        height: 200,
        plugins: [
            'lists', 'advlist', 'autolink', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'wordcount', 'code', 'fullscreen',
            'insertdatetime', 'table', 'paste'
        ],
        toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen',
        menubar: false,
        content_style: `
            body {
                font-family: Helvetica, Arial, sans-serif;
                font-size: 16px;
                background-color: white;
                color: #333;
                padding: 1.5rem 2.5rem;
            }
            #tinymce[data-mce-placeholder]:not(.mce-visual-caret)::before {
                color: #666;
            }
            p {
                margin: 0;
                padding: 0;
            }
        `,
        skin: 'oxide',
        paste_data_images: false,
        automatic_uploads: false,
        file_picker_types: 'image',
        setup: function(editor) {
            // Handle paste events
            editor.on('paste', function(e) {
                const items = (e.clipboardData || e.originalEvent.clipboardData).items;
                let hasImage = false;
                
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        hasImage = true;
                        e.preventDefault();
                        const blob = items[i].getAsFile();
                        const fileName = 'pasted-image-' + Date.now() + '.png';
                        
                        // Add to files map
                        allFiles.set(fileName, blob);
                        
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewItem = createPreviewItem(fileName, e.target.result, false, true);
                            document.getElementById('uploadPreview').appendChild(previewItem);
                        };
                        reader.readAsDataURL(blob);
                    }
                }
                
                if (hasImage) {
                    return false; // Prevent default paste behavior for images
                }
            });

            // Handle drop events on editor
            editor.on('drop', function(e) {
                e.preventDefault();
                const items = e.dataTransfer.items;
                for (let i = 0; i < items.length; i++) {
                    if (items[i].type.indexOf('image') !== -1) {
                        const blob = items[i].getAsFile();
                        const fileName = 'dropped-image-' + Date.now() + '.png';
                        
                        // Add to files map
                        allFiles.set(fileName, blob);
                        
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewItem = createPreviewItem(fileName, e.target.result, false, true);
                            document.getElementById('uploadPreview').appendChild(previewItem);
                        };
                        reader.readAsDataURL(blob);
                    }
                }
            });
        }
    });

    // Form submission handling
    const form = document.getElementById('messageForm');
    const submitButton = document.getElementById('submitButton');
    const buttonText = submitButton.querySelector('.button-text');
    const spinner = submitButton.querySelector('.spinner-border');
    const fileUpload = document.getElementById('fileUpload');
    const uploadPreview = document.getElementById('uploadPreview');
    const subjectInput = document.getElementById('message_subject');

    // Handle file input changes
    fileUpload.addEventListener('change', function(e) {
        handleRegularFiles(this.files);
        // Clear the input to prevent accumulation
        this.value = '';
    });

    // Handle drag and drop on upload preview area
    uploadPreview.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('drag-over');
    });

    uploadPreview.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');
    });

    uploadPreview.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        handleRegularFiles(files);
    });

    function handleRegularFiles(files) {
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            
            // Check if file already exists
            if (allFiles.has(file.name)) {
                console.log('File already exists, skipping:', file.name);
                continue;
            }
            
            // Add to files map
            allFiles.set(file.name, file);
            
            // Create loading preview item
            const loadingItem = createPreviewItem(file.name, null, true);
            uploadPreview.appendChild(loadingItem);
            
            if (file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Remove loading item
                    loadingItem.remove();
                    // Create actual preview
                    const previewItem = createPreviewItem(file.name, e.target.result);
                    uploadPreview.appendChild(previewItem);
                };
                reader.readAsDataURL(file);
            } else {
                // For non-image files, create a preview with file icon
                setTimeout(() => {
                    // Remove loading item
                    loadingItem.remove();
                    // Create actual preview
                    const previewItem = createPreviewItem(file.name, null);
                    uploadPreview.appendChild(previewItem);
                }, 500); // Simulate processing time
            }
        }
    }

    function getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        let iconClass = 'fa-file';
        
        switch(extension) {
            case 'pdf':
                iconClass = 'fa-file-pdf';
                break;
            case 'doc':
            case 'docx':
                iconClass = 'fa-file-word';
                break;
            case 'xls':
            case 'xlsx':
                iconClass = 'fa-file-excel';
                break;
            case 'ppt':
            case 'pptx':
                iconClass = 'fa-file-powerpoint';
                break;
            case 'txt':
                iconClass = 'fa-file-lines';
                break;
            case 'zip':
            case 'rar':
                iconClass = 'fa-file-zipper';
                break;
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'bmp':
            case 'webp':
                return null; // Return null for images to use img tag instead
            default:
                iconClass = 'fa-file';
        }
        
        return `<i class="fa-solid ${iconClass} file-icon ${extension}"></i>`;
    }

    function createPreviewItem(fileName, previewUrl, isLoading = false, isPasted = false) {
        const div = document.createElement('div');
        div.className = 'upload-preview-item';
        if (isLoading) {
            div.classList.add('loading');
        }
        if (isPasted) {
            div.setAttribute('data-pasted', 'true');
        }
        div.setAttribute('data-filename', fileName);
        
        if (previewUrl && (previewUrl.startsWith('data:image') || previewUrl.endsWith('.jpg') || previewUrl.endsWith('.png') || previewUrl.endsWith('.gif'))) {
            const img = document.createElement('img');
            img.src = previewUrl;
            img.alt = fileName;
            div.appendChild(img);
        } else if (!isLoading) {
            const fileIcon = getFileIcon(fileName);
            if (fileIcon) {
                div.innerHTML = fileIcon;
            }
        }
        
        if (!isLoading) {
            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-file';
            removeBtn.innerHTML = '×';
            removeBtn.onclick = function() {
                // Remove from files map
                allFiles.delete(fileName);
                console.log('Removed file:', fileName);
                console.log('Remaining files:', Array.from(allFiles.keys()));
                div.remove();
            };
            
            const fileNameDiv = document.createElement('div');
            fileNameDiv.className = 'file-name';
            fileNameDiv.textContent = fileName;
            
            div.appendChild(removeBtn);
            div.appendChild(fileNameDiv);
        }
        
        return div;
    }

    // Email recipients modal handling
    const emailRecipientsModal = new bootstrap.Modal(document.getElementById('emailRecipientsModal'));
    const saveRecipientsBtn = document.getElementById('saveRecipients');
    let selectedRecipients = new Set();

    // Initialize selected recipients for non-client members
    if (!document.querySelector('input[name="sendMessageAlong"]')) {
        document.querySelectorAll('.recipient-checkbox[data-default="true"]').forEach(checkbox => {
            selectedRecipients.add(checkbox.value);
        });
    }

    // Handle "Let me choose" radio button for client members
    const selectNewRadio = document.getElementById('selectNew');
    if (selectNewRadio) {
        selectNewRadio.addEventListener('change', function() {
            if (this.checked) {
                emailRecipientsModal.show();
            }
        });
    }

    // Handle checkbox changes
    document.querySelectorAll('.recipient-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                selectedRecipients.add(this.value);
            } else {
                selectedRecipients.delete(this.value);
            }
        });
    });

    // Handle save button
    saveRecipientsBtn.addEventListener('click', function() {
        if (selectedRecipients.size === 0) {
            alert('Please select at least one recipient');
            return;
        }
        emailRecipientsModal.hide();
    });

    // Handle modal close
    document.getElementById('emailRecipientsModal').addEventListener('hidden.bs.modal', function () {
        if (selectedRecipients.size === 0) {
            // If no recipients selected, keep the default recipients
            document.querySelectorAll('.recipient-checkbox[data-default="true"]').forEach(checkbox => {
                checkbox.checked = true;
                selectedRecipients.add(checkbox.value);
            });
        }
    });

    // Form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        console.log('Form submission started');

        try {
            // Get TinyMCE content
            const messageContent = tinymce.get('message_editor').getContent();
            const subjectContent = subjectInput ? subjectInput.value : '';

            console.log('Subject:', subjectContent);
            console.log('Message:', messageContent);
            console.log('Files to upload:', Array.from(allFiles.keys()));

            // Validate required fields
            if (!subjectContent.trim()) {
                alert('Please enter a subject');
                subjectInput.focus();
                return;
            }

            if (!messageContent.trim()) {
                alert('Please enter a message');
                return;
            }

            // Show loading state
            submitButton.disabled = true;
            buttonText.textContent = 'POSTING...';
            spinner.classList.remove('d-none');

            // Create FormData object
            const formData = new FormData(form);
            
            // Add subject
            formData.set('subject', subjectContent);
            
            // Add message content
            formData.set('message', messageContent);

            // Add all files from the map
            console.log('Total files to upload:', allFiles.size);
            allFiles.forEach((file, filename) => {
                formData.append('files[]', file, filename);
                console.log('Adding file:', filename, 'Size:', file.size);
            });

            // Add email recipients if selected
            const sendMessageAlongRadio = document.querySelector('input[name="sendMessageAlong"]:checked');
            const sendMessageAlong = sendMessageAlongRadio ? sendMessageAlongRadio.value : 'all';
            formData.set('sendMessageAlong', sendMessageAlong);
            console.log('Send message along:', sendMessageAlong);

            if (sendMessageAlong === 'selected') {
                const selectedRecipients = document.querySelectorAll('.recipient-checkbox:checked');
                console.log('Selected recipients:', selectedRecipients.length);
                selectedRecipients.forEach(checkbox => {
                    formData.append('email_recipients[]', checkbox.value);
                });
            }

            // Send the request
            console.log('Sending request to:', form.action);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                }
            });

            console.log('Response status:', response.status);
            const data = await response.json();
            console.log('Response data:', data);

            if (data.success) {
                window.location.href = data.redirect;
            } else {
                throw new Error(data.message || 'Failed to post message');
            }
        } catch (error) {
            console.error('Error:', error);
            alert(error.message || 'Failed to post message. Please try again.');
            // Reset button state
            submitButton.disabled = false;
            buttonText.textContent = 'ADD COMMENT';
            spinner.classList.add('d-none');
        }
    });
});
</script>
@endpush

@endsection