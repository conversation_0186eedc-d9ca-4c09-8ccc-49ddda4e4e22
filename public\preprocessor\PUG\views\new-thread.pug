- let pageName = 'New Thread';
- let mainPage = 'Message Center';
- let pageTitle = `${pageName}: Message`;

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.opacity-50.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>[SP-003-25] Sample Project 3</i>', false, true, true, 'Back to Client Portal')

        section.client-project.bg-white
            .container-xxl
                .go-back
                    a.d-inline-flex.align-items-center.text-decoration-none(href="message-center.html") #[img.me-2(src="images/back-arrow-icon.svg", alt="")] Back to Message Center
                .status.d-flex.justify-content-between
                    +statusSection('COMPOSE YOUR MESSAGE', 'Type a message subject here...')

        section.bg-white.pb-5
            form.container-xxl.pb-5(action="")
                .message-box.reply.d-flex
                    .pic
                    .message-wrap.d-flex.flex-grow-1
                        .message.flex-fill.d-flex.align-items-center
                            textarea.border-0.w-100(name="", placeholder="Add reply...")

                .message-box.upload.d-flex.flex-column.mt-3
                    .send-along
                        .form-check
                            input#selectedOnes.form-check-input(type='radio', name='sendMessageAlong')
                            label.form-check-label(for='selectedOnes') When I post this message, email it to all #[a(href="#") 4 people] who want emails about this project.
                        .form-check.mt-2.pt-1
                            input#selectNew.form-check-input(type='radio', name='sendMessageAlong')
                            label.form-check-label(for='selectNew') Let me choose who should get an email...

                    .cta-row.d-flex.justify-content-between.mt-4
                        .upload-btn-wrapper
                            button.btn-upload.text-uppercase.d-flex.align-items-center #[i.bi.bi-upload.me-2] Upload a file
                            input(type="file", name="myfile")
                        button.cta.text-uppercase.mt-0(type="submit") ADD COMMENT

        include ../partials/footer.pug
        +footer()