- let pageName = 'Team Portal';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/projectBoxBor.pug

mixin linkBox(link, pageLink=false)
    - let linkHref = pageLink ? `${link.replaceAll(' ', '-').toLowerCase()}.html` : '#';
    a.box.d-flex.align-items-center.text-decoration-none(href=linkHref)
        .icon.d-flex.align-items-center.justify-content-center #[img(src=`images/${link.replaceAll(' ', '-').toLowerCase()}-icon.svg`, alt="")]
        .text #[p=link]

mixin taskProjects(icon, head, color)
    .task-project.d-flex(class=color)
        .icon.d-flex.align-items-center.justify-content-center
            img(src=`images/${icon}-icon.svg`, alt="", width="20", height="20")
        .copy.d-flex.flex-column.flex-grow-1
            h2.text-uppercase=head
            .detail.d-flex.align-items-center.w-100
                .date.me-3 12/12/25
                .task.me-3 #[a(href="task-view.html") Home Page Margins]
                .text.d-flex.flex-grow-1.align-items-center
                    .text.over-text.d-grid
                        p.text-truncate Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla nec purus feugiat, vestibulum mi nec, ultricies metus.
                    a.read-more.text-decoration-none.ms-3(href="#") Read More

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('Happy <i>Friday</i>, Drew M.')

        section.client-project.project-dashboard
            .container-xxl
                .row.projects-row
                    .col-md-3.col-xl-2.project-column.quick-links
                        h2.text-uppercase Quick LINKS
                        hr.mt-0.mb-4.border-white
                        .quick-links-list
                            -let quickLinks = ['Status Hub', 'Project List', 'Tasks', 'Message Center']
                            each link, index in quickLinks
                                - let isPrimary = index === 0
                                +linkBox(link, isPrimary)

                            .more-links.box.d-flex.align-items-center
                                .icon.d-flex.align-items-center.justify-content-center #[img(src='images/add-new-icon.svg', alt="")]
                                .text #[p New...]
                                .drop-list
                                    each add in ['New Project', 'New Client', 'New Enterprise']
                                        +linkBox(add, true)

                        hr.border-white
                        .quick-links-list
                            each link, index in ['Harvest', 'PTO Calendar', 'Edit Teams', 'Hub Settings']
                                - let isPrimary = index === 2
                                +linkBox(link, isPrimary)
                    .col-md-6.col-xl-7.project-column.task-overview
                        .col-head.align-items-center.d-flex.justify-content-between
                            h2.text-uppercase TASKS OVERVIEW
                            a.cta.link.border-0.p-0.mt-0.mb-2(href="task-overview.html") See All
                        hr.mt-0.mb-4.border-white
                        .task-overview-list
                            +taskProjects('star', 'SP-003-25 Sample project 3 - URGENT', 'purple')
                            +taskProjects('star', 'SP-003-25 Sample project 3 - URGENT', 'purple')
                            +taskProjects('alert', 'SP-003-25 Sample project 3', 'purple')
                            +taskProjects('alert', 'SP-003-25 Sample project 3', 'purple')
                            +taskProjects('alert', 'SP-003-25 Sample project 3', 'purple')
                            .more-tasks.mt-5.text-center
                                a.text-decoration-none(href="#") 12 MORE +
                    .col-md-3.project-column.task-overview
                        .col-head.align-items-center.d-flex.justify-content-between
                            h2.text-uppercase YOUR PROJECTS
                            a.cta.link.border-0.p-0.mt-0.mb-2(href="#") See All
                        hr.mt-0.mb-4.border-white
                        .project-list
                            - for(let i = 0; i < 5; i++)
                                +projectBoxBor('green', 'content', '[SP-001-25]', 'Sample Project Lorem Ipsum', 2)
                        .more.text-center.text-center.mt-2
                            a.text-decoration-none(href="#") 7 MORE

        section.messages-wrap.pb-5
            .container-xxl
                .head
                    h2 LATEST MESSAGES
                hr.mt-0.mb-4.border-white
                .messages-list
                    .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.green
                        .col-md-auto.icons.d-flex
                            .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                            .icon.pic #[img(src='images/drew-pic.jpg', alt='')]
                        .col-md
                            .head.text-uppercase
                                h2 SP-003-25 Sample project 3 - MEssage
                            .row
                                .col-md-auto.name
                                    p Drew M.
                                .col-md-auto.date
                                    p 12/12/25
                                .col-md-auto.notification
                                    p #[a(href="#") You’re six days behind schedule, buddy]
                                .col-md.message.d-flex.align-items-center
                                    .over-text.d-grid
                                        p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                    a.ms-3(href="#") Read More

                    .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.green
                        .col-md-auto.icons.d-flex
                            .icon.icon-alert.d-flex.align-items-center.justify-content-center #[img(src="images/alert-white-logo.svg", alt="") ]
                        .col-md
                            .head.text-uppercase
                                h2 SP-003-25 Sample project 3 - Missing CONTENT
                            .row
                                .col-md-auto.name
                                    p Sally  M.
                                .col-md-auto.date
                                    p 12/11/25
                                .col-md-auto.notification
                                    p #[a(href="#") Just checking in]
                                .col-md.message.d-flex.align-items-center
                                    .over-text.d-grid
                                        p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                    a.ms-3(href="#") Read More

                    .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.green
                        .col-md-auto.icons.d-flex
                            .icon.icon-alert.d-flex.align-items-center.justify-content-center #[img(src="images/alert-white-logo.svg", alt="") ]
                        .col-md
                            .head.text-uppercase
                                h2 SP-003-25 Sample project 3 - NEED APPROVAL
                            .row
                                .col-md-auto.name
                                    p Drew M.
                                .col-md-auto.date
                                    p 12/11/25
                                .col-md-auto.notification
                                    p #[a(href="#") You’re six days behind schedule, buddy]
                                .col-md.message.d-flex.align-items-center
                                    .over-text.d-grid
                                        p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                    a.ms-3(href="#") Read More
                    .message-row.row.flex-nowrap.align-items-center.gx-md-3.mt-4.purple
                        .col-md-auto.icons.d-flex
                            .icon.icon-envelop.d-flex.align-items-center.justify-content-center #[i.bi.bi-envelope-fill]
                            .icon.pic #[img(src='images/drew-pic.jpg', alt='')]
                        .col-md
                            .head.text-uppercase
                                h2 SP-002-25 Sample project 2
                            .row
                                .col-md-auto.name
                                    p Drew M.
                                .col-md-auto.date
                                    p 12/11/25
                                .col-md-auto.notification
                                    p #[a(href="#") You’re six days behind schedule, buddy]
                                .col-md.message.d-flex.align-items-center
                                    .over-text.d-grid
                                        p Lorem ipsum, dolor sit amet consectetur adipisicing elit. Dignissimos esse suscipit unde molestiae facere magni est nobis labore pariatur ullam.
                                    a.ms-3(href="#") Read More

        include ../partials/footer.pug
        +footer(true)