<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //
        // Define the permission names for each resource
        $statuses = [
            'Urgent',
            'New',
            'In Progress',
            'Ready For Approval',
            'Feedback',
            'Recently Finished',
        ];

        // Iterate through the permissions and insert them into the table
        foreach ($statuses as $status) {
            DB::table('status')->insert([
                'name' => $status,  // Insert the permission name
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }
}
