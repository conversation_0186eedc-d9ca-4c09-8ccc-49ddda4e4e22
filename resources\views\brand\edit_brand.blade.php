@extends('layout.app')
@section('title', 'Edit Enterprise')
@section('content')


@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
@endif
@if (session('error'))
    <script>
        errorToast(@json(session('error')));
    </script>
@endif
@if ($errors->any())
    @foreach ($errors->all() as $error)
        <script>
            errorToast(@json($error));
        </script>
    @endforeach
@endif


<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            {{-- @if(admin_superadmin_permissions()) --}}
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-brands') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Enterprise List</a>
            {{-- @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('brands') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Enterprise List</a>
            @endif --}}
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Update <i>Enterprise</i></h1>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl projects-row">
        <div class="project-column task-overview">
            <div class="col-head align-items-center d-flex justify-content-between">
                <h2 class="text-uppercase">Update Enterprise</h2>
            </div>
            <hr class="mt-0 mb-4 border-white" />
            <form class="row form-wrap" action="{{ route('update-brand', ['brand_id' => $brand->id]) }}" method="post" enctype="multipart/form-data" id="update_brand_form">
                @csrf
                @method('put')
                <input type="hidden" name="admin_id" value="{{ Auth::user()->id }}">
                <div class="col-md-4 col-xl-3 mb-4">
                    <input name="job_code" value="{{ $brand->job_code }}"  class="form-control border-0" type="text" id="job_code" placeholder="JOB CODE PREFIX" />
                    <div class="form-text fst-italic opacity-50">Suggested Length: 2-4 Characters</div>
                </div>
                <div class="col-md-8 col-xl-9 mb-4">
                    <input name="name" value="{{ $brand->name }}" class="form-control border-0" type="text" id="name" placeholder="Add a client name..." />
                </div>
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">CONTACTS</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4 d-flex align-items-center justify-content-between">
                    <h2 class="text-white mb-0">Add Contacts</h2>
                    <button id="addMoreContactsButton" type="button" class="cta ms-3 mt-0">Add More Contact</button>
                </div>
                
                <div id="contactsWrapper">
                    @forelse ($social_details as $index => $detail)
                        <div class="col-12 mt-4 contacts-input">
                            <div class="input-wrap d-flex">
                                <div class="icon d-flex align-items-center justify-content-center">
                                    <img src="{{ asset('images/email-icon.svg') }}" alt="" />
                                </div>
                                <div class="input flex-grow-1">
                                    <input class="form-control border-0" 
                                           type="email" 
                                           name="social_detail[]" 
                                           value="{{ old("social_detail.$index", $detail->value) }}" />
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12 mt-4 contacts-input">
                            <div class="input-wrap d-flex">
                                <div class="icon d-flex align-items-center justify-content-center">
                                    <img src="{{ asset('images/email-icon.svg') }}" alt="" />
                                </div>
                                <div class="input flex-grow-1">
                                    <input class="form-control border-0" 
                                           type="email" 
                                           name="social_detail[]" 
                                           value="{{ old('social_detail.0') }}" />
                                </div>
                            </div>
                        </div>
                    @endforelse
                </div>
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">ENTERPRISE</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4">
                    <div class="row">
                        <div class="col-md-4">
                            <h2 class="text-white">Update clients of Enterprise</h2>
                        </div>                        <div class="col-md-8">
                            <input type="hidden" name="clients" class="d-none" value="{{ json_encode($brand->clients->pluck('id')) }}">
                            <div class="brand-wrap">
                            @if(isset($clients) && count($clients) > 0)
                                @foreach($clients as $client)
                                    @php
                                        $isSelected = $brand->clients->contains($client->id);
                                    @endphp
                                    <div class="brand-select d-flex align-items-center justify-content-between {{ $isSelected ? 'added' : '' }}">
                                        <div class="name text-uppercase">{{ $client->name }}</div>
                                        <button class="cta text-uppercase mt-0 {{ $isSelected ? 'added' : '' }}" data-client-id="{{ $client->id }}" type="button">
                                            {{ $isSelected ? 'Added' : 'Add' }}
                                        </button>
                                    </div>
                                @endforeach
                            @endif

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h2 class="text-white">Upload Logo</h2>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="upload-btn-wrapper">
                                <input type="file" name="logo" id="logo" class="d-none" />
                                <button type="button" id="uploadTrigger" class="btn-upload text-uppercase text-white d-flex align-items-center">
                                    <i class="bi bi-upload me-2"></i> Upload a file
                                </button>
                            </div>
                            <div class="upload-files d-flex align-items-center justify-content-between mt-2">
                                    <div class="form-text fst-italic opacity-50" id="selectedFileName">{{ set_user_image($brand->logo) }}</div>
                                    <span class="remove-selected" style="cursor: pointer;"> <i class="fa-solid fa-xmark fa-lg" style="color: #ffffff;"></i> </span>
                                </div>
                        </div>
                    </div>
                    <div class="form-text fst-italic opacity-50">Suggested Format: One-color white SVG or PNG</div>
                </div>
                <div class="col-12 mt-5 text-center">
                    <button class="cta text-uppercase mt-0" type="submit">Update Enterprise</button>
                </div>
            </form>
        </div>
    </div>
</section>

<script>

document.addEventListener('DOMContentLoaded', function () {
   

    initializeApp();
    });

    function initializeApp() {

        initializeContactManagement();
    }

    function initializeContactManagement() {
		const addMoreContactsButton = document.getElementById('addMoreContactsButton');
		const contactsWrapper = document.getElementById('contactsWrapper');
		
		if (addMoreContactsButton && contactsWrapper) {
			let contactCounter = 1;
			
			addMoreContactsButton.addEventListener('click', function() {
				contactCounter++;
				addNewContactField(contactsWrapper, contactCounter);
			});
		}
	}
	
	
	function addNewContactField(contactsWrapper, contactCounter) {
		const newContactId = `email_${contactCounter}`;
		
		const newContactDiv = document.createElement('div');
		newContactDiv.className = 'col-12 mt-3 contacts-input';
		
		const iconSrc = document.querySelector('.contacts-input .icon img')?.src || '';
		
		newContactDiv.innerHTML = `
			<div class="input-wrap d-flex">
				<div class="icon d-flex align-items-center justify-content-center"><img src="${iconSrc}" alt="" /></div>
				<div class="input flex-grow-1">
					<input class="form-control border-0" type="email" id="${newContactId}" name="social_detail[]" />
				</div>
				<div class="remove-contact ms-2">
					<button type="button" class="btn btn-sm btn-danger">Remove</button>
				</div>
			</div>
		`;
		contactsWrapper.appendChild(newContactDiv);
	
	   
		if (typeof validation !== 'undefined') {
			validation.addField(`#${newContactId}`, [
				{
					rule: 'email',
					errorMessage: 'Please provide a valid email address',
				}
			]);
		}
		
	   
		const removeButton = newContactDiv.querySelector('.remove-contact button');
		if (removeButton) {
			removeButton.addEventListener('click', function() {
				if (typeof validation !== 'undefined') {
					validation.removeField(`#${newContactId}`);
				}
				contactsWrapper.removeChild(newContactDiv);
			});
		}
	}





    document.getElementById("uploadTrigger").addEventListener("click", function () {
        document.getElementById("logo").click();
    });

    document.getElementById("logo").addEventListener("change", function () {
        const file = this.files[0];
        if (file) {
            console.log("selected file");
            document.getElementById("selectedFileName").append(file.name);
            document.querySelector(".upload-files").classList.remove("d-none");
        }
    });
    document.querySelector(".remove-selected").addEventListener("click", function () {
        document.getElementById("logo").value = "";
        document.getElementById("selectedFileName").innerText = "";
        document.querySelector(".upload-files").classList.add("d-none");
    });    const clientsInput = document.querySelector('[name="clients"]');
    // Parse initial clients on page load
    let selectedClients = JSON.parse(clientsInput.value || '[]');

    document.querySelectorAll('.brand-select').forEach(function (item) {
        item.querySelector('button').addEventListener('click', function () {
            const clientId = this.getAttribute('data-client-id');
            
            // Toggle button and container state
            item.classList.toggle('added');
            this.classList.toggle('added');
            this.innerText = item.classList.contains('added') ? 'Added' : 'Add';

            // Update selected clients array
            if (item.classList.contains('added')) {
                if (!selectedClients.includes(clientId)) {
                    selectedClients.push(clientId);
                }
            } else {
                selectedClients = selectedClients.filter(id => id != clientId);
            }

            // Update hidden input value
            clientsInput.value = JSON.stringify(selectedClients);
        });
    });

     /**
     * validate form
     */
    const form_id = 'update_brand_form';
    let args = {
            rules : {
                '#name': GlobalFormValidator.validators.required,
                '#job_code': GlobalFormValidator.validators.required,
                '#social_detail': GlobalFormValidator.validators.required,
            },
            messages: {
                '#name': "Please select a client",
                '#job_code': "Please add a job code",
                '#social_detail': "Please add a project title",
            },
            onSuccess: (form) => {
                form.submit();
            }
        }
    let Validator = new GlobalFormValidator(form_id, args);
    Validator.initialize();

    
</script>

@endsection