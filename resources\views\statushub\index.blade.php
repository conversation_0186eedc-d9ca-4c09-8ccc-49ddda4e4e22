@extends('layout.app')
@section('title', 'Status Hub')
@section('page_heading', 'STATUSHUB')
@section('content')




<section class="projects-hub d-flex">
    <div class="controls-hub d-flex flex-column">
        <div class="go-back my-3 text-center">
            <a href="{{ route('admin-dashboard') }}"><img src="{{ asset('images/go-back-orange.svg') }}" alt="" width="10" height="17" /></a>
        </div>
        <div class="action-set d-flex flex-column">
            <a class="action d-flex align-items-center justify-content-center" href="{{ route('add-project') }}"
                title="New Project" id=""><img src="{{ asset('images/new-project-hub-icon.svg') }}" alt="" width="36"
                    height="36" />
            </a>
            <a class="action d-flex align-items-center justify-content-center" href="#"
                title="New List" id=""><img src="{{ asset('images/new-list-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
            <a
                class="action d-flex align-items-center justify-content-center" href="#" title="Reorganize Lists"
                id=""><img src="{{ asset('images/reorganize-lists-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
            <a
                class="action d-flex align-items-center justify-content-center" href="#" title="Move Selected"
                id=""><img src="{{ asset('images/move-selected-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
        </div>
        <hr />
        <div class="action-set d-flex flex-column mb-auto">
            <a class="action d-flex align-items-center justify-content-center" href="{{ route('admin-projects') }}" title="Your Projects"
                id="myProjects"><img src="{{ asset('images/your-projects-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
            @if( request('by') == 'phases' )
            <a
                class="action d-flex align-items-center justify-content-center" href="{{ route('statushub') }}" title="Main Phases" id=""><img
                    src="{{ asset('images/main-phases-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
            @else
            <a
                class="action d-flex align-items-center justify-content-center" href="{{ route('statushub', ['by' => 'phases']) }}" title="Main Phases" id=""><img
                    src="{{ asset('images/main-phases-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
            @endif
            <a
                class="action d-flex align-items-center justify-content-center" href="{{ route('teams') }}" title="Employee List"
                id=""><img src="{{ asset('images/employee-list-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
            <a
                class="action d-flex align-items-center justify-content-center" href="#" title="Project Master List"
                id=""><img src="{{ asset('images/project-master-list-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
        </div>
        <div class="action-set d-flex flex-column my-4">
            <a class="action d-flex align-items-center justify-content-center" href="#" title="Settings" id=""><img
                    src="{{ asset('images/settings-hub-icon.svg') }}" alt="" width="36" height="36" />
            </a>
        </div>
    </div>
    @if( request('by') == 'phases' )
    <!-- By Phase -->
    <div class="projects-columns flex-grow-1 d-flex">
        <!-- Start  -->
        @foreach( $phases as $phase )
        <div class="project-column {{ $phase->getColorClass() }}">
            <div class="head d-flex align-items-center pb-3">
                <div class="meta d-flex align-items-center me-3">
                    @php
                        $phase_icon = $phase->icon;
                        $phase_icon = str_replace('localhost', '127.0.0.1:8000', $phase_icon); //IMP
                    @endphp
                    <div class="icon d-flex me-2"><img src="{{ $phase_icon }}" alt="" width="22"
                            height="22" /></div>
                    @php
                        $words = explode(' ', $phase->name);
                    @endphp
                    <h2 class="text-uppercase mb-0" style="color:{{$phase->color_code}}">{{ implode(' ', array_slice($words, 0, 1)) }}</h2>
                </div>
                <div class="sort d-flex align-items-center ms-auto">
                    <span>Sort By</span>
                    <div class="icon ms-2"><img src="{{ asset('images/sort-down-arrow.svg') }}" alt="" /></div>
                </div>
                <div class="icon-more ms-3"><i class="bi bi-three-dots"></i></div>
            </div>
            @php
                $categories = $phase->categories;
                $c_id = isset($categories[0]) ? $categories[0]->id : null;
            @endphp


            <div class="project-list pb-3" id="project-list"  data-category-id="{{ $c_id }}"  data-phase-id="{{ $phase->id }}">

                @foreach($projects as $project)

                @if(!$project->archived)
                    @if( $project->phase_id === $phase->id )
                        <div class="project-box mt-3 d-flex" id="project-{{ $c_id }}-{{ $project->id }}" data-project-id="{{ $project->id }}" data-category-id="{{ optional($phase->categories->first())->id }}" data-phase-id="{{ $phase->id }}" draggable="true" >
                            <div class="project-box__flag-list pt-3">
                                @foreach( $project->users as $user )
                                @php $color = $user->color_code ?? '#bcbcbc' @endphp
                                <div class="project-box__flag" style="background: {{ $color }};"></div>
                                @endforeach
                            </div>
                            <div class="project-box__meta flex-grow-1">
                                <a href="{{ route('edit-project', $project->id) }}" class="text-decoration-none text-dark">
                                <div class="project-box__phase">
                                    <h2 class="text-uppercase mb-0" style="color:{{$phase->color_code}}">[{{ $project->job_code }}] - PHASE {{ optional($project->phases->first())->order ?? '?' }}/{{ $project->phases->count() }} </h2>
                                </div>
                            </a>
                                <div class="project-box__name mb-3">
                                    <h2 class="mb-0">{{ $project->name }}</h2>
                                </div>
                                <div class="project-box__info d-flex align-items-center justify-content-between">
                                    <div class="meta">

                                    @php
                                        $total_steps = count($phase->categories);
                                        $current_step = optional($project->category)->order ?? 0;

                                        $percentage = $total_steps > 0
                                            ? round(($current_step / $total_steps) * 100)
                                            : 0;
                                    @endphp
                                        <span class="complete">{{ $percentage }}% </span> |
                                        @php
                                        $tasks = $project->tasksExceptRecentlyFinished()->count();
                                        @endphp
                                        <span class="comments"> {{ $tasks }} <a id="taskLink" href="{{ route('show-tasks', ['project_id' => $project->id]) }}">Tasks</a></span>
                                    </div>
                                    <div class="time">
                                        @php
                                            $projectPhase = $project->phases->where('id', $phase->id)->first();
                                            $projectTarget = $projectPhase && isset($projectPhase->pivot) ? $projectPhase->pivot->project_target : null;
                                        @endphp

                                        @if($projectTarget)
                                        <i class="bi bi-clock"></i> {{ \Carbon\Carbon::parse($projectTarget)->format('M d') }}
                                        @else
                                        <i class="bi bi-clock"></i>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                    @endif
                @endforeach


            </div>
            <div class="foot">
                <a class="add-project-cta d-flex align-items-center justify-content-between text-decoration-none"
                    href="{{ route('add-project', ['category_id' => $c_id , 'phase_id' => $phase->id]) }}">Add Project<span class="icon">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.5634 0.836914V20.4482" stroke-width="1.5"></path>
                            <path d="M20.369 10.6426L0.75769 10.6426" stroke-width="1.5"></path>
                        </svg></span></a>
            </div>
        </div>
        @endforeach
        <!-- End Category -->

    </div>
    @else
    <!-- By Category -->
    <div class="projects-columns flex-grow-1 d-flex">
        <!-- Start  -->
        @foreach( $phases as $phase )
        @foreach( $phase->categories as $category )
        <div class="project-column {{ $phase->getColorClass() }}">
            <div class="head d-flex align-items-center pb-3">
                <div class="meta d-flex align-items-center me-3">
                    @php
                        $phase_icon = $phase->icon;
                        $phase_icon = str_replace('localhost', '127.0.0.1:8000', $phase_icon); //IMP
                    @endphp
                    <div class="icon d-flex me-2"><img src="{{ $phase_icon }}" alt="" width="22"
                            height="22" /></div>
                    <h2 class="text-uppercase mb-0" style="color:{{$phase->color_code}}">{{ $category->name }}</h2>
                </div>
                <div class="sort d-flex align-items-center ms-auto">
                    <span>Sort By</span>
                    <div class="icon ms-2"><img src="{{ asset('images/sort-down-arrow.svg') }}" alt="" /></div>
                </div>
                <div class="icon-more ms-3"><i class="bi bi-three-dots"></i></div>
            </div>
            <div class="project-list pb-3" id="project-list" data-category-id="{{ $category->id }}" data-phase-id="{{ $phase->id }}">

                @foreach($projects as $project)
                    @if(!$project->archived && $project->category_id === $category->id && $project->phase_id === $phase->id)


                    <div class="project-box mt-3 d-flex" id="project-{{ $category->id}}-{{ $project->id }}" data-project-id="{{ $project->id }}" data-category-id="{{ $category->id }}" data-phase-id="{{ $phase->id }}" draggable="true" >
                        <div class="project-box__flag-list pt-3">
                            @foreach( $project->users as $user )
                            @php $color = $user->color_code ?? '#bcbcbc' @endphp
                            <div class="project-box__flag" style="background: {{ $color }};"></div>
                            @endforeach
                        </div>
                        <div class="project-box__meta flex-grow-1">
                            {{-- <a href="{{ route('edit-project', $project->id) }}" class="text-decoration-none text-dark"> --}}
                                <div class="project-box__phase" data-project-id="{{ $project->id }}" data-project-name="{{ $project->name }}" style="cursor: pointer;">
                                    <h2 class="text-uppercase mb-0" style="color:{{$phase->color_code}}">[{{ $project->job_code }}] - PHASE {{ optional($project->phases->first())->order ?? '?' }}/{{ $project->phases->count() }}</h2> {{-- IMP --}}
                                </div>
                            {{-- </a> --}}
                            <div class="project-box__name mb-3" data-project-id="{{ $project->id }}" data-project-name="{{ $project->name }}" style="cursor: pointer;">
                                <h2 class="mb-0">{{ $project->name }} </h2>
                            </div>
                            <div class="project-box__info d-flex align-items-center justify-content-between">
                                <div class="meta">
                                    @php
                                        $total_steps = count($phase->categories);
                                        $current_step = optional($project->category)->order ?? 0;

                                        $percentage = $total_steps > 0
                                            ? round(($current_step / $total_steps) * 100)
                                            : 0;
                                    @endphp


                                    <span class="complete">{{$percentage}}%</span> |
                                    @php
                                    $tasks = $project->tasksExceptRecentlyFinished()->count();
                                    @endphp
                                    <span class="comments"> {{ $tasks }} <a id="taskLink" href="{{ route('show-tasks', ['project_id' => $project->id]) }}">Tasks</a></span>
                                </div>
                                <div class="time">
                                    @php
                                        $projectPhase = $project->phases->where('id', $phase->id)->first();
                                        $projectTarget = $projectPhase && isset($projectPhase->pivot) ? $projectPhase->pivot->project_target : null;
                                    @endphp

                                    @if($projectTarget)
                                    <i class="bi bi-clock"></i> {{ \Carbon\Carbon::parse($projectTarget)->format('M d') }}
                                    @else
                                    <i class="bi bi-clock"></i>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                @endforeach


            </div>
            <div class="foot">
                <a class="add-project-cta d-flex align-items-center justify-content-between text-decoration-none"
                    href="{{ route('add-project', ['category_id' => $category->id, 'phase_id' => $phase->id]) }}">Add Project<span class="icon">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.5634 0.836914V20.4482" stroke-width="1.5"></path>
                            <path d="M20.369 10.6426L0.75769 10.6426" stroke-width="1.5"></path>
                        </svg></span></a>
            </div>
        </div>
        @endforeach
        @endforeach
        <!-- End Category -->

    </div>

    
    <div class="modal fade" id="projectEditModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="projectEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl projectEditModalSize">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="projectEditModalLabel">Project Options</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center d-flex justify-content-center align-items-center">
                        <div class="edit-project">
                            <a href="#" id="editProjectButton" class="btn mx-1">
                                <i class="fas fa-edit me-2"></i> Edit Project
                            </a>
                        </div>
                        <div class="track-project">
                            <a href="#" id="trackProjectButton" class="btn mx-1">
                                <i class="fas fa-line-chart me-2"></i> Track Project
                            </a>
                        </div>
                        <div class="add-actions">
                            <button id="addActionsButton" class="btn mx-1">
                                <i class="fas fa-plus me-2"></i> Add Actions
                            </button>
                        </div>
                        <div class="mark-phase-complete">
                            <button id="markPhaseCompleteButton" class="btn mx-1">
                                <i class="fas fa-check-circle me-2"></i> Mark Phase as Complete
                            </button>
                        </div>
                    </div>
                    
                    <div class="addActionsArea d-none">
                        <hr>
                        
                        
                        <div class="d-flex justify-content-around align-items-center">
                            <div class="w-75">
                                <div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="text-dark mb-2 d-flex col-5">
                                            <label class="form-label w-50" for="project_message_title">Add Title<span class="text-danger fs-4">*</span>:</label>
                                            <div class="w-50">
                                                <input type="text" class="form-control project_message_title" id="project_message_title" name="project_message_title" value="" placeholder="Enter Message Title">
                                                <div>
                                                    <span class="project_message_title_validation_error invalid-feedback d-none">
                                                        {{-- <strong>{{ $message }}</strong> --}}
                                                        <strong></strong>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-dark mb-2 d-flex col-5">
                                            <label class="form-label w-50" for="project_message_due_date">Add Due Date:</label>
                                            <div class="w-50">
                                                <input type="date" class="form-control project_message_due_date" id="project_message_due_date" name="project_message_due_date" value="" placeholder="mm/dd/yyyy">
                                                <div>
                                                    <span class="project_message_due_date_validation_error invalid-feedback d-none">
                                                        {{-- <strong>{{ $message }}</strong> --}}
                                                        <strong></strong>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="text-dark mb-2 d-flex align-items-center col-5">
                                            <label class="form-label w-50" for="project_message_reason">Select Reason:</label>
                                            <div class="w-50 dropdown">
                                                <select class="form-control project_message_reason" id="project_message_reason" name="project_message_reason">
                                                    <option value="">Select option</option>
                                                    <option value="Other">Other</option>
                                                </select>
                                                <i class="fa fa-caret-down dropdown-icon" aria-hidden="true"></i>
                                            </div>
                                        </div>

                                        <div class="text-dark mb-2 col-5 d-none project_message_new_reason_dropdown_input align-items-center">
                                            <label class="form-label w-50" for="project_message_new_reason">Add Reason:</label>
                                            <div class="w-50">
                                                <input type="text" class="form-control project_message_new_reason" id="project_message_new_reason" name="project_message_new_reason" value="" placeholder="Enter Message Reason">
                                                <div>
                                                    <span class="project_message_new_reason_validation_error invalid-feedback d-none">
                                                        {{-- <strong>{{ $message }}</strong> --}}
                                                        <strong></strong>
                                                    </span>
                                                </div>
                                            </div>                                            
                                        </div>
                                    </div>
                                    

                                    <p class="text-dark">Add Description<span class="text-danger fs-4">*</span>:</p>
                                    <textarea class="project_message_description w-100 border border-dark rounded p-2" autofocus></textarea>
                                    <span class="project_message_description_validation_error invalid-feedback d-none">
                                        {{-- <strong>{{ $message }}</strong> --}}
                                        <strong></strong>
                                    </span>
                                </div>
                                <div class="upload-files scrollbar d-flex align-items-center mt-2"></div>
                            </div>
                            <div class="text-center">
                                <div class="uploaded-items">
                                    <div class="upload-btn-wrapper">
                                        <button id="uploadTrigger" class="btn-upload text-uppercase text-dark border border-dark d-flex align-items-center">
                                            <i class="bi bi-upload me-2"></i> 
                                            Upload a file
                                        </button>
                                        <input type="file" name="task_attachments[]" id="attachement" multiple/>
                                    </div>
                                </div>
                                <div class="mb-1">
                                    <button class="submit-btn btn btn-success">Submit</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="current-step-area d-none">
                        <hr>
                        <div class="d-flex justify-content-around align-items-center">
                            <div class="w-75">
                                <div class="">
                                    <div class="text-dark mb-2 d-flex">
                                        <label class="form-label w-50" for="current_step_title">Current Step Title<span class="text-danger fs-4">*</span>:</label>
                                        <div class="w-75">
                                            <input type="text" class="form-control current_step_title" id="current_step_title" name="current_step_title" value="">
                                            <div>
                                                <span class="current_step_title_validation_error invalid-feedback d-none">
                                                    <strong></strong>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                                                            
                                    <p class="text-dark">Current Step Description<span class="text-danger fs-4">*</span>:</p>
                                    <textarea class="current_step_description w-100 border border-dark rounded p-2" id="current_step_description" autofocus></textarea>
                                    <span class="current_step_description_validation_error invalid-feedback d-none">
                                        <strong></strong>
                                    </span>

                                    <div class="text-dark mt-2 d-flex col-12">
                                        <label class="form-label w-50" for="current_step_completion_date">Add Phase Completion Date<span class="text-danger fs-4">*</span>:</label>
                                        <div class="w-75">
                                            @php
                                                $timezone = new DateTimeZone(config('app.timezone'));
                                                $now = new DateTime('now', $timezone);
                                                $defaultDateTime = $now->format('m/d/y h:i A');
                                            @endphp
                                            <input type="datetime-local" class="form-control current_step_completion_date" id="current_step_completion_date" name="current_step_completion_date" value="{{ $defaultDateTime }}">
                                            <div>
                                                <span class="current_step_completion_date_validation_error invalid-feedback d-none">
                                                    {{-- <strong>{{ $message }}</strong> --}}
                                                    <strong></strong>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-center">
                                <div class="mb-1">
                                    <button class="mark-phase-submit-btn btn btn-success">Mark Phase as Complete</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel">
                        <div class="modal-dialog modal-dialog-centered modal-lg">
                            <div class="modal-content p-1">
                                <div class="modal-header border-0">
                                    <div class="modal-heading"></div>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body d-flex justify-content-center align-items-center">
                                    <img id="" class="modal-image img-fluid" src="" alt="Full Size Image"
                                        style='height:450px; width:auto'>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif


</section>

<style>

    
    .drop-hover {
    border: 2px dashed #999;
    background-color: #f9f9f9;
    transition: background 0.2s ease;
    }
    .drop-placeholder {
        height: 80px;
        margin: 10px 0;
        border: 2px dashed #999;
        border-radius: 8px;
        background-color: #f8f8f8;
    }

    #taskLink{
        text-decoration: none;
        color:rgba(17, 17, 17, .5);
    }
    .project-box__phase:hover, .project-box__name:hover{
        text-decoration:underline;
        cursor:pointer;
    }
    .projectEditModalSize{
        max-width:95%;
        max-height:95%;
        margin-top:5px
    }
    #editProjectButton, #trackProjectButton, #addActionsButton, #markPhaseCompleteButton{
        background-color:#ff5811;
        color:white;
    }
    .project_message_description, .project_message_description:focus{
        outline:none;
        resize:none;
        overflow:hidden;
        border:none;
        box-shadow:none;
    }
    input{
        padding-left:3px !important;
        padding-right:3px !important;
    }
    .dropdown select {
        padding-left:3px !important;
        padding-right: 30px; /* Leave space for the icon */
    }
    .dropdown-icon{
        position: absolute;
        top: 35%;
        right: 5%;
        color:grey;
    }

    .scrollbar::-webkit-scrollbar {
      height: 10px;
    }
    .scrollbar::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;
      /* border:1px solid #ff5811; */

    }
    .scrollbar::-webkit-scrollbar-thumb {
      background: #ff5811;
      border-radius: 10px;
    }

    .upload-files{
        overflow-x:auto;
    }
    .upload-btn-wrapper .btn-upload{
        border:1px solid #ff5811 !important;
        color:#ff5811 !important;
    }
</style>

@push('script')
<script src="{{ asset('js/tinymce/tinymce.min.js') }}" ></script>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    flatpickr("#project_message_due_date", {
        dateFormat: "m/d/Y", // MM/DD/YYYY format
        allowInput: true
    });

    flatpickr("#current_step_completion_date", {
        enableTime: true,
        dateFormat: "m/d/y h:i K", // Matches your format
        defaultDate: "{{ $defaultDateTime }}",
        allowInput: true
    });

    // Initialize Bootstrap modal
    const projectEditModal = new bootstrap.Modal(document.getElementById('projectEditModal'));

    // Add click handler for project boxes
    $('.project-box__phase, .project-box__name').on('click', function() {
        const projectId = $(this).data('project-id');
        const projectName = $(this).data('project-name');
        const phaseId = $(this).closest('.project-box').data('phase-id');

        const projectEditModal = $('#projectEditModal');
        projectEditModal.data('current-phase-id', phaseId);
        
        const editProjectButton = $('#editProjectButton');
        const trackProjectButton = $('#trackProjectButton');
        const addActionsButton = $('#addActionsButton');
        const markPhaseCompleteDiv = $('.mark-phase-complete');
        const addActionsArea = $('.addActionsArea');
        const projectMessageTitle = $('.project_message_title'); // Get the message title
        const projectMessageDueDate = $('.project_message_due_date'); // Get the message due-date
        const projectMessageReason = $('.project_message_reason'); // Get the message reason  
        const projectMessageNewReason = $('.project_message_new_reason'); // Get the message new reason
        const projectMessageDescription = $('.project_message_description'); // Get the message textarea      
        const currentStepArea = $('.current-step-area');
        const currentStepCompletionDate = $('.current_step_completion_date');

        // Reset all areas
        addActionsArea.addClass('d-none').removeClass('d-block'); // Hide the add actions area
        addActionsButton.removeClass('border border-3 border-dark'); // Remove border from the add actions button
        projectMessageTitle.val(''); // Clear the text input
        projectMessageDueDate.val(''); // Clear the text input
        projectMessageReason.val(''); // Clear the text input
        projectMessageNewReason.val(''); // Clear the text input
        projectMessageDescription.val(''); // Clear the textarea
        // currentStepCompletionDate.val(''); //default value already set in input in html code

        
        // Clear TinyMCE editor content if it's used elsewhere for project actions
        if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
            // Assuming your TinyMCE instance is on the .project_message_description
            // You might need to get the specific editor instance if you have multiple
            const editor = tinymce.get(projectMessageDescription.attr('id')) || tinymce.activeEditor;
            if (editor) {
                editor.setContent(''); // Clear the editor's content
            }
        } else {
            // Fallback for non-TinyMCE if for some reason it's not initialized
            projectMessageDescription.val('');
        }

        $('.project_message_title_validation_error').removeClass("d-block").addClass("d-none");
        $('.project_message_title_validation_error strong').html("");

        $('.project_message_reason_validation_error').removeClass("d-block").addClass("d-none");
        $('.project_message_reason_validation_error strong').html("");

        $('.project_message_description_validation_error').removeClass("d-block").addClass("d-none");
        $('.project_message_description_validation_error strong').html("");

        currentStepArea.addClass('d-none').removeClass('d-block');
        addActionsButton.removeClass('border border-2 border-dark');
        $('#markPhaseCompleteButton').removeClass('border border-2 border-dark');

        // Update the modal title
        projectEditModal.find('.modal-title').text(`Project ( ${projectName} ) Options`);

        // Update the href for the edit and track buttons
        const editProjectUrl = `/projects/${projectId}/edit`;
        editProjectButton.attr('href', editProjectUrl);

        const trackProjectUrl = `/track-project/${projectId}`;
        trackProjectButton.attr('href', trackProjectUrl);

        // Add click handler for addActionsButton
        addActionsButton.off('click').on('click', function() {
            // Hide current step area if visible
            currentStepArea.addClass('d-none').removeClass('d-block');
            $('#markPhaseCompleteButton').removeClass('border border-2 border-dark');
            
            // Toggle add actions area
            addActionsArea.toggleClass('d-none d-block');
            
            // Toggle button border
            if (addActionsArea.hasClass('d-block')) {
                addActionsButton.addClass('border border-2 border-dark');
            } else {
                addActionsButton.removeClass('border border-2 border-dark');
            }
        });

        // Check if phase is already completed
        fetch(`/projects/${projectId}/check-phase-completion/${phaseId}`)
            .then(response => response.json())
            .then(data => {
                if (data.isCompleted) {
                    // If phase is completed, show completion text instead of button
                    markPhaseCompleteDiv.html(`
                        <div class="text-success">
                            <i class="fas fa-check-circle me-2"></i> Phase Marked as Complete
                        </div>
                    `);
                } else {
                    // If phase is not completed, show the button
                    markPhaseCompleteDiv.html(`
                        <button id="markPhaseCompleteButton" class="btn mx-1">
                            <i class="fas fa-check-circle me-2"></i> Mark Phase as Complete
                        </button>
                    `);
                    
                    // Add click handler for markPhaseCompleteButton
                    $('#markPhaseCompleteButton').off('click').on('click', function() {
                        // Hide add actions area if visible
                        addActionsArea.addClass('d-none').removeClass('d-block');
                        addActionsButton.removeClass('border border-2 border-dark');
                        
                        // Show current step area
                        currentStepArea.removeClass('d-none').addClass('d-block');
                        $(this).addClass('border border-2 border-dark');
                        
                        // Clear any existing validation errors
                        $('.current_step_title_validation_error').removeClass("d-block").addClass("d-none");
                        $('.current_step_title_validation_error strong').html("");
                        $('.current_step_description_validation_error').removeClass("d-block").addClass("d-none");
                        $('.current_step_description_validation_error strong').html("");
                    });
                }
            })
            .catch(error => {
                console.error('Error checking phase completion:', error);
            });

        // Show the modal
        projectEditModal.modal('show');

        //if textarea or file attachments are not empty inside the modal, ask if user wants to particlar clicked link or not
        $('#editProjectButton, #trackProjectButton').off('click').on('click', function(event){
            event.preventDefault();

            if(confirm("Do you want to go to this link?") === true){
                window.location.href = $(this).attr('href');
            }
        })

        $(projectMessageReason).on('change', function(){
            if(projectMessageReason.val() === "Other"){
                $('.project_message_new_reason_dropdown_input').addClass('d-flex').removeClass('d-none');
            }
            else{
                $('.project_message_new_reason_dropdown_input').addClass('d-none').removeClass('d-flex')
            }
        })
        
        $('.submit-btn').off('click').on('click', function(event){
            event.preventDefault();
            
            // alert("submit button clicked")
    
            if(projectMessageTitle.val() == ''){
                $('.project_message_title_validation_error').removeClass("d-none").addClass("d-block");
                $('.project_message_title_validation_error strong').html("Please fill title field!");
            }
            else{
                $('.project_message_title_validation_error').removeClass("d-block").addClass("d-none");
                $('.project_message_title_validation_error strong').html("");
            }


            if(projectMessageReason.val() === "Other"){
                if(projectMessageNewReason.val() === ''){
                    $('.project_message_new_reason_validation_error').removeClass("d-none").addClass("d-block");
                    $('.project_message_new_reason_validation_error strong').html("Please specify a reason!");
                }
            }

            if(projectMessageDescription.val() == ''){
                $('.project_message_description_validation_error').removeClass("d-none").addClass("d-block");
                $('.project_message_description_validation_error strong').html("Please fill description field!");
            }
            else{
                $('.project_message_description_validation_error').removeClass("d-block").addClass("d-none");
                $('.project_message_description_validation_error strong').html("");
            }

            const csrf_token = document.querySelector('meta[name="csrf-token"]').getAttribute('content'); // A more robust way without jQuery

            formData.set("_token", csrf_token);
            formData.set("project_id", projectId);
            formData.set("project_message_title", projectMessageTitle.val());
            formData.set("project_message_due_date", projectMessageDueDate.val());

            if(projectMessageReason.val() != ""){
                if(projectMessageReason.val() !== "Other"){
                    formData.set("project_message_reason", projectMessageReason.val());
                }
                else{
                    formData.set("project_message_reason", projectMessageNewReason.val())
                }
            }

            formData.set("project_message_description", projectMessageDescription.val());

            for (const [key, value] of formData.entries()) {
                console.log(`${key}: ${value}`);
            }

            $.ajax({
                url: "{{ route('save-project-messages') }}",
                method: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function(response){
                    successToast(response.message || "Task created successfully!");

                    // Reset logic
                   
                    projectMessageTitle.val(''); // Clear the text input
                    projectMessageDueDate.val(''); // Clear the text input
                    projectMessageReason.val(''); // Clear the text input
                    projectMessageNewReason.val(''); // Clear the text input
                    projectMessageDescription.val(''); // Clear the textarea
                    
                    // Clear TinyMCE editor content if it's used elsewhere for project actions
                    if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                        // Assuming your TinyMCE instance is on the .project_message_description
                        // You might need to get the specific editor instance if you have multiple
                        const editor = tinymce.get(projectMessageDescription.attr('id')) || tinymce.activeEditor;
                        if (editor) {
                            editor.setContent(''); // Clear the editor's content
                        }
                    } else {
                        // Fallback for non-TinyMCE if for some reason it's not initialized
                        projectMessageDescription.val('');
                    }

                    $('.project_message_title_validation_error').removeClass("d-block").addClass("d-none");
                    $('.project_message_title_validation_error strong').html("");

                    $('.project_message_new_reason_dropdown_input').addClass('d-none').removeClass('d-flex')

                    $('.project_message_description_validation_error').removeClass("d-block").addClass("d-none");
                    $('.project_message_description_validation_error strong').html("");
                    
                    selectedFiles = []; // Clear any selected files
                    updateAllPreviews(); // Update file previews (assuming this function handles clearing as well)
                    updateFileInput(); // Reset file input (assuming this function handles clearing)
                    

                    setTimeout(function() {
                        addActionsArea.addClass('d-none').removeClass('d-block'); // Hide the add actions area
                        addActionsButton.removeClass('border border-3 border-dark'); // Remove border from the add actions button
                        projectEditModal.modal('hide');
                    }, 2500);
                    
                },
                error: function(){
                    
                }
            })
        })
    });



    function fetch_project_message_reasons_in_dropdown(){
        $.ajax({
            url:"{{ route('fetch_project_message_reasons') }}",
            method:"GET",
            success: function(response){
                // console.log(response);

                const selectElement = document.getElementById('project_message_reason');

                // Get references to your fixed options
                const selectOption = selectElement.querySelector('option[value=""]');
                const otherOption = selectElement.querySelector('option[value="Other"]');

                // Loop through your dynamic reasons and insert them
                response.forEach(item => {
                    // Check if the reason is not null or undefined (though your backend should handle this)
                    if (item.reason) {
                        const option = document.createElement('option');
                        option.value = item.reason; // Set the option's value
                        option.textContent = item.reason; // Set the option's visible text

                        // Insert the new option before the "Other" option
                        selectElement.insertBefore(option, otherOption);
                    }
                });
            },
            error: function(){
                console.error('Error fetching project message reasons:', error);
                // Optionally, display an error message to the user
            }
        });
    }
    fetch_project_message_reasons_in_dropdown();


    let selectedFiles = [];
    let dragAndDroppedFiles = [];
    let formData = new FormData();

    tinymce.init({
        selector: "textarea.project_message_description, textarea.current_step_description",
        height: 200,
        plugins: [
            "lists",
            "advlist",
            "autolink",
            "link",
            "image",
            "charmap",
            "preview",
            "anchor",
            "searchreplace",
            "wordcount",
            "code",
            "fullscreen",
            "insertdatetime",
            "table",
        ],
        toolbar:
            "undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen",
        menubar: false,
        content_style: `
            body {
                font-family: Helvetica, Arial, sans-serif;
                font-size: 16px;
                background-color: #1e1e1e;
                color: #f5f5f5;
            }
            #tinymce[data-mce-placeholder]:not(.mce-visual-caret)::before{
                color:white;
            }
            p {
                margin: 0;
                padding: 0;
            }
        `,
        paste_data_images: false,
        automatic_uploads: false,
        paste_as_text: true,
        setup: function (editor) {
            editor.on("init", function () {
                const container = editor.getContainer();
                container.style.width = "100%";
                container.style.border = "1px solid #282828";
                container.style.borderRadius = "4px";
                container.style.backgroundColor = "#282828";
                editor.on("focus", function () {
                    container.style.border = "1px solid #282828";
                    container.style.boxShadow = "0 0 0 0.2rem rgba(120, 120, 120, 0.25)";
                });
                editor.on("blur", function () {
                    container.style.border = "1px solid #444";
                    container.style.boxShadow = "none";
                });
                editor.on('change undo redo input keypress', function () {
                    editor.save();
                });
            });
        },
        skin: "oxide-dark",
        content_css: false,
    });

    document.getElementById("uploadTrigger").addEventListener("click", function () {
        document.getElementById("attachement").click();
    });


    document.getElementById("attachement").addEventListener("change", function () {
        // alert("attachement change")

        var fileInput = this;
        var taskAttachmentsDiv = $('.upload-files');

        if (taskAttachmentsDiv.length === 0) {
            taskAttachmentsDiv = $("<div class='upload-files'></div>").addClass("scrollbar").css({
                "padding": "0px 10px",
                "height": "50px",
                "overflow-y": "hidden",
                "display": "flex",
                "align-items": "center"
            });
            // Append to the correct parent element, assuming it's within a div with class 'upload-files-wrapper'
            // You might need to adjust this selector based on your HTML structure
            $('.upload-files-wrapper').append(taskAttachmentsDiv); 
        }

        if (fileInput.files && fileInput.files.length > 0) {
            for (var i = 0; i < fileInput.files.length; i++) {
                var file = fileInput.files[i];
                // Check against selectedFiles to prevent duplicates from multiple selections
                var existingIndex = selectedFiles.findIndex(sf => 
                    sf.name === file.name && sf.size === file.size && sf.type === file.type
                );

                if (existingIndex === -1) {
                    selectedFiles.push(file);
                }
            }
            // Re-render all previews and update the input/formData after adding new files
            updateAllPreviews(); 
            updateFileInput();
        }
    });


    function displayAllPreviews(file, index) {
        var objectUrl = URL.createObjectURL(file);
        var previewContainer = $(
            "<div class='preview-container' style='margin-top: 10px;margin-bottom:10px; margin-right: 15px;position: relative;display:flex'></div>");
        var previewImg = $(
            "<span class='border border-secondary me-2 p-1 rounded text-dark preview_img_span' data-img_url='" +
            objectUrl +
            "' style='cursor:pointer; max-width:150px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis' title='" +
            file.name + "'>" + file.name + "</span>");
        var closeButton = $("<button type='button' class='close-preview' data-index='" + index +
            "' style='position: absolute; top: -5px; right: -5px; border: none; color: red; font-size: 14px; border-radius: 50%; padding:0px 5px; cursor: pointer;'>&times;</button>"
        );

        previewContainer.append(previewImg);
        previewContainer.append(closeButton);
        $('.upload-files').append(previewContainer);

        // No need for load event on span, it's not an img element
        // previewImg.on('load', function() {
        //     URL.revokeObjectURL(objectUrl);
        // });
        
        // Revoke object URL when the preview container is removed
        previewContainer.on('remove', function() { // Use 'remove' event for jQuery .remove()
            URL.revokeObjectURL(objectUrl);
        });

        closeButton.on('click', function() {
            // var indexToRemove = parseInt($(this).data('index'));
            
            // // Remove from selectedFiles
            // selectedFiles.splice(indexToRemove, 1);

            var container = $(this).parent('.preview-container');
            var filename = container.find('.preview_img_span').text().trim();
            selectedFiles = selectedFiles.filter(f => f.name !== filename);

            
            // Remove the preview container from the DOM
            $(this).parent('.preview-container').remove(); 
            
            // Re-render all previews and update the input/formData
            updateAllPreviews();
            updateFileInput();
        });
    }

    // Function to clear all existing previews and re-render them based on selectedFiles
    function updateAllPreviews() {
        $('.upload-files').empty(); // Clear existing previews
        selectedFiles.forEach((file, index) => {
            displayAllPreviews(file, index);
        });

        if (selectedFiles.length === 0) {
            $('.upload-files').css({
                "height": "0px"
            });
        } else {
            $('.upload-files').css({
                "height": "auto" // Or whatever your desired height is when files are present
            });
        }
    }


    function updateFileInput() {
        var fileInput = $('input[name="task_attachments[]"]')[0];
        var dataTransfer = new DataTransfer();

        // Clear existing task_attachments from formData
        formData.delete("task_attachments[]");

        // Add all files from selectedFiles to dataTransfer and formData
        selectedFiles.forEach(function(file) {
            if (file instanceof File) {
                formData.append("task_attachments[]", file);
                dataTransfer.items.add(file);
            } else {
                console.warn('Skipped non-File object in selectedFiles:', file);
            }
        });

        // Update the actual file input's files property
        fileInput.files = dataTransfer.files;

        console.log("Current files in input:", fileInput.files.length);
        console.log("Current files in formData:", Array.from(formData.getAll("task_attachments[]")).length);
    }

    // Handle attachment preview and download
    $(document).on('click', '.preview_img_span', function(e) {
        e.preventDefault();
        const imgUrl = $(this).data('img_url');
        const fileName = $(this).text().trim();
        const fileExtension = fileName.split('.').pop().toLowerCase();

        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        const browsableExtensions = ['html', 'css', 'js', 'pdf'];

        if (imageExtensions.includes(fileExtension)) {
            $('.modal-image').attr('src', imgUrl);
            $('.modal-heading').text(fileName);
            $('#imageModal').modal('show');
        } 
        else if (browsableExtensions.includes(fileExtension)) {
            window.open(imgUrl, '_blank');
        } 
        else {
            const link = document.createElement('a');
            link.href = imgUrl;
            link.download = fileName; 
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            // Revoke URL *after* the download is initiated, only if it's a Blob URL
            // If imgUrl is from a server, do NOT revoke.
            // For files selected via input, imgUrl is typically a Blob URL.
            URL.revokeObjectURL(imgUrl); 
        }
    });

    // Initial call to updateFileInput to ensure it's in a clean state if no files are initially selected
    // $(document).ready(function() {
        updateFileInput(); 
    // });




    const dragItems = document.querySelectorAll('.project-box');
    const dropContainers = document.querySelectorAll('.project-list');
    let dropPlaceholder = document.createElement('div');
    dropPlaceholder.classList.add('drop-placeholder');
    
    // Track current container to prevent unnecessary operations
    let currentContainer = null;
    let draggedElement = null;

    // Add draggable attribute to all drag items
    dragItems.forEach(item => {
        item.draggable = true;
        item.addEventListener('dragstart', dragHandler);
        item.addEventListener('dragend', dragEndHandler);
    });

    dropContainers.forEach(container => {
        container.addEventListener('dragover', dragOverHandler);
        container.addEventListener('dragleave', dragLeaveHandler);
        container.addEventListener('drop', dropHandler);
    });

    function dragHandler(event) {
        draggedElement = event.target;
        event.dataTransfer.setData('text/plain', event.target.id);
        event.dataTransfer.effectAllowed = 'move';
        
        // Add a slight delay to prevent immediate issues
        setTimeout(() => {
            if (draggedElement) {
                draggedElement.style.opacity = '0.5';
            }
        }, 0);
    }

    function dragEndHandler(event) {
        // Clean up after drag operation
        if (draggedElement) {
            draggedElement.style.opacity = '';
            draggedElement = null;
        }
        
        // Remove placeholder if it still exists
        if (dropPlaceholder.parentNode) {
            dropPlaceholder.remove();
        }
        
        currentContainer = null;
    }

    function dragOverHandler(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
        
        const container = event.currentTarget;
        
        // Only update placeholder if we're in a different container or position
        if (currentContainer !== container) {
            currentContainer = container;
            updatePlaceholderPosition(event, container);
        } else {
            // Still update position within the same container for smooth movement
            const target = getDropTarget(event, container);
            const currentNext = dropPlaceholder.nextElementSibling;
            
            if (target !== currentNext) {
                updatePlaceholderPosition(event, container);
            }
        }
    }

    function dragLeaveHandler(event) {
        const container = event.currentTarget;
        const rect = container.getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;
        
        // Only remove placeholder if we're actually leaving the container
        // Check if the mouse is outside the container bounds
        if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
            // Additional check: make sure we're not entering a child element
            if (!container.contains(event.relatedTarget)) {
                if (dropPlaceholder.parentNode === container) {
                    dropPlaceholder.remove();
                    currentContainer = null;
                }
            }
        }
    }

    function dropHandler(event) {
        event.preventDefault();
        
        const container = event.currentTarget;
        const data = event.dataTransfer.getData('text/plain');
        const draggedItem = document.getElementById(data);
        
        if (!draggedItem || !dropPlaceholder.parentNode) {
            return;
        }

        // Store reference before DOM manipulation
        const oldDraggedItem = draggedItem.cloneNode(true);
        
        // Replace placeholder with dragged item
        dropPlaceholder.parentNode.replaceChild(draggedItem, dropPlaceholder);
        
        // Update data attributes
        const newCategoryId = container.getAttribute('data-category-id');
        const newPhaseId = container.getAttribute('data-phase-id');
        
        draggedItem.setAttribute('data-category-id', newCategoryId);
        draggedItem.setAttribute('data-phase-id', newPhaseId);
        
        // Send update to backend
        const projectId = draggedItem.getAttribute('data-project-id');
        console.log("Project ID", projectId, "Category ID", newCategoryId, "Phase ID", newPhaseId);
        sendUpdateToBackend(projectId, newCategoryId, newPhaseId, draggedItem);
        
        // Clean up
        currentContainer = null;
    }

    function updatePlaceholderPosition(event, container) {
        const target = getDropTarget(event, container);
        
        // Remove placeholder from current position
        if (dropPlaceholder.parentNode) {
            dropPlaceholder.remove();
        }
        
        // Insert placeholder in new position
        if (target) {
            container.insertBefore(dropPlaceholder, target);
        } else {
            container.appendChild(dropPlaceholder);
        }
    }

    function getDropTarget(event, container) {
        const children = [...container.children].filter(child =>
            child !== dropPlaceholder && 
            child !== draggedElement && 
            child.classList.contains('project-box')
        );
        
        return children.find(child => {
            const rect = child.getBoundingClientRect();
            return event.clientY < rect.top + rect.height / 2;
        });
    }

    function sendUpdateToBackend(projectId, categoryId, phaseId, draggedItem) {
        fetch(`/projects/${projectId}/update_project_information`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                category_id: categoryId,
                phase_id: phaseId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Update success:', data);
            if (data.percentage !== undefined) {
                const meta = draggedItem.querySelector('.project-box__info .meta');
                if (meta) {
                    const completeElement = meta.querySelector('.complete');
                    if (completeElement) {
                        completeElement.textContent = `${data.percentage}%`;
                    }
                }
            }
        })
        .catch(error => {
            console.error('Update failed:', error);
            // Optionally revert the UI change or show an error message
        });
    }

    // Update the mark phase complete button click handler
    $('#markPhaseCompleteButton').off('click').on('click', function() {
        // Hide add actions area if visible
        addActionsArea.addClass('d-none').removeClass('d-block');
        addActionsButton.removeClass('border border-2 border-dark');
        
        // Show current step area
        currentStepArea.removeClass('d-none').addClass('d-block');
        $(this).addClass('border border-2 border-dark');
        
        // Clear any existing validation errors
        $('.current_step_title_validation_error').removeClass("d-block").addClass("d-none");
        $('.current_step_title_validation_error strong').html("");
        $('.current_step_description_validation_error').removeClass("d-block").addClass("d-none");
        $('.current_step_description_validation_error strong').html("");

        // Clear the editor content
        if (tinymce.get('current_step_description')) {
            tinymce.get('current_step_description').setContent('');
        }
    });

    // Update the mark phase submit button click handler
    $(document).on('click', '.mark-phase-submit-btn', function() {
        const currentStepTitle = $('.current_step_title').val();
        const currentStepDescription = tinymce.get('current_step_description').getContent();
        const currentStepCompletionDate = $('.current_step_completion_date').val();

        let hasError = false;

        if(currentStepTitle == '') {
            $('.current_step_title_validation_error').removeClass("d-none").addClass("d-block");
            $('.current_step_title_validation_error strong').html("Please fill title field!");
            hasError = true;
        } else {
            $('.current_step_title_validation_error').removeClass("d-block").addClass("d-none");
            $('.current_step_title_validation_error strong').html("");
        }
        
        if(currentStepDescription == '') {
            $('.current_step_description_validation_error').removeClass("d-none").addClass("d-block");
            $('.current_step_description_validation_error strong').html("Please fill description field!");
            hasError = true;
        } else {
            $('.current_step_description_validation_error').removeClass("d-block").addClass("d-none");
            $('.current_step_description_validation_error strong').html("");
        }

        if(currentStepCompletionDate == ''){
            $('.current_step_completion_date_validation_error').removeClass("d-none").addClass("d-block");
            $('.current_step_completion_date_validation_error strong').html("Please fill phase completion date!");
            hasError = true;
        }
        else{
            $('.current_step_completion_date_validation_error').removeClass("d-block").addClass("d-none");
            $('.current_step_completion_date_validation_error strong').html("");
        }

        if (hasError) return;

        const editButtonHref = $(this).closest('.modal').find('#editProjectButton').attr('href');
        const urlParts = editButtonHref.split('/').filter(Boolean);
        const projectId = urlParts[urlParts.length - 2];
        const currentPhaseId = $(this).closest('.modal').data('current-phase-id');

        // Send request to mark phase as complete
        fetch(`/projects/${projectId}/mark-phase-complete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                phase_id: currentPhaseId,
                title: currentStepTitle,
                description: currentStepDescription,
                completion_date: currentStepCompletionDate,
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Show success message
                successToast('Phase marked as complete successfully!');
                
                // Close the modal
                $('#projectEditModal').modal('hide');
                
                // Reload the page to show updated status
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                throw new Error(data.message || 'Failed to mark phase as complete');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            errorToast(error.message || 'Failed to mark phase as complete');
        });
    });
});

    </script>

@endpush
@endsection