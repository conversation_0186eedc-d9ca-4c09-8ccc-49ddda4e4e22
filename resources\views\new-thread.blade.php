@extends('layout.app')
@section('title', 'New Thread')
@section('content')
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
               @if ( admin_superadmin_permissions() && !Auth::user()->has<PERSON><PERSON><PERSON><PERSON>('Client Member') )
                    <a class="d-inline-flex align-items-center text-decoration-none"
                        href="{{ route('statushub') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to StatusHub
                    </a>
                @else
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{route('client.dashboard')}}"><img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Client Portal</a>
                @endif 
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project: <i>[{{$project->job_code}}] {{$project->name}}</i></h1>
                    <div class="page-links mt-3"><a class="" href="{{ route('track-project', ['id'=> $project->id]) }}">Track Project</a><a class="active" href="{{ route('message-centre', ['id'=> $project->id]) }}">Message Center</a><a href="{{ route('file-upload', ['id'=> $project->id]) }}">File Upload</a><a href="{{ route('billing-history', ['id'=> $project->id]) }}">Billing History</a></div>
                </div>
                <div class="logo">
                    <a href="#"><img src="{{ asset('images/bear-tide-oysters-logo.png') }}" alt="" width="72" height="80" /></a>
                </div>
            </div>
        </div>
    </section>
    <section class="client-project bg-white">
        <div class="container-xxl">
            <div class="go-back">
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('message-centre', ['id'=> $project->id]) }}"><img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Message Center</a>
            </div>
            <div class="status d-flex justify-content-between">
                <div class="text">
                    <h2 class="text-uppercase mb-2">COMPOSE YOUR MESSAGE</h2>
                    {{-- <h3 class="opacity-50 mb-0">Type a message subject here...</h3> --}}
                    <input class="fs-6" type="text" placeholder="Type a message subject here...">
                </div>
            </div>
        </div>
    </section>
    <section class="bg-white pb-5">
        <form class="container-xxl pb-5" action="">
            <div class="message-box reply d-flex">
                <div class="pic"></div>
                <div class="message-wrap d-flex flex-grow-1">
                    <div class="message flex-fill d-flex align-items-center">
                        <textarea class="border-0 w-100" name="" placeholder="Add reply..."></textarea>
                    </div>
                </div>
            </div>
            <div class="message-box upload d-flex flex-column mt-3">
                <div class="send-along">
                    <div class="form-check">
                        <input class="form-check-input" id="selectedOnes" type="radio" name="sendMessageAlong" />
                        <label class="form-check-label" for="selectedOnes">When I post this message, email it to all <a href="#">4 people</a> who want emails about this project.</label>
                    </div>
                    <div class="form-check mt-2 pt-1">
                        <input class="form-check-input" id="selectNew" type="radio" name="sendMessageAlong" />
                        <label class="form-check-label" for="selectNew">Let me choose who should get an email...</label>
                    </div>
                </div>
                <div class="cta-row d-flex justify-content-between mt-4">
                    <div class="upload-btn-wrapper">
                        <button class="btn-upload text-uppercase d-flex align-items-center"><i class="bi bi-upload me-2"></i> Upload a file</button>
                        <input type="file" name="myfile" />
                    </div>
                    <button class="cta text-uppercase mt-0" type="submit">ADD COMMENT</button>
                </div>
            </div>
        </form>
    </section>
@endsection