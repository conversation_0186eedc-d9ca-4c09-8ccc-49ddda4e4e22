h2,
.h2 {
	font: 700 40px $futura;

	@media (min-width: 992px) {
		font-size: 72px;
	}

	i,
	em {
		font-family: $play;
	}

	&.h2-big {
		font-size: 36px;

		@media (min-width: 992px) {
			font-size: 48px;
		}
	}
}

h3,
.h3 {
	font: 700 18px $futura;

	@media (min-width: 992px) {
		font-size: 32px;
	}

	i,
	em {
		font-family: $play;
	}
}

p,
.p,
li {
	color: $lightFont;
	font: 16px/1.5 $inter;

	a {
		color: $orange;
		text-decoration: none;
	}
}

p {
	&:last-child {
		margin-bottom: 0;
	}
}

.bg-white {
	p,
	.p,
	li {
		color: $darkFont;
	}
}

.cta {
	border: 2px solid $orange;
	border-radius: 26px;
	font: 600 14px/1 $inter;
	margin-top: 30px;
	padding: 15px 30px;
	white-space: nowrap;

	@media (min-width: 992px) {
		margin-top: 50px;
	}

	&:hover {
		background-color: $orange;
	}
}

.text-dark {
	color: $siteBlack !important;
}

.text-orange {
	color: $orange !important;
}

.text-ocean {
	color: #5bc4d2 !important;
}

.font-futura {
	font-family: $futura !important;
}

.font-inter {
	font-family: $inter !important;
}

.font-play {
	font-family: $play !important;
}

.text-balance {
	text-wrap: balance;
}

.text-pretty {
	text-wrap: pretty;
}

.text-stable {
	text-wrap: stable;
}
