body {
	background: $siteBlack;

	&:has(.hero) {
		.header {
			position: absolute;
		}
	}

	&.navopened {
		overflow: hidden;

		.header {
			h1 {
				color: $white;
			}

			.site-nav {
				.cta {
					border-color: rgba(255, 255, 255, 0.3);
					color: $white;
				}

				.nav_call {
					border-color: rgba(255, 255, 255, 0.3);
				}
			}
		}
	}
}

.sec {
	padding: 100px 0;

	@media (min-width: 992px) {
		padding: 135px 0;
	}
}

.container-xxl {
	&.wide {
		max-width: 1440px;
	}
}

.imgpocket,
.img_pocket {
	padding: 50px 0;

	@media (min-width: 768px) {
		padding: 100px 0;
	}

	.img:not(.no-cover) {
		height: 100%;

		img {
			height: 100%;
			object-fit: cover;
		}
	}

	.map {
		@media (min-width: 768px) {
			height: 100%;
		}

		iframe {
			height: 100%;
			left: 0;
			position: absolute;
			top: 0;
			width: 100%;
		}

		@media (max-width: 991px) {
			&:before {
				content: '';
				display: block;
				padding-top: 100%;
			}
		}
	}
}

.taglist {
	a {
		border-radius: 4px;
		font: 12px $inter;
		padding: 10px;
	}
}

.hover-underline {
	position: relative;

	&:after {
		background-color: $orange;
		bottom: 0;
		content: '';
		height: 1px;
		left: 0;
		position: absolute;
		transform: scaleX(0);
		transform-origin: bottom right;
		transition: transform 0.25s ease-out;
		width: 100%;
	}

	&:hover {
		&:after {
			transform: scaleX(1);
			transform-origin: bottom left;
		}
	}
}

.has-bg {
	isolation: isolate;
	position: relative;

	.bg-img {
		background-position: center top;
		background-repeat: no-repeat;
		height: 100%;
		left: 0;
		position: absolute;
		top: 0;
		width: 100%;
		z-index: -1;
	}
}

.thankyou-popup {
	.btn-close {
		border: 1px solid rgba(0, 0, 0, 0.25);
		font-size: 12px;
		right: -10px;
		top: -10px;
	}
}

.ribbon {
	aspect-ratio: 800 / 22;
	background: #282828;
	border-radius: 20px;
	gap: 1rem;
	width: 100%;

	.dot {
		aspect-ratio: 1;
		background: #111;
		border-radius: 50%;
		height: 60%;
		margin: 0 6px;
	}

	.bar {
		flex: 1;
		height: 100%;

		&.orange {
			background: #ff4c00;
			flex: 0.125;
		}

		&.green {
			background: #65ccb0;
			flex: 0.6;
		}

		&.pink {
			background: #f45689;
		}

		&.purple {
			background: #a15cd4;
		}
	}
}
