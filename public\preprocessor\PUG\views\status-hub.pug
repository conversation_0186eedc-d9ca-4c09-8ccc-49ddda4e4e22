- let pageName = 'Status Hub';
- let mainPage = 'Home';
- let clientPage = 'Brand';
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/projectBoxMeta.pug

mixin iconAction(icon, pageLink=false)
    - let linkHref = pageLink ? `${icon.replaceAll(' ', '-').toLowerCase()}.html` : '#';
    - let myProject = icon === 'Your Projects' ? 'myProjects' : ''
    a.action.d-flex.align-items-center.justify-content-center(href=linkHref, title=icon, id=myProject) #[img(src=`images/${icon.replaceAll(' ', '-').toLowerCase()}-hub-icon.svg`, alt="", width="36", height="36")]

mixin columnHead(icon, head)
    .head.d-flex.align-items-center.pb-3
        .meta.d-flex.align-items-center.me-3
            .icon.d-flex.me-2
                img(src=`images/${icon}-icon.svg`, alt="", width="22", height="22")
            h2.text-uppercase.mb-0=head
        .sort.d-flex.align-items-center.ms-auto
            span Sort By
            .icon.ms-2 #[img(src="images/sort-down-arrow.svg", alt="")]
        .icon-more.ms-3 #[i.bi.bi-three-dots]

mixin projectBox()
    .project-box.mt-3.d-flex
        .project-box__flag-list.pt-3
            .project-box__flag.green
            .project-box__flag.gold
            .project-box__flag.blue
            .project-box__flag.orange
            .project-box__flag.dull
        .project-box__meta.flex-grow-1
            .project-box__phase
                h2.text-uppercase.mb-0 [BT-003-25] - PHASE 1/5
            .project-box__name.mb-3
                h2.mb-0 Bear Tide Sample Project
            .project-box__info.d-flex.align-items-center.justify-content-between
                .meta #[span.complete 75%] | #[span.comments 3 Comments]
                .time #[i.bi.bi-clock] Mar 12

mixin columnFoot(icon, head)
    .foot
        a.add-project-cta.d-flex.align-items-center.justify-content-between.text-decoration-none(href="new-project.html") Add Project
            span.icon
                +addProjectPlusIcon()

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        section.projects-hub.d-flex
            .controls-hub.d-flex.flex-column
                .go-back.my-3.text-center
                    a(href="team-portal.html") #[img(src="images/go-back-orange.svg", alt="", width="10", height="17")]
                .action-set.d-flex.flex-column
                    each icon, index in ['New Project', 'New List', 'Reorganize Lists', 'Move Selected']
                        - let indexPage = index === 0
                        +iconAction(icon, indexPage)
                hr
                .action-set.d-flex.flex-column.mb-auto
                    each icon in ['Your Projects', 'Main Phases', 'Employee List', 'Project Master List']
                        +iconAction(icon)

                .action-set.d-flex.flex-column.my-4
                    each icon in ['Settings']
                        +iconAction(icon)
            .projects-columns.flex-grow-1.d-flex
                .project-column.stale
                    +columnHead('define-project-dull', 'Cold/Stale')
                    .project-list.pb-3
                        - for(let i = 0; i < 10; i++)
                            +projectBox
                    +columnFoot
                .project-column.orange
                    +columnHead('define-project', 'LEAD CONVERSION')
                    .project-list.pb-3
                        - for(let i = 0; i < 10; i++)
                            +projectBox
                    +columnFoot
                .project-column.orange
                    +columnHead('define-project', 'ON DECK')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.green
                    +columnHead('content-project', 'STRATEGY')
                    .project-list.pb-3
                        - for(let i = 0; i < 10; i++)
                            +projectBox
                    +columnFoot
                .project-column.green
                    +columnHead('content-project', 'CONTENT')
                    .project-list.pb-3
                        - for(let i = 0; i < 1; i++)
                            +projectBox
                    +columnFoot
                .project-column.pink
                    +columnHead('design-project', 'DESIGN (P)')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.pink
                    +columnHead('design-project', 'DESIGN (D)')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.pink
                    +columnHead('design-project', 'EXECUTION (P)')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.purple
                    +columnHead('design-purple-project', 'HTML/CMS')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.purple
                    +columnHead('design-purple-project', 'DEV/PROD')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.orange
                    +columnHead('deploy-project', 'DEPLOY/MANAGE')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.dull
                    +columnHead('slow-project', 'IF I’M SLOW')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot
                .project-column.dull
                    +columnHead('finance-project', 'FINANCE')
                    .project-list.pb-3
                        - for(let i = 0; i < 2; i++)
                            +projectBox
                    +columnFoot
                .project-column.dull
                    +columnHead('finance-project', 'INVOICE THIS WEEK')
                    .project-list.pb-3
                        - for(let i = 0; i < 4; i++)
                            +projectBox
                    +columnFoot
                .project-column.dull
                    +columnHead('finance-project', 'WAIT TO INVOICE')
                    .project-list.pb-3
                        - for(let i = 0; i < 3; i++)
                            +projectBox
                    +columnFoot

        include ../partials/footer.pug
        +footer(true, false)