<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProjectMessageFile extends Model
{
    protected $fillable = [
        'message_id',
        'file_name',
        'file_path',
        'file_type',
        'file_size'
    ];

    public function message(): BelongsTo
    {
        return $this->belongsTo(ProjectMessageThread::class, 'message_id');
    }
} 