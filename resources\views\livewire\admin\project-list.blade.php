<section class="client-project pt-0">
       

<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('dashboard') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @endif
        </div>
        <div class="meta d-flex justify-content-between align-items-center">
            @if( count($projects) > 0 )
            <div class="copy">
                <h1 class="text-white mb-0">All, <i>Projects</i></h1>

            </div>
            @else
            <div class="copy">
                <h1 class="text-white mb-0">No, <i>Projects</i></h1>
            </div>
            @endif
        </div>
    </div>
</section>

<div class="search-container">
    <div class="search-wrapper">
        <i class="bi bi-search search-icon"></i>
        <input type="text" class="form-control search-input background-dark" placeholder="Search by project name or job code" wire:model.live.debounce.500ms="search" />
        <i class="bi bi-x-circle clear-icon"></i>
    </div>
</div>


@if( count($projects) > 0 )
<section class="client-project pt-0">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="projects-list-table">
            <div class="head d-flex align-items-center justify-content-between">
                @if( count($projects) > 0 )
                    @php
                        $currentYear = now()->year;
                    @endphp

                    <div class="years d-flex">
                        <a href="{{ route('admin-projects', ['year' => 'all']) }}" class="{{ $year === 'all' || !$year ? 'active' : '' }}">
                            All
                        </a>

                        @foreach ($years as $y)
                            <a href="{{ route('admin-projects', ['year' => $y]) }}" class="{{ $year == $y ? 'active' : '' }}">
                                {{ $y }}
                            </a>
                        @endforeach
                    </div>

                    <div class="alphabet d-flex flex-wrap">
                        @foreach (range('A', 'Z') as $letter)
                            @php
                                $letterCount = $letterCounts[$letter] ?? 0;
                                $hasClients = $letterCount > 0;
                            @endphp
                            <a wire:click="{{ $hasClients ? '$set(\'alphabet\', \''.$letter.'\')' : 'null' }}"
                               wire:loading.class="loading-blur"
                               href="javascript:void(0)"
                               class="alphabet-letter me-2 mb-2 px-2 py-1 {{ $alphabet === $letter ? 'alphabet-active' : '' }} {{ !$hasClients ? 'disabled-letter' : '' }}">
                                {{ $letter }}
                                @if($letterCount > 0)
                                    <span class="letter-count">{{ $letterCount }}</span>
                                @endif
                            </a>
                        @endforeach
                        <a wire:click="$set('alphabet', null)"
                           wire:loading.class="loading-blur"
                           href="javascript:void(0)"
                           class="alphabet-letter me-2 mb-2 px-2 py-1 {{ is_null($alphabet) ? 'alphabet-active' : '' }}">
                            All
                        </a>
                    </div>


                @endif
            </div>
            <div class="pages-top d-flex justify-content-end mt-3 mb-4">
                <div class="pages d-flex">
                    <span>Page</span>
                    @for ($i = 1; $i <= $projects->lastPage(); $i++)
                    <a wire:click="gotoPage({{ $i }})" href="javascript:void(0)" class="{{ $projects->currentPage() == $i ? 'active' : '' }}">
                        {{ $i }}
                  </a>
                    @endfor
                </div>
            </div>

           

            <div class="project-list-container" wire:loading.class="loading">
            @forelse( $projects as $project )
                <div class="meta-project d-flex orange">
                    <div class="icon d-flex align-items-center justify-content-center checked"><img src="{{ asset('images/checked-project-icon.svg') }}" alt="" /></div>
                    <div class="copy flex-grow-1 d-flex align-items-end justify-content-between">
                        <div class="text">
                            <h2 class="mb-1"><a href="{{ route('edit-project', [ 'project_id' => $project->id ] ) }}">[{{ $project->job_code }}]</a></h2>
                            <p class="text-white">{{ $project->name }}</p>
                        </div>
                        <div class="d-flex align-items-center">


                            @if($project->archived)
                            <div class="cta-row me-3">
                               <button class="unarchive_project" data-id="{{ $project->id }}" data-project-name="{{ $project->name }}">
                                    Unarchive Project
                                </button>
                            </div>
                            @else
                            <div class="cta-row me-3">
                                <button class="archive_project " data-id="{{ $project->id }}" data-project-name="{{ $project->name }}">
                                    Archive Project
                                </button>
                            </div>
                            @endif
                            <form method="POST" action="{{ route('delete-project', ['project_id' => $project->id]) }}" name="delete_project{{ $project->id }}" id="delete_project_form_{{ $project->id }}">
                                <div class="cta-row me-3">
                                    @csrf
                                    @method('DELETE')
                                    <div class="cta-row me-3">
                                        <button type="button" class="btn btn-link text-white delete-project-button" onclick="confirmDelete('delete_project_form_{{ $project->id }}')" data-project-id="{{ $project->id }}" data-project-name="{{ $project->name }}"">
                                            Delete Project
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <div class="cta-row"><a href="{{ route('essential-metrics', ['project_id' => $project->id]) }}">Project Voyager</a></div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="text-white py-4">No clients found for selected filters.</div>
            @endforelse
            </div>


            <div class="d-flex justify-content-end my-5">
                <div class="pages d-flex">
                    <span>Page</span>
                    @for ($i = 1; $i <= $projects->lastPage(); $i++)
                    <a wire:click="gotoPage({{ $i }})" href="javascript:void(0)" class="{{ $projects->currentPage() == $i ? 'active' : '' }}">
                        {{ $i }}
                  </a>
                    @endfor
                </div>
            </div>
        </div>
        <hr class="mt-0 mb-4 border-white" />
    </div>
</section>

@else
<div class="no-item">
    <!-- Floating background icons -->
    <i class="bi bi-folder flying-icon"></i>
    <i class="bi bi-file-earmark flying-icon"></i>
    <i class="bi bi-search flying-icon"></i>
    <i class="bi bi-clipboard flying-icon"></i>
    
    <div class="no-items-container">
        <div class="icon-container">
            <i class="bi bi-folder-x main-icon"></i>
        </div>
        <h4 class="title">No Projects Found</h2>
        <p class="description">Let's Create a New One</p>
        <button class="action-button" onclick="window.location.href='{{ route('add-project') }}'">
            <i class="bi bi-plus-circle me-2"></i>Create New Project
        </button>
    </div>
</div>
@endif
<section class="ask-question">
    <div class="container-xxl">
        <div class="d-flex align-items-center justify-content-between pb-4">
            <div class="head">
                <h2 class="mb-0">
                    Have a question?<br />
                    <i>We’re here to help.</i>
                </h2>
            </div>
            <div class="cta-row"><a class="cta link text-decoration-none text-white" href="#">GET EMAIL UPDATES</a><a class="cta link text-decoration-none text-white" href="#">NEED HELP? CONTACT</a></div>
        </div>
        <hr class="mt-0 mt-4 border-white" />
    </div>
</section>

<script>
    function confirmDelete(formId) {
        const projectId = document.querySelector(`#${formId} button`).dataset.projectId;
        const projectName = document.querySelector(`#${formId} button`).dataset.projectName;
        if (confirm(`Delete this project (${projectName}) ?`)) {
            document.getElementById(formId).submit();
        }
    }




        function unarchiveHandler(e) {
            e.stopPropagation();

            const button = this;
            const projectId = button.getAttribute('data-id');
            var projectName = "";
 
            projectName = button.getAttribute('data-project-name');
            if (!confirm(`Unarchive this project (${projectName}) ?`)) {
                return;
            }
            const projectContainer = button.closest('.meta-project');

            button.disabled = true;
            const originalText = button.textContent.trim();
            // Store original width and height
            const originalWidth = button.offsetWidth;
            const originalHeight = button.offsetHeight;
            // Set fixed dimensions before changing content
            button.style.width = originalWidth + 'px';
            button.style.height = originalHeight + 'px';
            button.innerHTML = '<span class="btn-spinner"></span>';

            fetch(`/projects/${projectId}/unarchive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if(data.success) {

                    const ctaRow = button.closest('.cta-row');
                    ctaRow.innerHTML = `
                        <button class="archive_project" data-id="${projectId}" data-project-name="${projectName}">
                            Archive Project
                        </button>
                    `;


                    ctaRow.querySelector('.archive_project').addEventListener('click', archiveHandler);


                    projectContainer.classList.remove('archived-project');
                } else {
                    alert('Failed to unarchive project');
                    button.disabled = false;
                    button.style.width = '';
                    button.style.height = '';
                    button.innerHTML = 'Unarchive Project';
                }
            })
            .catch(error => {
                console.error('Unarchive error:', error);
                button.disabled = false;
                button.style.width = '';
                button.style.height = '';
                button.innerHTML = 'Unarchive Project';
            });
        }


        function archiveHandler(e) {
            e.stopPropagation();

            const button = this;
            const projectId = button.getAttribute('data-id');
            var projectName = "";
            projectName = button.getAttribute('data-project-name');
            if (!confirm(`Archive this project (${projectName}) ?`)) {
                return;
            }
            const projectContainer = button.closest('.meta-project');

            button.disabled = true;
            const originalText = button.textContent.trim();

            const originalWidth = button.offsetWidth;
            const originalHeight = button.offsetHeight;
            button.style.width = originalWidth + 'px';
            button.style.height = originalHeight + 'px';
            button.innerHTML = '<span class="btn-spinner"></span>';

            fetch(`/projects/${projectId}/archive`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if(data.success) {

                    const ctaRow = button.closest('.cta-row');
                    ctaRow.innerHTML = `
                        <button class="unarchive_project" data-id="${projectId}" data-project-name="${projectName}">
                            Unarchive Project
                        </button>
                    `;


                    ctaRow.querySelector('.unarchive_project').addEventListener('click', unarchiveHandler);


                    projectContainer.classList.add('unarchived-project');
                } else {
                    alert('Failed to archive project');
                    button.disabled = false;
                    button.style.width = '';
                    button.style.height = '';
                    button.innerHTML = 'Archive Project';
                }
            })
            .catch(error => {
                console.error('Archive error:', error);
                button.disabled = false;
                button.style.width = '';
                button.style.height = '';
                button.innerHTML = 'Archive Project';
            });
        }

        //event delegation in dynamic content
        document.addEventListener('DOMContentLoaded', function() {

        const projectListContainer = document.querySelector('#projects-list') || document.body; // Use a specific container if available

        projectListContainer.addEventListener('click', function(event) {
            const clickedElement = event.target; // The actual element clicked (could be child of button)

            // Use .closest() to find the nearest ancestor (including itself) that matches the selector
            const archiveButton = clickedElement.closest('.archive_project');
            const unarchiveButton = clickedElement.closest('.unarchive_project');

            if (archiveButton) {
                archiveHandler.call(archiveButton, event);
            } else if (unarchiveButton) {
                unarchiveHandler.call(unarchiveButton, event);
            }
        });
    });
</script>

</section>
