@extends('admin.layouts.app')
@section('title', 'Projects')
@section('content')


@livewire('admin.project-list') 

@endsection


@push('styles')

<style>


        .archive_project{
            position: relative;
            border-radius:30px;
            background-color: #ff4c00;
            border: transparent;
            color:white;
            padding: 5px 12px;
            font: 13px / 1.5 "Inter", sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 110px;
        }

         .unarchive_project{
            position: relative;
            border-radius:30px;
            background-color: #ff4c00;
            border: transparent;
            color:white;
            padding: 5px 12px;
            font: 13px / 1.5 "Inter", sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 130px;
        }

        .btn-spinner {
            display: inline-block;
            width: 1.2rem;
            height: 1.2rem;
            vertical-align: middle;
            border: 0.2em solid #fff;
            border-right-color: transparent;
            border-radius: 50%;
            animation: spinner-border .75s linear infinite;
            position: absolute;
            top: 50%;
            left: 50%;
            margin: -0.6rem 0 0 -0.6rem;
        }

        @keyframes spinner-border {
            to { transform: rotate(360deg); }
        }
        /* Alphabet filter styling */
        .alphabet-letter {
            border-radius: 4px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #fff;
            font-weight: 500;
            position: relative;
        }

        .alphabet-letter:hover {
            background-color: rgba(255, 102, 0, 0.3);
            color: #fff;
        }

        .alphabet-active {
            background-color: #ff4c00;
            color: #fff;
            box-shadow: 0 0 10px rgba(255, 102, 0, 0.5);
        }

        .loading-blur {
            opacity: 0.6;
            filter: blur(1px);
            pointer-events: none;
        }

        .disabled-letter {
            opacity: 0.3;
            cursor: default;
            pointer-events: none;
        }

        .letter-count {
            font-size: 0.7em;
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: rgba(255, 102, 0, 0.8);
            border-radius: 50%;
            padding: 1px 5px;
            min-width: 18px;
            text-align: center;
        }

        /* Add loading indicator for filtered content */
        .project-list-container {
            position: relative;
            min-height: 200px;
        }

        .project-list-container.loading::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(3px);
            z-index: 10;
        }


        .no-items-container {
            text-align: center;
            padding: 3rem;
            border-radius: 1rem;
            background-color: black;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
            max-width: 500px;
            width: 100%;
            transition: all 0.3s ease;
            height: 25rem;;
        }
        .no-items-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        .icon-container {
            width: 120px;
            height: 120px;
            margin: 0 auto 2rem;
            border-radius: 50%;
            background-color: rgba(255, 76, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .main-icon {
            font-size: 3.5rem;
            color: #ff4c00;
        }
        .title {
            color: #343a40;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .description {
            color: #6c757d;
            margin-bottom: 2rem;
        }
        .action-button {
            background-color: #ff4c00;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .action-button:hover {
            background-color: #e64500;
            transform: translateY(-2px);
        }
        .flying-icon {
            position: absolute;
            font-size: 1.5rem;
            color: rgba(255, 76, 0, 0.2);
            animation: float 6s infinite ease-in-out;
        }
        .flying-icon:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .flying-icon:nth-child(2) {
            top: 30%;
            right: 15%;
            animation-delay: 1s;
        }
        .flying-icon:nth-child(3) {
            bottom: 25%;
            left: 20%;
            animation-delay: 2s;
        }
        .flying-icon:nth-child(4) {
            bottom: 15%;
            right: 10%;
            animation-delay: 3s;
        }
        @keyframes float {
            0%, 100% {
                transform: translateY(0) rotate(0deg);
            }
            50% {
                transform: translateY(-15px) rotate(5deg);
            }
        }
        .no-item {
            display:flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 3rem;
        }

        /* Search Bar Styles */
        .search-container {
            width: 100%;
            max-width: 1250px; /* limits the max width for desktop */
            margin: 0 auto 2rem auto;
            padding: 0 1rem; /* adds padding on small screens */
        }

        .search-wrapper {
            position: relative;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            width: 100%;
        }

        .form-control.background-dark {
            background-color: #2a2a2a;
            border: none;
            color: #ffffff;
            padding: 1rem 1rem 1rem 3rem;
            font-size: 1rem;
            height: auto;
            width: 100%; /* ✅ ensures input fills the wrapper */
            box-sizing: border-box;
        }

        @media (max-width: 768px) {
            .form-control.background-dark {
                font-size: 0.9rem;
                padding: 0.9rem 0.9rem 0.9rem 2.5rem;
            }

            .search-icon,
            .clear-icon {
                font-size: 1rem;
            }
        }
        .search-wrapper:hover, .search-wrapper:focus-within {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .form-control.background-dark {
            background-color: #2a2a2a;
            border: none;
            color: #ffffff;
            padding: 1rem 1rem 1rem 3rem;
            font-size: 1rem;
            height: auto;
        }
        .form-control.background-dark::placeholder {
            color: #adb5bd;
        }
        .form-control.background-dark:focus {
            box-shadow: none;
            border: none;
            outline: 2px solid #ff4c00;
        }
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #ff4c00;
            font-size: 1.2rem;
            z-index: 10;
        }
        .clear-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #adb5bd;
            font-size: 1rem;
            cursor: pointer;
            z-index: 10;
            transition: color 0.2s ease;
        }
        .clear-icon:hover {
            color: #ff4c00;
        }
 </style>


@endpush
@push('script')

<script>

        document.addEventListener('DOMContentLoaded', function() {
                    const searchInput = document.querySelector('.form-control.background-dark');
                    const clearButton = document.querySelector('.clear-icon');
                    
                    if (searchInput && clearButton) {
                        clearButton.addEventListener('click', function() {
                            searchInput.value = '';
                            searchInput.focus();
                            
                          
                            if (typeof window.Livewire !== 'undefined') {
                               
                                const event = new Event('input', { bubbles: true });
                                searchInput.dispatchEvent(event);
                            }
                        });
                    }
                });
        
        
        
                // function confirmDelete(formId) {
                //     const projectId = document.querySelector(`#${formId} button`).dataset.projectId;
                //     const projectName = document.querySelector(`#${formId} button`).dataset.projectName;
                //     if (confirm(`Delete this project (${projectName}) ?`)) {
                //         document.getElementById(formId).submit();
                //     }
                // }




                //     function unarchiveHandler(e) {
                //         const button = this;
                //         const projectId = button.getAttribute('data-id');
                //         var projectName = "";
            
                //         projectName = button.getAttribute('data-project-name');
                //         if (!confirm(`Unarchive this project (${projectName}) ?`)) {
                //             return;
                //         }
                //         const projectContainer = button.closest('.meta-project');

                //         button.disabled = true;
                //         const originalText = button.textContent.trim();
                //         // Store original width and height
                //         const originalWidth = button.offsetWidth;
                //         const originalHeight = button.offsetHeight;
                //         // Set fixed dimensions before changing content
                //         button.style.width = originalWidth + 'px';
                //         button.style.height = originalHeight + 'px';
                //         button.innerHTML = '<span class="btn-spinner"></span>';

                //         fetch(`/projects/${projectId}/unarchive`, {
                //             method: 'POST',
                //             headers: {
                //                 'Content-Type': 'application/json',
                //                 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                //             }
                //         })
                //         .then(response => response.json())
                //         .then(data => {
                //             if(data.success) {

                //                 const ctaRow = button.closest('.cta-row');
                //                 ctaRow.innerHTML = `
                //                     <button class="archive_project" data-id="${projectId}" data-project-name="${projectName}">
                //                         Archive Project
                //                     </button>
                //                 `;


                //                 ctaRow.querySelector('.archive_project').addEventListener('click', archiveHandler);


                //                 projectContainer.classList.remove('archived-project');
                //             } else {
                //                 alert('Failed to unarchive project');
                //                 button.disabled = false;
                //                 button.style.width = '';
                //                 button.style.height = '';
                //                 button.innerHTML = 'Unarchive Project';
                //             }
                //         })
                //         .catch(error => {
                //             console.error('Unarchive error:', error);
                //             button.disabled = false;
                //             button.style.width = '';
                //             button.style.height = '';
                //             button.innerHTML = 'Unarchive Project';
                //         });
                //     }


                //     function archiveHandler(e) {
                //         const button = this;
                //         const projectId = button.getAttribute('data-id');
                //         var projectName = "";
                //         projectName = button.getAttribute('data-project-name');
                //         if (!confirm(`Archive this project (${projectName}) ?`)) {
                //             return;
                //         }
                //         const projectContainer = button.closest('.meta-project');

                //         button.disabled = true;
                //         const originalText = button.textContent.trim();

                //         const originalWidth = button.offsetWidth;
                //         const originalHeight = button.offsetHeight;
                //         button.style.width = originalWidth + 'px';
                //         button.style.height = originalHeight + 'px';
                //         button.innerHTML = '<span class="btn-spinner"></span>';

                //         fetch(`/projects/${projectId}/archive`, {
                //             method: 'POST',
                //             headers: {
                //                 'Content-Type': 'application/json',
                //                 'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                //             }
                //         })
                //         .then(response => response.json())
                //         .then(data => {
                //             if(data.success) {

                //                 const ctaRow = button.closest('.cta-row');
                //                 ctaRow.innerHTML = `
                //                     <button class="unarchive_project" data-id="${projectId}" data-project-name="${projectName}">
                //                         Unarchive Project
                //                     </button>
                //                 `;


                //                 ctaRow.querySelector('.unarchive_project').addEventListener('click', unarchiveHandler);


                //                 projectContainer.classList.add('archived-project');
                //             } else {
                //                 alert('Failed to archive project');
                //                 button.disabled = false;
                //                 button.style.width = '';
                //                 button.style.height = '';
                //                 button.innerHTML = 'Archive Project';
                //             }
                //         })
                //         .catch(error => {
                //             console.error('Archive error:', error);
                //             button.disabled = false;
                //             button.style.width = '';
                //             button.style.height = '';
                //             button.innerHTML = 'Archive Project';
                //         });
                //     }

                //     // document.addEventListener('DOMContentLoaded', function (){


                //     document.querySelectorAll('.archive_project').forEach(button => {
                //         button.addEventListener('click', archiveHandler);
                //     });

                //     document.querySelectorAll('.unarchive_project').forEach(button => {
                //         button.addEventListener('click', unarchiveHandler);
                //     });
                // // });
</script>

@endpush