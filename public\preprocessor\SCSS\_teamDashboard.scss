.team-header {
	padding-bottom: 2.25rem;

	.meta {
		margin-block: 2rem;

		a {
			color: #fff;
			font: 500 16px/1 $inter;
		}

		.cta {
			font-size: 14px;
		}
	}

	.member-meta {
		.pic {
			margin-right: 1.25rem;
			position: relative;

			.img {
				border-radius: 50%;
				height: 7rem;
				overflow: hidden;
				width: 7rem;

				img {
					height: 100%;
					object-fit: cover;
					width: 100%;
				}
			}

			.change {
				background: $orange;
				border: 3px solid #111;
				border-radius: 50%;
				bottom: 0;
				height: 36px;
				position: absolute;
				right: 0;
				width: 36px;
			}
		}

		.meta {
			a {
				color: $orange;
				font: 500 14px $inter;
			}
		}
	}

	h1 {
		font: 700 32px $futura;

		i {
			font-family: $play;
		}
	}

	.cta {
		&:hover {
			.img {
				svg {
					path {
						stroke: #fff;
					}
				}
			}
		}
	}
}

.team-dashboard {
	padding-block: 4rem 2rem;

	.team-col {
		.col-head {
			h2 {
				color: #ff5811;
				font-size: 16px;
			}
		}

		.team-box {
			.img {
				border-radius: 50%;
				flex: 0 0 40px;
				height: 40px;
				margin-right: 1.25rem;
				overflow: hidden;
				width: 40px;

				img {
					height: 100%;
					object-fit: cover;
					width: 100%;
				}
			}

			.name {
				color: #fff;
				font: 700 16px $inter;
			}

			a {
				color: #ff5811;
				font: 16px $inter;
			}
		}
	}

	.edit-member {
		.pic,
		.upload-icon {
			border-radius: 50%;
			height: 96px;
			overflow: hidden;
			width: 96px;
		}

		.pic {
			img {
				height: 100%;
				object-fit: cover;
				width: 100%;
			}
		}

		.upload-icon {
			background: $orange;
		}

		.form-label {
			color: #fff;
			font: 16px $inter;
		}

		.form-control,
		.form-select {
			color: #fff;
			font: 16px $inter;

			&:focus {
				background: #fff;
				color: #111;
			}
		}

		.form-select {
			option {
				color: #000;
			}
		}

		.edit-icon {
			position: relative;

			.icon {
				color: $orange;
				cursor: pointer;
				position: absolute;
				right: 2rem;
				top: 50%;
				transform: translateY(-50%);
			}
		}
	}
}
