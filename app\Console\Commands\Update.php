<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\Project;
use App\Models\ProjectPhase;
use App\Models\ProjectPhaseCompletion;
use Carbon\Carbon;
use Throwable;

class Update extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */

     protected $signature = 'dates:update-from-phase-completion '
     . '{projectId? : The ID of a specific project to process} '
     . '{phaseId? : The ID of a specific phase to sync from. If omitted, syncs from all relevant phases.} '
     . '{--dry-run : Simulate the update without making actual changes to the database.} '
     . '{--force : Skip confirmation prompt}';
 

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates various website dates based on the completion date of associated project phases.';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $now = Carbon::now()->toDateTimeString(); // current datetime as string
        $this->info("** Starting update process at: {$now} **");

        $today = Carbon::today();
        $projectId = $this->argument('projectId');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        if (!$force && !$this->confirm('Proceed with adjusting phase target dates?')) {
            $this->info('Operation cancelled.');
            return self::SUCCESS;
        }

        $query = Project::with(['phases' => fn ($q) => $q->orderBy('phases.order')]);
        if ($projectId) {
            $query->where('id', $projectId);
        }

        $projects = $query->get();
        $totalProjectsAdjusted = 0;
        $totalPhasesAdjusted = 0;

        foreach ($projects as $project) {
            $this->info('');
            $this->info('');
            $this->info("Processing Project: {$project->name} (ID: {$project->id})");
            $phases = $project->phases;
            $shouldDateChange = false;
            $phaseAdjusted = false;

            foreach ($phases as $phase) {
                $pivot = $phase->pivot;
                $targetDate = $pivot->project_target ? Carbon::parse($pivot->project_target) : null;

                $targetPhase = $project->phaseCompletions()->where('phase_id', $phase->id)->first();
                $completedAt = $targetPhase?->completed_at;

                $targetDateStr = $targetDate ? $targetDate->toDateString() : 'null';
                $completedAtStr = $completedAt ? $completedAt->toDateString() : 'null';
                $dateChanged = $shouldDateChange ? 'yes' : 'no';

                $this->line("Checking phase '{$phase->name}': targetDate={$targetDateStr}, completedAt={$completedAtStr}, dateChanged={$dateChanged}");

                // If completed late
                if ($targetDate && $completedAt && $completedAt->gt($targetDate)) {
                    $shouldDateChange = true;
                    continue; // don't adjust this phase
                }

                // If delay was previously triggered, adjust current phase's target by +1 day
                if ($shouldDateChange) {
                    if ($targetDate) {
                        $newTarget = $targetDate->copy()->addDay();

                        if ($dryRun) {
                            $this->line("[DRY RUN] Phase '{$phase->name}' target date: {$targetDate->toDateString()} → {$newTarget->toDateString()}");
                        } 
                        else {
                            $project->phases()->updateExistingPivot($phase->id, [
                                'project_target' => $newTarget->toDateString(),
                            ]);
                            $this->line("Phase '{$phase->name}' target date: {$targetDate->toDateString()} → {$newTarget->toDateString()}");
                        }

                        $phaseAdjusted = true;
                        $totalPhasesAdjusted++;
                    } 
                    else {
                        $this->line("Phase '{$phase->name}' has no target date — skipping update.");
                    }
                }
            }

            if ($phaseAdjusted) {
                $this->info("Project '{$project->name}' target dates adjusted.");
                $totalProjectsAdjusted++;
            } 
            else {
                $this->info("Project '{$project->name}' is on track, no adjustments.");
            }
        }

        $this->info("\n");
        $this->info("** Summary **");
        $this->info("Projects adjusted: {$totalProjectsAdjusted}");
        $this->info("Phases adjusted: {$totalPhasesAdjusted}");

        if ($dryRun) {
            $this->warn("This was a dry run. No changes saved.");
        }
        $this->info('');
        $this->info('');
        $this->info('-----------------------------------------------------------------------------------------------------------------'); 
        $this->info(''); 
        $this->info('');        

        return self::SUCCESS;
    }
}
