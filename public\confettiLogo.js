const canvas = document.querySelector('#confettibox');

async function createfireworks() {
	canvas.confetti = canvas.confetti || (await confetti.create(canvas, { resize: true }));

	function randomInRange(min, max) {
		return Math.random() * (max - min) + min;
	}

	canvas.confetti({
		angle: randomInRange(55, 125),
		spread: randomInRange(50, 50),
		particleCount: randomInRange(50, 50),
		origin: { x: 0.5, y: 0.9 },
	});
}

window.addEventListener('scroll', function () {
	if (window.innerHeight + window.scrollY >= document.body.offsetHeight) {
		createfireworks();
	}
});