$colors: (
	'green': $green,
	'orange': $orange,
	'pink': $pink,
	'purple': $purple,
);

@mixin project-card($color) {
	.flag {
		border: 1px solid $color;

		.head {
			background: $color;
		}

		.text {
			.h3 {
				color: $color;
			}
		}
	}

	.icon-wrap {
		.line {
			background: $color;
		}

		.icon {
			border: 1px solid $color;
		}
	}
}

@mixin timeline-bar-after($color) {
	&:before {
		border-color: $color;
	}
}

@mixin timeline-color($color) {
	background: rgba($color, 0.2);

	.icon-bar {
		background: $color;

		.icon-circle {
			background: $color;

			&:after {
				background: $color;
			}
		}

		.icon {
			border: 1px solid $color;
		}
	}
}

@mixin timeline-current($color) {
	.logo,
	.wrap-meta {
		border-color: $color;
	}

	.title {
		background-color: $color;
	}
}

@mixin timeline-current-logo($color) {
	.logo {
		border-color: $color;
	}
}

@mixin task-overview-head($color) {
	.copy {
		h2 {
			color: $color;
		}

		.date-cta {
			background: $color;
		}
	}
}

@mixin message-color($color) {
	.icons {
		.icon:not(.pic) {
			background: $color;
		}
	}

	.head {
		h2 {
			color: $color;
		}
	}
}

@mixin project-column($color) {
	.head {
		.meta {
			h2 {
				color: $color;
			}
		}

		.icon-more {
			i {
				color: $color;
			}
		}
	}

	.project-list {
		.project-box {
			&__phase {
				h2 {
					color: $color;
				}
			}
			&__info {
				.time {
					color: $color;
				}
			}
		}
	}

	.foot {
		.icon {
			svg {
				path {
					stroke: $color;
				}
			}
		}
	}
}

@mixin plan-col-color($color) {
	border-color: $color;
	.icon-head {
		background: $color;
		.icon {
			border-color: $color;
		}
	}
	.icon-check {
		background: $color;
	}
}
