@extends('layout.app')
@section('title', 'Billing History')
@section('content')
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
               @if ( admin_superadmin_permissions() && !Auth::user()->has<PERSON><PERSON><PERSON><PERSON>('Client Member') )
                    <a class="d-inline-flex align-items-center text-decoration-none"
                        href="{{ route('statushub') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to StatusHub
                    </a>
                @else
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{route('client.dashboard')}}"><img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Client Portal</a>
                @endif
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project: <i>[{{$project->job_code}}] {{$project->name}}</i></h1>
                    <div class="page-links mt-3"><a class="" href="{{ route('track-project', ['id'=> $project->id]) }}">Track Project</a><a href="{{ route('message-centre', ['id'=> $project->id]) }}">Message Center</a><a href="{{ route('file-upload', ['id'=> $project->id]) }}">File Upload</a><a class="active" href="{{ route('billing-history', ['id'=> $project->id]) }}">Billing History</a></div>
                </div>
                <div class="logo">
                    <a href="#"><img src="{{asset('images/bear-tide-oysters-logo.png')}}" alt="" width="72" height="80" /></a>
                </div>
            </div>
        </div>
    </section>
    <section class="client-project bg-white">
        <div class="container-xxl">
            <div class="heading d-flex align-items-center justify-content-between">
                <h2 class="mb-0">Billing <i>History</i></h2>
            </div>
        </div>
    </section>
    <section class="current-timeline bg-white pb-5">
        <div class="container-xxl">
            <div class="meta-row d-flex mt-b orange">
                <div class="logo d-flex align-items-center justify-content-center rounded-circle"><img src="images/billing-project-icon.svg" alt="" /></div>
                <div class="wrap-meta payment ms-3 d-flex flex-grow-1 rounded-2">
                    <div class="title d-flex align-items-center justify-content-center">BILLING</div>
                    <div class="payment-type col-meta d-none d-lg-block align-self-center"><strong>Payment Type</strong></div>
                    <div class="due-date col-meta d-none d-lg-block align-self-center text-center"><strong>Due Date</strong></div>
                    <div class="amount col-meta d-none d-lg-block align-self-center text-center"><strong>Amount</strong></div>
                    <div class="received col-meta d-none d-lg-block align-self-center text-center"><strong>Received</strong></div>
                </div>
            </div>
            <div class="payment-list">
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <span class="check text-orange me-1"><i class="bi bi-check2-circle"></i></span><span class="text"><strong>Initial Deposit</strong></span>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        12/31/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        12/30/25
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <span class="check text-orange me-1"><i class="bi bi-check2-circle"></i></span><span class="text"><strong>Monthly Payment: January</strong></span>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        1/1/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        1/10/25
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <div class="d-flex">
                            <span class="check text-orange me-1"><i class="bi bi-check2-circle"></i></span>
                            <div class="text">
                                <span class="text"><strong>Monthly Payment: February</strong></span> <br />
                                <i class="blame text-orange">18 Days Past Due</i>
                            </div>
                        </div>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        2/1/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        -
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <strong>Monthly Payment: March</strong>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        3/1/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        -
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <strong>Monthly Payment: April</strong>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        4/1/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        -
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <strong>Monthly Payment: May</strong>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        5/1/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        -
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <strong>Monthly Payment: June</strong>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        6/1/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        -
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <strong>Project Launch Payment</strong>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        6/10/25
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $2500
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        -
                    </div>
                </div>
                <div class="payment-row d-flex flex-column flex-sm-row">
                    <div class="payment-type col-meta align-self-center text-center text-md-start">
                        <div class="col-meta px-0 d-md-none"><strong>Payment Type</strong></div>
                        <strong>Post-Launch: Monthly Maintenance Retainer</strong>
                    </div>
                    <div class="due-date col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Due Date</strong></div>
                        Ongoing
                    </div>
                    <div class="amount col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Amount</strong></div>
                        $100
                    </div>
                    <div class="received col-meta align-self-center text-center d-flex flex-column">
                        <div class="col-meta px-0 d-md-none"><strong>Received</strong></div>
                        -
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection