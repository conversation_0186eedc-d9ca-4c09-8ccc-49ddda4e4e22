@extends('layout.app')
@section('title', 'Add Task')
@section('content')


@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
@endif
@if (session('error'))
    <script>
        errorToast(@json(session('error')));
    </script>
@endif
@if ($errors->any())
    @foreach ($errors->all() as $error)
        <script>
            errorToast(@json($error));
        </script>
    @endforeach
@endif


    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-tasks') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Tasks List</a>

                @else

                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('show-all-tasks') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Tasks List</a>

                @endif
            </div>
            @if(request()->route()->named('add-new-task'))
            <div class="meta d-flex">
                <h1 class="heavy text-white mb-0">New <i>Task</i></h1>
            </div>
            @endif
        </div>
    </section>

    <section class="client-project project-dashboard">
        <div class="container-xxl">
            <form class="row new-task-form" method="POST" action="{{ route('save-new-task') }}" enctype="multipart/form-data">
            @csrf
            <div class="row projects-row">
                <div class="col-md-3 col-xl-2 project-column quick-links">
                    <div class="row">
                        <div class="col-12">
                            <h2 class="text-uppercase">SET STATUS</h2>
                            <hr class="mt-0 mb-4 border-white" />
                            <div class="statuses-list d-flex flex-column">
                                @foreach($statuses as $status)
                                    <div class="select-status active form-check text-white">
                                    <input type="radio" class="form-check-input " name="task_status" value="{{ $status->id }}" id="{{ $status->id }}" @checked(old('task_status') == $status->id)>
                                    <label class="form-check-label" for="{{ $status->id }}">{{ $status->name }}</label>
                                    </div>
                                @endforeach
                                {{-- @error('task_status') --}}
                                    <span class="invalid-feedback d-none">
                                        {{-- <strong>{{ $message }}</strong> --}}
                                        <strong></strong>
                                    </span>
                                {{-- @enderror --}}
                            </div>
                        </div>
                        <div class="col-12 mt-5">
                            <h2 class="text-uppercase">SET Project</h2>
                            <hr class="mt-0 mb-4 border-white" />
                            <div class="select-wrap">
                                <select class="form-select border-0 select-project pe-5" name="project_id" id="project_id">
                                    <option value="">SELECT Project</option>
                                    @foreach( $projects as $project )
                                        <option value="{{ $project->id }}"{{ $project->id == old('project_id') ? 'selected' : '' }}>{{ $project->name }} ({{ $project->job_code }})</option>
                                    @endforeach
                                </select>
                                <div class="select-icon"><img src="{{ set_user_image('images/down-arrow-orange.svg') }}" alt="" width="18" height="10"></div>
                            </div>
                            {{-- @error('project_id') --}}
                                <span class="invalid-feedback d-none">
                                    {{-- <strong>{{ $message }}</strong> --}}
                                    <strong></strong>
                                </span>
                            {{-- @enderror --}}
                        </div>
                    </div>
                    
                </div>
               
                <div class="col-md-9 col-xl-10 project-column task-overview">
                    <div class="col-head align-items-center d-flex justify-content-between">
                        <h2 class="text-uppercase">COMPOSE YOUR MESSAGE</h2>
                    </div>
                    <hr class="mt-0 mb-4 border-white" />
                    
                        <div class="col-12 mb-4">
                            <input class="input-text border-0" type="text" name="task_name" value="{{ old('task_name') }}" placeholder="Add a task subject..." />
                            {{-- @error('task_name') --}}
                                <span class="invalid-feedback d-none">
                                    {{-- <strong>{{ $message }}</strong> --}}
                                    <strong></strong>
                                </span>
                            {{-- @enderror --}}
                        </div>
                        <div class="col-12 comment-box">
                            <textarea class="textarea border-0" id="task_description" name="task_description"  placeholder="Type your message here...">{{ old('task_description') }}</textarea>
                            {{-- @error('task_description') --}}
                                <span class="invalid-feedback d-none">
                                    {{-- <strong>{{ $message }}</strong> --}}
                                    <strong></strong>
                                </span>
                            {{-- @enderror --}}
                        </div>
                        <div class="upload-files scrollbar d-flex align-items-center mt-2">
                            {{-- <div class="form-text fst-italic opacity-50 mx-1" id="selectedFileName"></div>
                            <span class="remove-selected" style="cursor: pointer;"> <i class="fa-solid fa-xmark fa-lg" style="color: #ffffff;"></i> </span> --}}
                        </div>
                        <div class="col-head col-12 mt-5">
                            <h2 class="text-uppercase">Assigned to</h2>
                            <hr class="mt-0 mb-4 border-white" />
                        </div>
                        <div class="col-12">
                            <div class="row assigned_users">
                            {{--
                                //IMP
                                @php
                                    $allusers = [];
                                    if ($user->role->name !== 'SuperAdmin') {
                                        $client = $user->client;
                                        $allusers = $client ? $client->users : collect();
                                    } 
                                    else {
                                        $allusers = \App\Models\User::all(); 
                                    }
                                @endphp
                            --}}
                            </div>
                            <span class="invalid-feedback d-none">
                                <strong></strong>
                            </span>
                        </div>
                        <div class="col-12 cta-row d-flex mt-5">
                            <div class="btn-col d-flex">
                                <div class="uploaded-items">
                                    <div class="upload-btn-wrapper">
                                        <button id="uploadTrigger" class="btn-upload text-uppercase text-white d-flex align-items-center"><i class="bi bi-upload me-2"></i> Upload a file</button>
                                        <input type="file" name="task_attachments[]" id="attachement" multiple/>
                                    </div>
                                    
                                </div>
                                <div class="add-time ms-4" position="relative" onclick="openDatePicker(event)" style="cursor:pointer">
                                    <div class="meta-label d-flex align-items-center"><i class="bi bi-clock me-2"></i><span class="label-text">ADD DUE DATE</span></div>
                                    <input class="due-date date-picker" id="dueDate" type="date" name="due-date" value="{{ old('due-date') }}" onchange="updateLabel(event)" />
                                </div>
                            </div>
                            <div class="btn-col ms-auto">
                                <button class="cta mt-0" type="submit">ADD TASK</button>
                            </div>
                        </div>
                        <input type="hidden" name="checked_users" value="{{ old('checked_users') }}">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content p-1">
                    <div class="modal-header border-0">
                        <div class="modal-heading"></div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body d-flex justify-content-center align-items-center">
                        <img id="" class="modal-image img-fluid" src="" alt="Full Size Image"
                            style='height:450px; width:auto'>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
  
@push('styles')
<style>
    .scrollbar::-webkit-scrollbar {
      height: 10px;
    }
    
    .scrollbar::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 10px;

    }
    
    .scrollbar::-webkit-scrollbar-thumb {
      background: #ff5811;
      border-radius: 10px;
    }

    .upload-files{
        overflow-x:auto;
    }
</style>
@endpush

@push('script')

<script src="{{ asset('js/tinymce/tinymce.min.js') }}" ></script>
    <script>
document.addEventListener("DOMContentLoaded", function () {
    tinymce.init({
        selector: "textarea#task_description",
        height: 300,
        plugins: [
            "lists",
            "advlist",
            "autolink",
            "link",
            "image",
            "charmap",
            "preview",
            "anchor",
            "searchreplace",
            "wordcount",
            "code",
            "fullscreen",
            "insertdatetime",
            "table",
            "paste"
        ],
        toolbar:
            "undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist | link image | print fullscreen",
        menubar: false,
        content_style: `
            body {
                font-family: Helvetica, Arial, sans-serif;
                font-size: 16px;
                background-color: #1e1e1e; /* dark inner editor background */
                color: #f5f5f5; /* light text */
            }
            #tinymce[data-mce-placeholder]:not(.mce-visual-caret)::before{ /* IMP => font color of tinymce text editor placeholder should be white */
                color:white;
            }
            p {
                margin: 0;
                padding: 0;
            }
        `,
        paste_data_images: false,
        automatic_uploads: false,
        paste_as_text: false,
        paste_auto_cleanup_on_paste: true,
        paste_remove_styles: false,
        paste_remove_spans: false,
        paste_retain_style_properties: "color font-size font-family background-color",
        link_assume_external_targets: true,
        link_target_list: false,
        setup: function (editor) {
            editor.on("init", function () {
                const container = editor.getContainer();
                container.style.width = "100%";
                container.style.border = "1px solid #282828";
                container.style.borderRadius = "4px";
                container.style.backgroundColor = "#282828"; // Your required greyish tone
                editor.on("focus", function () {
                    container.style.border = "1px solid #282828";
                    container.style.boxShadow =
                        "0 0 0 0.2rem rgba(120, 120, 120, 0.25)";
                });
                editor.on("blur", function () {
                    container.style.border = "1px solid #444";
                    container.style.boxShadow = "none";
                });
                editor.on('change undo redo input keypress', function () {
                    editor.save(); // This updates the original textarea
                });
                
            });
            editor.on('Paste', function (e) {
                const clipboardData = e.clipboardData || e.originalEvent.clipboardData; // Get clipboard data
                let imageContentDetected = false;
                let textPasted = false;
                let pastedText = '';

                // Check if there are any items on the clipboard
                if (clipboardData && clipboardData.items) {
                    for (let i = 0; i < clipboardData.items.length; i++) {
                        const item = clipboardData.items[i];

                        // Handle image data
                        // if (item.type.indexOf('image') !== -1) {
                        if (item.kind === 'file') {
                            const file = item.getAsFile();
                            if (file) {
                                console.log("file")
                                // Create a File object with a meaningful name for pasted images
                                // const pastedImageFile = new File([file], `pasted_image_${Date.now()}.${file.type.split('/')[1] || 'png'}`, { type: file.type });
                                const uniqueName = `pasted_${Date.now()}_${Math.floor(Math.random() * 1000)}_${file.name}`; //give different name to pasted image(which is already uploaded somehwere) so that while removing, the images of same name should not get removed. only image whose remove button is clicked is removed.
                                const pastedImageFile = new File([file], uniqueName, { type: file.type });

                                // Check against selectedFiles to prevent duplicates based on size+type (ignore name)
                                const existingIndex = selectedFiles.findIndex(sf =>
                                    sf.size === pastedImageFile.size && sf.type === pastedImageFile.type
                                );

                                if (existingIndex === -1) {
                                    selectedFiles.push(pastedImageFile); // Add uniquely named file
                                }
                                imageContentDetected = true;

                            }
                        } 
                        else if (item.type.indexOf('text/plain') !== -1) {
                            console.log("pasted text")
                            // Handle plain text data
                            item.getAsString(function (text) {
                                pastedText += text;
                                const imageUrlRegex = /\.(jpeg|jpg|png|gif|bmp|webp)$/i;
                                if (imageUrlRegex.test(text.trim())) {
                                console.log("Image URL detected in plain text:", text.trim());
                                
                                /* Last change in this */
                                    // Create a dummy File object for the URL, or handle as a separate URL list
                                    // For simplicity, we'll add it to selectedFiles as a File-like object
                                    // In a real app, you might want a separate array for URL attachments
                                    // const urlFile = new File([], text.trim().split('/').pop() || 'image.png', { type: 'image/png' }); // Dummy file
                                    // urlFile.src = text.trim(); // Store the URL for later use
                                    
                                    // const existingIndex = selectedFiles.findIndex(sf =>
                                    //     sf.src === urlFile.src // Check by URL for duplicates
                                    // );

                                    // if (existingIndex === -1) {
                                    //     selectedFiles.push(urlFile);
                                    // }

                                imageContentDetected = true;
                            } else {
                                textPasted = true; // Mark as text if not an image URL
                            }
                            });
                        }
                        else if (item.type.indexOf('text/html') !== -1) {
                            item.getAsString(function (html) {
                                const tempDiv = document.createElement('div');
                                tempDiv.innerHTML = html;
                                const imgTags = tempDiv.querySelectorAll('img');
                                imgTags.forEach(img => {
                                    const src = img.getAttribute('src');
                                    if (src) {
                                        console.log("Image URL detected in HTML:", src);

                                        // Check if src already exists in selectedFiles or in urlAttachments
                                        const alreadyExistsInFiles = selectedFiles.some(sf => sf.name === src || sf.src === src);
                                        if (alreadyExistsInFiles) {
                                            console.log("Image URL already added:", src);
                                            return; // skip duplicate
                                        }

                                        // Instead of creating a dummy File with empty blob (which is problematic),
                                        // store URLs in a separate array (e.g., urlAttachments)
                                        if (!window.urlAttachments) {
                                            window.urlAttachments = [];
                                        }

                                        if (!window.urlAttachments.includes(src)) {
                                            window.urlAttachments.push(src);
                                        }

                                        imageContentDetected = true;
                                    }
                                });
                            });
                        }

                    }
                }

                // If an image was pasted and handled by our logic, prevent TinyMCE's default behavior
                // for image insertion, but allow text to be inserted if present.
                if (imageContentDetected) {
                    e.preventDefault(); // This is critical for preventing image insertion

                    console.log("pasting image")
                    // Call your functions to update the attachment previews
                    updateAllPreviews();
                    updateFileInput();
                    // Prevent TinyMCE from inserting the image into the editor content
                }

                // If text was pasted, let TinyMCE handle its insertion.
                // If only image was pasted, and you preventDefault, TinyMCE won't add anything.
                // If both text and image were pasted, TinyMCE will handle the text.
                if (textPasted && !imageContentDetected) { // Only append text if no image was found, or if you want it duplicated
                    // In this setup, if text and image are both pasted, the text will go to editor
                    // and image to attachments. If only text, it goes to editor.
                    // If only image, it goes to attachments, nothing to editor.
                    // You might need to refine this based on exact UX.
                    // For direct text manipulation: editor.insertContent(pastedText);
                    // But usually, TinyMCE handles text paste automatically unless you preventDefault.
                    // If you preventDefault generally, you MUST handle text paste here explicitly.
                } 
                else if (textPasted && imageContentDetected) {
                    // If both image and text are pasted, TinyMCE will generally insert the text.
                    // The preventDefault for the image means the image won't be inserted as content.
                    // No extra action needed for text if you want it to be handled normally by TinyMCE.
                }
            });
            window.addEventListener("resize", function () {
                editor.execCommand("mceAutoResize");
            });
        },
        // autoresize_on_init: true,
        resize: true,
        responsive: true,
        mobile: {
            menubar: false,
            toolbar_mode: "sliding",
        },
        skin: "oxide-dark",
        content_css: false, // Disable default dark CSS to use our own
        ui_container: ".tinymce-custom-ui",
    });
});











    document.getElementById("uploadTrigger").addEventListener("click", function () {
        document.getElementById("attachement").click();
    });

    let selectedFiles = [];
    let dragAndDroppedFiles = []; // Consider if you truly need a separate array, or merge into selectedFiles

    // Store the original FormData instance, or create it dynamically as needed
    let formData = new FormData(); 

    document.getElementById("attachement").addEventListener("change", function () {
        var fileInput = this;
        var taskAttachmentsDiv = $('.upload-files');

        if (taskAttachmentsDiv.length === 0) {
            taskAttachmentsDiv = $("<div class='upload-files'></div>").addClass("scrollbar").css({
                "padding": "0px 10px",
                "height": "50px",
                "overflow-y": "hidden",
                "display": "flex",
                "align-items": "center"
            });
            // Append to the correct parent element, assuming it's within a div with class 'upload-files-wrapper'
            // You might need to adjust this selector based on your HTML structure
            $('.upload-files-wrapper').append(taskAttachmentsDiv); 
        }

        if (fileInput.files && fileInput.files.length > 0) {
            for (var i = 0; i < fileInput.files.length; i++) {
                var file = fileInput.files[i];
                // Check against selectedFiles to prevent duplicates from multiple selections
                var existingIndex = selectedFiles.findIndex(sf => 
                    sf.name === file.name && sf.size === file.size && sf.type === file.type
                );

                if (existingIndex === -1) {
                    selectedFiles.push(file);
                }
            }
            // Re-render all previews and update the input/formData after adding new files
            updateAllPreviews(); 
            updateFileInput();
        }
    });


    function displayAllPreviews(file, index) {
        var objectUrl = URL.createObjectURL(file);
        var previewContainer = $(
            "<div class='preview-container' style='margin-top: 10px;margin-bottom:10px; margin-right: 15px;position: relative;display:flex'></div>");
        var previewImg = $(
            "<span class='border border-secondary me-2 p-1 rounded text-white preview_img_span' data-img_url='" +
            objectUrl +
            "' style='cursor:pointer; max-width:150px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis' title='" +
            file.name + "'>" + file.name + "</span>");
        var closeButton = $("<button type='button' class='close-preview' data-index='" + index +
            "' style='position: absolute; top: -5px; right: -5px; border: none; color: red; font-size: 14px; border-radius: 50%; padding:0px 5px; cursor: pointer;'>&times;</button>"
        );

        previewContainer.append(previewImg);
        previewContainer.append(closeButton);
        $('.upload-files').append(previewContainer);

        // No need for load event on span, it's not an img element
        // previewImg.on('load', function() {
        //     URL.revokeObjectURL(objectUrl);
        // });
        
        // Revoke object URL when the preview container is removed
        previewContainer.on('remove', function() { // Use 'remove' event for jQuery .remove()
            URL.revokeObjectURL(objectUrl);
        });

        closeButton.on('click', function() {
            // var indexToRemove = parseInt($(this).data('index'));
            
            // // Remove from selectedFiles
            // selectedFiles.splice(indexToRemove, 1);

            var container = $(this).parent('.preview-container');
            var filename = container.find('.preview_img_span').text().trim();
            selectedFiles = selectedFiles.filter(f => f.name !== filename);

            
            // Remove the preview container from the DOM
            $(this).parent('.preview-container').remove(); 
            
            // Re-render all previews and update the input/formData
            updateAllPreviews();
            updateFileInput();
        });
    }

    // Function to clear all existing previews and re-render them based on selectedFiles
    function updateAllPreviews() {
        $('.upload-files').empty(); // Clear existing previews
        selectedFiles.forEach((file, index) => {
            displayAllPreviews(file, index);
        });

        if (selectedFiles.length === 0) {
            $('.upload-files').css({
                "height": "0px"
            });
        } else {
            $('.upload-files').css({
                "height": "auto" // Or whatever your desired height is when files are present
            });
        }
    }


    function updateFileInput() {
        var fileInput = $('input[name="task_attachments[]"]')[0];
        var dataTransfer = new DataTransfer();

        // Clear existing task_attachments from formData
        formData.delete("task_attachments[]");

        // Add all files from selectedFiles to dataTransfer and formData
        selectedFiles.forEach(function(file) {
            if (file instanceof File) {
                formData.append("task_attachments[]", file);
                dataTransfer.items.add(file);
            } else {
                console.warn('Skipped non-File object in selectedFiles:', file);
            }
        });

        // Update the actual file input's files property
        fileInput.files = dataTransfer.files;

        console.log("Current files in input:", fileInput.files.length);
        console.log("Current files in formData:", Array.from(formData.getAll("task_attachments[]")).length);
    }

    // Handle attachment preview and download
    $(document).on('click', '.preview_img_span', function(e) {
        e.preventDefault();
        const imgUrl = $(this).data('img_url');
        const fileName = $(this).text().trim();
        const fileExtension = fileName.split('.').pop().toLowerCase();

        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        const browsableExtensions = ['html', 'css', 'js', 'pdf'];

        if (imageExtensions.includes(fileExtension)) {
            $('.modal-image').attr('src', imgUrl);
            $('.modal-heading').text(fileName);
            $('#imageModal').modal('show');
        } 
        else if (browsableExtensions.includes(fileExtension)) {
            window.open(imgUrl, '_blank');
        } 
        else {
            const link = document.createElement('a');
            link.href = imgUrl;
            link.download = fileName; 
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            // Revoke URL *after* the download is initiated, only if it's a Blob URL
            // If imgUrl is from a server, do NOT revoke.
            // For files selected via input, imgUrl is typically a Blob URL.
            URL.revokeObjectURL(imgUrl); 
        }
    });

    // Initial call to updateFileInput to ensure it's in a clean state if no files are initially selected
    // $(document).ready(function() {
        updateFileInput(); 
    // });

    $('.new-task-form').on('submit', function(event){
        event.preventDefault();

        let formData = new FormData(this);
        for (let [key, value] of formData.entries()) { // 'formData' is your global variable
            if (value instanceof File) {
                console.log(`${key}: File Name: ${value.name}, Size: ${value.size}, Type: ${value.type}`);
            } else {
                console.log(`${key}:`, value);
            }
        }

        $.ajax({
            url: "{{ route('save-new-task') }}",
            method: "POST",
            data: formData,
            processData: false, 
            contentType: false,
            success: function(response){
                console.log("task created")
                // Show success toast immediately
                successToast(response.message || "Task created successfully!");

                // Clear TinyMCE content and file previews
                tinymce.get('task_description').setContent('');
                $('.new-task-form')[0].reset();

                // 5. Remove all validation error classes and messages
                $('.is-invalid').removeClass('is-invalid');
                $('.invalid-feedback').removeClass('d-block').addClass('d-none').find('strong').text('');

                selectedFiles = [];
                updateAllPreviews();

                // Redirect after 5 seconds
                setTimeout(function() {
                    window.location.href = "";
                }, 2500);
            },
            error: function(xhr, status, error) {
                // This function runs if the server returns an error status code (e.g., 400, 401, 403, 404, 422, 500)
                console.error('Error response:', xhr.responseJSON);
                console.error('Status:', status);
                console.error('Error text:', error);

                // 2. Check for validation errors (HTTP status 422)
                if (xhr.status === 422) {
                    var errors = xhr.responseJSON.errors; // Access the 'errors' object from your PHP response
                    $('.invalid-feedback strong').html("");

                    // Iterate over each field that has validation errors
                    $.each(errors, function(field, messages) {
                        errorToast(messages[0]);

                        // 'field' will be the name of the input (e.g., 'task_name', 'task_description')
                        // 'messages' will be an array of error strings for that field (e.g., ["The task name field is required."])

                        // Find the corresponding input element using its 'name' attribute
                       var inputElement = $('[name="' + field + '"]');

                         // Find the appropriate error span based on the field type
                        if (field === 'task_description') {
                            // Special handling for TinyMCE editor
                            // Find the TinyMCE editor instance first
                            var editorContainer = $('#' + field).closest('.tox-tinymce'); 
                            
                            // Add the 'is-invalid' class to the TinyMCE wrapper for visual error indication
                            editorContainer.addClass('tox-form__field--invalid'); 

                            // Find the parent container of the original textarea (e.g., .col-12 comment-box)
                            var parentContainer = inputElement.closest('.comment-box'); 
                            
                            // Then find the error span within that parent container
                            var errorSpan = parentContainer.find('.invalid-feedback');

                            if (errorSpan.length > 0) {
                                errorSpan.find('strong').text(messages[0]);
                                errorSpan.removeClass('d-none').addClass('d-block');
                            }
                            else {
                                console.warn('No predefined .invalid-feedback span found for TinyMCE field: "' + field + '". Appending one below the original textarea container.');
                                parentContainer.append('<span class="invalid-feedback d-block"><strong>' + messages[0] + '</strong></span>');
                            }
                        } 
                       else if (field === 'task_status') {
                            // Find all radio buttons for task_status and add 'is-invalid' to their immediate container
                            $('[name="' + field + '"]').closest('.form-check').addClass('is-invalid');

                            // The error message for the group is in the span after the last radio button's div.
                            var lastRadioWrapper = $('[name="' + field + '"]').last().closest('.select-status');
                            var errorSpan = lastRadioWrapper.next('.invalid-feedback'); 

                            if (errorSpan.length > 0) {
                                errorSpan.find('strong').text(messages[0]);
                                errorSpan.removeClass('d-none').addClass('d-block');
                            } else {
                                console.warn('No .invalid-feedback span found directly after the last radio button wrapper for "' + field + '". Appending one.');
                                lastRadioWrapper.after('<span class="invalid-feedback d-block"><strong>' + messages[0] + '</strong></span>');
                            }
                        }
                        else if (field.startsWith('task_assigned_to')) {
                            // Apply 'is-invalid' class to the .form-check div of each checkbox
                            $('[name^="task_assigned_to"]').closest('.form-check').addClass('is-invalid');

                            // Find the error span. It's now a sibling of the '.assigned_users' div.
                            var assignedUsersContainer = $('.assigned_users');
                            var errorSpan = assignedUsersContainer.next('.invalid-feedback'); 

                            if (errorSpan.length > 0) {
                                errorSpan.find('strong').text(messages[0]); // Display the first error message
                                errorSpan.removeClass('d-none').addClass('d-block');
                            } else {
                                console.warn('No .invalid-feedback span found directly after .assigned_users for "' + field + '". Appending one.');
                                // Fallback to append if it's somehow missing from the static HTML.
                                assignedUsersContainer.after('<span class="invalid-feedback d-block"><strong>' + messages[0] + '</strong></span>');
                            }
                        }
                        else {
                            // General handling for other input fields (text, select, etc.)
                            inputElement.addClass('is-invalid'); // Add red border to input
                            var errorSpan = inputElement.next('.invalid-feedback'); // Assumes span is direct next sibling

                            if (errorSpan.length > 0) {
                                errorSpan.find('strong').text(messages[0]);
                                errorSpan.removeClass('d-none').addClass('d-block');
                            } else {
                                console.warn('No predefined .invalid-feedback span found for field: "' + field + '". Appending one.');
                                inputElement.after('<span class="invalid-feedback d-block"><strong>' + messages[0] + '</strong></span>');
                            }
                        }
                    });
                } 
                else {
                    // 3. Handle other types of HTTP errors (e.g., 404 Not Found, 500 Internal Server Error)
                    var errorMessage = xhr.responseJSON?.message || 'An unexpected error occurred. Please try again.';
                    errorToast(errorMessage);
                }
            }
        })
    })

    // document.querySelector(".remove-selected").addEventListener("click", function () {
    //     document.getElementById("attachement").value = "";
    //     document.getElementById("selectedFileName").innerText = "";
    //     document.querySelector(".upload-files").classList.add("d-none");
    // });


    /*
        //IMP
        input is selected so it displays date on span. but when its value is selected and form is submitted then after form submission and page reload, we need to display 
        selected date on span but updatedLabel will only run when input is changed so we trigger change event when it has value(date) but date is not rendered as html on span
    */
    if($('input[name="due-date"]').val()!='' && $('.label-text').html("ADD DUE DATE")){
        $('input[name="due-date"]').trigger('change');
    }





    //IMP CODE BELOW(retaining selected project after form submission and page reload and also retaining the selected users/clients assigned as per selected project)
       
    // Keep track of checked user IDs
    let checkedUsers = [];

    // Function to extract all checked user IDs
    function getCheckedUserIds() {
        console.log("get checked users")
        checkedUsers = [];

        // Loop through all checked checkboxes
        $('input[name="task_assigned_to[]"]:checked').each(function() {
            checkedUsers.push(parseInt($(this).val())); // Assuming IDs are numbers
        });

        // Set or clear the hidden input value
        if (checkedUsers.length > 0) {
            $('input[name="checked_users"]').val(checkedUsers);
        } 
        else {
            $('input[name="checked_users"]').val('');
        }
    }

   
    $('.assigned_users').html("<div class='col-md-12 col-xl-12 mb-3 text-danger'>Please select project from dropdown to view/add the assigned users..</div>").css({"color":"white"});    

    $('select.select-project').on('change focus', function(event){
        event.preventDefault();

        console.log("dropdown value: " + $(this).val());
        var select_client_dropdown_value = $(this).val();
        if(select_client_dropdown_value !== ''){
            $.ajax({
                url: "{{ route('add-new-task') }}",
                method: "get",
                data: {"fetch_project_users":"1", "project_id": select_client_dropdown_value},
                success: function(response){
                    var html='';
                    var html='';
                    if(response!=''){
                        response.forEach(function(userItem) {                          
                            // Check if userItem.id is in oldValue array
                            checkedUsers = $('input[name="checked_users"]').val();
                            let isChecked = checkedUsers.includes(userItem.id) ? 'checked' : '';
                            console.log(checkedUsers)

                            // Create the HTML dynamically for each user
                            html += `
                                <div class="col-md-3 col-xl-2 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" name="task_assigned_to[]" type="checkbox" value="${userItem.id}" id="user-${userItem.id}" ${isChecked} />
                                        <label class="form-check-label" for="user-${userItem.id}">${userItem.name}</label>
                                    </div>
                                </div>
                            `;
                        });
                        // Append the HTML to the container
                        $('.assigned_users').html(html);
                    }
                    else{
                        $('.assigned_users').html("<div class='col-md-12 col-xl-12 mb-3 text-danger'>No users assigned in this project!</div>");
                    }
                },

            })
        }
        else{
            $('.assigned_users').html("<div class='col-md-12 col-xl-12 mb-3 text-danger'>Please select project from dropdown to view/add the assigned users..</div>");
        }

    })

    $('select.select-project').each(function() {
        var selectedValue = $(this).val();
        console.log('On page load - Select value:', selectedValue);
        if (selectedValue) {
            console.log('On page load - Triggering change for value:', selectedValue);
            $(this).trigger('change');
        }
    });

    $(document).on('change', 'input[name="task_assigned_to[]"]', function(){
        getCheckedUserIds();
    })

    </script>
@endpush