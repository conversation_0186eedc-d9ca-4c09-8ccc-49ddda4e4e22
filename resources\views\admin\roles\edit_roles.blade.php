@extends('admin.layouts.app')
@section('title', 'Edit Role')
@section('content')

@if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
    @endif
@if ($errors->any())
    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert" id="backend-error">
        {{ $errors->first() }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif
<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('roles') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('roles') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal</a>
            @endif
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Assign <i>Permissions</i></h1>
        </div>
    </div>
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl projects-row">
        <div class="project-column task-overview">
            <div class="col-head align-items-center d-flex justify-content-between">
                <h2 class="text-uppercase">Hey {{ $role->name }}</h2>
            </div>
            <hr class="mt-0 mb-4 border-white" />
            <form class="row form-wrap" action="{{ route('update-role', ['role_id'=>request()->route('role_id')]) }}" method="post" enctype="multipart/form-data">
                @csrf
                @method('put')
                
                <div class="col-md-12 col-xl-9 mb-4">
                    <input name="role_name" class="form-control border-0" type="text" id="clientName" value="{{ $role->name }}" placeholder="Add a Role name..." />
                </div>
                <div class="col-3 cta-row d-flex align-items-start justify-content-center">
                    <button type="button" class="cta dark mx-4 mt-0" id="delete-role" data-user-id="3">DELETE ROLE</button>
                </div>
               
                <div class="col-head col-12 mt-5">
                    <h2 class="text-uppercase">Assign Permissions</h2>
                    <hr class="mt-0 mb-4 border-white" />
                </div>
                <div class="col-12 mt-4">
                    <div class="row">
                        <div class="col-md-4">
                            <h2 class="text-white">Add Permission</h2>
                        </div>
                        <div class="col-md-8">
                            <input type="hidden" name="role_permissions" class="d-none" value="@json($role->permissions->pluck('id'))">
                            <div class="brand-wrap">
                            @if(isset($permissions) && count($permissions) > 0)
                                @foreach($permissions as $permission)
                                    @php
                                        $isAdded = $role->permissions->contains($permission->id);
                                    @endphp
                                    <div class="brand-select d-flex align-items-center justify-content-between {{ $isAdded ? 'added' : '' }}">
                                        <div class="name text-uppercase">{{ $permission->name }}</div>
                                        <button class="cta text-uppercase mt-0 text-white {{ $isAdded ? 'added' : '' }}" data-permission-id="{{ $permission->id }}" type="button">
                                            {{ $isAdded ? 'Added' : 'Add' }}
                                        </button>
                                    </div>
                                @endforeach
                            @endif

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-5 text-center">
                    <button class="cta text-uppercase mt-0" type="submit">Update Role & Permission</button>
                </div>
            </form>
            <form id="delete-role-form" action="{{ route('delete-role', ['role_id' => $role->id]) }}" method="POST" style="display: none;">
                    @csrf
                    @method('DELETE') {{-- Fix: Use DELETE not PUT --}}
                </form>
        </div>
    </div>
</section>

<script>
   
    document.addEventListener("DOMContentLoaded", function () {
        const clientsInput = document.querySelector('[name="role_permissions"]');
        let selectedClients = JSON.parse(clientsInput.value || '[]').map(id => parseInt(id)); // 🧠 Normalize to integers

        document.querySelectorAll('.brand-select').forEach(function (item) {
            const button = item.querySelector('button');
            const permissionId = parseInt(button.getAttribute('data-permission-id')); // 🧠 Parse ID to integer

            // Pre-fill Added button text
            if (item.classList.contains('added')) {
                button.innerText = 'Added';
                if (!selectedClients.includes(permissionId)) {
                    selectedClients.push(permissionId);
                }
            }

            button.addEventListener('click', function () {
                item.classList.toggle('added');
                button.classList.toggle('added');

                if (item.classList.contains('added')) {
                    if (!selectedClients.includes(permissionId)) {
                        selectedClients.push(permissionId);
                        button.innerText = 'Added';
                    }
                } else {
                    selectedClients = selectedClients.filter(id => id !== permissionId);
                    button.innerText = 'Add';
                }

                // Update hidden input value
                clientsInput.value = JSON.stringify(selectedClients);
            });
        });

        clientsInput.value = JSON.stringify(selectedClients); // Clean value set once

        // Delete role button
        const deleteRoleButton = document.getElementById('delete-role');
        const deleteForm = document.getElementById('delete-role-form');

        if (deleteRoleButton && deleteForm) {
            deleteRoleButton.addEventListener('click', function (event) {
                event.preventDefault();

                if (confirm("Are you sure you want to delete this role?")) {
                    deleteForm.submit();
                }
            });
        }
    });

</script>

@endsection