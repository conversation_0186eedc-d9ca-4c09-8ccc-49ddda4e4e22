<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClientUserLink extends Model
{
    protected $table = 'client_user_links';

    protected $fillable = [
        'client_id',
        'user_id',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
