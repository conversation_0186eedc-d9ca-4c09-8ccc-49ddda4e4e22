@extends('layout.app')
@section('title', 'Add Project')
@section('content')
    <section>
        <div class="main-div">
            <div class="content-div">
                <div class="content-heading d-flex">

                    <h4>
                        @if(request()->route()->named('add-project'))
                            Add Project
                        @endif
                    </h4>
                </div>

                <div class="content-body">
                    @if(request()->route()->named('add-project'))
                        <div class="add-project-div">
                            <form method="post" action="{{ route('save-project') }}" class="border border-secondary w-50 mx-auto p-3" name="add-project-form">
                                @csrf


                                <div class="d-flex">
                                    <div class="add-project-div-label">
                                        Project Name:
                                    </div>
                                    <div class="add-project-div-input d-flex flex-column">
                                        <input type="text" name="project_name" value="">
                                        @error("project_name")
                                            <div class="text-danger">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>
                                <br>
                                <div class="d-flex">
                                    <div class="add-project-div-label">
                                        Assign to Company:
                                    </div>
                                    <div class="add-project-div-input d-flex flex-column">
                                        <select class="add-project-div-select companies-list-dropdown" name="company_id[]" id="0" multiple>
                                            <option value=''>Select Company</option>
                                        </select>
                                        @error("company_id")
                                            <div class="text-danger">
                                                {{ $message }}
                                            </div>
                                        @enderror
                                    </div>
                                </div>
                                <br>
                                
                                <br>
                                <div class="d-flex justify-content-center">
                                    <button>Submit</button>
                                </div>
                            </form>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection


@push('styles')
    <style>
        body{
            color:black;
            margin:0px;
        }
        .navbar{
            height:50px;
            padding:10px 50px;
            margin:0px;
            align-items:center;
            background-color:lightgrey;
        }
        .flt-lft{
            float:left;
            margin-right:5px;
            align-items:center;
        }
        .flt-rgt{
            float:right;
        }
        .flt-rgt div{
            padding:0 5px;
            margin:0px 3px;
        }
        .flt-rgt a:hover{
            text-decoration:none;
        }
        .active-page{
            font-weight:bold;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
        }
        .dropdown-content a {
            float: none;
            color: black;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            text-align: left;
        }
        .dropdown-content a:hover {
            background-color: #ddd;
        }
        .dropdown:hover .dropdown-content {
            display: block;
        }
        .loggedInMsg{
            font-size: 13px;
            padding: 2px 0px;
            margin-bottom:3px;
            text-align: center;
            color: white;
            background-color: #1a8234;
        }

        .main-div{
                display:flex;
                height:100%;
                background-color:white;
            }
        .section-div{
            width:5%;
            box-sizing: border-box;
            border-right:0.5px solid grey;
            background-color:lightgrey;
            overflow:scroll;

            left: 0;
            z-index: 0;
            height: calc(100vh - 50px);
        }
        .section-div a{
            text-decoration:none;
        }
        .section-div a:hover{
            background-color:lightblue;
        }
        .section-div div{
            color:black;
            padding:10px;
        }
        .navbar-menu-button-div{
            display:flex;
        }
        .navbar-menu-button{
            margin:auto;
        }
        .sidebar-link-icon{
            text-align:center;
            font-size:20px;
            padding:10px 0px;
            width:100%;
        }
        .sidebar-link-text{
            display:none;
        }
        .section-div, .content-div, .sidebar-link, .sidebar-link-text, .sidebar-link-icon {
            transition: all 0s ease;
        }
        .content-div{
            width:100%;
            padding:10px;
            overflow:scroll;
        }
        /* .content-heading{
            didplay:flex;
        } */
        .back-btn{
            background-color: black;
            color: white;
            height: 27px;
            width: 25px;
            border-radius: 50%;
            font-size: 15px;
            padding: 5px;
            margin-right: 10px;
        }
        .content-body{
            padding:0px 50px;
        }
        .add-project-div{
            width:100%;
            display: block;
        }
        .add-project-div-label{
            text-align:left;
            width:60%;
        }
        .add-project-div-input{
            width:100%;
        }
        .add-project-div-select{
            width: 225px;
        }
        .no_projects_found_error{
            text-align: center;
            margin-top:10px;
            padding: 10px;
            background-color: pink;
            color: red;
            font-weight: bold;
        }
    </style>
@endpush

@push('script')
    <script>
        $(document).ready(function(){
            
            load_companies_of_selected_user_in_dropdown(0);

            function load_companies_of_selected_user_in_dropdown(dropdown_id=0){

                $.ajax({
                    url: "{{ route('companies-list-of-user-ajax') }}",
                    method: "get",
                    success: function(response){
                        console.log('.companies-list-dropdown#'+dropdown_id)
                        $('.companies-list-dropdown').html("<option value=''>Select Company</option>");//to destroy cached response
                        if(response!=""){
                            $.each(response, function(index, company) {
                                company.id = parseInt(company.id, 10); // Parse as integer
                                company.user_id = parseInt(company.user_id, 10); // Parse as integer
                              $('.companies-list-dropdown#'+dropdown_id).append(`<option value="${company.id}" data-user_id="${company.user_id}">${company.name}</option>`);
                            });
                        }
                        else
                        {
                        }
                    }
                })
            }
        });
    </script>
@endpush