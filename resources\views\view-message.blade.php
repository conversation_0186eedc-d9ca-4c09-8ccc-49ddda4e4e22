@extends('layout.app')
@section('title', 'Message Center')
@section('content')
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
               @if ( admin_superadmin_permissions() && !Auth::user()->has<PERSON><PERSON><PERSON><PERSON>('Client Member') )
                    <a class="d-inline-flex align-items-center text-decoration-none"
                        href="{{ route('statushub') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to StatusHub
                    </a>
                @else
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{route('client.dashboard')}}"><img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Client Portal</a>
                @endif
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project: <i>[{{$project->job_code}}] {{$project->name}}</i></h1>
                    <div class="page-links mt-3"><a href="{{ route('track-project', ['id'=> $project->id]) }}">Track Project</a><a class="active" href="{{ route('message-centre', ['id'=> $project->id]) }}">Message Center</a><a href="{{ route('file-upload', ['id'=> $project->id]) }}">File Upload</a><a href="{{ route('billing-history', ['id'=> $project->id]) }}">Billing History</a></div>
                </div>
                <div class="logo">
                    <a href="#"><img src="{{asset('images/bear-tide-oysters-logo.png')}}" alt="" width="72" height="80" /></a>
                </div>
            </div>
        </div>
    </section>
    <section class="client-project bg-white">
        <div class="container-xxl">
            <div class="go-back">
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('message-centre', ['id'=> $project->id]) }}"><img class="me-2" src="{{asset('images/back-arrow-icon.svg')}}" alt="" /> Back to Message Center</a>
            </div>
            <div class="status d-flex justify-content-between">
                <div class="text">
                    <h2 class="text-uppercase mb-2">Thread</h2>
                    <h3 class="mb-0">{{ $projectMessage->title }}</h3>
                </div>
            </div>
        </div>
    </section>
    <section class="bg-white pb-5">
        <form class="container-xxl pb-5" action="">
            @php
                $created_by = \App\Models\User::find($projectMessage->created_by_user_id);
                $created_by_user_name = $created_by?->name;
                $created_by_user_profile_image = $created_by?->profile_image;

                $auth_user = auth()->user();
                $auth_user_profile_image = $auth_user?->profile_image;
            @endphp
            <div class="message-box d-flex">
                <div class="pic"><img src="{{ set_user_image($created_by_user_profile_image) }}" alt="User Profile Image" /></div>
                <div class="message-wrap w-100 d-flex">
                    <div class="message w-100">
                        
                        <div class="copy">
                            <div class="meta d-flex mb-3"><a class="user me-4" href="#">{{$created_by_user_name}}</a><span class="date">{{ $projectMessage->created_at->format('d/m/Y') }}</span></div>
                            <p>{{ strip_tags($projectMessage->description) }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="message-box reply d-flex">
                <div class="pic"><img src="{{ set_user_image($created_by_user_profile_image) }}" alt="User Profile Image" /></div>
                <div class="message-wrap d-flex flex-grow-1">
                    <div class="message flex-fill d-flex align-items-center">
                        <div class="copy w-100">
                            <textarea class="message-textarea w-100" placeholder="Add reply..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="message-box upload d-flex flex-column mt-3">
                <div class="upload-row">
                    <p>
                        Your comment will be sent to Drew McKenna, Sally McCarty and Client <a href="#">(<span class="text-decoration-underline">change</span>)</a>
                    </p>
                </div>
                <div class="cta-row d-flex justify-content-between mt-4">
                    <div class="upload-btn-wrapper">
                        <button class="btn-upload text-uppercase d-flex align-items-center"><i class="bi bi-upload me-2"></i> Upload a file</button>
                        <input type="file" name="myfile" />
                    </div>
                    <button class="cta text-uppercase mt-0" type="submit">ADD COMMENT</button>
                </div>
            </div>
        </form>
    </section>
@endsection
@push('styles')
    <style>
        textarea.message-textarea{
            resize:none;
            overflow-y:auto;
            height:50px;
        }
    </style>
@endpush