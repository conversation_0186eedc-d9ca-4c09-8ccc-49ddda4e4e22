<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    //
    protected $table = 'categories';
    protected $fillable = [
        'name',
        'order',
        'description'
    ];
    protected $hidden = [
        'created_at',
        'updated_at'
    ];
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function companies()
    {
        return $this->belongsToMany(Company::class, 'company_category', 'category_id', 'company_id');
    }

    public function tasks()
    {
        return $this->hasMany(Task::class, 'category_id');
    }

  

    public function phase() {
        return $this->belongsTo(Phase::class, 'phase_id');
    }


}
