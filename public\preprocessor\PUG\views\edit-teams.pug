- let pageName = 'Edit Teams';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug

mixin teamBox()
    .team-box.mb-3.d-flex.align-items-center
        a.d-flex.align-items-center.text-decoration-none.me-4(href="team-member-profile.html")
            .img
                img(src="images/drew-pic.jpg", alt="")
            .name <PERSON> McKenna
        a.cta-edit.text-decoration-none.ms-auto(href="edit-team-member.html") Edit

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('Edit <i>Teams</i>', 'Back to Team Portal', 'NEW TEAM MEMBER')

        section.team-dashboard.pb-5

            .container-xxl
                .row.gx-lg-5
                    each head in ['Design Team', 'Dev Team', 'Admin']
                        .col-md-4.team-col
                            .col-head.align-items-center.d-flex.justify-content-between
                                h2.text-uppercase #{head}
                            hr.mt-0.mb-4.border-white
                            .team-list
                                - for (let j = 0; j < 5; j++)
                                    +teamBox()

        include ../partials/footer.pug
        +footer(true)