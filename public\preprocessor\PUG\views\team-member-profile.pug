- let pageName = 'Team Member Profile';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/fieldForms.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('Editing <i>Drew M.</i>', 'Back to Team Portal', 'See Projects')

        section.team-dashboard.pt-0.pb-5
            .container-xxl
                hr.mt-0.mb-4.border-white
                form.edit-member.row(action="")
                    .col-md-auto
                        .pic
                            img(src="images/drew-pic.jpg", alt="")

                    .col-md
                        .row
                            .col-md-6.mb-4
                                +inputFieldLabel('Email', 'email', '', '<EMAIL>', true)
                            .col-md-6.mb-4
                                +inputFieldLabel('Password', 'password', '',  '.........', true)
                            .col-md-6.mb-4
                                +selectFieldLabel('Set Team', ['Dev Team', 'Design Team',  'Admin'])
                            .col-md-6.mb-4
                                +selectFieldLabel('Set Access Level', ['Team Member', 'Lorem',  'Ipsum'])
                            .col-12.cta-row.d-flex
                                button.cta.dark ARCHIVE TEAM MEMBER
                                button.cta.dark.mx-4 DELETE TEAM MEMBER
                                button.cta.orange.ms-auto SAVE CHANGES

        include ../partials/footer.pug
        +footer(true)