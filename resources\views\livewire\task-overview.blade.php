<div>
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">

                @if (admin_superadmin_permissions())
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal
                    </a>
                @else
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('dashboard') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal
                    </a>
                @endif

            </div>

            @if (admin_superadmin_permissions())
                <div class="meta d-flex">
                    <h1 class="heavy text-white mb-0">Tasks <i>Overview</i></h1>
                    <div class="add-task ms-auto d-flex">
                        <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0"
                            href="{{ route('add-new-task') }}">
                            NEW TASK
                            <span class="img ms-2">
                                <svg width="21" height="21" viewBox="0 0 21 21" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                                    <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5">
                                    </path>
                                </svg>
                            </span>
                        </a>
                    </div>
                </div>
            @endif
        </div>
    </section>
    <div class="search-container">
        <div class="search-wrapper">
            <i class="bi bi-search search-icon"></i>
            <input type="text" class="form-control search-input background-dark" placeholder="Search by project name or job code" wire:model.live.debounce.500ms="search" />
            <i class="bi bi-x-circle clear-icon"></i>
        </div>
    </div>
    {{-- @if(count($projects) > 0) --}}
    <section class="client-project pt-0">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="projects-list-table">
                <div class="head d-flex align-items-center justify-content-between">
                    
                    {{-- Year Filter --}}
                    <div class="years d-flex align-items-center gap-2">
                        <label for="yearSelect" class="mb-0 text-white">Select year:</label>
                        <select id="yearSelect" onchange="window.location.href=this.value" class="form-select" style="width: auto;">
                            <option value="{{ route('admin-tasks', ['year' => null]) }}"
                                {{ ($year === null || !$year) ? 'selected' : '' }}>
                                All
                            </option>
                            @foreach ($years as $y)
                                <option value="{{ route('admin-tasks', ['year' => $y]) }}"
                                    {{ ($year == $y) ? 'selected' : '' }}>
                                    {{ $y }}
                                </option>
                            @endforeach
                        </select>
                    </div>                   
                
                    {{-- Alphabet Filter --}}
                    <div class="alphabet d-flex flex-wrap">
                        @foreach (range('A', 'Z') as $letter)
                            @php
                                $letterCount = $letterCounts[$letter] ?? 0;
                                $hasClients = $letterCount > 0;
                            @endphp
                    
                            <a wire:click="{{ $hasClients ? '$set(\'alphabet\', \''.$letter.'\')' : 'null' }}"
                               wire:loading.class="loading-blur"
                               href="javascript:void(0)"
                               class="alphabet-letter me-2 mb-2 px-2 py-1 {{ $alphabet === $letter ? 'alphabet-active' : '' }} {{ !$hasClients ? 'disabled-letter' : '' }}">
                                {{ $letter }}
                                @if($letterCount > 0)
                                    <span class="letter-count">{{ $letterCount }}</span>
                                @endif
                            </a>
                        @endforeach
                    
                        {{-- All/Reset --}}
                        <a wire:click="$set('alphabet', null)"
                           wire:loading.class="loading-blur"
                           href="javascript:void(0)"
                           class="alphabet-letter me-2 mb-2 px-2 py-1 {{ is_null($alphabet) ? 'alphabet-active' : '' }}">
                            All
                        </a>
                    </div>
                    

                </div>
            </div>
        </div>
    </section>
{{-- @endif --}}

    <section class="client-project project-dashboard">
        <div class="container-xxl">
            <div class="row projects-row">
                <!-- Status Filter Sidebar -->
                <div class="col-md-3 col-xl-2 project-column quick-links">
                    <h2 class="text-uppercase">STATUSES</h2>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="statuses-list d-flex flex-column">
                        <a class="{{ $selectedStatus === 'all' ? 'active' : '' }}"
                            wire:click.prevent="filterByStatus('all')" href="#">
                            All ({{ $filteredTaskCount }})
                        </a>
                        @foreach ($statuses as $status)
                            <a class="{{ $selectedStatus == $status->id ? 'active' : '' }}"
                                wire:click.prevent="filterByStatus({{ $status->id }})" href="#">
                                {{ $status->name }} ({{ $statusCounts[$status->id] ?? 0 }})
                            </a>
                        @endforeach
                        {{-- <a class="{{ $selectedStatus === 'past_finished' ? 'active' : '' }}"
                            wire:click.prevent="filterByStatus('past_finished')" href="#">
                                Past Finished ({{ $statusCounts['past_finished'] ?? 0 }})
                        </a> --}}
                        {{-- <a class="{{ $selectedStatus === 'archived' ? 'active' : '' }}"
                            wire:click.prevent="filterByStatus('archived')" href="#">
                                Archived ({{ $statusCounts['archived'] ?? 0 }})
                        </a> --}}
                        <br>
                        <label class="text-white mt-3">See monthly tasks:</label>
                        {{-- Month Filter Block --}}
                        <div class="mt-2">
                            {{-- Header / Toggle --}}
                            <div class="d-flex justify-content-between align-items-center text-secondary" style="cursor: pointer;" wire:click="toggleBillingMonthDropdown">
                                <span class="">
                                    {{ $selectedMonth && isset($billingMonths[$selectedMonth]) 
                                        ? $billingMonths[$selectedMonth]['label'] . ' (' . $billingMonths[$selectedMonth]['count'] . ')' 
                                        : 'Select Month' }}
                                </span>
                                <i class="bi bi-chevron-down"></i> {{-- Bootstrap Icon (optional) --}}
                            </div>

                            {{-- Month List --}}
                            @if ($showBillingMonths)
                                <div class="border rounded mt-2 p-2" style="max-height: 250px; overflow-y: auto;">
                                    <div class="py-1">
                                        <a class="text-secondary d-block text-decoration-none{{ $selectedMonth === null ? 'fw-bold text-light' : '' }}" wire:click="selectBillingMonth(null)">
                                            Select Month
                                        </a>
                                    </div>
                                    @foreach ($billingMonths as $value => $info)
                                        <div class="py-1">
                                            <a 
                                                href="#"
                                                class="text-secondary d-block text-decoration-none {{ $selectedMonth === $value ? 'fw-bold text-light' : '' }}"
                                                wire:click.prevent="filterByBillingMonth('{{ $value }}')"
                                            >
                                                {{ $info['label'] }} ({{ $info['count'] }})
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>                                                                                 
                    </div>                    
                </div>



                <!-- Task List -->
                <div wire:loading.class="blur-sm" wire:target="filterByStatus"
                    class="col-md-9 col-xl-10 project-column task-overview">

                    <div class="col-head align-items-center d-flex justify-content-between">
                        <h2 class="text-uppercase">TASKS OVERVIEW</h2>
                        <div class="sort d-flex align-items-center">
                            <div class="sort-by position-relative d-flex align-items-center">
                                <h3 class="mb-0">Sort By: {{ $sortField === 'job_code' ? 'Job' : 'Date' }}</h3>

                                <span class="ms-2 sort-arrow" style="cursor: pointer;" x-data="{ open: false }"
                                    @click="open = !open">
                                    <img src="{{ asset('images/down-arrow-orange.svg') }}" alt="Sort" />

                                    {{-- Dropdown menu --}}
                                    <div x-show="open" @click.away="open = false"
                                        class="dropdown-menu show position-absolute mt-2"
                                        style="min-width: 100px; background: white; border: 1px solid #ddd; z-index: 1000;">
                                        <a href="#" wire:click.prevent="sortBy('created_at')"
                                            class="dropdown-item {{ $sortField === 'created_at' ? 'active' : '' }}">
                                            Date
                                        </a>
                                        <a href="#" wire:click.prevent="sortBy('job_code')"
                                            class="dropdown-item {{ $sortField === 'job_code' ? 'active' : '' }}">
                                            Job
                                        </a>
                                    </div>
                                </span>
                            </div>

                            <div class="more-icon ms-3"><img src="{{ asset('images/three-dots-more.svg') }}"
                                    alt="" /></div>
                        </div>
                    </div>
                    <hr class="mt-0 mb-4 border-white" />

                    @php
                        $urgentStatusId = \App\Models\Status::where('name', 'Urgent')->value('id');
                    @endphp

                    <!-- Task content -->
                    <div class="task-overview-list">
                        <!-- Urgent Tasks Section -->
                        {{-- <div>{{ $selectedStatus }}</div> --}}

                        @if((int) $selectedStatus === (int) $urgentStatusId || $selectedStatus=='all')                            
                            <div class="head-sort mb-4">
                                <h2 class="text-white text-capitalize">Urgent</h2>
                            </div>
                                @if ($hasUrgentTasks)
                                @foreach ($groupedTasks['urgent'] as $task)
                                    <div class="task-project d-flex purple">
                                        <div class="star-check">
                                            <div class="icon-star d-flex me-3">
                                                <img src="{{ asset('images/star-icon.svg') }}" alt=""
                                                    height="20" width="20" />
                                            </div>
                                            {{-- <div class="check-icon me-3">
                                                <i class="bi bi-square text-white"></i>
                                            </div> --}}
                                        </div>
                                        <div class="copy d-flex flex-column flex-grow-1">
                                            <h2 class="text-uppercase">
                                                {{ $task->project->job_code }} {{ $task->project->name }} -
                                                @if($selectedStatus !== 'past_finished')
                                                    {{ $task->status->name }}
                                                @else
                                                    PAST FINISHED
                                                @endif
                                            </h2>

                                            <div class="detail d-flex align-items-center w-100" style="height:20px">
                                                <div class="date-cta py-1 px-2 rounded-1 d-flex align-items-center me-3">
                                                    <i class="bi bi-clock me-1"></i>
                                                    {{ $task->due_date ? \Carbon\Carbon::parse($task->due_date)->format('M j') : '—' }}
                                                </div>
                                                <div class="task me-3">
                                                    <a href="{{ route('view-task', ['task_id' => $task->id]) }}">
                                                        {{ $task->name }}
                                                    </a>
                                                </div>
                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                    <div class="text over-text d-grid">
                                                        <p class="text-truncate">{{ strip_tags(html_entity_decode($task->description)) }}</p>
                                                    </div>
                                                    <a class="read-more text-decoration-none ms-3"
                                                        href="{{ route('view-task', ['task_id' => $task->id]) }}">
                                                        Read More
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach

                                @if ($hasMoreUrgent)
                                    <div class="text-center my-3">
                                        <button wire:click="loadMoreUrgent" class="btn btn-outline-light">See More Urgent Tasks</button>
                                    </div>
                                @endif
                            @else
                                    <div class="text-center text-dark alert alert-light p-1 mb-5">No results found in "Urgent" status !!</div>
                            @endif
                        @endif

                        {{-- <div>one</div> --}}

                        @php
                            if($selectedStatus!='all') //selectedStatus=all is not found in the database so currrentStatusName will be "All" just for printing when no results found
                                if ($selectedStatus === 'archived') {
                                    $currentStatusName = "Archived";
                                }
                                else if ($selectedStatus === 'past_finished') {
                                    $currentStatusName = "Past Finished";
                                }
                                else {
                                    $currentStatusName = \App\Models\Status::where('id', '=', $selectedStatus)->value('name');
                                }
                            else
                                    $currentStatusName = "All"
                        @endphp

                        <!-- Regular Tasks Section -->
                        @if ($groupedTasks['regular']->isEmpty() && $groupedTasks['urgent']->isEmpty())
                            <div class="empty-state text-center py-5 bg-dark bg-opacity-50 rounded-4 shadow-sm">
                                <div class="d-flex flex-column align-items-center justify-content-center">
                                    <i class="bi bi-file-earmark-x fa-3x mb-3 text-warning"
                                        style="color: #ff4c00 !important;"></i>
                                    <div class="text-center text-dark alert alert-light p-1 mb-5">No results found in "<span class="text-capitalize">{{ $currentStatusName }}</span>" status !!</div>
                                    <p class="text-light mb-0">Try changing the filters or create a new task to get started.</p>
                                </div>
                            </div>
                        @else
                            {{-- <div>two</div> --}}
                            @if((int) $selectedStatus !== (int) $urgentStatusId)
                                @if ($selectedStatus === 'all')
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize">All</h2>
                                    </div>
                                @elseif($hasUrgentTasks)
                                    <!--  don't re-mention urgent tasks in the view file as urgent tasks are mentioned previously in the starting of this view file -->
                                @elseif($selectedStatus === 'archived') 
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize">Archived</h2>
                                    </div>
                                @elseif($selectedStatus === 'past_finished') 
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize">Past Finished</h2>
                                    </div>
                                @else
                                    <div class="head-sort mb-4">
                                        <h2 class="text-white text-capitalize">{{ $statusName }}</h2>
                                    </div>
                                @endif
                            
                                @if($sortField!='job_code')
                                    @if (!empty($groupedTasks['regular']))
                                        @foreach ($groupedTasks['regular'] as $projectName => $tasks)
                                            @if ($tasks->isNotEmpty())
                                                @foreach ($tasks as $task)
                                                    <div class="task-project d-flex purple">
                                                        {{-- <div class="star-check d-flex">
                                                            <div class="check-icon me-3">
                                                                <i class="bi bi-square text-white"></i>
                                                            </div> 
                                                        </div> --}}
                                                        <div class="copy d-flex flex-column flex-grow-1">
                                                            <h2 class="text-uppercase">
                                                                 {{ $task->project->job_code }}  {{ $task->project->name }} -
                                                                @if($selectedStatus !== 'past_finished')
                                                                    {{ $task->status->name }}
                                                                @else
                                                                    PAST FINISHED
                                                                @endif
                                                            </h2>

                                                            <div class="detail d-flex align-items-center w-100" style="height:20px">
                                                                <div class="date me-3">
                                                                    {{ $task->due_date ? \Carbon\Carbon::parse($task->due_date)->format('M j') : '—' }}
                                                                </div>
                                                                <div class="task me-3">
                                                                    <a href="{{ route('view-task', ['task_id' => $task->id]) }}">
                                                                        {{ $task->name }}
                                                                    </a>
                                                                </div>
                                                                <div class="text d-flex flex-grow-1 align-items-center">
                                                                    <div class="text over-text d-grid">
                                                                        <p class="text-truncate">{{ strip_tags(html_entity_decode($task->description)) }}</p>
                                                                    </div>
                                                                    <a class="read-more text-decoration-none ms-3"
                                                                        href="{{ route('view-task', ['task_id' => $task->id]) }}">
                                                                        Read More
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @else
                                                <div class="text-center text-dark alert alert-light p-1 mb-5">
                                                    No results found in "<span class="text-capitalize">{{ $currentStatusName }}</span>" status !!
                                                </div>
                                            @endif
                                        @endforeach

                                        @if ($hasMoreRegular)
                                            <div class="text-center my-3">
                                                <button wire:click="loadMoreRegular" class="btn btn-outline-light">See More Tasks</button>
                                            </div>
                                        @endif
                                    @else
                                        <div class="text-center text-dark alert alert-light p-1 mb-5">
                                            No results found in "<span class="text-capitalize">{{ $currentStatusName }}</span>" status !!
                                        </div>
                                    @endif
                                @elseif($sortField=='job_code')
                                    @if($groupedTasks['regular']->isNotEmpty() && $groupedTasks['regular']->has('Tasks') && $groupedTasks['regular']['Tasks']->isNotEmpty())
                                        @php
                                            $tasksGroupedByJobCode = $groupedTasks['regular']['Tasks']->groupBy(fn($task) => $task->project->job_code ?? 'Unknown');
                                        @endphp

                                        @foreach ($tasksGroupedByJobCode as $jobCode => $tasks)
                                            <div class="head-sort">
                                                <h2 class="text-light text-capitalize my-3">{{ $jobCode }} {{ $tasks->first()->project->name ?? 'No Name' }}</h2>
                                            </div>

                                            @foreach ($tasks as $task)
                                                <div class="task-project d-flex purple ps-5">
                                                    <div class="copy d-flex flex-column flex-grow-1">
                                                        <h2 class="text-uppercase">
                                                            {{ $task->project->job_code }} {{ $task->project->name }} -
                                                            @if($selectedStatus !== 'past_finished')
                                                                {{ $task->status->name }}
                                                            @else
                                                                PAST FINISHED
                                                            @endif
                                                        </h2>

                                                        <div class="detail d-flex align-items-center w-100" style="height:20px">
                                                            <div class="date me-3">
                                                                {{ $task->due_date ? \Carbon\Carbon::parse($task->due_date)->format('M j') : '—' }}
                                                            </div>
                                                            <div class="task me-3">
                                                                <a href="{{ route('view-task', ['task_id' => $task->id]) }}">
                                                                    {{ $task->name }}
                                                                </a>
                                                            </div>
                                                            <div class="text d-flex flex-grow-1 align-items-center">
                                                                <div class="text over-text d-grid">
                                                                    <p class="text-truncate">{{ strip_tags(html_entity_decode($task->description)) }}</p>
                                                                </div>
                                                                <a class="read-more text-decoration-none ms-3"
                                                                    href="{{ route('view-task', ['task_id' => $task->id]) }}">
                                                                    Read More
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endforeach

                                        @if ($hasMoreRegular)
                                            <div class="text-center my-3">
                                                <button wire:click="loadMoreRegular" class="btn btn-outline-light">See More Tasks</button>
                                            </div>
                                        @endif
                                    @else
                                        <div class="text-center text-dark alert alert-light p-1 mb-5">
                                            No results found in "<span class="text-capitalize">{{ $currentStatusName }}</span>" status !!
                                        </div>
                                    @endif
                                @endif
                            @endif
                        @endif

                        <!-- Loading Spinner -->
                        <div wire:loading wire:target="loadMoreUrgent,loadMoreRegular" class="text-center my-3">
                            <div class="spinner-border text-light" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </section>
</div>
@push('styles')
    <style>
        .star-check{
            width:3.5%
        }
        .copy{
            width:96.5%
        }
    </style>
@endpush
