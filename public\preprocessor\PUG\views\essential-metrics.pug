- let pageName = 'Client Portal';
- let mainPage = 'Site Analytics';
- let pageTitle = 'Essential Metrics';

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(`${mainPage} - ${pageTitle}`)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>Bear Tide Oysters</i>', false, true, false, 'Back to Client Portal', false, false, true)

        include ../layouts/projectPortal.pug
        +projectPortal('Site <i>Analytics</i>', ['Essential Metrics', 'Audience and Reach', 'Monthly Insight & Next Focus'])

        section.analytics-meta.bg-white.py-5
            .container-xxl
                hr.my-0
                .head.d-flex
                    .title
                        h2.mb-0.lh-1.text-uppercase=pageTitle
                    .select-month
                        select#month.ps-3.border-0
                            option(value='') February 2025
                            option(value='') January 2025
                            option(value='') December 2024
                        span.arrow.d-inline-flex
                            img(src='images/select-down-arrow.svg', alt='', width='14', height='9')
                    a.circle-icon.ms-auto.align-self-center(href='#') ?
                .row.analytics-row
                    include ../layouts/analyticBox.pug
                    +analyticBox(78, 'Performance', '+3 mo/mo')
                    +analyticBox(69, 'Accessibility', '+3 mo/mo')
                    +analyticBox(100, 'Best Practices', 'Perfect!')
                    +analyticBox(92, 'SEO', '+3 mo/mo')
                    +analyticBox(96, 'Site Health Score', '+3 mo/mo')
                hr.my-0

        include ../partials/footer.pug
        +footer()