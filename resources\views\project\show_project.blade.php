@extends('layout.app')
@section('title', 'Edit Project')
@section('content')
<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-projects') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />Back to Projects List</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('projects') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />Back to Projects List</a>
            @endif
        </div>
        <div class="meta d-flex">
            <div class="w-75">
                <h1 class="heavy text-white mb-0">Edit <i>Project</i></h1>
            </div>

           @if(admin_superadmin_permissions())
            <div class="w-25 d-flex justify-content-around">
                <div class="add-task ms-auto d-flex">
                    <a href="{{ route('show-tasks', ['project_id' => $project->id, 'redirect'=>1]) }}"
                        class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0">
                         SHOW TASKS
                         <span class="img ms-2">
                             <svg width="21" height="21" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                 <path d="M3 6H21M3 12H21M3 18H15" stroke="#ff5811" stroke-width="1.5" stroke-linecap="round"/>
                                 <path d="M17 17L19 19L23 15" stroke="#ff5811" stroke-width="1.5" stroke-linecap="round"/>
                             </svg>
                         </span>
                     </a>
                </div>
                <div class="add-task ms-auto d-flex">
                    <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0" href="{{ route('add-new-task') }}">
                        NEW TASK
                        <span class="img ms-2">
                            <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                                <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5"></path>
                            </svg>
                        </span>
                    </a>
                </div>
            </div>
            @endif
        </div>
    </div>

    @if (session('success'))
    <script>
        successToast(@json(session('success')));
    </script>
    @endif
    @if ($errors->any())
        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert" id="backend-error">
            {{ $errors->first() }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif
</section>
<section class="client-project project-dashboard">
    <div class="container-xxl">
        <div class="row projects-row">
            <div class="col-md-3 col-xl-2 project-column templates-links">
                <h2 class="text-uppercase">TEMPLATES</h2>
                <hr class="mt-0 mb-4 border-white" />
                <div class="templates-list d-flex flex-column">
                    <a class="template" href="#">Web Project</a>
                    <a class="template" href="#">Voyager Project</a>
                    <a class="template" href="#">Print Project</a>
                    <a class="template" href="#">Publication Project</a>
                    <a class="template" href="#">Video Project</a>
                    <a class="template" href="#">Social Media Project</a>
                    <a class="template" href="#">ACOMSGF Project</a>
                    <a class="template" href="#">CA Group Project</a>
                </div>
            </div>
            <div class="col-md-9 col-xl-10 project-column task-overview">
                <div class="col-head align-items-center d-flex justify-content-between">
                    <h2 class="text-uppercase">EDIT PROJECT</h2>
                </div>
                <hr class="mt-0 mb-4 border-white" />
                <form id="editProject" class="row form-wrap" action="{{ route('update-project', $project->id) }}" method="post" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="d-none">
                        <input type="hidden" id="category_id" name="category_id" value="{{ old('category_id', $project->category_id ?? request()->get('category_id', 1)) }}">
                        <input type="hidden" id="phase_id" name="phase_id" value="{{ old('phase_id', $project->phase_id ?? request()->get('phase_id', 1)) }}">
                    </div>
                    
                    <div class="col-12 mb-4">
                        <div class="select-wrap">
                            <select class="form-select border-0 select-client" name="client_id" id="client">
                                <option value="">SELECT CLIENT</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}" {{ (old('client_id') == $client->id || $project->client_id == $client->id) ? 'selected' : '' }}>
                                        {{ $client->name }}
                                    </option>
                                @endforeach
                            </select>
                            
                            <div class="select-icon"><img src="{{ asset('images/down-arrow-orange.svg') }}" alt="" width="18" height="10" /></div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="select-wrap">
                            <input class="form-control border-0" type="text" name="job_code" id="job_code" value="{{ old('job_code', $project->job_code) }}" placeholder="Add Job Code...">
                        </div>
                    </div>
                    <div class="col-md-8 mb-4">
                        <input class="form-control border-0" type="text" id="projectTitle" name="project_title" value="{{ old('project_name', $project->name) }}" placeholder="Add a project title..." />
                    </div>
                    <div class="col-head col-12 mt-5">
                        <h2 class="text-uppercase">PROJECT TIMELINE TYPE</h2>
                        <hr class="mt-0 mb-4 border-white" />
                    </div>
                    <div class="col-12">
                        <div class="row">
							@foreach($timelines as $timeline)
							<div class="col-md-3 mb-3">
								<div class="form-check">
									<input 
										class="form-check-input timeline-radio" 
										name="timeline_type" 
										type="radio" 
										value="{{ $timeline->id }}" 
										id="{{ $timeline->name }}"
										{{ old('timeline_type', optional($project->timeline)->id ?? $project->timeline_id ?? '') == $timeline->id ? 'checked' : '' }}
									/>
									<label class="form-check-label" for="timeline_{{ $timeline->id }}">
										{{ $timeline->name }}
									</label>
								</div>
							</div>
						@endforeach
						
                        </div>
                    </div>
                    <div class="col-12 mt-5 color-timeline-bar">
                        <div class="ribbon d-flex align-items-center justify-content-between">
                            <span class="dot"></span>
                            <span class="bar orange"></span>
                            <span class="bar second green"></span>
                            <span class="bar pink"></span>
                            <span class="bar purple"></span>
                            <span class="bar orange"></span>
                            <span class="dot"></span>
                        </div>
                    </div>
                    <div class="col-head col-12 mt-5 set-timeline">
                        <h2 class="text-white">Set Timeline</h2>
						<div class="timeline-set">
							@foreach($phases as $index => $phase)
							@php
								$phase_icon = $phase->icon;
								$uppercase_name = strtoupper(Str::before($phase->name, ' '));
								$project_phase = $project->phases->firstWhere('id', $phase->id);

								$project_duration = $project_phase->pivot->project_duration ?? null;
								$project_target = $project_phase->pivot->project_target ?? null;
							@endphp
						
							<div class="meta-row d-flex mt-3 orange">
								<div class="logo d-flex align-items-center justify-content-center rounded-circle">
									<img src="{{ $phase_icon }}" alt="Phase Icon" />
								</div>
						
								<div class="wrap-meta ms-3 d-flex flex-grow-1 rounded-2">
									<div class="title d-flex align-items-center justify-content-end fs-6">
										{{ $uppercase_name }}
									</div>
						
									<div class="duration col-meta align-self-center text-center">
										@if($project_duration)
											<span class="current-duration">{{ $project_duration }}</span>
										@else
											<strong>
												<a href="javascript:void(0)" 
												   class="duration-modal-link text-decoration-underline cursor-pointer" 
												   data-phase_id="{{ $phase->id }}">
													Set Duration
												</a>
											</strong>
										@endif
									</div>
						
									<div class="target col-meta align-self-center text-center" data-phase_id="{{ $phase->id }}">
										Suggested Duration:
									</div>
						
									<div class="status col-meta align-self-center text-center pt-0">
										@if($project_target)
											<span class="current-target">{{ \Carbon\Carbon::parse($project_target)->format('m/d/Y') }}</span>
										@else
											<strong>
												<a href="javascript:void(0)" 
												   class="target-modal-link" 
												   data-phase_id="{{ $phase->id }}">
													Set Target
												</a>
											</strong>
										@endif
									</div>
								</div>
							</div>
						@endforeach
						
						
						</div>
						
						
                    </div>
                    <!-- Single Duration Modal -->
                    <div class="modal fade" id="durationModal">
						<div class="modal-dialog modal-dialog-centered">
							<div class="modal-content" style="background: linear-gradient(135deg, rgb(20, 20, 20) 0%, rgb(10, 10, 10) 100%); border: 1px solid rgba(255, 76, 0, 0.3); border-radius: 16px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);">
								<div class="modal-body text-center">
									<div class="position-relative mb-5">
										<div class="position-absolute" style="width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,76,0,0.15) 0%, rgba(255,76,0,0) 70%); top: -50px; right: -50px; border-radius: 50%;"></div>
										<div class="position-absolute" style="width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,76,0,0.1) 0%, rgba(255,76,0,0) 70%); bottom: -30px; left: -30px; border-radius: 50%;"></div>
										
										<div class="d-flex justify-content-between align-items-center mt-2">
											<h2 class="text-uppercase text-white mb-0" style="font-family: 'Futura Std', sans-serif; font-weight: 700; letter-spacing: 1px;">Project Duration</h2>
											<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
										</div>
										<div class="mt-1" style="height: 3px; width: 60px; background: #ff4c00; border-radius: 2px;"></div>
									</div>
									
									<div class="row mx-2 align-items-center">
										<div class="col-6">
											<div class="position-relative">
												<input class="form-control select-day" type="number" min="1" value="{{ old('duration_value') ?? 1 }}" style="height: 60px; background: rgba(30, 30, 30, 0.6); border: 1px solid rgba(255, 255, 255, 0.1); color: white; font-size: 22px; font-weight: 600; text-align: center; border-radius: 12px; padding-right: 15px; font-family: 'Futura Std', sans-serif;">
												<label class="position-absolute text-uppercase" style="top: -10px; left: 15px; background: rgb(15, 15, 15); padding: 0 10px; color: #ff4c00; font-size: 12px; letter-spacing: 1px; font-family: 'Futura Std', sans-serif;">Duration</label>
											</div>
										</div>
										<div class="col-6">
											<div class="position-relative">
												<select class="form-select select-type" style="height: 60px; background: rgba(30, 30, 30, 0.6); border: 1px solid rgba(255, 255, 255, 0.1); color: white; font-size: 18px; border-radius: 12px; font-family: 'Futura Std', sans-serif; appearance: none; -webkit-appearance: none; background-image: url('data:image/svg+xml;utf8,<svg fill=\"white\" height=\"24\" viewBox=\"0 0 24 24\" width=\"24\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M7 10l5 5 5-5z\"/><path d=\"M0 0h24v24H0z\" fill=\"none\"/></svg>'); background-repeat: no-repeat; background-position: right 10px center; padding-right: 30px;">
													<option value="day" {{ old('duration_type') == 'day' ? 'selected' : '' }}>Day</option>
													<option value="week" {{ old('duration_type') == 'week' ? 'selected' : '' }}>Week</option>
													<option value="month" {{ old('duration_type') == 'month' ? 'selected' : '' }}>Month</option>
													<option value="year" {{ old('duration_type') == 'year' ? 'selected' : '' }}>Year</option>
												</select>
												<label class="position-absolute text-uppercase" style="top: -10px; left: 15px; background: rgb(15, 15, 15); padding: 0 10px; color: #ff4c00; font-size: 12px; letter-spacing: 1px; font-family: 'Futura Std', sans-serif;">Unit</label>
											</div>
										</div>
									</div>
									
									<div class="mt-5 mb-2">
										<button class="text-uppercase set-duration-button" type="button" style="background: linear-gradient(90deg, #ff4c00, #ff7e00); border: none; border-radius: 30px; padding: 12px 35px; color: white; font-family: 'Futura Std', sans-serif; font-weight: 600; letter-spacing: 1px; box-shadow: 0 4px 15px rgba(255, 76, 0, 0.3); transition: all 0.3s ease;">Set Duration</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					
                    
                    <!-- Single Target Modal -->
                    <div class="modal fade" id="targetModal">
						<div class="modal-dialog modal-dialog-centered">
							<div class="modal-content" style="background: linear-gradient(135deg, rgb(20, 20, 20) 0%, rgb(10, 10, 10) 100%); border: 1px solid rgba(255, 76, 0, 0.3); border-radius: 16px; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.5);">
								<div class="modal-body text-center">
									<div class="position-relative mb-5">
										<div class="position-absolute" style="width: 150px; height: 150px; background: radial-gradient(circle, rgba(255,76,0,0.15) 0%, rgba(255,76,0,0) 70%); top: -50px; right: -50px; border-radius: 50%;"></div>
										<div class="position-absolute" style="width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,76,0,0.1) 0%, rgba(255,76,0,0) 70%); bottom: -30px; left: -30px; border-radius: 50%;"></div>
										
										<div class="d-flex justify-content-between align-items-center mt-2">
											<h2 class="text-uppercase text-white mb-0" style="font-family: 'Futura Std', sans-serif; font-weight: 700; letter-spacing: 1px;">Project Target</h2>
											<button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
										</div>
										<div class="mt-1" style="height: 3px; width: 60px; background: #ff4c00; border-radius: 2px;"></div>
									</div>
									
									<div class="mx-2">
										<div class="position-relative mb-4">
											<input class="form-control target-date" type="date" value="{{ old('target_date') }}" style="height: 60px; background: rgba(30, 30, 30, 0.6); border: 1px solid rgba(255, 255, 255, 0.1); color: white; font-size: 18px; border-radius: 12px; font-family: 'Futura Std', sans-serif; padding-left: 15px; padding-right: 15px;">
											<label class="position-absolute text-uppercase" style="top: -10px; left: 15px; background: rgb(15, 15, 15); padding: 0 10px; color: #ff4c00; font-size: 12px; letter-spacing: 1px; font-family: 'Futura Std', sans-serif;">Target Date</label>
										</div>
									</div>
									
									<div class="mt-5 mb-2">
										<button class="text-uppercase cta" type="button" style="background: linear-gradient(90deg, #ff4c00, #ff7e00); border: none; border-radius: 30px; padding: 12px 35px; color: white; font-family: 'Futura Std', sans-serif; font-weight: 600; letter-spacing: 1px; box-shadow: 0 4px 15px rgba(255, 76, 0, 0.3); transition: all 0.3s ease;">Set Target</button>
									</div>
								</div>
							</div>
						</div>
					</div>
                    <div class="col-head col-12 mt-5">
                        <h2 class="text-uppercase">CONTACTS & BILLING</h2>
                        <hr class="mt-0 mb-4 border-white" />
                    </div>
                    
                    <div class="col-12 mt-4 d-flex align-items-center justify-content-between">
                        <h2 class="text-white mb-0">Add Contacts</h2>
                        <button id="addMoreContactsButton" type="button" class="cta ms-3 mt-0">Add More Contact</button>
                    </div>
                    
                    <div id="contactsWrapper">
                        @forelse ($social_details as $index => $detail)
                            <div class="col-12 mt-4 contacts-input">
                                <div class="input-wrap d-flex">
                                    <div class="icon d-flex align-items-center justify-content-center">
                                        <img src="{{ asset('images/email-icon.svg') }}" alt="" />
                                    </div>
                                    <div class="input flex-grow-1">
                                        <input class="form-control border-0" 
                                               type="email" 
                                               name="social_detail[]" 
                                               value="{{ old("social_detail.$index", $detail->value) }}" />
                                    </div>
                                </div>
                            </div>
                        @empty
                        @endforelse
                    </div>
                    
                    <div class="col-12">
                        <div class="row">
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add PM Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="pm_hours" value="{{ $project->pm_hours }}">
                            </div>
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add Designer Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="designer_hours" value="{{ $project->designer_hours }}">
                            </div>
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add Developer Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="developer_hours" value="{{ $project->developer_hours }}">
                            </div>
                        <div>
                        <div class="row">
                            <div class="col-md-4 mt-4">
                                <label for="" class="form-label h2">Add CS Hours</label>
                                <input type="number" min="0" class="form-control border-0 w-100" name="cs_hours" value="{{ $project->cs_hours }}">
                            </div>
                        </div>                            
                    </div>
					
                    <div class="col-12 mt-4">
                        <h2 class="mt-4 text-white">Add Harvest Estimate Link</h2>
                    </div>
                    <div class="col-12 mt-4">
                        <div class="input-wrap d-flex">
                            <div class="icon d-flex align-items-center justify-content-center"><img src="{{ asset('images/harvest-icon.svg') }}" alt="" /></div>
                            <div class="input flex-grow-1">
                                <input class="form-control border-0" id="harvest" name="harvest_link" value="{{ old('harvest_link', $project->harvest_link) }}" />
                            </div>
                        </div>
                    </div>
					<div class="col-12 mt-4">
						<h2 class="mt-4 text-white">Invoice Schedule</h2>
						<div class="row mt-4 invoice-options">
							@php
								$invoice_schedule = old('invoice_schedule') ?? $project->invoice_schedule;
							@endphp
					
							@php
								$options = [
									'by_phase' => 'By Phase',
									'by_date' => 'By Date',
									'reoccurring' => 'Reccurring',
									'other' => 'Other',
								];
							@endphp
					
							@foreach ($options as $value => $label)
								<div class="col-md-3 mb-3">
									<div class="form-check">
										<input class="form-check-input" type="radio" name="invoice_schedule" value="{{ $value }}" id="invoice_{{ $value }}"
											{{ $invoice_schedule == $value ? 'checked' : '' }}>
										<label class="form-check-label" for="invoice_{{ $value }}">{{ $label }}</label>
									</div>
								</div>
							@endforeach
						</div>
					</div>
					
                    <div class="col-head col-12 mt-5">
                        <h2 class="text-uppercase">SEND KICKOFF MESSAGE (OPTIONAL)</h2>
                        <hr class="mt-0 mb-4 border-white" />
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="yes" id="yes" name="kickoff_type" 
                                        {{ old('kickoff_type', $project->kickoff_type) == 'yes' ? 'checked' : '' }} />
                                    <label class="form-check-label" for="yes">Yes</label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" value="no" id="no" name="kickoff_type" 
                                        {{ old('kickoff_type', $project->kickoff_type) == 'no' ? 'checked' : '' }} />
                                    <label class="form-check-label" for="no">No</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="messageWrapper" style="{{ (old('kickoff_type', $project->kickoff_type) == 'yes') ? '' : 'display: none;' }}">
                        <div class="col-12 mt-4">
                            <input class="form-control border-0" type="text" id="kickoffTitle" name="kickoff_title" 
                                value="{{ old('kickoff_title', $project->kickoff_title) }}" placeholder="Add a message title here..." />
                        </div>
                        <div class="col-12 mt-4">
                            <textarea class="textarea border-0" name="kickoff_description" placeholder="Type your message here...">{{ old('kickoff_description', $project->kickoff_description) }}</textarea>
                            <div class="form-text">Your comment will be sent to Drew McKenna, Sally McCarthy and Client <a href="#">(change)</a> and appear in Message Center</div>
                        </div>
                        <div class="col-12 mt-5">
                            <div class="upload-btn-wrapper" id="fileUploadWrapper">
                                <button class="btn-upload text-uppercase text-white d-flex align-items-center">
                                    <i class="bi bi-upload me-2"></i> Upload a file
                                </button>
                                <input type="file" name="kickoff_file" id="kickoffFileInput" />
                                @if($project->kickoff_file)
                                    <div class="current-file mt-2"> 
                                        Current file: {{ \Illuminate\Support\Str::limit(basename($project->kickoff_file),30) }}
                                        <input type="hidden" name="existing_kickoff_file" value="{{ $project->kickoff_file }}" />
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12 mt-5">
                        <h2 class="text-uppercase">Assign to</h2>
                        <hr class="mt-0 mb-4 border-white" />
                    </div>
                    <div class="col-12">
                        <div class="row user-checkbox-row">
                            <!-- assigned Users -->
                        <div class="col-12">
                            <div class="row user-checkbox-row">
                                @forelse($users as $user)
                                    <div class="col-md-3 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input"
                                                   type="checkbox"
                                                   name="assigned_users[]"
                                                   value="{{ $user->id }}"
                                                   id="user_{{ $user->id }}"
                                                   {{ in_array($user->id, $assigned_user_ids) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="user_{{ $user->id }}">
                                                {{ $user->name }}
                                            </label>
                                        </div>
                                    </div>
                                @empty
                                    <div class="col-12 text-danger">No Users Found For Client.</div>
                                @endforelse
                            </div>  
                            </div> 
                    </div>
                    <div class="col-12 mt-5 text-center">
                        <button class="cta text-uppercase mt-0" type="submit">UPDATE PROJECT</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
	@endsection
	
	@push('styles')
	<style>
		.upload-btn-wrapper input[type="file"] {
		opacity: 0;
		position: absolute;
		left: 0;
		top: 0;
		height: 100%;
		width: 100%;
		cursor: pointer;
	}
	.upload-btn-wrapper {
		position: relative;
		overflow: hidden;
		display: inline-block;
	}
	.btn-upload {
		background-color: #6a11cb;
		padding: 10px 20px;
		border: none;
		border-radius: 6px;
		cursor: pointer;
	}
	
	</style>
	<link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet" />
	<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
	@endpush
	
	@push('script')
	
	<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
	
	
	
	<script src="https://cdn.jsdelivr.net/npm/dayjs@1/dayjs.min.js"></script>


    <script>

 let timelineRadioButtons = document.querySelectorAll('.timeline-radio');

timelineRadioButtons.forEach((radio) => {
  radio.addEventListener('change', () => {
    const checkedRadio = document.querySelector('.timeline-radio:checked');

    if(checkedRadio.id=='Video Project'){
        console.log('going');
        handleVideoProjectPhases();
    } else if(checkedRadio.id=='Print Project'){
        console.log('print project');
        handlePrintProjectPhases();
    } else if(checkedRadio.id=='Publication Project'){
        console.log('publication project');
        handlePrintProjectPhases(); // Use same function as Print Project
    } else if(checkedRadio.id=='Social Media Project'){
        console.log('social media project');
        handleSocialMediaProjectPhases();
    } else {
        resetToDefaultPhases();
    }
  });
});

function handleVideoProjectPhases() {
    const metaRows = document.querySelectorAll('.timeline-set .meta-row');
    console.log('Found meta rows:', metaRows.length);
    
    // Change phase names for Video Project
    if (metaRows.length >= 4) {
        // Second phase (index 1) -> SCRIPT
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'SCRIPT';
        }
        
        // Third phase (index 2) -> EDIT
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'EDIT';
        }
        
        // Fourth phase (index 3) -> PUBLISH
        

        if(metaRows[3]){

         metaRows[3].style.setProperty('display', 'none', 'important');
            metaRows[3].style.setProperty('visibility', 'hidden', 'important');
            metaRows[3].style.setProperty('opacity', '0', 'important');
            metaRows[3].style.setProperty('height', '0', 'important');
            metaRows[3].style.setProperty('overflow', 'hidden', 'important');
        }

        if (metaRows[4] && metaRows[3].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'PUBLISH';
        }


        
        
        // Hide all phases after the fourth one (index 3)
        for (let i = 5; i < metaRows.length; i++) {
            metaRows[i].style.setProperty('display', 'none', 'important');
            metaRows[i].style.setProperty('visibility', 'hidden', 'important');
            metaRows[i].style.setProperty('opacity', '0', 'important');
            metaRows[i].style.setProperty('height', '0', 'important');
            metaRows[i].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding phase:', i);
        }
    }
}

function handlePrintProjectPhases() {
    const metaRows = document.querySelectorAll('.timeline-set .meta-row');
    console.log('Found meta rows for print:', metaRows.length);
    
    // Change phase names for Print Project
    if (metaRows.length >= 4) {
        // First phase (index 0) -> DEFINE
        if (metaRows[0] && metaRows[0].querySelector('.title')) {
            metaRows[0].querySelector('.title').textContent = 'DEFINE';
        }
        
        // Second phase (index 1) -> CONTENT
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'CONTENT';
        }
        
        // Third phase (index 2) -> DESIGN
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'DESIGN';
        }
        
      


        if(metaRows[3]){

            metaRows[3].style.setProperty('display', 'none', 'important');
            metaRows[3].style.setProperty('visibility', 'hidden', 'important');
            metaRows[3].style.setProperty('opacity', '0', 'important');
            metaRows[3].style.setProperty('height', '0', 'important');
            metaRows[3].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:');
        }

          // Fourth phase (index 3) -> PRINT
        if (metaRows[4] && metaRows[3].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'PRINT';

        }
        
        // Hide all phases after the fourth one (index 3)
        for (let i = 5; i < metaRows.length; i++) {
            metaRows[i].style.setProperty('display', 'none', 'important');
            metaRows[i].style.setProperty('visibility', 'hidden', 'important');
            metaRows[i].style.setProperty('opacity', '0', 'important');
            metaRows[i].style.setProperty('height', '0', 'important');
            metaRows[i].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:', i);
        }
    }
}

function handleSocialMediaProjectPhases() {
    const metaRows = document.querySelectorAll('.timeline-set .meta-row');
    console.log('Found meta rows for social media:', metaRows.length);
    
    // Change phase names for Social Media Project (same as Print but last phase is PUBLISH)
    if (metaRows.length >= 4) {
        // First phase (index 0) -> DEFINE
        if (metaRows[0] && metaRows[0].querySelector('.title')) {
            metaRows[0].querySelector('.title').textContent = 'DEFINE';
        }
        
        // Second phase (index 1) -> CONTENT
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'CONTENT';
        }
        
        // Third phase (index 2) -> DESIGN
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'DESIGN';
        }
        
    if(metaRows[3]){

            metaRows[3].style.setProperty('display', 'none', 'important');
            metaRows[3].style.setProperty('visibility', 'hidden', 'important');
            metaRows[3].style.setProperty('opacity', '0', 'important');
            metaRows[3].style.setProperty('height', '0', 'important');
            metaRows[3].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:');
        }

          // Fourth phase (index 3) -> PRINT
        if (metaRows[4] && metaRows[3].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'PUBLISH';

        }
        
        // Hide all phases after the fourth one (index 3)
        for (let i = 5; i < metaRows.length; i++) {
            metaRows[i].style.setProperty('display', 'none', 'important');
            metaRows[i].style.setProperty('visibility', 'hidden', 'important');
            metaRows[i].style.setProperty('opacity', '0', 'important');
            metaRows[i].style.setProperty('height', '0', 'important');
            metaRows[i].style.setProperty('overflow', 'hidden', 'important');
            console.log('Hiding print phase:', i);
        }
    }
}

function resetToDefaultPhases() {
    const metaRows = document.querySelectorAll('.timeline-set .meta-row');
    
    // Reset all phases to be visible
    metaRows.forEach(row => {
        row.style.removeProperty('display');
        row.style.removeProperty('visibility');
        row.style.removeProperty('opacity');
        row.style.removeProperty('height');
        row.style.removeProperty('overflow');
        row.style.display = 'flex';
    });
    
    // Reset phase names to original
    if (metaRows.length >= 5) {
        // Reset first phase
        if (metaRows[0] && metaRows[0].querySelector('.title')) {
            metaRows[0].querySelector('.title').textContent = 'DEFINE';
        }
        
        // Reset second phase
        if (metaRows[1] && metaRows[1].querySelector('.title')) {
            metaRows[1].querySelector('.title').textContent = 'CONTENT';
        }
        
        // Reset third phase
        if (metaRows[2] && metaRows[2].querySelector('.title')) {
            metaRows[2].querySelector('.title').textContent = 'DESIGN';
        }
        
        // Reset fourth phase
        if (metaRows[3] && metaRows[3].querySelector('.title')) {
            metaRows[3].querySelector('.title').textContent = 'CODE';
        }
        
        // Reset fifth phase
        if (metaRows[4] && metaRows[4].querySelector('.title')) {
            metaRows[4].querySelector('.title').textContent = 'DEPLOY';
        }
    }
}


    </script>


    <script>
        $(document).ready(function () {
            function toggleTimelineVisibility() {
                const selectedId = $('input[name="timeline_type"]:checked').attr('id');
                if (selectedId === 'Maintenance Project' || selectedId === 'Voyager Project') {

                   
                    $('.color-timeline-bar, .set-timeline').removeClass('d-block').addClass('d-none');
                } else {
                   
                    $('.color-timeline-bar, .set-timeline').removeClass('d-none').addClass('d-block');
                }
            }
    
           
            toggleTimelineVisibility();
    
         
            $('input[name="timeline_type"]').on('change', function () {
                validateTimelineRows(); 
                toggleTimelineVisibility();
            });
        });
    </script>
    
	
	
	<script>





function validateTimelineRows() {

let isValid = true;
let errorMessage = '';
let incompletePhasesFound = [];
let atLeastOneCompletePhase = false;


const durationInputs = document.querySelectorAll('input[name^="project_duration"]');
const targetInputs = document.querySelectorAll('input[name^="project_target"]');


const phaseMap = {};


durationInputs.forEach(input => {

  const phaseMatch = input.name.match(/project_duration(\d+)/);
  if (phaseMatch && phaseMatch[1]) {
    const phaseNum = phaseMatch[1];
    
    if (!phaseMap[phaseNum]) {
      phaseMap[phaseNum] = {};
    }
    
    phaseMap[phaseNum].hasDuration = !!input.value;
  }
});


targetInputs.forEach(input => {
  const phaseMatch = input.name.match(/project_target(\d+)/);
  if (phaseMatch && phaseMatch[1]) {
    const phaseNum = phaseMatch[1];
    
    if (!phaseMap[phaseNum]) {
      phaseMap[phaseNum] = {};
    }
    
    phaseMap[phaseNum].hasTarget = !!input.value;
  }
});


Object.keys(phaseMap).forEach(phaseNum => {
  const phase = phaseMap[phaseNum];
  

  if (phase.hasDuration && phase.hasTarget) {
    atLeastOneCompletePhase = true;
  }

  else if ((phase.hasDuration && !phase.hasTarget) || (!phase.hasDuration && phase.hasTarget)) {
    isValid = false;
    incompletePhasesFound.push(phaseNum);
  }
});

if (
$('input[name="timeline_type"]:checked').attr('id') !== 'Maintenance Project' &&
$('input[name="timeline_type"]:checked').attr('id') !== 'Voyager Project'
)
{
        
      

      if (!atLeastOneCompletePhase) {
          isValid = false;
          errorMessage = "At least one project phase must have both duration and target date set.";
          
      
          const firstMetaRow = document.querySelector('.meta-row');
          if (firstMetaRow) {
          firstMetaRow.classList.add('incomplete-row');
          
          const durationCell = firstMetaRow.querySelector('.duration');
          const statusCell = firstMetaRow.querySelector('.status');
          
          if (durationCell) durationCell.classList.add('missing-value');
          if (statusCell) statusCell.classList.add('missing-value');
          }
          
          showValidationError(errorMessage);
      }
      // If any phases are incomplete, show error
      else if (incompletePhasesFound.length > 0) {
          errorMessage = `Phase ${incompletePhasesFound.join(', ')} must have both duration and target date filled out.`;
          
          // Highlight the incomplete rows
          incompletePhasesFound.forEach(phaseNum => {
          const index = parseInt(phaseNum) - 1;
          const metaRow = document.querySelectorAll('.meta-row')[index];
          
          if (metaRow) {
              metaRow.classList.add('incomplete-row');
              
              // Check what's missing and highlight it
              if (!phaseMap[phaseNum].hasDuration) {
              const durationCell = metaRow.querySelector('.duration');
              if (durationCell) {
                  durationCell.classList.add('missing-value');
              }
              }
              
              if (!phaseMap[phaseNum].hasTarget) {
              const statusCell = metaRow.querySelector('.status');
              if (statusCell) {
                  statusCell.classList.add('missing-value');
              }
              }
          }
          });
          
          showValidationError(errorMessage);
      } else {
          clearValidationErrors();
      }
  }
  else{
      isValid = true;
  }

return isValid;
}

function showValidationError(message) {

clearValidationErrors();


const errorDiv = document.createElement('div');
errorDiv.className = 'alert alert-danger timeline-validation-error mt-3';
errorDiv.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${message}`;


const timelineSection = document.querySelector('.timeline-section') || document.querySelector('.ribbon');
if (timelineSection) {
  timelineSection.parentNode.insertBefore(errorDiv, timelineSection.nextSibling);
  

  errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
}
}

function clearValidationErrors() {

const existingErrors = document.querySelectorAll('.timeline-validation-error');
existingErrors.forEach(error => error.remove());


document.querySelectorAll('.incomplete-row').forEach(row => {
  row.classList.remove('incomplete-row');
});

document.querySelectorAll('.missing-value').forEach(cell => {
  cell.classList.remove('missing-value');
});
}


function addValidationStyles() {

if (!document.getElementById('timeline-validation-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'timeline-validation-styles';
  styleElement.textContent = `
    .incomplete-row {
      background-color: rgba(255, 0, 0, 0.05);
    }
    .missing-value {
      position: relative;
    }
    .missing-value::after {
      content: '!';
      position: absolute;
      top: 5px;
      right: 5px;
      color: #dc3545;
      font-weight: bold;
      font-size: 16px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: rgba(220, 53, 69, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    /* Add animation for attention */
    @keyframes highlight-pulse {
      0% { background-color: rgba(255, 0, 0, 0.05); }
      50% { background-color: rgba(255, 0, 0, 0.1); }
      100% { background-color: rgba(255, 0, 0, 0.05); }
    }
    
    .incomplete-row {
      animation: highlight-pulse 2s infinite;
    }
  `;
  document.head.appendChild(styleElement);
}
}
function setupRealTimeValidation() {

document.addEventListener('click', function(event) {

  if (event.target.classList.contains('set-duration-button') ||
      event.target.closest('#targetModal .cta')) {
 
    setTimeout(validateTimelineRows, 300);
  }
});
}


function addTimelineRequirementHint() {
const timelineSection = document.querySelector('.timeline-section') || document.querySelector('.ribbon');
if (timelineSection) {
 
  if (!document.querySelector('.timeline-requirement-hint')) {
    const hintDiv = document.createElement('div');
    hintDiv.className = 'timeline-requirement-hint alert alert-info mt-2 d-none';
    hintDiv.innerHTML = `
      <i class="fas fa-info-circle me-2"></i>
      <strong>Note:</strong> At least one project phase must have both duration and target date set.
    `;
    timelineSection.parentNode.insertBefore(hintDiv, timelineSection.nextSibling);
  }
}
}






let colorgrid = [
    { index: 1, color: '#ff4c00' },
    { index: 2, color: '#65ccb0' },
    { index: 3, color: '#f45689' },
    { index: 4, color: '#a15cd4' },
    { index: 5, color: '#ff4c00' },
];
		$(document).ready(function() {

      
		initializeApp();
	
		const errorDiv = document.getElementById('backend-error');
			if (errorDiv) {
			   
				const firstErrorFieldName = @json(array_key_first($errors->toArray()));
				const field = document.querySelector(`[name="${firstErrorFieldName}"]`);
	
				if (field) {
					field.scrollIntoView({ behavior: 'smooth', block: 'center' });
					field.focus();
					field.classList.add('is-invalid');
				}
			}

        // Check if any radio button is already selected and apply phase changes
        const checkedRadio = document.querySelector('.timeline-radio:checked');
        if (checkedRadio) {
            if (checkedRadio.id == 'Video Project') {
                handleVideoProjectPhases();
            } else if (checkedRadio.id == 'Print Project') {
                handlePrintProjectPhases();
            } else if (checkedRadio.id == 'Publication Project') {
                handlePrintProjectPhases();
            } else if (checkedRadio.id == 'Social Media Project') {
                handleSocialMediaProjectPhases();
            }
        }
	});
	
	document.addEventListener('DOMContentLoaded', function () {
    const wrapper = document.getElementById('fileUploadWrapper');
    const fileInput = document.getElementById('kickoffFileInput');
    const uploadBtn = wrapper.querySelector('.btn-upload');
    const currentFileElement = wrapper.querySelector('.current-file');
    

    if (currentFileElement) {
        uploadBtn.classList.add('d-none');
        fileInput.classList.add('d-none');
        
        const fileName = currentFileElement.textContent.trim().replace('Current file: ', '');
        const fileExt = fileName.split('.').pop().toLowerCase();
        
        const fileIcon = getFileIcon(fileExt);
        const fileDisplay = document.createElement('div');
        fileDisplay.className = 'file-preview-wrapper';
        fileDisplay.innerHTML = `
            <div class="d-flex flex-column align-items-start">
                ${fileIcon}
                <p class="mb-1">${fileName}</p>
                <button type="button" class="btn btn-sm btn-danger mt-2 btn-remove-upload">
                    <i class="bi bi-x-lg me-1"></i>Remove file
                </button>
            </div>
        `;
        
  
        const hiddenInput = currentFileElement.querySelector('input[type="hidden"]');
        currentFileElement.innerHTML = '';
        currentFileElement.appendChild(fileDisplay);
        if (hiddenInput) currentFileElement.appendChild(hiddenInput);
        
      
        const removeBtn = fileDisplay.querySelector('.btn-remove-upload');
        if (removeBtn) {
            removeBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                currentFileElement.remove();
                uploadBtn.classList.remove('d-none');
                fileInput.classList.remove('d-none');
                fileInput.value = '';
                
               
                const existingInput = wrapper.querySelector('input[name="existing_kickoff_file"]');
                if (existingInput) existingInput.remove();
            };
        }
    }

 
    uploadBtn.onclick = function(e) {
        e.preventDefault();
        fileInput.click();
    };

 
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
     
        uploadBtn.classList.add('d-none');
        
       
        const oldPreview = wrapper.querySelector('.file-preview-wrapper');
        if (oldPreview) {
            oldPreview.remove();
        }

        
        const previewWrapper = document.createElement('div');
        previewWrapper.className = "file-preview-wrapper mt-3";

        let previewContent = '';
        if (file.type.startsWith('image/')) {
            const imgURL = URL.createObjectURL(file);
            previewContent = `<img src="${imgURL}" class="img-fluid rounded mb-2" style="max-height: 200px;" alt="Preview" />`;
        } else {
            const fileExt = file.name.split('.').pop().toLowerCase();
            const fileIcon = getFileIcon(fileExt);
            previewContent = `${fileIcon}<p class="mb-1">${file.name}</p>`;
        }

        previewWrapper.innerHTML = `
            <div class="d-flex flex-column align-items-start">
                ${previewContent}
                <button type="button" class="btn btn-sm btn-danger mt-2 btn-remove-upload">
                    <i class="bi bi-x-lg me-1"></i>Remove file
                </button>
            </div>
        `;

       
		wrapper.parentNode.insertBefore(previewWrapper, wrapper.nextSibling);
        const removeBtn = previewWrapper.querySelector('.btn-remove-upload');
        if (removeBtn) {
            removeBtn.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                fileInput.value = '';
                previewWrapper.remove();
                uploadBtn.classList.remove('d-none');
            };
        }
    });
    
   
    function getFileIcon(extension) {
        switch(extension.toLowerCase()) {
            case 'pdf':
                return '<i class="bi bi-file-earmark-pdf-fill fs-1 text-danger"></i>';
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
            case 'webp':
                return '<i class="bi bi-file-earmark-image-fill fs-1 text-primary"></i>';
            case 'doc':
            case 'docx':
                return '<i class="bi bi-file-earmark-word-fill fs-1 text-primary"></i>';
            case 'xls':
            case 'xlsx':
                return '<i class="bi bi-file-earmark-excel-fill fs-1 text-success"></i>';
            case 'ppt':
            case 'pptx':
                return '<i class="bi bi-file-earmark-ppt-fill fs-1 text-warning"></i>';
            case 'zip':
            case 'rar':
            case '7z':
                return '<i class="bi bi-file-earmark-zip-fill fs-1 text-secondary"></i>';
            case 'txt':
                return '<i class="bi bi-file-earmark-text-fill fs-1 text-info"></i>';
            default:
                return '<i class="bi bi-file-earmark-fill fs-1 text-secondary"></i>';
        }
    }
});
     
function initializeApp() {
	  
		flatpickr('input[type="date"]');
		
	 
		initializeClientSelection();
		
	  
		initializeTimeline();
		
	   
		initializeContactManagement();
		
	
		initializeFormValidation();
	}
	
	
	function initializeClientSelection() {
    $('.select-client').on('change', function() {
        const clientId = $(this).val();
        if (clientId === 'null' || !clientId) {
          
            const container = document.querySelector('.user-checkbox-row');
            if (container) container.innerHTML = "";
            return;
        }

        loadClientUsers(clientId, false); 
    });
}
	
	
function loadClientUsers(clientId, isInitialLoad) {
    $.ajax({
        method: 'POST',
        url: "{{ route('client-users') }}",
        data: {
            client_id: clientId,
            _token: '{{ csrf_token() }}'
        },
        success: function(response) {
            renderUserCheckboxes(response.users, isInitialLoad);
        },
        error: function(err) {
            console.error('Error loading client users:', err);
            console.log(err.responseText);
        }
    });
}

	
function renderUserCheckboxes(users, isInitialLoad) {
    const container = document.querySelector('.user-checkbox-row');
    if (!container) return;

    container.innerHTML = "";

 

    if (users.length === 0) {
      
        container.innerHTML = `<div class="col-12 text-danger">No Users Found For This Client.</div>`;

        return;
    }

    const assignedUserIds = @json($assigned_user_ids);
  

    users.forEach(user => {
        let isChecked = '';
                if (!assignedUserIds || !Array.isArray(assignedUserIds)) {
                    console.log('No assigned users');
                } else {
                    console.log('Assigned:', assignedUserIds);
                    if (assignedUserIds.includes(user.id)) {
                        isChecked = 'checked';
                    }
                
            }
        const html = `
            <div class="col-md-3 col-xl-2 mb-3">
                <div class="form-check">
                    <input class="form-check-input" name="assigned_users[]" type="checkbox" 
                           value="${user.id}" id="user_${user.id}" ${isChecked} />
                    <label class="form-check-label" for="user_${user.id}">${user.name}</label>
                </div>
            </div>`;

        container.insertAdjacentHTML('beforeend', html);
    });
}

	
	
	function initializeTimeline() {
    const durationModal = document.getElementById('durationModal');
    const targetModal = document.getElementById('targetModal');
    

    setupExistingDurations();
    
   
    setupExistingTargets();
    
   
    setupDurationLinks(durationModal);
    setupTargetLinks(targetModal);
    setupDurationButton(durationModal);
    setupTargetButton(targetModal);
    
 
    updateProgressBar();
}

function setupExistingDurations() {
   
    const currentDurations = document.querySelectorAll('.current-duration');
    
    currentDurations.forEach(durationElement => {
        const metaRow = durationElement.closest('.meta-row');
        const durationCell = metaRow.querySelector('.duration');
        const targetCell = metaRow.querySelector('.target');
        const phaseId = targetCell.getAttribute('data-phase_id');
        
        
        if (durationCell) {
            const durationText = durationElement.textContent.trim();
            const colorObj = colorgrid.find(item => item.index === parseInt(phaseId));
			const textColor = colorObj ? colorObj.color : 'orange';
            
           
            durationElement.parentNode.innerHTML = `<strong style="color:${textColor};">${durationText}</strong>`;
            
            
            const editBtn = createEditDurationButton(phaseId);
            durationCell.appendChild(editBtn);
            
           
            const phaseIndex = parseInt(phaseId);
            let hiddenInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
            if (!hiddenInput) {
                hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = `project_duration${phaseIndex}`;
                document.querySelector('#editProject').appendChild(hiddenInput);
            }
            hiddenInput.value = durationText;
        }
    });
}

function setupExistingTargets() {
    const currentTargets = document.querySelectorAll('.current-target');
    
    currentTargets.forEach(targetElement => {
        const metaRow = targetElement.closest('.meta-row');
        const statusCell = metaRow.querySelector('.status');
        const targetCell = metaRow.querySelector('.target');
        const phaseId = targetCell.getAttribute('data-phase_id');
        
        if (statusCell) {
            const targetDate=targetElement.textContent.trim();
            const targetDateRaw = targetElement.textContent.trim();
            const parsedDate = new Date(targetDateRaw);
            const displayDate = parsedDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            const colorObj = colorgrid.find(item => item.index === parseInt(phaseId));
			const textColor = colorObj ? colorObj.color : 'orange';
            
            targetElement.parentNode.innerHTML = `<strong style="color:${textColor};">${displayDate}</strong>`;
            
            const editBtn = createEditTargetButton(phaseId);
            statusCell.appendChild(editBtn);
            
            const phaseIndex = parseInt(phaseId);
            let hiddenInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
            if (!hiddenInput) {
                hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = `project_target${phaseIndex}`;
                document.querySelector('#editProject').appendChild(hiddenInput);
            }
            
            const dateParts = targetDate.split('/');
            if (dateParts.length === 3) {
                const isoDate = `${dateParts[2]}-${dateParts[0].padStart(2, '0')}-${dateParts[1].padStart(2, '0')}`;
                hiddenInput.value = isoDate;
            } else {
                hiddenInput.value = targetDate; 
            }
        }
    });
}
function setupDurationLinks(durationModal) {
    const durationLinks = document.querySelectorAll('.duration-modal-link');
    
    durationLinks.forEach(link => {
        link.addEventListener('click', function() {
            const phaseId = this.getAttribute('data-phase_id');
            durationModal.setAttribute('data-active-phase', phaseId);
            
            const phaseIndex = parseInt(phaseId);
            const hiddenInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
            
            if (hiddenInput && hiddenInput.value) {
                const durationText = hiddenInput.value;
                const match = durationText.match(/(\d+)\s+(\w+)/);
                if (match) {
                    const value = match[1];
                    const type = match[2].replace(/s$/, '');
                    
                    document.querySelector('#durationModal .select-day').value = value;
                    document.querySelector('#durationModal .select-type').value = type;
                }
            } else {
                const daySelect = document.querySelector('#durationModal .select-day');
                const typeSelect = document.querySelector('#durationModal .select-type');
                if (daySelect) daySelect.value = '';
                if (typeSelect) typeSelect.value = 'day';
            }
            
            new bootstrap.Modal(durationModal).show();
        });
    });
}

function setupTargetLinks(targetModal) {
    const targetLinks = document.querySelectorAll('.target-modal-link');
    
    targetLinks.forEach(link => {
        link.addEventListener('click', function() {
            const phaseId = this.getAttribute('data-phase_id');
            targetModal.setAttribute('data-active-phase', phaseId);
            
            const phaseIndex = parseInt(phaseId);
            const hiddenInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
            
            const targetDateInput = document.querySelector('#targetModal .target-date');
            if (hiddenInput && hiddenInput.value && targetDateInput) {
                targetDateInput.value = hiddenInput.value;
            } else if (targetDateInput) {
                targetDateInput.value = '';
            }
            
            new bootstrap.Modal(targetModal).show();
        });
    });
}

function setupDurationButton(durationModal) {
    const setDurationButton = document.querySelector('.set-duration-button');
    
    if (setDurationButton) {
        setDurationButton.addEventListener('click', function() {
            const activePhase = durationModal.getAttribute('data-active-phase');
            const phaseIndex = parseInt(activePhase);
            
            const durationValue = document.querySelector('#durationModal .select-day').value;
            const durationType = document.querySelector('#durationModal .select-type').value;
            
            if (durationValue) {
                saveDurationValue(activePhase, phaseIndex, durationValue, durationType);
                bootstrap.Modal.getInstance(durationModal).hide();
                updateProgressBar();
            }
        });
    }
}







function createEditDurationButton(phaseId) {


    const colorObj = colorgrid.find(item => item.index === parseInt(phaseId));
    const iconColor = colorObj ? colorObj.color : 'orange';
    const editBtn = document.createElement('button');
    editBtn.className = 'btn btn-sm btn-outline-secondary edit-duration-btn ms-2';
    editBtn.innerHTML = `<i class="fas fa-edit" style="color:${iconColor};"></i>`;
    editBtn.setAttribute('data-phase_id', phaseId);
    
    editBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const clickedPhaseId = this.getAttribute('data-phase_id');
        const durationModal = document.getElementById('durationModal');
        durationModal.setAttribute('data-active-phase', clickedPhaseId);
        
        const phaseIndex = parseInt(clickedPhaseId);
        const hiddenInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
        
        if (hiddenInput && hiddenInput.value) {
            const durationText = hiddenInput.value;
            const match = durationText.match(/(\d+)\s+(\w+)/);
            if (match) {
                const value = match[1];
                const type = match[2].replace(/s$/, '');
                
                document.querySelector('#durationModal .select-day').value = value;
                document.querySelector('#durationModal .select-type').value = type;
            }
        }
        
        new bootstrap.Modal(durationModal).show();
    });
    
    return editBtn;
}


function createEditTargetButton(phaseId) {

    const colorObj = colorgrid.find(item => item.index === parseInt(phaseId));
    const iconColor = colorObj ? colorObj.color : 'orange';
    const editBtn = document.createElement('button');
    editBtn.className = 'btn btn-sm btn-outline-secondary edit-target-btn ms-2';
    editBtn.innerHTML = `<i class="fas fa-edit" style="color:${iconColor};"></i>`;
    editBtn.setAttribute('data-phase_id', phaseId);
    
    editBtn.addEventListener('click', function(e) {
        e.preventDefault();
        const clickedPhaseId = this.getAttribute('data-phase_id');
        const targetModal = document.getElementById('targetModal');
        targetModal.setAttribute('data-active-phase', clickedPhaseId);
        
        const phaseIndex = parseInt(clickedPhaseId);
        const hiddenInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
        
        const targetDateInput = document.querySelector('#targetModal .target-date');
        if (hiddenInput && hiddenInput.value && targetDateInput) {
            targetDateInput.value = hiddenInput.value;
        }
        
        new bootstrap.Modal(targetModal).show();
    });
    
    return editBtn;
}
	
	function setupTargetButton(targetModal) {
		const setTargetButton = document.querySelector('#targetModal .cta');
		
		if (setTargetButton) {
			setTargetButton.addEventListener('click', function() {
				const activePhase = targetModal.getAttribute('data-active-phase');
				const phaseIndex = parseInt(activePhase);
				
				const targetDateInput = document.querySelector('#targetModal .target-date');
				const targetDate = targetDateInput ? targetDateInput.value : null;
				
				if (targetDate) {
					saveTargetDate(activePhase, phaseIndex, targetDate);
					bootstrap.Modal.getInstance(targetModal).hide();
				}
			});
		}
	}
	
	
	function saveDurationValue(activePhase, phaseIndex, durationValue, durationType) {
    const durationText = `${durationValue} ${durationType}${durationValue > 1 ? 's' : ''}`;
    
 
    let hiddenInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
    if (!hiddenInput) {
        hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = `project_duration${phaseIndex}`;
        document.querySelector('#editProject').appendChild(hiddenInput);
    }
    hiddenInput.value = durationText;
    
    const metaRow = document.querySelector(`.meta-row .target[data-phase_id="${activePhase}"]`).closest('.meta-row');
    
    if (metaRow) {
        const durationCell = metaRow.querySelector('.duration');
        if (durationCell) {

            const colorObj = colorgrid.find(item => item.index === parseInt(phaseIndex));
			const textColor = colorObj ? colorObj.color : 'orange';


            durationCell.innerHTML = `<strong style="color:${textColor};">${durationText}</strong>`;
            
            let editBtn = durationCell.querySelector('.edit-duration-btn');
            if (!editBtn) {
                editBtn = createEditDurationButton(activePhase);
                durationCell.appendChild(editBtn);
            }
        }
    }
}

function saveTargetDate(activePhase, phaseIndex, targetDate) {
    // Format the date for display
    let formattedDate;
    if (typeof dayjs !== 'undefined') {
        formattedDate = dayjs(targetDate).format('MMM D, YYYY');
    } else {
        const date = new Date(targetDate);
        formattedDate = date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric', 
            year: 'numeric' 
        });
    }
    

    let hiddenInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
    if (!hiddenInput) {
        hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = `project_target${phaseIndex}`;
        document.querySelector('#editProject').appendChild(hiddenInput);
    }
    hiddenInput.value = targetDate;
    
   
    const metaRow = document.querySelector(`.meta-row .target[data-phase_id="${activePhase}"]`).closest('.meta-row');
    
    if (metaRow) {
        const statusCell = metaRow.querySelector('.status');
        if (statusCell) {


            const colorObj = colorgrid.find(item => item.index === parseInt(activePhase));
			const textColor = colorObj ? colorObj.color : 'orange';
            statusCell.innerHTML = `<strong style="color:${textColor};">${formattedDate}</strong>`;
            
            let editBtn = statusCell.querySelector('.edit-target-btn');
            if (!editBtn) {
                editBtn = createEditTargetButton(activePhase);
                statusCell.appendChild(editBtn);
            }
        }
    }
}
	
	
	function updateProgressBar() {
		const durationInputs = [];
		let index = 1;
		let input = document.querySelector(`input[name="project_duration${index}"]`);
	
	  
		while (input) {
			durationInputs.push(input);
			index++;
			input = document.querySelector(`input[name="project_duration${index}"]`);
		}
	
		if (durationInputs.length === 0) return;
	
		const durationValues = [];
		let totalDuration = 0;
	
	  
		durationInputs.forEach(input => {
			let days = 0;
	
			if (input && input.value) {
				const match = input.value.trim().toLowerCase().match(/(\d+(?:\.\d+)?)\s*(day|week|month|year)s?/i);
	
				if (match) {
					const value = parseFloat(match[1]);
					const unit = match[2];
	
					switch (unit) {
						case 'day':
							days = value;
							break;
						case 'week':
							days = value * 7;
							break;
						case 'month':
							days = value * 30;
							break;
						case 'year':
							days = value * 365;
							break;
					}
				}
			}
	
			durationValues.push(days);
			totalDuration += days;
		});
	
	
		const bars = document.querySelectorAll('.ribbon .bar');
	bars.forEach((bar, i) => {
		const value = durationValues[i] || 0;
		const percentage = totalDuration > 0 ? (value / totalDuration) * 100 : (100 / bars.length);
	
	   
		bar.style.flex = 'unset';
		bar.style.width = `${percentage}%`;
		bar.style.transition = 'width 0.5s ease-in-out';
	});
	
	}
	
	
	function setupEditButtons(durationModal, targetModal) {
		document.querySelectorAll('.meta-row').forEach((row, index) => {
			const phaseIndex = index;
			
		 
			const durationInput = document.querySelector(`input[name="project_duration${phaseIndex}"]`);
			if (durationInput && durationInput.value) {
				const durationCell = row.querySelector('.duration');
				if (durationCell && !durationCell.querySelector('.edit-duration-btn')) {
					const editDurationBtn = createEditDurationButton(index);
					durationCell.appendChild(editDurationBtn);
				}
			}
			
		   
			const targetInput = document.querySelector(`input[name="project_target${phaseIndex}"]`);
			if (targetInput && targetInput.value) {
				const statusCell = row.querySelector('.status');
				if (statusCell && !statusCell.querySelector('.edit-target-btn')) {
					const editTargetBtn = createEditTargetButton(index);
					statusCell.appendChild(editTargetBtn);
				}
			}
		});
	}
	
	/**
	 * Initialize contact management functionality
	 */
	function initializeContactManagement() {
		const addMoreContactsButton = document.getElementById('addMoreContactsButton');
		const contactsWrapper = document.getElementById('contactsWrapper');
		
		if (addMoreContactsButton && contactsWrapper) {
			let contactCounter = 1;
			
			addMoreContactsButton.addEventListener('click', function() {
				contactCounter++;
				addNewContactField(contactsWrapper, contactCounter);
			});
		}
	}
	
	
	function addNewContactField(contactsWrapper, contactCounter) {
		const newContactId = `email_${contactCounter}`;
		
		const newContactDiv = document.createElement('div');
		newContactDiv.className = 'col-12 mt-3 contacts-input';
		
		const iconSrc = document.querySelector('.contacts-input .icon img')?.src || '';
		
		newContactDiv.innerHTML = `
			<div class="input-wrap d-flex">
				<div class="icon d-flex align-items-center justify-content-center"><img src="${iconSrc}" alt="" /></div>
				<div class="input flex-grow-1">
					<input class="form-control border-0" type="email" id="${newContactId}" name="social_detail[]" />
				</div>
				<div class="remove-contact ms-2">
					<button type="button" class="btn btn-sm btn-danger">Remove</button>
				</div>
			</div>
		`;
		contactsWrapper.appendChild(newContactDiv);
	
	   
		if (typeof validation !== 'undefined') {
			validation.addField(`#${newContactId}`, [
				{
					rule: 'email',
					errorMessage: 'Please provide a valid email address',
				}
			]);
		}
		
	   
		const removeButton = newContactDiv.querySelector('.remove-contact button');
		if (removeButton) {
			removeButton.addEventListener('click', function() {
				if (typeof validation !== 'undefined') {
					validation.removeField(`#${newContactId}`);
				}
				contactsWrapper.removeChild(newContactDiv);
			});
		}
	}


    function initializeFormValidation() {
  setupKickoffValidation();
  addValidationStyles();
  setupRealTimeValidation();
  addTimelineRequirementHint();

  const projectValidator = new GlobalFormValidator('editProject', {
    rules: {
      '#client': GlobalFormValidator.validators.required,
      '#job_code': GlobalFormValidator.validators.required,
      '#projectTitle': GlobalFormValidator.validators.required,
      '#email': GlobalFormValidator.validators.email,
      'input[name="timeline_type"]': GlobalFormValidator.validators.required,
      '.invoice-options input[type="radio"]': GlobalFormValidator.validators.required,
      '.user-checkbox-row input[type="checkbox"]': GlobalFormValidator.validators.anyChecked,
    },
    messages: {
      '#client': "Please select a client",
      '#job_code': "Please add a job code",
      '#projectTitle': "Please add a project title",
      '#email': "Please provide a valid contact email",
      'input[name="timeline_type"]': "Please select a timeline option",
      '.invoice-options input[type="radio"]': "Please select at least one invoice schedule option",
      '#kickoffTitle': "Please add a kickoff message title",
      '.user-checkbox-row input[type="checkbox"]': "Please assign the project to at least one user"
    },
    onSuccess: (form) => {
      console.log('Form is valid, submitting...');
      form.submit();
    }
  }).initialize();
}


function setupKickoffValidation() {
    const yesRadio = document.getElementById('yes');
    const noRadio = document.getElementById('no');
    const kickoffField = document.getElementById('kickoffTitle');

    if (yesRadio) {
        yesRadio.addEventListener('change', function() {
            if (this.checked && kickoffField) {
				let messageWrapper=document.getElementById('messageWrapper');
				messageWrapper.style.display="block";
                kickoffField.classList.add('is-required'); 
            }
        });
    }

    if (noRadio) {
        noRadio.addEventListener('change', function() {
            if (this.checked && kickoffField) {

				let messageWrapper=document.getElementById('messageWrapper');
				messageWrapper.style.display="none";
                kickoffField.classList.remove('is-required');
            }
        });
    }
}
	</script>
	@endpush