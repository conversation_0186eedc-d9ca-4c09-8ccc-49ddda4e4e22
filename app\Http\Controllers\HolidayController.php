<?php

namespace App\Http\Controllers;

use App\Models\Holiday;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class HolidayController extends Controller
{

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_global' => 'nullable|boolean',
            'region' => 'nullable|string|in:indian,us',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user = Auth::user();
        $isAdmin = $user->hasRole(['Admin', 'SuperAdmin']);

        $holiday = new Holiday();
        $holiday->title = $request->input('title');
        $holiday->description = $request->input('description');
        $holiday->start_date = $request->input('start_date');
        $holiday->end_date = $request->input('end_date');
        $holiday->user_id = $user->id;


        $holiday->is_global = $isAdmin && $request->input('is_global') == '1' ? true : false;

        // Set region if it's a company-wide holiday
        if ($holiday->is_global && $request->has('region')) {
            $holiday->region = $request->input('region');
        }

        $holiday->save();

        return redirect()->route('admin-calender')->with('success', 'Holiday added successfully!');
    }

    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:holidays,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_global' => 'nullable|boolean',
            'region' => 'nullable|string|in:indian,us',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user = Auth::user();
        $isAdmin = $user->hasRole(['Admin', 'SuperAdmin']);

        $holiday = Holiday::findOrFail($request->input('id'));


        if (!$isAdmin && $holiday->user_id !== $user->id) {
            return redirect()->route('admin-calender')->with('error', 'You do not have permission to update this holiday.');
        }

        $holiday->title = $request->input('title');
        $holiday->description = $request->input('description');
        $holiday->start_date = $request->input('start_date');
        $holiday->end_date = $request->input('end_date');


        if ($isAdmin) {
            $holiday->is_global = $request->input('is_global') == '1' ? true : false;

            // Update region if it's a company-wide holiday
            if ($holiday->is_global && $request->has('region')) {
                $holiday->region = $request->input('region');
            } else if (!$holiday->is_global) {
                $holiday->region = null; // Clear region if not a company-wide holiday
            }
        }

        $holiday->save();

        return redirect()->route('admin-calender')->with('success', 'Holiday updated successfully!');
    }


    public function destroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:holidays,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $user = Auth::user();
        $isAdmin = $user->hasRole(['Admin', 'SuperAdmin']);

        $holiday = Holiday::findOrFail($request->input('id'));


        if (!$isAdmin && $holiday->user_id !== $user->id) {
            return redirect()->route('admin-calender')->with('error', 'You do not have permission to delete this holiday.');
        }

        $holiday->delete();

        return redirect()->route('admin-calender')->with('success', 'Holiday deleted successfully!');
    }

    public function storeAjax(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_global' => 'nullable|boolean',
            'region' => 'nullable|string|in:indian,us',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $isAdmin = $user->hasRole(['Admin', 'SuperAdmin']);

        $holiday = new Holiday();
        $holiday->title = $request->input('title');
        $holiday->description = $request->input('description');
        $holiday->start_date = $request->input('start_date');
        $holiday->end_date = $request->input('end_date');
        $holiday->user_id = $user->id;
        $holiday->is_global = $isAdmin && $request->input('is_global') == '1' ? true : false;

        // Set region if it's a company-wide holiday
        if ($holiday->is_global && $request->has('region')) {
            $holiday->region = $request->input('region');
        }

        $holiday->save();

        // Load the user relationship for the response
        $holiday->load('user');

        // Format the holiday data for the calendar
        $eventData = [
            'id' => $holiday->id,
            'title' => !$holiday->is_global && $holiday->user ? $holiday->user->name . " is out" : $holiday->title,
            'start' => $holiday->start_date->format('Y-m-d'),
            // 'end' => $holiday->end_date ? $holiday->end_date->format('Y-m-d') : null,
            'end' => $holiday->end_date ? $holiday->end_date->copy()->addDay()->format('Y-m-d') : null, // added 1 day
            'allDay' => true,
            'color' => $holiday->is_global ? '#ffc107' : '#28a745', // Use the same colors as in the calendar
            'description' => !$holiday->is_global && $holiday->user
                ? $holiday->user->name . " is on personal leave" . ($holiday->title ? " - " . $holiday->title : "")
                : ($holiday->description ?: ""),
            'holiday' => true,
            'holidayId' => $holiday->id,
            'isGlobal' => $holiday->is_global,
            'userId' => $holiday->user_id,
            'region' => $holiday->region,
            'userName' => $holiday->user ? $holiday->user->name : ""
        ];

        return response()->json([
            'success' => true,
            'message' => 'Holiday added successfully!',
            'holiday' => $holiday,
            'eventData' => $eventData
        ]);
    }
}
