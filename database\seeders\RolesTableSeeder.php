<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RolesTableSeeder extends Seeder
{
    public function run()
    {
        $roles = [
            'SuperAdmin',
            'Admin',
            'User',
            'Developer',
            'Designer',
        ];
        
        foreach ($roles as $role) {
            $exists = DB::table('roles')->where('name', $role)->exists();
        
            if (!$exists) {
                DB::table('roles')->insert([
                    'name' => $role,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            }
        }
    }
}
