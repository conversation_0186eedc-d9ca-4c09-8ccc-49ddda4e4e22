ii  libapache2-mod-php                         2:8.1+92ubuntu1                         all          server-side, HTML-embedded scripting language (Apache 2 module) (default)
ii  libapache2-mod-php8.1                      8.1.2-1ubuntu2.20                       amd64        server-side, HTML-embedded scripting language (Apache 2 module)
ii  php                                        2:8.1+92ubuntu1                         all          server-side, HTML-embedded scripting language (default)
ii  php-bz2                                    2:8.1+92ubuntu1                         all          bzip2 module for PHP [default]
ii  php-common                                 2:92ubuntu1                             all          Common files for PHP packages
ii  php-composer-ca-bundle                     1.3.1-1                                 all          utility library to find a path to the system CA bundle
ii  php-composer-metadata-minifier             1.0.0-2                                 all          Small utility library that handles metadata minification and expansion
ii  php-composer-pcre                          1.0.1-1                                 all          PCRE wrapping library that offers type-safe preg_* replacements
ii  php-composer-semver                        3.2.9-1                                 all          utilities, version constraint parsing and validation
ii  php-composer-spdx-licenses                 1.5.6-1                                 all          SPDX licenses list and validation library
ii  php-composer-xdebug-handler                2.0.4-1build1                           all          Restarts a process without Xdebug
ii  php-gd                                     2:8.1+92ubuntu1                         all          GD module for PHP [default]
ii  php-google-recaptcha                       1.2.4-3                                 all          reCAPTCHA PHP client library
ii  php-intl                                   2:8.1+92ubuntu1                         all          Internationalisation module for PHP [default]
ii  php-json                                   2:8.1+92ubuntu1                         all          JSON module for PHP [default]
ii  php-json-schema                            5.2.11-1                                all          implementation of JSON schema
ii  php-mariadb-mysql-kbs                      1.2.12-1                                all          Knowledge base about MariaDB and MySQL server variables
ii  php-mbstring                               2:8.1+92ubuntu1                         all          MBSTRING module for PHP [default]
ii  php-mysql                                  2:8.1+92ubuntu1                         all          MySQL module for PHP [default]
ii  php-nikic-fast-route                       1.3.0-3                                 all          Fast request router for PHP
ii  php-phpmyadmin-motranslator                5.2.0-1build1                           all          translation API for PHP using Gettext MO files
ii  php-phpmyadmin-shapefile                   2.1-5                                   all          translation API for PHP using Gettext MO files
ii  php-phpmyadmin-sql-parser                  5.4.1-2                                 all          validating SQL lexer and parser
ii  php-phpseclib                              2.0.36-1                                all          implementations of an arbitrary-precision integer arithmetic library
ii  php-psr-cache                              3.0.0-1                                 all          Common interface for caching libraries
ii  php-psr-container                          2.0.2-1                                 all          Common Container Interface (PHP FIG PSR-11)
ii  php-psr-log                                3.0.0-1                                 all          common interface for logging libraries
ii  php-react-promise                          2.7.0-2                                 all          lightweight implementation of CommonJS Promises/A for PHP
ii  php-symfony-cache                          5.4.4+dfsg-1ubuntu8                     all          provides an extended PSR-6, PSR-16 (and tags) implementation
ii  php-symfony-cache-contracts                2.4.0-1ubuntu2                          all          Generic abstractions related to caching
ii  php-symfony-config                         5.4.4+dfsg-1ubuntu8                     all          load configurations from different data sources
ii  php-symfony-console                        5.4.4+dfsg-1ubuntu8                     all          run tasks from the command line
ii  php-symfony-dependency-injection           5.4.4+dfsg-1ubuntu8                     all          standardize and centralize construction of objects
ii  php-symfony-deprecation-contracts          2.4.0-1ubuntu2                          all          A generic function and convention to trigger deprecation notices
ii  php-symfony-expression-language            5.4.4+dfsg-1ubuntu8                     all          compile and evaluate expressions
ii  php-symfony-filesystem                     5.4.4+dfsg-1ubuntu8                     all          basic filesystem utilities
ii  php-symfony-finder                         5.4.4+dfsg-1ubuntu8                     all          find files and directories
ii  php-symfony-polyfill-php80                 1.24.0-1ubuntu2                         all          Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions
ii  php-symfony-polyfill-php81                 1.24.0-1ubuntu2                         all          Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions
ii  php-symfony-process                        5.4.4+dfsg-1ubuntu8                     all          execute commands in sub-processes
ii  php-symfony-service-contracts              2.4.0-1ubuntu2                          all          Generic abstractions related to writing services
ii  php-symfony-string                         5.4.4+dfsg-1ubuntu8                     all          object-oriented API to work with strings
ii  php-symfony-var-exporter                   5.4.4+dfsg-1ubuntu8                     all          export serializable PHP data structure to plain PHP code
ii  php-tcpdf                                  6.4.4+dfsg1-1                           all          PHP class for generating PDF files on-the-fly
ii  php-twig                                   3.3.8-2ubuntu4                          all          Flexible, fast, and secure template engine for PHP
ii  php-twig-i18n-extension                    3.0.0-2                                 all          i18n extension for the Twig template system
ii  php-zip                                    2:8.1+92ubuntu1                         all          Zip module for PHP [default]
ii  php8.1                                     8.1.2-1ubuntu2.20                       all          server-side, HTML-embedded scripting language (metapackage)
ii  php8.1-bcmath                              8.1.2-1ubuntu2.20                       amd64        Bcmath module for PHP
ii  php8.1-bz2                                 8.1.2-1ubuntu2.20                       amd64        bzip2 module for PHP
ii  php8.1-cgi                                 8.1.2-1ubuntu2.20                       amd64        server-side, HTML-embedded scripting language (CGI binary)
ii  php8.1-cli                                 8.1.2-1ubuntu2.20                       amd64        command-line interpreter for the PHP scripting language
ii  php8.1-common                              8.1.2-1ubuntu2.20                       amd64        documentation, examples and common module for PHP
ii  php8.1-curl                                8.1.2-1ubuntu2.20                       amd64        CURL module for PHP
ii  php8.1-gd                                  8.1.2-1ubuntu2.20                       amd64        GD module for PHP
ii  php8.1-intl                                8.1.2-1ubuntu2.20                       amd64        Internationalisation module for PHP
ii  php8.1-mbstring                            8.1.2-1ubuntu2.20                       amd64        MBSTRING module for PHP
ii  php8.1-mysql                               8.1.2-1ubuntu2.20                       amd64        MySQL module for PHP
ii  php8.1-opcache                             8.1.2-1ubuntu2.20                       amd64        Zend OpCache module for PHP
ii  php8.1-readline                            8.1.2-1ubuntu2.20                       amd64        readline module for PHP
ii  php8.1-xml                                 8.1.2-1ubuntu2.20                       amd64        DOM, SimpleXML, XML, and XSL module for PHP
ii  php8.1-zip                                 8.1.2-1ubuntu2.20                       amd64        Zip module for PHP
ii  phpmyadmin                                 4:5.1.1+dfsg1-5ubuntu1                  all          MySQL web administration tool
