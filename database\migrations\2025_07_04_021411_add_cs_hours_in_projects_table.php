<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('projects', 'cs_hours')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->integer('cs_hours')->nullable()->after('developer_hours'); // or any other column
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('projects', 'cs_hours')) {
            Schema::table('projects', function (Blueprint $table) {
                $table->dropColumn('cs_hours');
            });
        }
    }
};
