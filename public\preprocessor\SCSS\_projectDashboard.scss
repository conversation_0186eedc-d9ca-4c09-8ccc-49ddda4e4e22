.project-dashboard {
	.col-head {
		.link {
			color: #cbcbcb;
		}

		.sort {
			margin-bottom: 0.5rem;

			.sort-by {
				h3 {
					color: rgba(#fff, 0.5);
					font: 500 14px $inter;
					margin: 0;
				}
			}
		}
	}

	.quick-links {
		.icon-more {
			color: #bcbcbc;
		}

		.statuses-list {
			.flag-status {
				background: #111;
				border: 1px solid #fff;
				border-radius: 0.4rem;
				color: #fff;
				font: 500 14px $inter;
				padding: 0.5rem;
				text-align: center;

				&.assigned {
					background: #ff5811;
					border-color: #ff5811;
				}

				+ .flag-status {
					margin-top: 0.8rem;
				}
			}
		}

		.assigned-to-list {
			.meta {
				color: #fff;
				font: 500 14px $inter;

				.icon {
					color: #ff5811;
					font-size: 16px;
					isolation: isolate;
					margin-right: 10px;
					position: relative;

					&:before {
						background: #fff;
						content: '';
						height: 80%;
						left: 10%;
						position: absolute;
						top: 10%;
						width: 80%;
						z-index: -1;
					}
				}

				+ .meta {
					margin-top: 0.8rem;
				}
			}
		}

		.link-to-project {
			a {
				color: rgba(#fff, 0.5);
				font: 500 16px $inter;

				&:hover {
					color: #fff;
				}
			}
		}
	}

	.quick-links-list {
		.box {
			.icon {
				flex: 0 0 20px;
				height: 20px;
				margin-right: 10px;
				width: 20px;

				img {
					max-height: 100%;
					max-width: 100%;
				}
			}

			.text {
				p {
					font-weight: 500;
				}
			}

			&:hover {
				.text {
					p {
						color: #fff;
					}
				}
			}

			+ .box {
				margin-top: 1rem;
			}
		}

		.more-links {
			cursor: pointer;
			position: relative;

			.drop-list {
				background: #282828;
				border-left: 1px solid #ff5811;
				border-radius: 0 8px 8px 0;
				display: none;
				left: 100%;
				position: absolute;
				width: 182px;

				.box {
					padding: 1rem;

					+ .box {
						border-top: 1px solid #fff;
						margin: 0;
					}
				}
			}

			&:hover {
				.drop-list {
					display: block;
				}
			}
		}
	}

	.task-overview-list {
		.task-project {
			overflow: hidden;

			.icon {
				border: 1px solid #ff5811;
				border-radius: 50%;
				flex: 0 0 44px;
				height: 44px;
				margin-right: 1.25rem;
				width: 44px;
			}

			.copy {
				color: #fff;
				font: 14px/1.18 $inter;
				text-wrap: nowrap;

				h2 {
					font-size: 14px;
				}

				.date-cta {
					font: 500 14px/1 $inter;
				}

				.detail {
					font-weight: 700;
				}

				.task {
					font-weight: 500;
				}

				p {
					font: inherit;
					line-height: 1.18;
					margin: 0;
				}

				.text {
					a {
						color: #ff5811;
						font-weight: 400;
					}
				}
			}

			a {
				color: #fff;
			}

			@each $name, $color in $colors {
				&.#{ $name } {
					@include task-overview-head($color);
				}
			}

			+ .task-project {
				margin-top: 2rem;
			}

			+ .head-sort {
				margin-top: 3rem;
			}
		}

		.more-tasks {
			position: relative;

			a {
				background: #111;
				color: #ff5811;
				font: 700 12px/1 $inter;
				padding: 1rem;
				position: relative;
				z-index: 5;
			}

			&:before {
				background: rgba(#fff, 0.25);
				content: '';
				height: 1px;
				left: 0;
				position: absolute;
				top: 50%;
				width: 100%;
			}
		}
	}

	.task-view {
		.head {
			margin-top: -15px;

			h2 {
				color: #fff;
				font: 700 28px $futura;
			}

			.time {
				color: #fff;
				font: 700 28px $futura;

				.icon {
					color: #fe5811;
					font-size: 24px;
				}
			}
		}

		.message-task {
			padding: 2rem 0 0 2rem;
			position: relative;

			.icon-user {
				background: #d9d9d9;
				border-radius: 50%;
				height: 4rem;
				left: 0;
				overflow: hidden;
				position: absolute;
				top: 0;
				width: 4rem;

				img {
					height: 100%;
					width: 100%;
					object-fit: cover;
				}
			}

			.meta-text,
			.comment-input {
				background: #282828;
				border-radius: 10px;
				padding: 2rem;
				color: #fff;
				font: 500 14px $inter;

				> * {
					color: inherit;
					font: inherit;
				}
			}
		}

		.reply-task {
			.reply-message {
				.icon-user {
					height: 44px;
					top: 2.2rem;
					width: 44px;
				}

				textarea.meta-text {
					background: #282828;
					border-radius: 10px;
					color: #ffffff;
					height: 54px;
					overflow: hidden;
					padding: 0.8rem 2rem;
					resize: none;
					width: 100%;
				}
			}

			.cta-row {
				padding-left: 2rem;

				.cta {
					background: #ff5811;
					color: #fff;
				}
			}
		}
	}

	.project-column {
		.project-list {
			max-height: initial;
			overflow: inherit;
			position: relative;

			&:before {
				background: linear-gradient(to bottom, rgba(17, 17, 17, 0) 0%, rgba(17, 17, 17, 1) 100%);
				bottom: 0;
				content: '';
				height: 100%;
				left: 0;
				pointer-events: none;
				position: absolute;
				user-select: none;
				width: 100%;
				z-index: 1;
			}
		}

		.meta-project {
			&.bor {
				border: 1px solid #fff;
				border-radius: 0.5rem;
				padding: 10px;

				.icon-wrap {
					flex: 0 0 32px;
					height: 32px;
					width: 32px;

					.icon {
						height: 20px;
						width: 20px;
					}
				}

				.copy {
					h2 {
						font-size: 12px;
					}

					p {
						font-size: 14px;
					}
				}

				+ .meta-project {
					margin-top: 1rem;
				}
			}
		}

		.more {
			a {
				background: #111;
				color: #ff5811;
				font: 700 12px/1 $inter;
				padding: 1rem;
				position: relative;
				z-index: 5;
			}
		}
	}

	.statuses-list {
		a,
		.select-status {
			@extend %clickable-tile;
			+ a,
			+ .select-status {
				margin-top: 12px;
			}
		}
		a {
			text-decoration: none;
		}
	}

	.templates-list {
		.template {
			@extend %clickable-tile;

			+ .template {
				margin-top: 12px;
			}
		}
	}
}

%clickable-tile {
	color: rgba(#fff, 0.5);
	cursor: pointer;
	font: 500 14px $inter;

	&.active,
	&:hover {
		color: #fff;
	}

	.form-check {
		min-height: initial;
	}

	.form-check-label {
		margin-top: 2px;
	}
}
