@extends('layout.app')
@section('title', 'Dashboard')
@section('content')

		<section class="client-header main-header">
			<div class="container-xxl">
				<hr class="mt-0 mb-4 border-white" />
				<div class="meta d-flex justify-content-between align-items-center">
					<div class="copy">
						<h1 class="text-white mb-0">
							Welcome to your brand hub,<br />
							<i>{{$user->name}}</i>
						</h1>
					</div>
					<div class="page-links">
						<span class="p me-2">Show Projects by:</span>
						<a href="{{ route('brand.projects.by.client') }}" class="btn btn-link {{ request()->routeIs('brand.projects.by.client') ? 'active' : '' }}">Client</a>
						<a href="{{ route('brand.dashboard') }}" class="btn btn-link {{ request()->routeIs('brand.dashboard') ? 'active' : '' }}">Phase</a>
					</div>
				</div>
			</div>
		</section>
		<section class="client-project pt-0">
			<div class="container-xxl">
				<hr class="mt-0 mb-4 border-white" />
				<div class="project-row overflow-x-auto d-flex">
					<div class="project-column mb-5">
						<h2 class="text-uppercase">DEFINE</h2>
						<hr class="mt-0 mb-4 border-white" />

                        @foreach($projectInDefinePhase as $definePhaseProject)
						<div class="project-list">
                             

							<div class="meta-project d-flex orange hover">
								<div class="icon-wrap d-flex align-items-center justify-content-center me-3">
									<div class="progress-bar" data-steps="{{$definePhaseProject->progress_percentage}}" style="--value: {{$definePhaseProject->progress_percentage}}"></div>
									<div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/define-project-icon.svg')}}" alt="" /></div>
									<div class="over">
										<h2 class="mb-0">{{$definePhaseProject->progress_percentage}}%</h2>
									</div>
								</div>
								<div class="copy">
									<h2 class="mb-1"><a href="{{ route('track-project', $definePhaseProject->id) }}">[{{$definePhaseProject->job_code}}] - PHASE 1/5</a></h2>
									<p class="text-white">{{$definePhaseProject->name}}</p>
								</div>
							</div>
							
						</div>
                        @endforeach
					</div>
					<div class="project-column mb-5">
						<h2 class="text-uppercase">CONTENT</h2>
						<hr class="mt-0 mb-4 border-white" />

                        @foreach ($projectInContentPhase as $contentPhaseProject )
                            
                        
						<div class="project-list">
							<div class="meta-project d-flex green hover">
								<div class="icon-wrap d-flex align-items-center justify-content-center me-3">
									<div class="progress-bar" data-steps="{{$contentPhaseProject->progress_percentage}}" style="--value: {{$contentPhaseProject->progress_percentage}}"></div>
									<div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/content-project-icon.svg')}}" alt="" /></div>
									<div class="over">
										<h2 class="mb-0">{{$contentPhaseProject->progress_percentage}}%</h2>
									</div>
								</div>
								<div class="copy">
									<h2 class="mb-1"><a href="{{ route('track-project', $contentPhaseProject->id) }}">[{{$contentPhaseProject->job_code}}] - PHASE 2/5</a></h2>
									<p class="text-white">{{$contentPhaseProject->name}}</p>
								</div>
							</div>
							
						</div>

                        @endforeach
					</div>
					<div class="project-column mb-5">
						<h2 class="text-uppercase">DESIGN/PRODUCTION</h2>
						<hr class="mt-0 mb-4 border-white" />

                        @foreach($projectInDesignPhase as $designPhaseProject)
						<div class="project-list">
							<div class="meta-project d-flex pink hover">
								<div class="icon-wrap d-flex align-items-center justify-content-center me-3">
									<div class="progress-bar" data-steps="{{$designPhaseProject->progress_percentage}}" style="--value: {{$designPhaseProject->progress_percentage}}"></div>
									<div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/design-project-icon.svg')}}" alt="" /></div>
									<div class="over">
										<h2 class="mb-0">{{$designPhaseProject->progress_percentage}}%</h2>
									</div>
								</div>
								<div class="copy">
									<h2 class="mb-1"><a href="{{ route('track-project', $designPhaseProject->id) }}">[{{$designPhaseProject->job_code}}] - PHASE 3/5</a></h2>
									<p class="text-white">{{$designPhaseProject->name}}</p>
								</div>
							</div>
						</div>
                        @endforeach
					</div>
					<div class="project-column mb-5">
						<h2 class="text-uppercase">CODE</h2>
						<hr class="mt-0 mb-4 border-white" />
                        @foreach($projectInCodePhase as $codePhaseProject)
						<div class="project-list">
							<div class="meta-project d-flex purple hover">
								<div class="icon-wrap d-flex align-items-center justify-content-center me-3">
									<div class="progress-bar" data-steps="{{$codePhaseProject->progress_percentage}}" style="--value: {{$codePhaseProject->progress_percentage}}"></div>
									<div class="icon d-flex align-items-center justify-content-center"><img src="{{asset('images/code-project-icon.svg')}}" alt="" /></div>
									<div class="over">
										<h2 class="mb-0">{{$codePhaseProject->progress_percentage}}%</h2>
									</div>
								</div>
								<div class="copy">
									<h2 class="mb-1"><a href="{{ route('track-project', $codePhaseProject->id) }}">[{{$codePhaseProject->job_code}}] - PHASE 4/5</a></h2>
									<p class="text-white">{{$codePhaseProject->name}}</p>
								</div>
							</div>
						</div>
                        @endforeach
					</div>
					<div class="project-column mb-5"> 
						<h2 class="text-uppercase">FINISH (DEPLOY/PRINT/PUBLISH)</h2>
						<hr class="mt-0 mb-4 border-white" />
                        @foreach($projectInDeployPhase as $deployPhaseProject)
						<div class="project-list">
							<div class="meta-project d-flex orange hover">
								<div class="icon-wrap d-flex align-items-center justify-content-center me-3">
									<div class="progress-bar" data-steps="{{$deployPhaseProject->progress_percentage}}" style="--value: {{$deployPhaseProject->progress_percentage}}"></div>
									<div class="icon d-flex align-items-center justify-content-center">
										
										@if($deployPhaseProject->timeline->type=="web")
										<img src="{{asset('images/deploy-project-icon.svg')}}" alt="" />

										@elseif($deployPhaseProject->timeline->type=="print"||$deployPhaseProject->timeline->type=="publication" )

										<img src="{{asset('images/print-project-icon.svg')}}" alt="" />

										@elseif($deployPhaseProject->timeline->type=="video"||$deployPhaseProject->timeline->type=="social_media" )
                                          <img src="{{asset('images/publishing-project-icon.svg')}}" alt="" />
										  @endif
									</div>
									<div class="over">
										<h2 class="mb-0">{{$deployPhaseProject->progress_percentage}}%</h2>
									</div>
								</div>
								<div class="copy">
									<h2 class="mb-1"><a href="{{ route('track-project', $deployPhaseProject->id) }}">[{{$deployPhaseProject->job_code}}] - PHASE 5/5</a></h2>
									<p class="text-white">{{$deployPhaseProject->name}}</p>
								</div>
							</div>
						</div>
                        @endforeach
					</div>
				</div>
			</div>
		</section>

@endsection