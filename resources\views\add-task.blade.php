@extends('layout.app')
@section('title', 'Add Task')
@section('content')
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('show-tasks', ['project_id' => request()->route('project_id')]) }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Tasks List</a>
            </div>
            @if(request()->route()->named('add-task'))
            <div class="meta d-flex">
                <h1 class="heavy text-white mb-0">New <i>Task</i></h1>
            </div>
            @endif
        </div>
    </section>

    <section class="client-project project-dashboard">
        <div class="container-xxl">
            <form class="row new-task-form" method="POST" action="{{ route('save-task', ['project_id'=>request()->route('project_id')]) }}">
            @csrf
            <div class="row projects-row">
                <div class="col-md-3 col-xl-2 project-column quick-links">
                    <h2 class="text-uppercase">SET STATUS</h2>
                    <hr class="mt-0 mb-4 border-white" />
                    <div class="statuses-list d-flex flex-column">
                        @foreach($statuses as $status)
                            <div class="select-status active form-check text-white">
                            <input type="radio" class="form-check-input @error('task_status') is-invalid @enderror" name="task_status" value="{{ $status->id }}" id="{{ $status->id }}" @checked(old('task_status') == $status->id)>
                            <label class="form-check-label" for="{{ $status->id }}">{{ $status->name }}</label>
                            </div>
                        @endforeach
                        @error('task_status')
                            <span class="invalid-feedback d-block">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                    <div>
                        
                    </div>
                </div>
                <div class="col-md-9 col-xl-10 project-column task-overview">
                    <div class="col-head align-items-center d-flex justify-content-between">
                        <h2 class="text-uppercase">COMPOSE YOUR MESSAGE</h2>
                    </div>
                    <hr class="mt-0 mb-4 border-white" />
                    
                        <div class="col-12 mb-4">
                            <input class="input-text border-0" type="text" name="task_name" value="{{ old('task_name') }}" placeholder="Add a task subject..." />
                            @error('task_name')
                                <span class="invalid-feedback d-block">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>
                        <div class="col-12">
                            <textarea class="textarea border-0" name="task_description" value="{{ old('task_description') }}" placeholder="Type your message here..."></textarea>
                            @error('task_description')
                                <span class="invalid-feedback d-block">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                        </div>
                        <div class="col-head col-12 mt-5">
                            <h2 class="text-uppercase">Assigned to</h2>
                            <hr class="mt-0 mb-4 border-white" />
                        </div>
                        <div class="col-12">
                            <div class="row">
                            @php 
                                $users = $project->users; 
                            @endphp

                            @if ($users && $users->count() > 0)
                                @foreach ($users as $user)
                                    <div class="col-md-3 col-xl-2 mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="task_assigned_to[]" value="{{ $user->id }}" id="{{ $user->id }}" />
                                            <label class="form-check-label" for="{{ $user->id }}">{{ $user->name }}</label>
                                        </div>
                                    </div>
                                @endforeach
                            @endif

                            </div>
                        </div>
                        <div class="col-12 cta-row d-flex mt-5">
                            <div class="btn-col d-flex">
                                <div class="upload-btn-wrapper">
                                    <button class="btn-upload text-uppercase text-white d-flex align-items-center"><i class="bi bi-upload me-2"></i> Upload a file</button>
                                    <input type="file" name="myfile" />
                                </div>
                                <div class="add-time ms-4" position="relative" onclick="openDatePicker(event)">
                                    <div class="meta-label d-flex align-items-center"><i class="bi bi-clock me-2"></i><span class="label-text">ADD DUE DATE</span></div>
                                    <input class="due-date date-picker" id="dueDate" type="date" name="due-date" onchange="updateLabel(event)" />
                                </div>
                            </div>
                            <div class="btn-col ms-auto">
                                <button class="cta mt-0" type="submit">ADD TASK</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
@endsection
  

@push('script')
    <script>
        $(document).ready(function(){

        });
    </script>
@endpush