mixin headPortal(h1, heavyH1=false, logo=false, projectMeta=false, backLink=false, pageLink=false, clientProjects=false, siteAnalytics=false)
    section.client-header(class=(heavyH1 ? 'main-header' : ''))
        .container-xxl
            hr.mt-0.mb-4.border-white
            if backLink
                .back-page
                    a.d-inline-flex.align-items-center.text-decoration-none(href='client-portal.html') #[img.me-2(src='images/back-arrow-icon.svg', alt='') ] #{backLink}
            .meta.d-flex.justify-content-between.align-items-center
                .copy
                    h1.text-white.mb-0 !{h1}

                    if projectMeta
                        .page-links.mt-3
                            each link in ['Track Project', 'Message Center', 'File Upload', 'Billing History']
                                //- a(href=(link === 'Track Project' ? '/' : ''), class=(link === mainPage ? 'active' : ''))=link
                                a(href=`${link.replace(' ', '-').toLowerCase()}.html`, class=(link === mainPage ? 'active' : ''))=link

                    if clientProjects
                        .page-links.mt-3
                            each link in ['Active Projects (2)', 'Voyager Projects (1)', 'Archived Projects (12)']
                                a(href='#', class=(clientPage === link.replace(/[^a-zA-Z\s]/g, "").trim() ? 'active' : ''))=link

                    if siteAnalytics
                        .page-links.mt-3
                            each link, index in ['Site Analytics', 'Plan Details']
                                a(href=(index === 0 ? 'essential-metrics.html' : `${link.replaceAll(' ', '-').toLowerCase()}.html`), class=(mainPage === link ? 'active' : ''))=link

                if pageLink
                    .page-links
                        span.p.me-2 Show Projects by:
                        each link in ['Brand', 'Phase']
                            a(href=(link === 'Brand' ? '/' : 'project-phase.html'), class=(link === clientPage ? 'active' : ''))=link

                if logo
                    .logo
                        a(href='#') #[img(src='images/bear-tide-oysters-logo.png', alt='', width='72', height='80')]