body {
	&.loading {
		height: 100%;
		overflow: hidden;
		position: fixed;
		width: 100%;

		&:before {
			background: $siteBlack;
			content: '';
			height: 100%;
			left: 0;
			position: absolute;
			top: 0;
			width: 100%;
			z-index: 10000;
		}
	}
}

.loader {
	height: 60px;
	left: 50%;
	position: fixed;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 200px;
	z-index: 15000;

	&:after {
		color: $white;
		content: 'LOADING';
		font: 18px $inter;
		left: 50%;
		position: absolute;
		top: 125%;
		transform: translateX(-50%);
	}

	.circle,
	.shadow {
		background-color: $white;
		border-radius: 50%;
		left: 15%;
		position: absolute;
		width: 20px;

		&:nth-child(2),
		&:nth-child(4) {
			animation-delay: 0.2s;
			left: 45%;
		}

		&:nth-child(3),
		&:nth-child(5) {
			animation-delay: 0.3s;
			left: auto;
			right: 15%;
		}
	}

	.circle {
		animation: circlekey 0.5s alternate infinite ease;
		height: 20px;
		transform-origin: 50%;
	}

	.shadow {
		animation: shadowkey 0.5s alternate infinite ease;
		background-color: $black;
		filter: blur(1px);
		height: 4px;
		top: 62px;
		transform-origin: 50%;
		z-index: -1;
	}
}

@keyframes circlekey {
	0% {
		border-radius: 50px 50px 25px 25px;
		height: 5px;
		top: 60px;
		transform: scaleX(1.7);
	}

	40% {
		border-radius: 50%;
		height: 20px;
		transform: scaleX(1);
	}

	100% {
		top: 0%;
	}
}

@keyframes shadowkey {
	0% {
		transform: scaleX(1.5);
	}

	40% {
		opacity: 0.7;
		transform: scaleX(1);
	}

	100% {
		opacity: 0.4;
		transform: scaleX(0.2);
	}
}
