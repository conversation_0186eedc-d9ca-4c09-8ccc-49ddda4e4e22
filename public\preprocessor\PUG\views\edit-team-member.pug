- let pageName = 'Edit Team Member';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/fieldForms.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headTeam.pug
        +headTeam('Drew <i><PERSON><PERSON><PERSON></i>', 'VIEW MY PROJECTS')

    section.team-dashboard.pt-0.pb-5
        .container-xxl
            hr.mt-0.mb-4.border-white
            form.edit-member.row(action="")
                .col-md-6.mb-4
                    +inputFieldLabel('Email', 'email', '', '<EMAIL>', true)
                .col-md-6.mb-4
                    +inputFieldLabel('Password', 'password', '',  '.........', true)
                .col-md-6.mb-4
                    +inputColor('Set User Color', '#FE5811')

                .col-12.cta-row.text-center
                    button.cta.orange SAVE CHANGES

    include ../partials/footer.pug
    +footer(true)