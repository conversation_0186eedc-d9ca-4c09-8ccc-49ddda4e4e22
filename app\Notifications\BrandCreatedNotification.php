<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;


class BrandCreatedNotification extends Notification //implements ShouldQueue we will set it when queues will working
{
    use Queueable;

    protected $brand;

    protected $randomPassword;

    public function __construct($brand, $randomPassword)
    {
        $this->brand = $brand;
        $this->randomPassword = $randomPassword;
    }

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $url = route('login');

        return (new MailMessage)
            ->subject('New Enterprise Created: ' . $this->brand->name)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('You have been added as a new Enterprise named ' . $this->brand->name . '.')
            ->line('Email: ' . $this->brand->email)
            ->line('Password: ' . $this->randomPassword)
            ->action('Login here', $url)
            ->line('Thank you for using our application!');
    }
}
