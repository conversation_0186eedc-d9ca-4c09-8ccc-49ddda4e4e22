<header class="header" id="header">
    <div class="align-items-center container-xxl d-flex position-relative header_float">
        <div class="brand" id="brand">
            <a href="/"><img class="white" src="{{ asset('images/logo.svg') }}" alt="" height="44" width="120" /></a>
        </div>
        <h1 class="d-none d-lg-block mb-0 text-uppercase">
            @yield('page_heading', 'WE LOVE PEOPLE') <sup>&TRADE;</sup>
        </h1>
        <nav class="site-nav ms-auto d-flex" id="siteName">
            @if(auth()->check())
            <div class="user-pic me-3"><img src="{{ set_user_image(auth()->user()->profile_image) }}" alt="" /></div>
            @endif
            <div class="user-nav position-relative">
                <div class="nav_call align-items-center d-flex flex-column justify-content-center" id="navCall"><span></span><span></span></div>
                <div class="drop-nav d-none flex-column" id="dropNav">
                    <a class="link d-flex align-items-center text-decoration-none text-uppercase text-nowrap p-3" href="#">
                        <span class="icon me-2"><i class="bi bi-telephone"></i></span> COnTaCT
                    </a>
                    <a class="link d-flex align-items-center text-decoration-none text-uppercase text-nowrap p-3" href="/login">
                        <span class="icon me-2"><i class="bi bi-person-circle"></i></span> CLIeNT LOGIN
                    </a>
                    <form action="{{ route('logout') }}" method="post" class="m-0">
                        @csrf
                        @method('POST')
                        <button type="submit" class="link d-flex align-items-center text-uppercase text-nowrap p-3 w-100 bg-transparent border-0">
                            <span class="icon me-2"><i class="bi bi-box-arrow-right"></i></span>Log out
                        </button>
                    </form>
                </div>
            </div>
        </nav>
    </div>
</header>

@push('script')
<script>
    const navCall = document.getElementById("navCall");
    const dropNav = document.getElementById("dropNav");
    
    if (navCall && dropNav) {
        navCall.addEventListener("click", function () {
            dropNav.classList.toggle("d-none");
            dropNav.classList.toggle("d-flex");
        });
    }
    
    document.addEventListener("click", function(event) {
        const isClickInsideNav = navCall.contains(event.target);
        const isClickInsideDropdown = dropNav.contains(event.target);
        
        if (!isClickInsideNav && !isClickInsideDropdown) {
            dropNav.classList.add("d-none");
            dropNav.classList.remove("d-flex");
        }
    });
</script>
@endpush