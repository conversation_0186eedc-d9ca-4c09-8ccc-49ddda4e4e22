
<?php $__env->startSection('title', 'Edit Team Member'); ?>
<?php $__env->startSection('content'); ?>



<?php if(session('success')): ?>
    <script>
        successToast(<?php echo json_encode(session('success'), 15, 512) ?>);
    </script>
<?php endif; ?>
<?php if(session('error')): ?>
    <script>
        errorToast(<?php echo json_encode(session('error'), 15, 512) ?>);
    </script>
<?php endif; ?>
<?php if($errors->any()): ?>
    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <script>
            errorToast(<?php echo json_encode($error, 15, 512) ?>);
        </script>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>



<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            <?php if(admin_superadmin_permissions()): ?>
                <a class="d-inline-flex align-items-center text-decoration-none" href="<?php echo e(route('teams')); ?>"><img class="me-2" src="<?php echo e(asset('images/back-arrow-icon.svg')); ?>" alt="" /> Back to Teams List</a>
            <?php else: ?>
                <a class="d-inline-flex align-items-center text-decoration-none" href="<?php echo e(route('teams')); ?>"><img class="me-2" src="<?php echo e(asset('images/back-arrow-icon.svg')); ?>" alt="" /> Back to Team Portal</a>
            <?php endif; ?>
        </div>
        <div class="meta d-flex">
            <h1 class="heavy text-white mb-0">Editing <i><?php echo e($user->name); ?></i></h1>
            <div class="add-task ms-auto d-flex">
                <a class="cta d-inline-flex align-items-center text-uppercase text-decoration-none border-1 py-2 px-4 mt-0" href="#"
                    >See Projects<span class="img ms-2">
                        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.6992 0.573242V20.1846" stroke="#ff5811" stroke-width="1.5"></path>
                            <path d="M20.5049 10.3789L0.893555 10.3789" stroke="#ff5811" stroke-width="1.5"></path></svg></span
                ></a>
            </div>
        </div>
    </div>
</section>
<section class="team-dashboard pt-0 pb-5">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <form class="edit-member row" action="<?php echo e(route('update-team-member', ['team_member_id' => $user->id] )); ?>" method="POST" enctype="multipart/form-data" id="update-team-member-profile" >
            <?php echo csrf_field(); ?>
            <?php echo method_field('put'); ?>
            <input type="hidden" name="page_name" value="edit_team_member">
            <input type="hidden" name="user_id" value="<?php echo e($user->id); ?>">
            <input type="hidden" name="name" value="<?php echo e($user->name); ?>">
            <input type="hidden" name="color_code" value="<?php echo e($user->color_code); ?>">
            <input type="hidden" name="email_input_disabled" value="true">
            <div class="col-md-auto upload-profile-icon">
                <div class="pic" id="pic"><img src="<?php echo e(set_user_image($user->profile_image)); ?>" alt="" height="60" width="60"  /></div>
                <input type="file" name="profile_image" id="profile-image" class="d-none">
                <a href="#" class="d-inline-flex align-items-center text-decoration-none mt-2" id="chnage-image" ><span style="color: #ff4c00;">Change Image</span></a>
            </div>
            <div class="col-md">
                <div class="row">
                    
                    
                    
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="email">Email</label>
                        <div class="edit-icon" id="email">
                            <span class="icon d-flex"><img class="me-2" src="<?php echo e(asset('images/input-edit-icon.svg')); ?>" alt="" /> Edit</span>
                            <input class="form-control border-0 non-editable" type="email" id="email" name="email" placeholder="" value="<?php echo e(old('email', $user->email)); ?>" readonly/>                        
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="password">Password</label>
                        <div class="edit-icon" id="password">
                            <span class="icon d-flex"><img class="me-2" src="<?php echo e(asset('images/input-edit-icon.svg')); ?>" alt="" /> Edit</span>
                            <input class="form-control border-0 non-editable" type="password" id="password" name="password" placeholder="**************" value="" readonly/>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label mb-2" for="setTeam">Set Team</label>
                        <div class="select-wrap">
                            <select class="form-select border-0" name="setTeam" id="setTeam">
                                <?php
                                    $userTeamId = $user->teams->isNotEmpty() ? $user->teams[0]->id : null; // check if user is linked in any team. If yes, then fetch its id to match with team id
                                ?>

                                <option value="">Select Team</option>
                                <?php $__currentLoopData = $teams; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($team->id); ?>" <?php echo e(old('setTeam', $userTeamId) == $team->id ? 'selected' : ''); ?>><?php echo e($team->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="select-icon"><img src="<?php echo e(asset('images/down-arrow-orange.svg')); ?>" alt="" width="18" height="10" /></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label mb-2" for="setAccessLevel">Set Access Level</label>
                        <div class="select-wrap">
                            <select class="form-select border-0" name="setAccessLevel" id="setAccessLevel">
                                <option value="">Select</option>
                                <?php $__currentLoopData = $accessLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $access_level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($access_level->id); ?>"
                                        <?php echo e(old('setAccessLevel', $user->access_level_id) == $access_level->id ? 'selected' : ''); ?>>
                                        <?php echo e($access_level->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="select-icon"><img src="<?php echo e(asset('images/down-arrow-orange.svg')); ?>" alt="" width="18" height="10" /></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label mb-2" for="team">Team Member Location</label>
                        <div class="select-wrap">
                            <select class="form-select border-0" name="team" id="team">
                                <option value="US" <?php echo e(isset($resource) && $resource->team === 'US' ? 'selected' : ''); ?>>US</option>
                                <option value="India" <?php echo e(isset($resource) && $resource->team === 'India' ? 'selected' : ''); ?>>India</option>
                            </select>
                            <div class="select-icon"><img src="<?php echo e(asset('images/down-arrow-orange.svg')); ?>" alt="" width="18" height="10" /></div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="pm_hours">PM Hours/Week</label>
                        <div class="edit-icon" id="pm_hours">
                            <span class="icon d-flex"><img class="me-2" src="<?php echo e(asset('images/input-edit-icon.svg')); ?>" alt="" /> Edit</span>
                            <input class="form-control border-0 non-editable" type="number" id="pm_hours" name="pm_hours" placeholder="0" value="<?php echo e(old('pm_hours', isset($resource) ? $resource->pm_hours : 0)); ?>" min="0" readonly/>                        
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="designer_hours">Designer Hours/Week</label>
                        <div class="edit-icon" id="designer_hours">
                            <span class="icon d-flex"><img class="me-2" src="<?php echo e(asset('images/input-edit-icon.svg')); ?>" alt="" /> Edit</span>
                            <input class="form-control border-0 non-editable" type="number" id="designer_hours" name="designer_hours" placeholder="0" value="<?php echo e(old('designer_hours', isset($resource) ? $resource->designer_hours : 0)); ?>" min="0" readonly/>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="developer_hours">Developer Hours/Week</label>
                        <div class="edit-icon" id="developer_hours">
                            <span class="icon d-flex"><img class="me-2" src="<?php echo e(asset('images/input-edit-icon.svg')); ?>" alt="" /> Edit</span>
                            <input class="form-control border-0 non-editable" type="number" id="developer_hours" name="developer_hours" placeholder="0" value="<?php echo e(old('developer_hours', isset($resource) ? $resource->developer_hours : 0)); ?>" min="0" readonly/>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <label class="form-label" for="cs_hours">CS Hours/Week</label>
                        <div class="edit-icon" id="cs_hours">
                            <span class="icon d-flex"><img class="me-2" src="<?php echo e(asset('images/input-edit-icon.svg')); ?>" alt="" /> Edit</span>
                            <input class="form-control border-0 non-editable" type="number" id="cs_hours" name="cs_hours" placeholder="0" value="<?php echo e(old('cs_hours', isset($resource) ? $resource->cs_hours : 0)); ?>" min="0" readonly/>                        
                        </div>
                    </div>
                    <div class="col-12 cta-row d-flex">
                        <button class="cta dark">ARCHIVE TEAM MEMBER</button>
                        <button class="cta dark mx-4" id="delete-team-member" data-user-id="<?php echo e($user->id); ?>">DELETE TEAM MEMBER</button>
                        <button class="cta orange ms-auto">SAVE CHANGES</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</section>
<style>
    .edit-icon:focus{
        outline:none !important;
        border:none !important;
        user-select: none !important;
    }
   .non-editable, .non-editable:focus{
        cursor: not-allowed;
        user-select: none !important;
    }
    .editable, .editable:focus{
        cursor: text;
    }
   .non-editable, .non-editable:focus, .editable, .editable:focus {
        color: white !important;
        background-color: #282828 !important;
        outline: none !important;
        border: none !important;
        padding-right:100px !important;
    }
</style>
<script>
    $(document).ready(function() {

        //IMP - disable inputs unless edit button is clicked
        $('.edit-icon > span').on('click', function(event){
            // 'this' inside this function refers to the <span> element that was clicked.

            // To get the ID of the parent div.edit-icon:
            var $id = $(this).parent('.edit-icon').attr('id');

            // Alternative (more robust if structure changes slightly):
            // var $id = $(this).closest('.edit-icon').attr('id');


            // Now, target the input using the retrieved ID
            $('input#' + $id).removeClass('non-editable').addClass('editable').removeAttr('readonly');
            
            // This line seems to be for some form state tracking.
            // Ensure 'email_input_disabled' is the correct name for the input you want to update.
            // If you have multiple sections, you might need a more dynamic way to select this input too.
            $('input[name="email_input_disabled"]').val("false");
        });

        $('#delete-team-member').on('click', function(e) {
            e.preventDefault();
            var userId = $(this).data('user-id');
            var url = "<?php echo e(route('delete-team-member', ':team-member-id')); ?>";
            url = url.replace(':team-member-id', userId);
            if (confirm("Are you sure you want to delete this team member?")) {
                window.location.href = url;
            }
        });

        $('#pic').on('click', function() {
            console.log('clicked');
            $('#profile-image').click();
        });

        let chnage_image = document.getElementById('chnage-image');
        chnage_image.addEventListener('click', function (e) {
            e.preventDefault();
            document.getElementById('profile-image').click();
        });
        let upload_input = document.querySelector('.upload-profile-icon input[type="file"]');
        let upload_icon = document.querySelector('.upload-profile-icon img');
        upload_input.addEventListener('change', function () {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    upload_icon.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });


        const form_id = 'update-team-member-profile';
        let args = {
                rules : {
                    '#email': GlobalFormValidator.validators.required,
                    '#setTeam': GlobalFormValidator.validators.required,
                    '#setAccessLevel': GlobalFormValidator.validators.required,
                    '#team': GlobalFormValidator.validators.required,
                },
                messages: {
                    '#email': "Please add a Email Address",
                    '#setTeam': "Please Assign a Team",
                    '#setAccessLevel': "Please Assign a Access Level",
                    '#team': "Please select a Team Location",
                },
                onSuccess: (form) => {
                    form.submit();
                }
            }
        let Validator = new GlobalFormValidator(form_id, args);
        Validator.initialize();
    });
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layout.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\SGF-Portal\resources\views/team/edit_team_member.blade.php ENDPATH**/ ?>