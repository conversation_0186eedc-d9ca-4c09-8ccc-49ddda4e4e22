<div>
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('client.dashboard') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Client Portal
                </a>
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">All, <i>Projects</i></h1>
                    <div class="page-links mt-3">
                        <a href="#" wire:click.prevent="$set('filter', 'active')" class="{{ $filter === 'active' ? 'active' : '' }}">
                            Active Projects ({{ $counts['active'] }})
                        </a>
                        <a href="#" wire:click.prevent="$set('filter', 'voyager')" class="{{ $filter === 'voyager' ? 'active' : '' }}">
                            Voyager Projects ({{ $counts['voyager'] }})
                        </a>
                        <a href="#" wire:click.prevent="$set('filter', 'archived')" class="{{ $filter === 'archived' ? 'active' : '' }}">
                            Archived Projects ({{ $counts['archived'] }})
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="client-project pt-0">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="projects-list-table">
                <div class="head d-flex align-items-center justify-content-between">
                    <div class="years">
                        <a href="#" wire:click.prevent="$set('year', 'all')" class="{{ $year === 'all' ? 'active' : '' }}">All</a>
                        @php
                            $currentYear = now()->year;
                            $startYear = 2023;
                        @endphp
                        @for($year = $currentYear; $year >= $startYear; $year--)
                            <a href="#" wire:click.prevent="$set('year', '{{ $year }}')" class="{{ $year == $this->year ? 'active' : '' }}">{{ $year }}</a>
                        @endfor
                    </div>
                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $projects->lastPage(); $i++)
                            <a wire:click="gotoPage({{ $i }})" href="javascript:void(0)" class="{{ $projects->currentPage() == $i ? 'active' : '' }}">{{ $i }}</a>
                        @endfor
                    </div>
                </div>

                <div wire:loading.remove wire:target="filter, year, search" class="projects-list">
                    @forelse($projects as $project)
                        <div class="meta-project d-flex orange {{ $project->archived ? 'archived' : ($project->timeline?->type === 'voyager' ? 'voyager' : 'active') }}">
                            <div class="icon d-flex align-items-center justify-content-center checked">
                                <img src="{{ asset('images/checked-project-icon.svg') }}" alt="" />
                            </div>
                            <div class="copy flex-grow-1 d-flex align-items-end justify-content-between">
                                <div class="text">
                                    <h2 class="mb-1">
                                        <a href="">{{ $project->job_code }}</a>
                                    </h2>
                                    <p class="text-white">{{ $project->name }}</p>
                                </div>
                                <div class="cta-row">
                                    @if($project->timeline?->type === 'voyager')
                                        <a href="">Site Analytics</a>
                                    @else
                                        <a href="">Project Details</a>
                                    @endif
                                    <a href="">Billing History</a>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center text-white py-5">
                            <p>No projects found.</p>
                        </div>
                    @endforelse
                </div>

                <div wire:loading wire:target="filter, year, search" class="text-center text-white py-5">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <div class="d-flex justify-content-end my-5">
                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $projects->lastPage(); $i++)
                            <a wire:click="gotoPage({{ $i }})" href="javascript:void(0)" class="{{ $projects->currentPage() == $i ? 'active' : '' }}">{{ $i }}</a>
                        @endfor
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-4 border-white" />
        </div>
    </section>
</div>
	