- let pageName = 'Client Portal';
- let mainPage = 'Message Center';
- let pageTitle = `${pageName}: Message`;

mixin statusSection(title, subtitle, linkText=false, selector=false)
    .text(class=(selector ? 'text-md-end mt-4 mt-md-0' : ''))
        h2.text-uppercase.mb-2=title
        h3.mb-0(class=(linkText ? 'd-flex align-items-center' : '')) #{subtitle}
            if linkText
                a.circle-icon.ms-2(href='#')=linkText

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)
    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header

        include ../layouts/headPortal.pug
        +headPortal('Project: <i>[SP-003-25] Sample Project 3</i>', false, true, true, 'Back to Client Portal')

        section.client-project.bg-white
            .container-xxl
                .go-back
                    a.d-inline-flex.align-items-center.text-decoration-none(href="message-center.html") #[img.me-2(src="images/back-arrow-icon.svg", alt="")] Back to Message Center
                .status.d-flex.justify-content-between
                    +statusSection('Thread', 'You’re six days behind schedule, buddy.')

        section.bg-white.pb-5
            form.container-xxl.pb-5(action="")
                .message-box.d-flex
                    .pic
                        img(src='images/drew-pic.jpg', alt='')
                    .message-wrap.d-flex
                        .message
                            .copy
                                .meta.d-flex.mb-3
                                    a.user.me-4(href="#") Drew M.
                                    span.date 12/12/25
                                p Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam viverra dolor eget velit suscipit dapibus. Suspendisse posuere elit ut arcu facilisis semper. Nunc accumsan ut justo sed sollicitudin. Fusce vulputate, nibh eget aliquam interdum, ipsum metus tempor nisl, sit amet cursus neque.dapibus. Suspendisse posuere elit ut arcu facilaccumsan ut justo sed Fusce vulputate, nibh eget aliquam interdum, ipsum metus tempor nisl, sit amet cursus neque.
                                p Aenean eu nisi dignissim, semper lectus in, viverra nunc. Curabitur venenatis diam ipsum, vitae semper mauris facilisis et. Duis ultrices neque in mattis vestibulum. Mauris congue risus arcu, ac luctus sem fermentum at. Aenean blandit mollis odio, at placerat risus ornare eget. Mauris nec magna nec justo laoreet posuere.

                .message-box.reply.d-flex
                    .pic
                    .message-wrap.d-flex.flex-grow-1
                        .message.flex-fill.d-flex.align-items-center
                            .copy
                                p Add reply...

                .message-box.upload.d-flex.flex-column.mt-3
                    .upload-row
                        p Your comment will be sent to Drew McKenna, Sally McCarty and Client #[a(href="#") (#[span.text-decoration-underline change])]
                    .cta-row.d-flex.justify-content-between.mt-4
                        .upload-btn-wrapper
                            button.btn-upload.text-uppercase.d-flex.align-items-center #[i.bi.bi-upload.me-2] Upload a file
                            input(type="file", name="myfile")
                        button.cta.text-uppercase.mt-0(type="submit") ADD COMMENT

        include ../partials/footer.pug
        +footer()