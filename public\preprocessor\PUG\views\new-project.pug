- let pageName = 'New Project';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = pageName;

include ../layouts/svgIcon.pug
include ../layouts/fieldForms.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('New <i>Project</i>', 'Back to Team Portal')

        section.client-project.project-dashboard
            .container-xxl
                form.row.projects-row(action="")
                    .col-md-3.col-xl-2.project-column.templates-links
                        h2.text-uppercase Job Types
                        hr.mt-0.mb-4.border-white
                        .templates-list.d-flex.flex-column
                            each template, index in ['SSGF Project', 'ACOMSGF Project', 'CA Group Project']
                                .template.d-flex
                                    +formCheck(template, 'radio', 'jobType')

                    .col-md-9.col-xl-10.project-column.task-overview
                        .col-head.align-items-center.d-flex.justify-content-between
                            h2.text-uppercase SET UP NEW PROJECT
                        hr.mt-0.mb-4.border-white

                        .row.form-wrap
                            .col-12.mb-4
                                +selectField(['SELECT CLIENT', 'lorem', 'Ipsum'])
                            .col-md-4.mb-4
                                +selectField(['JOB CODE', 'lorem', 'Ipsum'])
                            .col-md-8.mb-4
                                +inputField('projectTitle', 'text', 'Add a project title...')
                            .col-head.col-12.mt-5
                                h2.text-uppercase PROJECT TIMELINE TYPE
                                hr.mt-0.mb-4.border-white
                            .col-12
                                .row
                                    each check in ['Web', 'Print', 'Video', 'Quick Turn', 'Voyager', 'Social Media', 'Other']
                                        .col-md-3.mb-3
                                            +formCheck(check)
                            .col-12.mt-5
                                //- img.w-100(src="images/timeline-ribbon.svg", alt="")
                                .ribbon.d-flex.align-items-center.justify-content-between
                                    span.dot
                                    span.bar.orange
                                    span.bar.second.green
                                    span.bar.pink
                                    span.bar.purple
                                    span.bar.orange
                                    span.dot
                            .col-head.col-12.mt-5
                                h2.text-white Set Timeline
                                mixin metaTimelineRow(color, title, duration)
                                    .meta-row.d-flex.mt-3(class=color)
                                        .logo.d-flex.align-items-center.justify-content-center.rounded-circle #[img(src=`images/${title}-project-icon.svg`, alt='')]
                                        .wrap-meta.ms-3.d-flex.flex-grow-1.rounded-2
                                            .title.d-flex.align-items-center.justify-content-end=title.toUpperCase()
                                            .duration.col-meta.align-self-center.text-center #[strong #[a(href="#") Set Duration]]
                                            .target.col-meta.align-self-center.text-center Suggested Duration: #{target}
                                            .status.col-meta.align-self-center.text-center.pt-0 #[strong #[a(href="#") Set Target]]
                                .timeline-set
                                    +metaTimelineRow('orange', 'define', '1 Week')
                                    +metaTimelineRow('green', 'content', '3-4 Weeks')
                                    +metaTimelineRow('pink', 'design', '4-8 Weeks')
                                    +metaTimelineRow('purple', 'code', '4-8 Weeks')

                            .col-head.col-12.mt-5
                                h2.text-uppercase CONTACTS & BILLING
                                hr.mt-0.mb-4.border-white
                            .col-12.mt-4
                                h2.text-white Add Contacts
                            .col-12.mt-4
                                +inputFieldIcon('email', 'email', 'email')
                            .col-12.mt-4
                                h2.mt-4.text-white Add Harvest Estimate Link
                            .col-12.mt-4
                                +inputFieldIcon('harvest', 'harvest')
                            .col-12.mt-4
                                h2.mt-4.text-white Invoice Schedule
                                .row.mt-4
                                    each check in ['By Phase', 'By Date', 'Reoccurring', 'Other']
                                        .col-md-3.mb-3
                                            +formCheck(check)
                            .col-head.col-12.mt-5
                                h2.text-uppercase SEND KICKOFF MESSAGE (OPTIONAL)
                                hr.mt-0.mb-4.border-white
                                .row
                                    each check in ['Yes', 'No']
                                        .col-md-3.mb-3
                                            +formCheck(check, 'radio', 'sendMessage')
                            .col-12.mt-4
                                +inputField('projectTitle', 'text', 'Add a message title here...')
                            .col-12.mt-4
                                textarea.textarea.border-0(placeholder="Type your message here...")
                                .form-text Your comment will be sent to Drew McKenna, Sally McCarthy and Client #[a(href="#") (change)] and appear in Message Center
                            .col-12.mt-5
                                .upload-btn-wrapper
                                    button.btn-upload.text-uppercase.text-white.d-flex.align-items-center #[i.bi.bi-upload.me-2] Upload a file
                                    input(type="file", name="myfile")
                            .col-12.mt-5
                                h2.text-uppercase Assign to
                                hr.mt-0.mb-4.border-white
                            .col-12
                                .row
                                    mixin formCheck(label)
                                        - let forLabel = label.replace(/[^a-zA-Z0-9 ]/g, '').split(' ').map((word, index) => index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1)).join('')
                                        .form-check
                                            input.form-check-input(type="checkbox", value="", id=forLabel)
                                            label.form-check-label(for=forLabel)= label

                                    each check in ['Design Team', 'Mike M.', 'Robert M.', 'Megan J.', 'Cynde S.','Dev Team', 'Drew M.', 'Sally M.', 'Jeff C.', 'Erin L.', 'Gursewak', 'Aiden M.', 'Jesse R.', 'Ali D.', 'Rob S.']
                                        .col-md-3.col-xl-2.mb-3
                                            +formCheck(check)
                            .col-12.mt-5.text-center
                                button.cta.text-uppercase.mt-0(type="submit") CREATE PROJECT

        include ../partials/footer.pug
        +footer(true)