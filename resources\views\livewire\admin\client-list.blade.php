<div>
    <style>
        /* Alphabet filter styling */
        .alphabet-letter {
            border-radius: 4px;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #fff;
            font-weight: 500;
            position: relative;
        }
        
        .alphabet-letter:hover {
            background-color: rgba(255, 102, 0, 0.3);
            color: #fff;
        }
        
        .alphabet-active {
            background-color: #ff6600;
            color: #fff;
            box-shadow: 0 0 10px rgba(255, 102, 0, 0.5);
        }
        
        .loading-blur {
            opacity: 0.6;
            filter: blur(1px);
            pointer-events: none;
        }
        
        .disabled-letter {
            opacity: 0.3;
            cursor: default;
            pointer-events: none;
        }
        
        .letter-count {
            font-size: 0.7em;
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: rgba(255, 102, 0, 0.8);
            border-radius: 50%;
            padding: 1px 5px;
            min-width: 18px;
            text-align: center;
        }
        
        /* Add loading indicator for filtered content */
        .client-list-container {
            position: relative;
            min-height: 200px;
        }
        
        .client-list-container.loading::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(3px);
            z-index: 10;
        }
    </style>
    
    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-dashboard') }}">
                    <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Team Portal
                </a>
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">All, <i>Clients</i></h1>
                </div>
                <div class="sort-controls">
                    <div class="d-flex align-items-center">
                        <span class="text-white me-2">Sort by:</span>
                        <a wire:click="sortBy('name')" href="javascript:void(0)" class="sort-link me-3 {{ $sortColumn === 'name' ? 'text-orange' : 'text-white' }}">
                            Name
                            @if ($sortColumn === 'name')
                                <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                            @endif
                        </a>
                        <a wire:click="sortBy('created_at')" href="javascript:void(0)" class="sort-link {{ $sortColumn === 'created_at' ? 'text-orange' : 'text-white' }}">
                            Date Added
                            @if ($sortColumn === 'created_at')
                                <i class="fa fa-sort-{{ $sortDirection === 'asc' ? 'up' : 'down' }}"></i>
                            @endif
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="client-project pt-0">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="projects-list-table">
                <div class="head d-flex align-items-center justify-content-between flex-wrap">
                    <div class="years d-flex mb-3 mb-md-0">
                        <a wire:click="$set('year', 'all')" href="javascript:void(0)" class="{{ $year === 'all' ? 'active' : '' }}">
                            All
                        </a>
                        @foreach ($years as $y)
                            <a wire:click="$set('year', '{{ $y }}')" href="javascript:void(0)" class="{{ $year == $y ? 'active' : '' }}">
                                {{ $y }}
                            </a>
                        @endforeach
                    </div>

                    <div class="alphabet d-flex flex-wrap">
                        @foreach (range('A', 'Z') as $letter)
                            @php
                                $letterCount = $letterCounts[$letter] ?? 0;
                                $hasClients = $letterCount > 0;
                            @endphp
                            <a wire:click="{{ $hasClients ? '$set(\'alphabet\', \''.$letter.'\')' : 'null' }}" 
                               wire:loading.class="loading-blur"
                               href="javascript:void(0)" 
                               class="alphabet-letter me-2 mb-2 px-2 py-1 {{ $alphabet === $letter ? 'alphabet-active' : '' }} {{ !$hasClients ? 'disabled-letter' : '' }}">
                                {{ $letter }}
                                @if($letterCount > 0)
                                    <span class="letter-count">{{ $letterCount }}</span>
                                @endif
                            </a>
                        @endforeach
                        <a wire:click="$set('alphabet', null)" 
                           wire:loading.class="loading-blur"
                           href="javascript:void(0)" 
                           class="alphabet-letter me-2 mb-2 px-2 py-1 {{ is_null($alphabet) ? 'alphabet-active' : '' }}">
                            All
                        </a>
                    </div>
                </div>

                <div class="pages-top d-flex justify-content-end mt-3 mb-4">
                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $clients->lastPage(); $i++)
                            <a wire:click="gotoPage({{ $i }})" href="javascript:void(0)" class="{{ $clients->currentPage() == $i ? 'active' : '' }}">
                                {{ $i }}
                            </a>
                        @endfor
                    </div>
                </div>

                <div class="client-list-container" wire:loading.class="loading">
                @forelse ($clients as $client)
                    <div class="meta-project d-flex orange">
                        <div class="icon d-flex align-items-center justify-content-center checked">
                            <img src="{{ set_user_image($client->logo) }}" alt="" style="height: 100%; width: 100%; border-radius: 50%;" />
                        </div>
                        <div class="copy flex-grow-1 d-flex align-items-end justify-content-between">
                            <div class="text">
                                <h2 class="mb-1"><a href="{{ route('edit-client', ['client_id' => $client->id]) }}">[{{ $client->job_code }}]</a></h2>
                                <p class="text-white">{{ $client->name }}</p>
                            </div>
                            <div class="cta-row">
                                <a href="{{ route('edit-client', ['client_id' => $client->id]) }}">Edit Client</a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="text-white py-4">No clients found for selected filters.</div>
                @endforelse
                </div>

                <div class="d-flex justify-content-end my-5">
                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $clients->lastPage(); $i++)
                            <a wire:click="gotoPage({{ $i }})" href="javascript:void(0)" class="{{ $clients->currentPage() == $i ? 'active' : '' }}">
                                {{ $i }}
                            </a>
                        @endfor
                    </div>
                </div>
            </div>
            <hr class="mt-0 mb-4 border-white" />
        </div>
    </section>
    <section class="ask-question">
        <div class="container-xxl">
            <div class="d-flex align-items-center justify-content-between pb-4 flex-column flex-md-row">
                <div class="head mb-3 mb-md-0">
                    <h2 class="mb-0">
                        Have a question?<br />
                        <i>We're here to help.</i>
                    </h2>
                </div>
                <div class="cta-row">
                    <a class="cta link text-decoration-none text-white" href="#">GET EMAIL UPDATES</a>
                    <a class="cta link text-decoration-none text-white" href="#">NEED HELP? CONTACT</a>
                </div>
            </div>
            <hr class="mt-0 mt-4 border-white" />
        </div>
    </section>
</div>