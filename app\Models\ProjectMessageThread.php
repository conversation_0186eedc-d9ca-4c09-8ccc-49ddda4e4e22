<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProjectMessageThread extends Model
{
    protected $fillable = [
        'project_id',
        'parent_id',
        'subject',
        'message',
        'posted_by'
    ];

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function postedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'posted_by');
    }

    public function files(): HasMany
    {
        return $this->hasMany(ProjectMessageFile::class, 'message_id');
    }

    public function parent()
    {
        return $this->belongsTo(ProjectMessageThread::class, 'parent_id');
    }

    public function replies()
    {
        return $this->hasMany(ProjectMessageThread::class, 'parent_id');
    }
} 