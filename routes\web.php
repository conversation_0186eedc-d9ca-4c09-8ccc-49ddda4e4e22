<?php

use App\Http\Controllers\ProjectController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\AdminDashboardController;
use App\Http\Controllers\HolidayController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\EssentialMetricsController;
use App\Http\Controllers\FileUploadController;
use App\Http\Controllers\ProjectMessageController;
use App\Http\Controllers\FileController;

/*****************       User       *****************/


Route::middleware(['guest'])->group(function () {
    Route::get('/', function () {
        return view('login');
    });

    Route::get('login', [AuthController::class, 'login'])->name('login');
    Route::post('login',  [AuthController::class, 'login_check'])->name('login-check');
});

Route::match(['get', 'post'], 'logout', action: [AuthController::class, 'logout'])->name('logout');


Route::group(['middleware' => ['auth']], function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('projects', [ProjectController::class, 'show_projects'])->name('projects')->middleware("hasPermission:projects-view");
    Route::get('tasks/{task_id}/', [TaskController::class, 'view_task'])->name('view-task');
    Route::get('tasks', [TaskController::class, 'tasks'])->name('show-all-tasks');
    Route::get("tasks/{task_id}/comments", [DashboardController::class, 'show_comments'])->name('show-comments');
    Route::post("tasks/{task_id}/comment", [DashboardController::class, 'save_comment'])->name('save-comment');
    Route::get('projects/status', [DashboardController::class, 'statushub'])->name('statushub');
    Route::get('calender', [AdminDashboardController::class, 'calender'])->name('admin-calender');
});






Route::group(['middleware' => ['auth', 'superAdmin']], function () {

    Route::match(['get', 'post'], 'company-selection', [AuthController::class, 'company_selection'])->name('company-selection');
    Route::get('companies-list', [AuthController::class, 'companies_list'])->name('companies-list');
    Route::get('companies-list-of-user-ajax', [AuthController::class, 'companies_list_of_user_ajax'])->name('companies-list-of-user-ajax');
    Route::post('add-company', [AuthController::class, 'add_company'])->name('add-company');
    Route::match(['get', 'post', 'put'], 'companies/{company_id}/edit', [AuthController::class, 'edit_company'])->name('edit-company');
    Route::match(['get', 'post'], 'companies/{company_id}/update', [AuthController::class, 'update_company'])->name('update-company');
    Route::delete('companies/{company_id}/delete', [AuthController::class, 'delete_company'])->name('delete-company');





    Route::get('profile', [DashboardController::class, 'show_profile'])->name('profile');


    Route::get('accounts', [DashboardController::class, 'show_user_accounts_on_accounts_page'])->name('accounts')->middleware("hasPermission:users-view");
    // Route::get('users-list-ajax',[DashboardController::class,'user_accounts_list_ajax'])->name('users-list-ajax');
    Route::get('users/add', [UserController::class, 'add_user'])->name('add-user');
    //SAVE USER LOGIC(Save user and redirect back)
    Route::match(['get', 'post', 'put'], 'users/{id}/edit', [DashboardController::class, 'edit_user'])->name('edit-user')->middleware("hasPermission:user-edit");
    Route::put('users/{id?}/update', [DashboardController::class, 'update_user'])->name('update-user')->middleware("hasPermission:user-update");
    Route::delete('users/{id}/delete', [DashboardController::class, 'delete_user'])->name('delete-user')->middleware("hasPermission:user-delete");



    Route::get('projects-ajax', [ProjectController::class, 'show_projects_ajax'])->name('projects-ajax')->middleware("hasPermission:projects-view");
    Route::get('projects/add', [ProjectController::class, 'add_project'])->name('add-project')->middleware("hasPermission:project-create");
    Route::post('projects/save', [ProjectController::class, 'save_project'])->name('save-project')->middleware("hasPermission:project-create");
    Route::match(['get', 'post'], 'projects/{project_id?}/edit', [ProjectController::class, 'edit_project'])->name('edit-project')->middleware("hasPermission:project-edit");
    Route::put('projects/{project_id?}/update', [ProjectController::class, 'update_project'])->name('update-project')->middleware("hasPermission:project-update");
    Route::get('project/{project_id}', [ProjectController::class, 'show_project'])->name('view-project')->middleware('hasPermission:projects-view');
    Route::delete('project/{project_id}/delete', [ProjectController::class, 'delete_project'])->name('delete-project')->middleware("hasPermission:project-delete");


    Route::match(['get', 'post'], 'projects/{project_id?}/tasks', action: [DashboardController::class, 'show_single_project_tasks'])->name('show-tasks');
    Route::get('project/{project_id}/tasks/add', [DashboardController::class, 'add_task'])->name('add-task')->middleware("hasPermission:task-create");
    Route::post('project/{project_id}/tasks/save', [DashboardController::class, 'save_task'])->name('save-task')->middleware("hasPermission:task-create");
    Route::match(['get', 'post'], 'tasks/{task_id?}/edit', [DashboardController::class, 'edit_task'])->name('edit-task')->middleware("hasPermission:task-edit");
    Route::put('tasks/{task_id?}/update', [DashboardController::class, 'update_task'])->name('update-task')->middleware("hasPermission:task-update");
    Route::get('task/{task_id}/users', [DashboardController::class, 'load_task_users_in_dropdown'])->name('load-task-users');


    Route::post("comments/{comment_id}/edit", [DashboardController::class, 'edit_comment'])->name('edit-comment');
    Route::post("comments/delete", [DashboardController::class, 'delete_comment'])->name('delete-comment');

    Route::post("reply/{comment_id}/save", [DashboardController::class, 'save_reply'])->name('save-reply');
    // Route::get("comment/{comment_id}/replies", [DashboardController::class, 'show_replies'])->name('show-replies');
    Route::get("comment/{comment_id}/replies", [DashboardController::class, 'getChildComments'])->name('show-replies');

    Route::get("statuses", [StatusController::class, 'show_statuses'])->name('statuses');
    Route::get("status/add", [StatusController::class, 'add_status'])->name('add-status');
    Route::post("status/save", [StatusController::class, 'save_status'])->name('save-status');
    Route::match(['get', 'post'], "statuses/{status_id}/edit", [StatusController::class, 'edit_status'])->name('edit-status');
    Route::post("statuses/{status_id}/update", [StatusController::class, 'update_status'])->name('update-status');
    Route::post("statuses/{status_id}/delete", [StatusController::class, 'delete_status'])->name('delete-status');

    Route::get("phases", [DashboardController::class, 'show_phase'])->name('phases')->middleware("hasPermission:phases-view");
    Route::get("phase/add", [DashboardController::class, 'add_phase'])->name('add-phase')->middleware("hasPermission:phases-create");
    Route::post('status/save', [DashboardController::class, 'store_phase'])->name('save-phase')->middleware("hasPermission:phases-create");
    Route::match(['get', 'post'], 'phase/{phase_id?}/edit', [DashboardController::class, 'edit_phase'])->name('edit-phase')->middleware("hasPermission:phases-edit");
    Route::put('phase/{phase_id?}/update', [DashboardController::class, 'update_phase'])->name('update-phase')->middleware("hasPermission:phases-edit");
    Route::delete('phase/{phase_id}/delete', [DashboardController::class, 'delete_phase'])->name('delete-phase')->middleware("hasPermission:phases-delete");


    Route::get("categories", [DashboardController::class, 'show_categories'])->name('categories')->middleware("hasPermission:categories-view");
    Route::get("category/add", [DashboardController::class, 'add_category'])->name('add-category')->middleware("hasPermission:categories-create");
    Route::post('category/save', [DashboardController::class, 'store_category'])->name('save-category')->middleware("hasPermission:categories-create");
    Route::match(['get', 'post'], 'category/{category_id?}/edit', [DashboardController::class, 'edit_category'])->name('edit-category')->middleware("hasPermission:categories-edit");
    Route::put('category/{category_id?}/update', [DashboardController::class, 'update_category'])->name('update-category')->middleware("hasPermission:categories-edit");
    Route::delete('category/{category_id}/delete', [DashboardController::class, 'delete_category'])->name('delete-category')->middleware("hasPermission:categories-delete");

    Route::get("clients", [ClientController::class, 'index'])->name('clients')->middleware("hasPermission:clients-view");
    Route::get("client/add", [ClientController::class, 'add_client'])->name('add-client')->middleware("hasPermission:clients-create");
    Route::post("client/save", [ClientController::class, 'save_client'])->name('save-client')->middleware("hasPermission:clients-create");
    Route::match(['get', 'post'], "client/{client_id}/edit", [ClientController::class, 'edit_client'])->name('edit-client')->middleware("hasPermission:clients-update");
    Route::put("client/{client_id}/update", [ClientController::class, 'update_client'])->name('update-client')->middleware("hasPermission:clients-update");
    Route::get("client/{client_id}/show", [ClientController::class, 'show'])->name('show-client')->middleware("hasPermission:clients-view");
    Route::post("client/users", [ClientController::class, 'client_users'])->name('client-users');
    Route::post("client/social-details", [ClientController::class, 'client_social_details'])->name('client-social-details');

    Route::get("brands", [BrandController::class, 'index'])->name('brands')->middleware("hasPermission:brands-view");
    Route::get("brand/add", [BrandController::class, 'add_brand'])->name('add-brand')->middleware("hasPermission:brands-create");
    Route::post("brand/save", [BrandController::class, 'save_brand'])->name('save-brand')->middleware("hasPermission:brands-create");
    Route::match(['get', 'post'], "brand/{brand_id}/edit", [BrandController::class, 'edit_brand'])->name('edit-brand')->middleware("hasPermission:brands-update");
    Route::put("brand/{brand_id}/update", [BrandController::class, 'update_brand'])->name('update-brand')->middleware("hasPermission:brands-update");
    Route::get("brand/{brand_id}/show", [BrandController::class, 'show_brand'])->name('show-brand')->middleware("hasPermission:brands-view");


    Route::get('task/add', [TaskController::class, 'add_task'])->name('add-new-task')->middleware("hasPermission:task-create");
    Route::post('tasks/save', [TaskController::class, 'save_task'])->name('save-new-task')->middleware("hasPermission:task-create");


    Route::post('projects/{project_id}/update_project_information', [ProjectController::class, 'update_information'])->name('update-project-information')->middleware("hasPermission:statushub-update");
    Route::get('fetch_project_message_reasons', [ProjectController::class, 'ajax_fetch_project_message_reasons'])->name('fetch_project_message_reasons');
    Route::post('projects/{project}/mark-phase-complete', [ProjectController::class, 'markPhaseComplete'])->name('mark-phase-complete')->middleware("hasPermission:statushub-update");


    Route::get('teams', [TeamController::class, 'teams'])->name('teams')->middleware("hasPermission:team-view");
    Route::get('teams/add', [TeamController::class, 'add_team_member'])->name('add-team-member')->middleware("hasPermission:team-create");
    Route::post('teams/save', [TeamController::class, 'save_team_member'])->name('save-team-member')->middleware("hasPermission:team-create");
    Route::get('teams/{team_member_id}/edit', [TeamController::class, 'edit_team_member'])->name('edit-team-member')->middleware("hasPermission:team-update");
    Route::get('teams/{team_member_id}/profile', [TeamController::class, 'team_member_profile'])->name('team-member-profile')->middleware("hasPermission:team-update");
    Route::put('teams/{team_member_id}/update', [TeamController::class, 'update_team_member'])->name('update-team-member')->middleware("hasPermission:team-update");
    Route::get('teams/{team_member_id}/delete', [TeamController::class, 'delete_team_member'])->name('delete-team-member')->middleware("hasPermission:team-delete");


    /*
            when we are on accounts page, the edit icon contains a form which goes on edit page(without data) via post request as the request contains
            user-id. When he edit page loads, ajax request runs which goes to edit page with another parameter "fetch_user_data"  via get request which
            fetches user data and shows on edit page.=
            Note* - We can use get request in edit icon form but it will send unnecessary data such as csrf token in the url so we use post request
            NOTE* - We don't send id in ajax request yet it fetches the required user details as we find the user details of user whose id is fetched
                    from url using $request->route("id")




        */


    Route::get('/admins', [TaskController::class, 'fetchAdmins'])->name('fetchAdmins');
});









/*****************    SuperAdmin       *****************/

Route::group(['prefix' => 'admin', 'middleware' => ['auth', 'superAdmin']], function () {
    Route::get('register_user', [AuthController::class, 'register'])->name('register');
    Route::post('register_user', [AuthController::class, 'register_check'])->name('register-check');

    Route::get('permissions', [AdminDashboardController::class, 'show_permissions'])->name('permissions')->middleware("hasPermission:permissions-view");
    Route::get('permissions/add', [AdminDashboardController::class, 'add_permission'])->name('add-permission')->middleware("hasPermission:permission-create");
    Route::post('permissions/save', [AdminDashboardController::class, 'save_permission'])->name('save-permission')->middleware("hasPermission:permission-create");
    Route::match(['get', 'post', 'put'], 'permissions/{id}/edit', [AdminDashboardController::class, 'edit_permission'])->name('edit-permission')->middleware("hasPermission:permission-edit");
    Route::put('permissions/{id?}/update', [AdminDashboardController::class, 'update_permission'])->name('update-permission')->middleware("hasPermission:permission-update");
    Route::delete('permissions/{id}/delete', [AdminDashboardController::class, 'delete_permission'])->name('delete-permission')->middleware("hasPermission:permission-delete");

    Route::get('roles', [AdminDashboardController::class, 'show_roles'])->name('roles')->middleware("hasPermission:roles-view");
    Route::get('roles/add', [AdminDashboardController::class, 'add_role'])->name('add-role')->middleware("hasPermission:role-create");
    Route::post('roles/save', [AdminDashboardController::class, 'save_role'])->name('save-role')->middleware("hasPermission:role-create");
    Route::match(['get', 'post', 'put'], 'roles/{role_id}/edit', [AdminDashboardController::class, 'edit_role'])->name('edit-role')->middleware("hasPermission:role-edit");
    Route::put('roles/{role_id?}/update', [AdminDashboardController::class, 'update_role'])->name('update-role')->middleware("hasPermission:role-update");
    Route::delete('roles/{role_id}/delete', [AdminDashboardController::class, 'delete_role'])->name('delete-role')->middleware("hasPermission:role-delete");


    Route::get('dashboard', [AdminDashboardController::class, 'dashboard'])->name('admin-dashboard')->middleware("hasPermission:dashboard-view");





    // projects 
    Route::get('projects', [AdminDashboardController::class, 'projects'])->name('admin-projects')->middleware("hasPermission:projects-view");

    Route::get('projects-list-view', [AdminDashboardController::class, 'projectsListView'])->name('admin-projects-list-view')->middleware("hasPermission:projects-view");


    Route::get('projects/tasks/{project_id}', [TaskController::class, 'show_project_tasks'])->name('show-project-tasks');

    Route::post('project_messages/save', [AdminDashboardController::class, 'save_project_messages'])->name("save-project-messages");








    Route::get('clients', [AdminDashboardController::class, 'clients'])->name('admin-clients')->middleware("hasPermission:clients-view");

    Route::get('tasks', [TaskController::class, 'tasks'])->name('admin-tasks')->middleware("hasPermission:tasks-view");

    Route::get('brands', [AdminDashboardController::class, 'brands'])->name('admin-brands')->middleware("hasPermission:brands-view");

    Route::get('essential-metrics/{project_id}/analysis', [EssentialMetricsController::class, 'index'])->name('essential-metrics')->middleware("hasPermission:metrics-view");
    Route::post('essential-metrics/{project_id}/metrics-by-month/{month}', [EssentialMetricsController::class, 'get_metrics_by_month'])->name('get-metrics-by-month')->middleware("hasPermission:metrics-view");
    Route::post('essential-metrics/save', [EssentialMetricsController::class, 'save_metrics_by_month'])->name('save-metrics-by-month');
    Route::post('essential-metrics/download', [EssentialMetricsController::class, 'download_metrics_by_month'])->name('download-metrics-by-month');
});


Route::group(['middleware' => ['auth', 'superAdmin']], function () {




    Route::post('mark-complete', [TaskController::class, 'markComplete'])->name('mark-complete');



    // calendar



    Route::get('resource-allocation', [AdminDashboardController::class, 'resource_allocation'])->name('resource-allocation');

    // Holiday routes
    Route::post('holidays/store', [HolidayController::class, 'store'])->name('holidays.store');
    Route::post('holidays/store-ajax', [HolidayController::class, 'storeAjax'])->name('holidays.store.ajax');
    Route::put('holidays/update', [HolidayController::class, 'update'])->name('holidays.update');
    Route::delete('holidays/destroy', [HolidayController::class, 'destroy'])->name('holidays.destroy');

    // Project archive/unarchive routes
    Route::post('projects/{id}/archive', [ProjectController::class, 'archiveProject'])->name('archive-project');
    Route::post('projects/{id}/unarchive', [ProjectController::class, 'unarchiveProject'])->name('unarchive-project');
});

// client routes
Route::prefix('client')->group(function () {
    Route::middleware(['auth'])->group(function () {
        Route::get('/', [ClientController::class, 'dashboard'])->name('client.dashboard');
        Route::get('/projects', [ClientController::class, 'projects'])->name('client.projects');
    });
});


//auth forget password
Route::get('/forgot-password', [AuthController::class, 'forgotPasswordPage'])->name('forgotPasswordPage');
Route::post('/forgot-password', [AuthController::class, 'forgotPassword'])->name('forgotPassword');
Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
Route::get('/reset-password/{token}', function (string $token) {
    $email = session('email');
    return view('auth.reset-password', ['token' => $token, 'email' => $email]);
})->name('password.reset');


// track project 
Route::get('track-project/{id}', [ProjectController::class, 'trackProject'])->name('track-project');

Route::get('/projects/{project}/check-phase-completion/{phase}', [ProjectController::class, 'checkPhaseCompletion'])
    ->middleware(['auth'])
    ->name('check-phase-completion');


Route::get('message-centre/{id}', [ProjectController::class, 'messageCenter'])->name('message-centre');
// Route::get('project/{project_id}/message/{message_id}', [ProjectController::class, 'viewMessage'])->name('view-message');
// Route::get('project/{project_id}/message/{message_id}/thread/new', [ProjectController::class, 'newThread'])->name('new-thread');

Route::get('project/{project_id}/message/new-thread', [ProjectController::class, 'newMessageThread'])->name('new-message-thread');
// Route::get('file-upload/{id}', [ProjectController::class, 'fileUpload'])->name('file-upload');

Route::get('file-upload/{id}', [FileUploadController::class, 'index'])->name('file-upload');
Route::get('billing-history/{id}', [ProjectController::class, 'billingHistory'])->name('billing-history');



// brands routes
Route::middleware(['auth', 'isBrand'])->group(function () {
    Route::get('brand/dashboard', [BrandController::class, 'dashboard'])->name('brand.dashboard');
    Route::get('brand/projects-by-client', [BrandController::class, 'projectsByClient'])->name('brand.projects.by.client');
});

Route::post('/projects/{project}/messages', [ProjectMessageController::class, 'store'])->name('project.messages.store');

// Message Center Routes
Route::get('/project/{project_id}/message/{message_id}', [ProjectMessageController::class, 'show'])->name('view-message');
Route::post('/project/{project_id}/message/{message_id}/reply', [ProjectMessageController::class, 'reply'])->name('message.reply');

Route::get('/download/file/{file}', [FileController::class, 'download'])->name('download.file');
