.upload-btn-wrapper {
	display: inline-block;
	overflow: hidden;
	position: relative;

	.btn-upload {
		background: transparent;
		border: 1px solid #fff;
		border-radius: 50px;
		color: #fff;
		font: 700 12px $inter;
		height: 50px;
		padding: 8px 20px;

		i {
			color: #ff5811;
			font-size: 22px;
		}
	}

	input[type='file'] {
		font-size: 100px;
		left: 0;
		opacity: 0;
		position: absolute;
		top: 0;
	}
}

.add-time {
	position: relative;

	.meta-label {
		background: transparent;
		border: 1px solid #fff;
		border-radius: 50px;
		color: #fff;
		font: 700 12px $inter;
		height: 50px;
		padding: 8px 20px;

		i {
			color: #ff5811;
			font-size: 22px;
		}
	}

	.due-date {
		height: 0;
		opacity: 0;
		pointer-events: none;
		position: absolute;
		width: 0;
	}
}

form,
.form-wrap,
.new-task-form {
	.input-text,
	.form-control,
	.form-select {
		font: 700 20px $futura;
		height: 56px;
		padding: 0 1rem;
	}

	.textarea {
		font: 500 14px $inter;
		height: 190px;
		padding: 1rem;
	}

	.input-text,
	.form-control,
	.form-select,
	.textarea {
		background-color: #282828;
		border-radius: 10px;
		color: #fff;
		width: 100%;
		outline: none !important;

		&::placeholder {
			color: rgba(#fff, 0.5);
		}

		&:focus,
		&:focus-visible {
			border-color: #ff5811 !important;
			box-shadow: 0 0 0 0.25rem rgba(#ff5811, 0.25);
		}
	}

	textarea {
		&:focus,
		&:focus-visible {
			border: none !important;
			outline: none !important;
		}
	}

	.form-select {
		background: #282828;
		color: rgba(#fff, 0.5);
		font-size: 16px;

		option {
			color: #fff;

			&:hover {
				background: #ff5811;
			}
		}
	}

	.select-wrap {
		position: relative;

		.select-icon {
			pointer-events: none;
			position: absolute;
			right: 20px;
			top: 50%;
			transform: translateY(-50%);
		}
	}

	.form-check-input {
		background-color: transparent;
		border: 1px solid #fff;

		&:checked {
			background-color: $orange;
			border-color: $orange;
		}

		&:focus {
			box-shadow: 0 0 0 0.25rem rgba($orange, 0.25);
		}
	}

	.form-check-label {
		color: #fff;
		font: 500 14px $inter;
	}

	.form-text {
		color: #fff;
		font: 14px $inter;

		a {
			color: #ff5811;
			text-decoration: none;
		}
	}

	.input-wrap {
		.icon {
			border: 1px solid #ff5811;
			border-radius: 50%;
			flex: 0 0 42px;
			height: 42px;
			margin-right: 1.25rem;
			padding: 5px;
			width: 42px;
		}

		.form-control {
			font-size: 18px;
			font-weight: 400;
			height: 42px;
		}
	}
	.input_color {
		border-radius: 10px;
		input {
			width: 100%;
			border: none;
			border-color: transparent;
		}
		.value,
		.picker {
			border-radius: 0;
		}
		.picker {
			transform: scale(1.5);
		}
	}
	.cta {
		background: #ff5811;
		color: #fff;

		&.dark {
			background: transparent;
			border: 1px solid $orange;
		}
	}
}

.timeline-set {
	.meta-row {
		.logo {
			border-style: solid;
			border-width: 1px;
			flex: 0 0 52px;
			height: 52px;
			width: 52px;
		}

		.wrap-meta {
			background: #282828;
			font: 16px $futura;
			overflow: hidden;

			.title {
				border-right: 10px solid #111;
				color: $white;
				flex: 0 0 9.25rem;
				font: 700 20px $futura;
				padding-right: 1rem;
			}

			a {
				color: #fff;
			}

			.col-meta {
				flex: 0 0 calc(calc(100% - 8.25rem) / 3);
				font: 16px $inter;
			}

			.target {
				color: #fff;
				font-size: 14px;
				font-style: italic;
				opacity: 0.5;
			}
		}

		@each $name, $color in $colors {
			&.#{ $name } {
				@include timeline-current-logo($color);
			}
		}
	}
}

.brand-wrap {
	background: #282828;
	border-radius: 10px;
	max-height: 320px;
	overflow-y: auto;
	padding: 1.5rem;

	.brand-select {
		.name {
			color: #fff;
			font: 700 16px/1.18 $futura;
		}

		.cta {
			background: #282828;
			border: 1px solid #fff;
			border-radius: 6px;
			font: 700 12px/1 $inter;
			padding: 0.5rem;
			width: 6rem;

			&.added {
				background: $orange;
				border-color: $orange;
			}
		}

		&.added {
			.name {
				color: $orange;
			}
		}

		&.claimed {
			opacity: 0.5;
			pointer-events: none;
		}

		+ .brand-select {
			margin-top: 0.8rem;
		}
	}
}
