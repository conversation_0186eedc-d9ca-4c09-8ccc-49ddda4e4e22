body .messages-wrap {
  margin-top: 0;
}

.message-wrap .message .copy {
  padding: 0 10px 0px 40px;
}

.bg-primary {
  background-color: #ff4c00 !important;
}
.tox-tinymce {
  border: none !important;
  border-radius: 10px !important;
  width: 100% !important;
}
.tox .tox-edit-area__iframe {
  background-color: white !important;
}
.message-box .message {
  padding: 0 !important;
}
/* .message-box .message .tox-tinymce {
        border: 1px solid #ff4c00 !important;
        border-radius: 10px !important;
    } */
.thread-subject h3 {
  font-size: 1.75rem;
  font-weight: 500;
  color: #333;
  margin: 0;
  padding: 0 0 0 1rem;
}
.message-subject {
  margin-top: 0.5rem;

  width: 100%;
}
.message-subject input {
  border: none !important;
  padding: 0.5rem 1rem !important;
  font-family: inherit !important;
  font-size: 1.25rem !important;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.7) !important;
  width: 100% !important;
  background-color: #f8f9fa !important;
  border-radius: 8px !important;
}
.message-subject input::placeholder {
  color: rgba(0, 0, 0, 0.5) !important;
  opacity: 1 !important;
}
.message-subject input:focus {
  outline: none !important;
  box-shadow: none !important;
}
.upload-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  min-height: 100px;
  border: 2px dashed #ff4c00;
  border-radius: 10px;
  padding: 20px;
  position: relative;
}
.upload-preview.drag-over {
  background-color: rgba(255, 76, 0, 0.1);
}
.upload-preview::before {
  content: "Drag and drop files here";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ff4c00;
  font-size: 1.2rem;
  opacity: 0.5;
  pointer-events: none;
  display: none;
}
.upload-preview:empty::before {
  display: block;
}
.upload-preview-item {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 5px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-preview-item.loading::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}
.upload-preview-item.loading::after {
  content: "";
  position: absolute;
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #ff4c00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 2;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.upload-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.upload-preview-item .file-icon {
  font-size: 2.5rem;
  color: #ff4c00;
}
.upload-preview-item .file-icon.pdf {
  color: #ff0000;
}
.upload-preview-item .file-icon.doc,
.upload-preview-item .file-icon.docx {
  color: #2b579a;
}
.upload-preview-item .file-icon.xls,
.upload-preview-item .file-icon.xlsx {
  color: #217346;
}
.upload-preview-item .file-icon.ppt,
.upload-preview-item .file-icon.pptx {
  color: #d24726;
}
.upload-preview-item .file-icon.txt {
  color: #666666;
}
.upload-preview-item .file-icon.zip,
.upload-preview-item .file-icon.rar {
  color: #ffd700;
}
.upload-preview-item .remove-file {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 88, 17, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.upload-preview-item .file-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 5px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.modal-content {
  border-radius: 10px;
  border: none;
}
.modal-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
}
.modal-body {
  padding: 1.5rem;
}
.modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
}
.recipient-item {
  padding: 0.5rem;
  border-radius: 5px;
  transition: background-color 0.2s;
}
.recipient-item:hover {
  background-color: #f8f9fa;
}
.recipient-item .form-check-label {
  cursor: pointer;
  width: 100%;
}
.recipient-item img {
  object-fit: cover;
  width: 32px;
  height: 32px;
}
.btn-primary {
  background-color: #ff4c00;
  border-color: #ff4c00;
}
.btn-primary:hover {
  background-color: #e64400;
  border-color: #e64400;
}
.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
}
.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}
.default-recipients {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 10px;
}
.default-recipients p {
  color: #666;
  font-size: 0.9rem;
}
.default-recipients ul li {
  font-size: 0.95rem;
}
.default-recipients .change-recipients {
  color: #ff4c00;
  font-size: 0.9rem;
}
.default-recipients .change-recipients:hover {
  color: #e64400;
}
.cta {
  position: relative;
  min-width: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.cta:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
.cta .spinner-border {
  margin-left: 8px;
}

/* Message Thread Styles */
.messages-wrap {
  padding: 20px 0;
}

.message-box {
  margin-bottom: 25px;
  position: relative;
  width: 100%;
}

.message-box.reply-message {
  border-radius: 10px;
  padding: 15px;
}
.message-box.reply-message .message-wrap .message {
  height: 100%;
}
.message-box.reply-message .message-wrap .message .copy {
  height: 100%;
  padding: 10px 10px 10px 40px;
}

.message-box.main-message {
  border-radius: 10px;
  padding: 15px;
}
.message-box.main-message .message-wrap .message {
  height: 100%;
}
.message-box.main-message .message-wrap .message .copy {
  height: 100%;
  padding: 10px 10px 10px 40px;
}

/*     
    .messageThreadPic {
        min-width: 48px;
        width: 48px;
        height: 48px;
        margin-right: -0.25rem !important;;
        flex-shrink: 0;
    } */

/* .messageThreadPic img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
    }
     */
.message-wrap {
  flex: 1;
  min-width: 0; /* Prevents flex item from overflowing */
}

.message {
  width: 100%;
}

.message .copy {
  padding: 0 10px;
}

.message .meta {
  flex-wrap: wrap;
  gap: 5px;
}

.message .meta .user {
  color: #333;
  font-weight: 500;
  text-decoration: none;
}

.message .meta .date {
  color: #666;
}

.message p {
  margin: 10px 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

.message .attachments {
  border-top: 1px solid #eee;
  padding-top: 10px;
  margin-top: 15px;
}

.message .attachments h4 {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
}

.message .attachments ul li {
  margin-bottom: 5px;
}

.message .attachments .text-orange {
  color: #ff4c00;
  text-decoration: none;
  font-size: 0.9rem;
}

.message .attachments .text-orange:hover {
  text-decoration: underline;
}

.message small.text-muted {
  display: block;
  margin-top: 8px;
  font-size: 0.85rem;
}

/* Badge Styles */
.badge {
  padding: 0.4em 0.6em;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge.bg-primary {
  background-color: #ff4c00 !important;
}

.badge.bg-secondary {
  background-color: #6c757d !important;
}
