<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectMessageThread;
use App\Models\ProjectMessageFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class FileUploadController extends Controller
{
    public function index(Request $request,  $project)
    {

        
        $project= Project::findOrFail($project);

        

        // Get the latest message that has files
        $latestMessageWithFiles = ProjectMessageThread::where('project_id', $project->id)
            ->whereHas('files')
            ->with(['files', 'postedBy'])
            ->latest()
            ->first();

        $client = $project->client;

        // Get all files with pagination
        $files = $project->files()
            ->with(['message.postedBy'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('file-upload', compact('project', 'latestMessageWithFiles', 'files', 'client'));
    }

    public function store(Request $request, Project $project)
    {
        $request->validate([
            'files.*' => 'required|file|max:20480', // 20MB max
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Create a new message
        $message = ProjectMessageThread::create([
            'project_id' => $project->id,
            'subject' => $request->subject,
            'message' => $request->message,
            'posted_by' => Auth::id()
        ]);

        // Handle file uploads
        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $file) {
                $path = $file->store('project-messages/' . $project->id);

                ProjectMessageFile::create([
                    'message_id' => $message->id,
                    'file_name' => $file->getClientOriginalName(),
                    'file_path' => $path,
                    'file_type' => $file->getMimeType(),
                    'file_size' => $file->getSize()
                ]);
            }
        }

        return redirect()->route('file-upload', ['id' => $project->id])
            ->with('success', 'Files uploaded successfully.');
    }
} 