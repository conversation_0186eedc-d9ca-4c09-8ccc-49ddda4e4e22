<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Models\User;
use App\Models\AccessLevel;
use App\Models\Resource;
use App\Models\Role;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

use App\Notifications\TeamMemberAssigned;

class TeamController extends Controller
{
    //
    public function teams()
    {
        $teams = Team::all();
        $design_team = Team::where('name', 'Design Team')->first();
        $dev_team = Team::where('name', 'Dev Team')->first();
        $admin_team = Team::where('name', 'Admin')->first();

        $design_team_users = $design_team ? $design_team->users()->get() : collect();
        $dev_team_users = $dev_team ? $dev_team->users()->get() : collect();
        $admin_team_users = $admin_team ? $admin_team->users()->get() : collect();

        $usersWithoutTeam = User::whereDoesntHave('teams')
            ->whereHas('role', function($query) {
                $query->whereNotIn('name', ['Client Member', 'Brand Member']);
            })
            ->get();

        return view('team.index', compact('teams', 'usersWithoutTeam'));
    }

    public function add_team_member()
    {
        $teams = Team::all();
        $accessLevels = AccessLevel::all();
        $roles = Role::where('name', '!=', 'SuperAdmin')->get();
        return view('team.add_team_member', compact('teams', 'accessLevels', 'roles'));
    }

    public function save_team_member(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'name' => 'required|string|max:255',
            'color_code' => 'required|string|max:255|unique:tasks,name',
            'email' => 'required|string|email|unique:users,email',
            'temporaryPassword' => 'required|string',
            'setTeam' => 'exists:team,id',
            'setAccessLevel' => 'exists:access_level,id',
            'team' => 'required|in:US,India',
            'pm_hours' => 'nullable|integer|min:0',
            'designer_hours' => 'nullable|integer|min:0',
            'developer_hours' => 'nullable|integer|min:0',
            'cs_hours' => 'nullable|integer|min:0',
        ], [
            'email.unique' => 'User with same email already exists'
        ]);

        if ($validate->fails()) {
            return back()->withErrors($validate)->withInput();
        }

        $user = new User();
        $user->name = $request->input('name');
        $user->email = $request->input('email');
        $user->color_code = $request->input('color_code');
        $user->password = Hash::make($request->input('temporaryPassword'));
        if ($request->hasFile('profile_image')) {
            $file = $request->file('profile_image');
            $filename = time() . '.' . $file->getClientOriginalExtension();
            $file->storeAs('images', $filename, 'public');
            $user->profile_image = $filename;
        }
        // $role = Role::where('id', $request->input('setAccessLevel'))->first();
        // $user->role_id = Role::where('name' ,'=', 'Team Member')->value('id');
        $user->access_level_id = $request->input('setAccessLevel');
        $user->save();
        $user->teams()->attach($request->input('setTeam'));

        // Create resource for the user
        $resource = new \App\Models\Resource();
        $resource->name = $user->name;
        $resource->team = $request->input('team');
        $resource->pm_hours = $request->input('pm_hours', 0);
        $resource->designer_hours = $request->input('designer_hours', 0);
        $resource->developer_hours = $request->input('developer_hours', 0);
        $resource->cs_hours = $request->input('cs_hours', 0);
        $resource->user_id = $user->id;
        $resource->save();

        try {
            $user->notify(new TeamMemberAssigned($user, $request->input('temporaryPassword')));
        } catch (\Exception $e) {
            return back()->with('error', 'Error sending user notification: ' . $e->getMessage());
        }

        return back()->with('success', 'Team Member saved successfully!');
    }

    public function edit_team_member($id)
    {
        $user = User::find($id);
        $teams = Team::all();
        $accessLevels = AccessLevel::all();
        $roles = Role::where('name', '!=', 'SuperAdmin')->get();
        $resource = $user->resource;
        return view('team.edit_team_member', compact('user', 'teams', 'roles', 'accessLevels', 'resource'));
    }

    public function update_team_member(Request $request)
    {

        // $validate = Validator::make($request->all(), [
        //     'name' => 'required|string|max:255',
        //     'email' => 'required|string',
        //     'setTeam' => 'exists:team,id',
        //     'setAccessLevel' => 'exists:roles,id',
        //     'team' => 'required|in:US,India',
        //     'pm_hours' => 'nullable|integer|min:0',
        //     'designer_hours' => 'nullable|integer|min:0',
        //     'developer_hours' => 'nullable|integer|min:0',
        // ]);

        // if ($validate->fails()) {
        //     return back()->withErrors($validate)->withInput();
        // }


        // $user = User::find($request->input('user_id'));
        // $user->name = $request->input('name');
        // $user->email = $request->input('email');
        // $user->color_code = $request->input('color_code');
        // if ($request->input('password')) {
        //     $user->password = Hash::make($request->input('password'));
        // }
        // if ($request->hasFile('profile_image')) {
        //     $file = $request->file('profile_image');
        //     $filename = time() . '.' . $file->getClientOriginalExtension();
        //     $file->storeAs('images', $filename, 'public');
        //     $user->profile_image = $filename;
        // }
        // if ($request->input('setAccessLevel')) {
        //     $role =  Role::where('id', $request->input('setAccessLevel'))->first()->id;
        //     $user->access_level_id = $role;
        // }

        // $user->save();
        // if ($request->input('setTeam')) {
        //     $user->teams()->sync([$request->input('setTeam')]);
        // }


        // $resource = \App\Models\Resource::firstOrNew(['user_id' => $user->id]);
        // $resource->name = $user->name;
        // $resource->team = $request->input('team');
        // $resource->pm_hours = $request->input('pm_hours', 0);
        // $resource->designer_hours = $request->input('designer_hours', 0);
        // $resource->developer_hours = $request->input('developer_hours', 0);
        // $resource->user_id = $user->id;
        // $resource->save();






        // return $request->all();
        // $page_name = $request->input('page_name');
        // $email_input_disabled = $request->input('email_input_disabled');

        // $rules = [];
        // if ($page_name === 'team_member_profile') {
        //     // Conditionally add email validation rules
        //     if ($email_input_disabled === 'false') {
        //         $rules['email'] = 'required|email';
        //         $rules['password'] = 'nullable|string|min:6';
        //     }
        //     else{
        //         $rules['email'] = '';
        //         $rules['password'] = '';
        //     }
        //     $validate = Validator::make($request->all(), $rules);

        // } 
        // elseif ($page_name === 'edit_team_member') {
        //     if ($email_input_disabled === 'false') {
        //         $rules['email'] = 'required|email';
        //         $rules['password'] = 'nullable|string|min:6';
        //     }
        //     else{
        //         $rules['email'] = '';
        //         $rules['password'] = '';
        //     }
        //     $validate = Validator::make($request->all(), [
        //         'name' => 'required|string|max:255',
        //         'setTeam' => 'exists:team,id',
        //         'setAccessLevel' => 'exists:roles,id',
        //         'team' => 'required|in:US,India',
        //         'pm_hours' => 'nullable|integer|min:0',
        //         'designer_hours' => 'nullable|integer|min:0',
        //         'developer_hours' => 'nullable|integer|min:0',
        //     ]);
        // } 
        // else {
        //     return redirect()->back()->withErrors(['form_origin' => 'Unknown form source.']);
        // }

        // if ($validate->fails()) {
        //     return back()->withErrors($validate)->withInput();
        // }

        // // --- User Update Logic ---
        // $user = User::find($request->input('user_id'));

        // // Always update name and color_code
        // $user->name = $request->input('name');
        // $user->color_code = $request->input('color_code');

        // // Conditionally update email based on page and disabled state
        // if ($page_name === 'edit_team_member') {
        //     // For 'edit_team_member', email is always expected and validated
        //     $user->email = $request->input('email');
        // } elseif ($page_name === 'team_member_profile') {
        //     // For 'team_member_profile', only update email if it was NOT disabled (and thus validated)
        //     if ($email_input_disabled !== 'true') {
        //         $user->email = $request->input('email');
        //     }
        //     // If email_input_disabled IS 'true', we do nothing, thus retaining the existing email.
        // }

        // // Update password if provided
        // if ($request->input('password')) {
        //     $user->password = Hash::make($request->input('password'));
        // }

        // // Handle profile image upload
        // if ($request->hasFile('profile_image')) {
        //     $file = $request->file('profile_image');
        //     $filename = time() . '.' . $file->getClientOriginalExtension();
        //     $file->storeAs('images', $filename, 'public');
        //     $user->profile_image = $filename;
        // }

        // // Update user role
        // if ($request->input('setAccessLevel')) {
        //     $role = Role::where('id', $request->input('setAccessLevel'))->first()->id;
        //     $user->access_level_id = $role;
        // }

        // $user->save();

        // // Sync teams (if 'setTeam' is present)
        // if ($request->input('setTeam')) {
        //     $user->teams()->sync([$request->input('setTeam')]);
        // }

        // // --- Resource Update Logic ---
        // $resource = \App\Models\Resource::firstOrNew(['user_id' => $user->id]);
        // $resource->name = $user->name; // Use user's name which might have been updated
        // $resource->team = $request->input('team');
        // $resource->pm_hours = $request->input('pm_hours', 0);
        // $resource->designer_hours = $request->input('designer_hours', 0);
        // $resource->developer_hours = $request->input('developer_hours', 0);
        // $resource->user_id = $user->id; // Ensure user_id is set
        // $resource->save();






        // return $request->all();
        $pageName = $request->input('page_name');
        // Convert 'email_input_disabled' to a boolean
        $emailInputDisabled = filter_var($request->input('email_input_disabled'), FILTER_VALIDATE_BOOLEAN);

        // Define base validation rules
        $rules = [
            'page_name' => 'required|string|in:team_member_profile,edit_team_member', // Ensure valid page_name
            'email_input_disabled' => 'required|string|in:true,false', // Ensure valid boolean string
            'user_id' => 'required|exists:users,id', // Make sure the user exists
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'color_code' => 'nullable|string|max:7', // Assuming hex color code
        ];

        // Apply page-specific rules
        if ($pageName === 'team_member_profile') {
            // No name, setTeam, setAccessLevel, team, pm_hours, etc. for this page
            $rules['name'] = 'required|string|max:255'; // Assuming name is always required here too
            $rules['password'] = 'nullable|string|min:6';

            // Only validate email if it's NOT disabled
            if (!$emailInputDisabled) {
                $rules['email'] = 'required|email|max:255';
            } else {
                // If email is disabled, ensure it's not present or is ignored by validation
                // By not adding a rule for 'email' here, if it's disabled, any submitted email will be ignored
                // as it's not expected for validation if not enabled.
                // We will handle the update conditionally later.
            }

        } elseif ($pageName === 'edit_team_member') {
            // General rules for editing a team member
            $rules['name'] = 'required|string|max:255';
            $rules['setTeam'] = 'nullable|exists:team,id'; // 'team' is likely your table name
            $rules['setAccessLevel'] = 'nullable|exists:access_level,id';
            $rules['team'] = 'required|in:US,India';
            $rules['pm_hours'] = 'nullable|integer|min:0';
            $rules['designer_hours'] = 'nullable|integer|min:0';
            $rules['developer_hours'] = 'nullable|integer|min:0';
            $rules['cs_hours'] = 'nullable|integer|min:0';
            $rules['password'] = 'nullable|string|min:6'; // Password can be updated here too

            // Email validation for 'edit_team_member' is determined by disabled status
            if (!$emailInputDisabled) {
                $rules['email'] = 'required|email|max:255';
            } else {
                // Same logic as above: if disabled, don't validate, keep current email
            }
        }

        // Perform validation
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // --- Data is validated, proceed with updates ---
        $validatedData = $validator->validated();

        $user = User::findOrFail($validatedData['user_id']); // Use findOrFail for robust error handling

        // Always update name and color_code if present in validated data
        if (isset($validatedData['name'])) {
            $user->name = $validatedData['name'];
        }
        if (isset($validatedData['color_code'])) {
            $user->color_code = $validatedData['color_code'];
        }


        // Conditionally update email based on page and disabled state
        // ONLY update email if it was ENABLED (not disabled) and thus passed validation
        if (!$emailInputDisabled && isset($validatedData['email'])) {
            $user->email = $validatedData['email'];
        }
        // If email_input_disabled IS 'true', we do nothing, thus retaining the existing email.
        // This implicitly covers both 'team_member_profile' and 'edit_team_member' cases

        // Update password if provided in the request and passed validation
        if (isset($validatedData['password'])) {
            $user->password = Hash::make($validatedData['password']);
        }

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            $file = $request->file('profile_image');
            $filename = time() . '.' . $file->getClientOriginalExtension();
            $file->storeAs('images', $filename, 'public');
            $user->profile_image = $filename;
        }

        // $user->role_id = Role::where('name' ,'=', 'Team Member')->value('id');

        // Update user role if provided
        if (isset($validatedData['setAccessLevel'])) {
            // Assuming 'roles' table has 'id' and your Role model is correctly set up
            // It's safer to use the validated ID directly
            $user->access_level_id = $validatedData['setAccessLevel'];
        }

        $user->save(); // Save User model changes

        // Sync teams (if 'setTeam' is present and valid)
        if (isset($validatedData['setTeam'])) {
            $user->teams()->sync([$validatedData['setTeam']]);
        }


        // --- Resource Update Logic ---
        // Ensure user_id is linked to the Resource
        $resource = Resource::firstOrNew(['user_id' => $user->id]);

        // Only update resource fields if they are relevant to the current page and passed validation
        if ($pageName === 'edit_team_member' || $pageName === 'team_member_profile') { // Both pages can potentially update resource data
            // Update resource name with user's updated name
            $resource->name = $user->name;

            // Conditionally update other resource fields based on validation and presence
            if (isset($validatedData['team'])) {
                $resource->team = $validatedData['team'];
            }
            // Use coalesce operator to set to 0 if null or not present in validated data
            $resource->pm_hours = $validatedData['pm_hours'] ?? 0;
            $resource->designer_hours = $validatedData['designer_hours'] ?? 0;
            $resource->developer_hours = $validatedData['developer_hours'] ?? 0;
            $resource->cs_hours = $validatedData['cs_hours'] ?? 0;

        }

        // Always ensure user_id is set for firstOrNew
        $resource->user_id = $user->id;
        $resource->save(); // Save Resource model changes   

        return back()->with('user_saved_success_message', 'Team Member updated successfully!');
    }

    public function team_member_profile($id)
    {
        $user = User::find($id);
        $teams = Team::all();
        $accessLevels = AccessLevel::all();
        return view('team.team_member_profile', compact('user', 'teams', 'accessLevels'));
    }

    public function delete_team_member($id)
    {
        $user = User::find($id);
        if (!$user) {
            return back()->with('error', 'User not found.');
        }

        // Delete associated resource
        if ($user->resource) {
            $user->resource->delete();
        }

        $user->teams()->detach();

        $user->delete();

        return redirect()->route('teams')->with('user_deleted_success_message', 'Team Member deleted successfully!');
    }
}
