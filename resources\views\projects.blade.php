@extends('admin.layouts.app')
@section('title', 'Projects')
@section('content')


<section class="client-header">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="back-page">
            @if(admin_superadmin_permissions())
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('admin-projects') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />Back to Projects List</a>
            @else
                <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('projects') }}"><img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" />Back to Projects List</a>
            @endif
        </div>
        <div class="meta d-flex justify-content-between align-items-center">
            @if( count($projects) > 0 )
            <div class="copy">
                <h1 class="text-white mb-0">All, <i>Projects</i></h1>
                
            </div>
            @else 
            <div class="copy">
                <h1 class="text-white mb-0">No, <i>Projects</i></h1>
            </div>
            @endif
        </div>
    </div>
</section>
@if( count($projects) > 0 )
<section class="client-project pt-0">
    <div class="container-xxl">
        <hr class="mt-0 mb-4 border-white" />
        <div class="projects-list-table">
            <div class="head d-flex align-items-center justify-content-between">
                @if( count($projects) > 0 )
                    @php
                        $currentYear = now()->year;
                    @endphp

                    <div class="years d-flex">
                        <a href="{{ route('admin-projects', ['year' => 'all']) }}" class="{{ $year === 'all' || !$year ? 'active' : '' }}">
                            All
                        </a>

                        @foreach ($years as $y)
                            <a href="{{ route('admin-projects', ['year' => $y]) }}" class="{{ $year == $y ? 'active' : '' }}">
                                {{ $y }}
                            </a>
                        @endforeach
                    </div>

                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $projects->lastPage(); $i++)
                            <a href="{{ $projects->url($i) }}" class="{{ $projects->currentPage() == $i ? 'active' : '' }}">
                                {{ $i }}
                            </a>
                        @endfor
                    </div>
                @endif
            </div>
            @foreach( $projects as $project )
            <div class="meta-project d-flex orange">
                <div class="icon d-flex align-items-center justify-content-center checked"><img src="{{ asset('images/checked-project-icon.svg') }}" alt="" /></div>
                <div class="copy flex-grow-1 d-flex align-items-end justify-content-between">
                    <div class="text">
                        <h2 class="mb-1"><a href="{{ route('view-project', [ 'project_id' => $project->id ] ) }}">[{{ $project->job_code }}]</a></h2>
                        <p class="text-white">{{ $project->name }}</p>
                    </div>
                </div>
            </div>
            @endforeach


            <div class="d-flex justify-content-end my-5">
                <div class="pages d-flex">
                    <span>Page</span>
                    @for ($i = 1; $i <= $projects->lastPage(); $i++)
                        <a href="{{ $projects->url($i) }}" class="{{ $projects->currentPage() == $i ? 'active' : '' }}">
                            {{ $i }}
                        </a>
                    @endfor
                </div>
            </div>
        </div>
        <hr class="mt-0 mb-4 border-white" />
    </div>
</section>
@endif
<section class="ask-question">
    <div class="container-xxl">
        <div class="d-flex align-items-center justify-content-between pb-4">
            <div class="head">
                <h2 class="mb-0">
                    Have a question?<br />
                    <i>We’re here to help.</i>
                </h2>
            </div>
            <div class="cta-row"><a class="cta link text-decoration-none text-white" href="#">GET EMAIL UPDATES</a><a class="cta link text-decoration-none text-white" href="#">NEED HELP? CONTACT</a></div>
        </div>
        <hr class="mt-0 mt-4 border-white" />
    </div>
</section>


@endsection