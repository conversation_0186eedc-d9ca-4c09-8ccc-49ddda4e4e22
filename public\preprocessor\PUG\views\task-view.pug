- let pageName = 'Team Portal';
- let mainPage = pageName;
- let clientPage = pageName;
- let pageTitle = 'Task Overview';

include ../layouts/svgIcon.pug
include ../layouts/projectBoxBor.pug

doctype html
html(lang='en')
    include ../partials/head.pug
    +head(pageTitle)

    body.loading
        include ../partials/loader.pug
        +loader

        include ../partials/header.pug
        +header(true)

        include ../layouts/headClient.pug
        +headClient('SP-003-25 Sample project 3 (URGENT)', 'Back to Tasks', false, true)

        section.client-project.project-dashboard
            .container-xxl
                .row.projects-row
                    .col-md-3.col-xl-2.project-column.quick-links
                        .head.d-flex.justify-content-between
                            h2.text-uppercase STATUS
                            .icon-more #[i.bi.bi-three-dots]
                        hr.mt-0.mb-4.border-white
                        .statuses-list.d-flex.flex-column
                            button.flag-status.assigned Urgent
                            button.flag-status In Progress
                        .head.d-flex.justify-content-between.mt-5
                            h2.text-uppercase Assigned to
                            .icon-more #[i.bi.bi-three-dots]
                        hr.mt-0.mb-4.border-white
                        .assigned-to-list
                            .meta.d-flex.align-items-center #[span.icon #[i.bi.bi-check-square-fill]] Dev Team
                            .meta.d-flex.align-items-center #[span.icon #[i.bi.bi-check-square-fill]] Robert M.
                        .link-to-project.mt-5
                            a(href="#") Link to Project Page

                    .col-md-9.col-xl-10.project-column.task-view
                        .head.d-flex.justify-content-between
                            h2.text-uppercase TASKS OVERVIEW
                            .time #[span.icon #[i.bi.bi-clock]] Mar 12
                        hr.mt-0.mb-4.border-white
                        .message-task
                            .icon-user
                                img(src="images/drew-pic.jpg", alt="")
                            .meta-text
                                p Nam mattis metus lorem, sit amet posuere ex laoreet convallis. Fusce  accumsan turpis eu ullamcorper elementum. Nunc venenatis egestas enim,  sit amet porta lorem congue sit amet. Nam euismod convallis tortor, sed  cursus tellus ornare sed. Sed luctus enim vel nulla rutrum scelerisque.  Praesent bibendum placerat leo sed sollicitudin. Donec congue enim nibh, id suscipit lorem tempor eu.
                                p Sed consectetur iaculis lacus, sed iaculis est. Vestibulum ut aliquam odio. Proin gravida sapien rhoncus dui ullamcorper tincidunt. Fusce facilisis et massa id suscipit.

                        form.reply-task(action="")
                            .message-task.reply-message
                                .icon-user
                                textarea.meta-text.border-0(placeholder="Add reply...")
                            .cta-row.d-flex.justify-content-between.mt-5
                                .upload-btn-wrapper
                                    button.btn-upload.text-uppercase.text-white.d-flex.align-items-center #[i.bi.bi-upload.me-2] Upload a file
                                    input(type="file", name="myfile")
                                button.cta.text-uppercase.mt-0(type="submit") MARK COMPLETE

        include ../partials/footer.pug
        +footer(true)