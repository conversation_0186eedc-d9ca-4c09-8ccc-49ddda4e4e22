@extends('layout.app')
@section('title', 'File Upload')
@section('content')

    <section class="client-header">
        <div class="container-xxl">
            <hr class="mt-0 mb-4 border-white" />
            <div class="back-page">
                @if (admin_superadmin_permissions() && !Auth::user()->has<PERSON><PERSON><PERSON><PERSON>('Client Member'))
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('statushub') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to StatusHub
                    </a>
                @else
                    <a class="d-inline-flex align-items-center text-decoration-none" href="{{ route('client.dashboard') }}">
                        <img class="me-2" src="{{ asset('images/back-arrow-icon.svg') }}" alt="" /> Back to Client Portal
                    </a>
                @endif
            </div>
            <div class="meta d-flex justify-content-between align-items-center">
                <div class="copy">
                    <h1 class="text-white mb-0">Project: <i>[{{ $project->job_code }}] {{ $project->name }}</i></h1>
                    <div class="page-links mt-3">
                        <a href="{{ route('track-project', ['id' => $project->id]) }}">Track Project</a>
                        <a href="{{ route('message-centre', ['id' => $project->id]) }}">Message Center</a>
                        <a class="active" href="{{ route('file-upload', ['id' => $project->id]) }}">File Upload</a>
                        {{-- <a href="{{ route('billing-history', ['id' => $project->id]) }}">Billing History</a> --}}
                    </div>
                </div>
                <div class="logo">
                    <a href="#"><img src="{{ $client->logo ? set_user_image($client->logo) : asset('images/default-user.jpg') }}"
                            alt="Client Logo" width="72" height="80" /></a>
                </div>
            </div>
        </div>
    </section>
    <section class="client-project bg-white">
        <div class="container-xxl">
            <div class="heading d-flex align-items-center justify-content-between">
                <h2 class="mb-0">File <i>Upload</i></h2>
                <div class="links">
                    <a class="{{ request()->routeIs('message-centre*') ? 'active' : '' }}"
                        href="{{ route('message-centre', ['id' => $project->id]) }}">Messages</a>
                    <a class="{{ request()->routeIs('file-upload*') ? 'active' : '' }}"
                        href="{{ route('file-upload', ['id' => $project->id]) }}">Project Files
                        ({{ $project->files ? $project->files->count() : 0 }})</a>
                </div>
            </div>
        </div>
    </section>

    @if ($latestMessageWithFiles)
        <section class="actions-need bg-white pb-4">
            <div class="container-xxl">
                <div class="heading">
                    <h2 class="text-uppercase text-orange mb-1">LATEST FILES</h2>
                    <h3 class="d-flex align-items-center mb-2">Upload on {{ $latestMessageWithFiles->created_at->format('m/d/Y') }}</h3>
                </div>
            </div>
        </section>
        <section class="bg-white pb-5">
            <div class="container-xxl">
                <div class="message-box d-flex">
                    <div class="pic">
                        <img src="{{ set_user_image($latestMessageWithFiles->postedBy->profile_image) }}"
                            alt="{{ $latestMessageWithFiles->postedBy->name }}" />
                    </div>
                    <div class="message-wrap d-flex align-items-start">
                        <div class="message">
                            <h2><span class="quote">"</span>{{ $latestMessageWithFiles->subject }}<span class="quote">"</span></h2>
                            <div class="copy">
                                <p>{!! Str::limit(strip_tags(trim($latestMessageWithFiles->message)), 200) !!}... <a href="{{ route('view-message', ['project_id' => $project->id, 'message_id' => $latestMessageWithFiles->id]) }}">See More</a></p>
                                <div class="meta d-flex">
                                    <a class="user me-4" href="#">{{ $latestMessageWithFiles->postedBy->name }}</a>
                                    <span class="date">{{ $latestMessageWithFiles->created_at->format('m/d/Y') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="upload-cta-row">
                    <div class="row align-items-md-center">
                        @foreach ($latestMessageWithFiles->files->take(3) as $file)
                            <div class="col-md-3 mt-3">
                                <a class="cta-file d-flex align-items-center text-decoration-none" href="{{ Storage::url($file->file_path) }}" target="_blank">
                                    <span class="icon d-flex align-items-center justify-content-center me-3 ">
                                        <i class="file-icon"></i>
                                    </span>
                                    <span class="text text-truncate">{{ $file->file_name }}</span>
                                </a>
                            </div>
                        @endforeach
                        @if ($latestMessageWithFiles->files->count() > 3)
                            <div class="col-md-3 mt-3">
                                <a class="more-file text-decoration-none" href="{{ route('view-message', ['project' => $project->id, 'message' => $latestMessageWithFiles->id]) }}">
                                    {{ $latestMessageWithFiles->files->count() - 3 }} MORE, SEE ALL
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </section>
    @endif

    <section class="bg-white uploaded-files pb-5">
        <div class="container-xxl pb-5">
            {{-- @if($allProjectAttachments->IsNotEmpty()) --}}
            <div class="head d-flex justify-content-between pb-3">
                <h2 class="mb-0">All Files ({{ $project->files ? $project->files->count() : 0 }})</h2>
                @if ($files->hasPages())
                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $files->lastPage(); $i++)
                            <a class="{{ $files->currentPage() == $i ? 'active' : '' }}" href="{{ $files->url($i) }}">{{ $i }}</a>
                        @endfor
                    </div>
                @endif
            </div>
            {{-- @endif --}}
    
            <div class="row files-wrap gx-0">
                @foreach ($files as $file)
                    <div class="col-md-3 box-wrap p-3">
                        <a href="{{ Storage::url($file->file_path) }}" class="text-decoration-none">
                            <div class="box">
                                <div class="img mb-3 {{ \App\Helpers\FileHelper::isImageFile($file->file_name) ? 'pic' : 'icon d-flex align-items-center justify-content-center' }}">
                                    @if (\App\Helpers\FileHelper::isImageFile($file->file_name))
                                        <img src="{{ Storage::url($file->file_path) }}" alt="{{ $file->file_name }}" />
                                    @else
                                       <span class="icon d-flex align-items-center justify-content-center me-3 file-background">
                                        <i class="file-icon"></i>
                                    </span>
                                    @endif
                                </div>
                                <div class="text text-center">
                                    <h2 class="text-truncate">{{ $file->file_name }}</h2>
                                    <h3 class="mb-0">Added by <span class="name">{{ $file->message->postedBy->name }}</span> on <span class="date">{{ $file->created_at->format('m/d/Y') }}</span> | <span class="size">{{ \App\Helpers\FileHelper::formatFileSize($file->file_size) }}</span></h3>
                                </div>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
            @if ($files->hasPages())
                <div class="d-flex justify-content-end pt-3">
                    <div class="pages d-flex">
                        <span>Page</span>
                        @for ($i = 1; $i <= $files->lastPage(); $i++)
                            <a class="{{ $files->currentPage() == $i ? 'active' : '' }}" href="{{ $files->url($i) }}">{{ $i }}</a>
                        @endfor
                    </div>
                </div>
            @endif
        </div>
    </section>

@endsection

@push('styles')
 <style>
  .message-box {
            width: 100%;
        }

        .message-box .message-wrap {
            width: 100%;
        }

        .message-box .message-wrap .message {
            width: 100%;
        }

        .file-icon {
            font-size: 17px;
            color: white;
        }

        .file-background{
                background: #ff4c00;
    border-radius: 50%;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 32px;
    flex: 0 0 50px;
    height: 46px;
    width: 46px;
        }
</style>
@endpush

@push('script')
<script>
function getFileIcon(filename) {
    const extension = filename.split('.').pop().toLowerCase();
    const iconMap = {
        // Documents
        'pdf': 'fa-file-pdf',
        'doc': 'fa-file-word',
        'docx': 'fa-file-word',
        'xls': 'fa-file-excel',
        'xlsx': 'fa-file-excel',
        'ppt': 'fa-file-powerpoint',
        'pptx': 'fa-file-powerpoint',
        'txt': 'fa-file-alt',
        // Images
        'jpg': 'fa-file-image',
        'jpeg': 'fa-file-image',
        'png': 'fa-file-image',
        'gif': 'fa-file-image',
        'svg': 'fa-file-image',
        // Archives
        'zip': 'fa-file-archive',
        'rar': 'fa-file-archive',
        '7z': 'fa-file-archive',
        // Code
        'html': 'fa-file-code',
        'css': 'fa-file-code',
        'js': 'fa-file-code',
        'php': 'fa-file-code',
        'py': 'fa-file-code',
        'java': 'fa-file-code',
        // Default
        'default': 'fa-file'
    };
    
    return iconMap[extension] || iconMap.default;
}

document.addEventListener('DOMContentLoaded', function() {
    // Set icons for all file elements
    document.querySelectorAll('.file-icon').forEach(icon => {
        const fileName = icon.closest('a').querySelector('.text').textContent.trim();
        const iconClass = getFileIcon(fileName);
        icon.className = `file-icon fas ${iconClass}`;
    });
});
</script>
@endpush
